# 移除Nacos的技术决策说明

## 背景

本项目最初计划集成Nacos作为服务注册发现和配置中心，但在实施过程中遇到了版本兼容性问题。本文档记录了相关技术决策的原因和考量。

## 移除Nacos的原因

### 1. JDK版本兼容性问题

- **OpenSlide要求**: OpenSlide库的Java绑定需要JDK 22或更高版本支持
- **Spring Cloud兼容性**: Spring Cloud与Spring Boot有严格的版本对应关系
  - 支持Spring Boot 3.4.3的Spring Cloud版本尚未稳定发布
  - 稳定的Spring Cloud版本(如2022.0.0)仅支持Spring Boot 3.0.x
  
### 2. 依赖冲突

- Spring Cloud 2022.0.0仅兼容Spring Boot 3.0.x系列
- Spring Cloud 2023.0.0尚未完全兼容Spring Boot 3.4.3
- Spring Cloud Alibaba组件（包括Nacos客户端）与这些版本存在兼容性问题

### 3. 启动错误

在项目中集成Nacos客户端时，遇到以下错误:

```
Spring Boot [3.4.3] is not compatible with this Spring Cloud release train

Your project setup is incompatible with our requirements due to following reasons:
- Spring Boot [3.4.3] is not compatible with this Spring Cloud release train

Change Spring Boot version to one of the following versions [3.0.x]
```

### 4. 可能的解决方案评估

1. **降级Spring Boot版本**: 将Spring Boot降级到3.0.x
   - 不可行，因为这将不兼容JDK 22，而OpenSlide库需要JDK 22

2. **降级JDK版本**: 使用JDK 17-21
   - 不可行，因为OpenSlide库要求JDK 22+

3. **禁用兼容性检查**: 设置`spring.cloud.compatibility-verifier.enabled=false`
   - 风险较高，可能在运行时出现未知问题

4. **移除Nacos依赖**: 不使用Nacos，简化项目结构
   - 最直接有效的解决方案，确保核心功能正常工作

## 技术决策

基于以上分析，项目决定移除Nacos相关依赖和配置，主要原因包括:

1. **保持OpenSlide功能完整性**: 确保OpenSlide与JDK 22的兼容性是第一优先级
2. **避免复杂的兼容性问题**: 移除非核心功能，专注于主要业务逻辑
3. **减少技术风险**: 避免使用未经充分验证的组件组合
4. **简化部署和维护**: 减少依赖，简化系统架构

## 后续建议

如果将来仍需要服务注册发现和配置中心功能，建议考虑以下方案:

1. **微服务拆分**: 将OpenSlide功能独立部署，通过API网关与使用Nacos的服务交互
2. **等待版本更新**: 等待Spring Cloud生态完全兼容Spring Boot 3.4.x和JDK 22
3. **替代方案**: 考虑使用其他配置中心解决方案，如Consul、Etcd或基于文件的配置

## 版本兼容性参考

| 组件 | 版本要求 | 说明 |
|-----|--------|------|
| OpenSlide Java | 0.13.0 | 要求JDK 22+ |
| Spring Boot | 3.4.3 | 支持JDK 22 |
| Spring Cloud | 2022.0.0 | 仅兼容Spring Boot 3.0.x |
| Spring Cloud | 2023.0.0 | 尚未完全兼容Spring Boot 3.4.x |
| Spring Cloud Alibaba | 2022.0.0.0 | 与Spring Cloud 2022.0.0兼容 |

## 结论

移除Nacos是基于技术兼容性考虑的最佳解决方案，确保了系统的稳定性和可维护性。这种方案虽然牺牲了一些分布式特性，但保障了核心功能的稳定运行，符合当前项目的实际需求。 