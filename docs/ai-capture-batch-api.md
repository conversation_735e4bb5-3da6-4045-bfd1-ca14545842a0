# AI批量截图接口文档

## 1. 接口说明
根据大模型返回的坐标批量生成图片。

---

## 2. 请求 URL
```
POST /slice/api/slice/SaveAIQpCaptureBatch
```

---

## 3. 功能描述
接收一组AI坐标批量请求，批量生成切片图片。

---

## 4. 入参
- Content-Type: application/json
- 请求体为JSON数组，每个元素为一个截图任务，结构如下：

### 字段说明（单个元素）
| 字段名   | 类型   | 必填 | 说明           |
| -------- | ------ | ---- | -------------- |
| fileId   | String | 是   | 切片文件ID     |
| cutimgid | String | 是   | 截图唯一标识   |
| x        | Int    | 是   | 截图左上角X坐标|
| y        | Int    | 是   | 截图左上角Y坐标|
| w        | Int    | 是   | 截图宽度       |
| h        | Int    | 是   | 截图高度       |

---

## 5. 出参
- Content-Type: application/json
- 返回结构如下：

| 字段名 | 类型   | 说明         |
| ------ | ------ | ------------ |
| status | String | 状态（success/error）|
| data   | Array  | 批量截图结果数组，每个元素结构见下表 |
| msg    | String | 总体提示信息 |

### data 数组元素结构
| 字段名   | 类型   | 说明                 |
| -------- | ------ | -------------------- |
| cutimgid | String | 截图唯一标识         |
| image    | String | 图片Base64字符串     |
| msg      | String | 单个截图处理结果说明 |

---

## 6. 请求示例
```
POST /slice/api/slice/SaveAIQpCaptureBatch
Content-Type: application/json

[
  {
    "fileId": "01K0BFG1ZEKWYP3S9NWWM853B7",
    "cutimgid": "ai_capture_001",
    "x": 1000,
    "y": 1000,
    "w": 200,
    "h": 200
  },
  {
    "fileId": "01K0BFG1ZEKWYP3S9NWWM853B7",
    "cutimgid": "ai_capture_002",
    "x": 1500,
    "y": 1200,
    "w": 150,
    "h": 150
  }
]
```

---

## 7. 返回示例
```
{
    "status": "success",
    "data": [
        {
            "cutimgid": "ai_capture_001",
            "image": "base64demo1",
            "msg": "成功"
        },
        {
            "cutimgid": "ai_capture_002",
            "image": "base64demo2",
            "msg": "成功"
        }
    ],
    "msg": "批量截图完成，成功: 2, 失败: 0"
}
``` 