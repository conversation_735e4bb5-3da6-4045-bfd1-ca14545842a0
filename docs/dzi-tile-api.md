# DZI 瓦片图获取接口文档

## 1. 接口说明
用于获取切片的 Deep Zoom Image（DZI）瓦片图片，支持多层级、坐标及多种图像参数调整，适用于 OpenSeadragon 等深度缩放场景。

---

## 2. 请求 URL
```
GET /slice/api/dzi/{identifier}_files/{level}/{x}_{y}.jpg
```

- 示例：http://127.0.0.1:8089/slice/api/dzi/01K0BFG1ZEKWYP3S9NWWM853B7_files/15/50_28.jpg?cname=qpDhc

---

## 3. 路径参数
| 参数名      | 类型   | 必填 | 说明           |
| ----------- | ------ | ---- | -------------- |
| identifier  | String | 是   | 切片唯一标识   |
| level       | int    | 是   | 瓦片层级       |
| x           | int    | 是   | 瓦片横向坐标   |
| y           | int    | 是   | 瓦片纵向坐标   |

---

## 4. 查询参数（QueryString）
| 参数名      | 类型   | 必填 | 默认值 | 取值范围/说明                |
| ----------- | ------ | ---- | ------ | ---------------------------- |
| cname       | String | 是   | -      | 切片名称（数据库 partner_code 字段） |
| brightness  | Float  | 否   | 0.0    | 亮度调整，0.0 ~ 1.0         |
| contrast    | Float  | 否   | 1.0    | 对比度调整，0.0 ~ 2.0        |
| gamma       | Float  | 否   | 1.0    | 伽马校正，0.0 ~ 2.0          |
| saturation  | Float  | 否   | 1.0    | 饱和度调整，0.0 ~ 2.0        |
| redGain     | Float  | 否   | 1.0    | 红色通道增益，0.0 ~ 2.0      |
| greenGain   | Float  | 否   | 1.0    | 绿色通道增益，0.0 ~ 2.0      |
| blueGain    | Float  | 否   | 1.0    | 蓝色通道增益，0.0 ~ 2.0      |
| sharpen     | Int    | 否   | 0      | 锐化强度，0 ~ 10             |
| colorStyle  | Int    | 否   | 0      | 颜色风格，0=默认，1=H&E标准化 |

---

## 5. 请求示例
```
GET /slice/api/dzi/01K0BFG1ZEKWYP3S9NWWM853B7_files/15/50_28.jpg?cname=qpDhc&brightness=0.2&contrast=1.1
```

---

## 6. 返回结果
- Content-Type: image/jpeg
- 正常时：返回对应瓦片的 JPEG 图片（二进制流） 