# ccaa-Slice 项目概览

本文档提供 ccaa-Slice 项目的概述，重点介绍该项目的功能、架构和技术特点。

## 项目说明

ccaa-Slice 是一个基于 Spring Boot 3.4.3 构建的服务项目，旨在提供数字病理切片处理的 Web API 服务。该项目集成了 OpenSlide 和 SqraySlide 库，支持多种数字病理切片格式的读取、处理和展示，包括 SVS、NDPI、SDPC 等格式。

## 主要特性

- **多格式支持**：集成 OpenSlide 0.13.0 库和 SqraySlide 库，支持多种病理切片格式
- **高性能处理**：内置多级缓存机制，提高图像处理和访问性能
- **大文件处理**：支持分区域读取超大切片，支持大文件分块上传及进度追踪
- **异步处理**：支持文件上传和处理的异步操作，提高系统响应性
- **对象存储集成**：集成 Minio 对象存储（版本 RELEASE.2021-04-22T15-44-28Z）
- **分布式缓存**：集成 Redis 7.4.0 用于进度跟踪和分布式缓存
- **完整 API**：提供全面的 RESTful API 接口，支持切片查看、上传和管理
- **现代技术栈**：支持 JDK 22（预览特性），基于 Spring Boot 3.4.3 实现

## 系统架构

ccaa-Slice 采用模块化设计，主要包括以下几个核心组件：

- **Web API 层**：处理 HTTP 请求和响应，提供 RESTful API 接口
- **服务层**：实现业务逻辑，协调各个模块的工作
- **解析器层**：负责解析不同格式的数字病理切片文件
- **存储层**：负责文件的存储和检索
- **缓存层**：提供多级缓存机制，提高系统性能

详细架构请参考 [技术架构文档](architecture.md)。

## 主要功能

### 切片文件处理

- 支持多种切片格式的读取和解析
- 提供切片元数据、缩略图和标签图提取
- 支持多分辨率层级访问
- 支持区域和瓦片图像提取

### 文件上传与管理

- 支持大文件分块上传
- 提供上传进度跟踪
- 支持异步文件处理
- 支持上传任务管理和取消

### 系统管理

- 提供缓存统计和管理功能
- 支持系统监控和性能分析
- 提供资源清理和优化功能

## 详细文档

如需了解更多关于项目的详细信息，请参考以下文档：

- [API 参考文档](api-reference.md)：详细的 API 接口规范
- [技术架构文档](architecture.md)：系统架构和技术实现详情
- [开发指南](development-guide.md)：开发环境搭建和代码规范
- [部署指南](deployment-guide.md)：系统部署和配置说明
- [Nacos移除说明](nacos-removal.md)：关于移除Nacos的技术决策说明

## 环境要求

- **JDK**：JDK 22（需启用预览特性）
- **依赖库**：
  - OpenSlide 0.13.0+
  - SqraySlide 库
  - openslide-java 0.13.0+
- **构建工具**：Maven 3.9.0+
- **存储服务**：Minio 对象存储服务（版本 RELEASE.2021-04-22T15-44-28Z）
- **缓存服务**：Redis 7.4.0 服务（用于上传进度跟踪和分布式缓存）

## 联系方式

如有问题或建议，请联系项目维护团队。
