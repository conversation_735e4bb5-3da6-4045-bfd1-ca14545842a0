# 重新推送接口文档

## 接口概述

新增的重新推送接口允许根据切片ID重新调用三方Info接口进行推送。该接口会检查图片文件是否存在，如果不存在则会重新解析切片文件生成图片，然后进行推送。

## 接口信息

**接口路径：** `POST /api/upload/repush/{id}`

**请求参数：**
- `id`：切片ID（主键），路径参数，必填

**响应格式：** JSON

## 处理流程

1. **查询切片信息**：根据ID查询数据库中的切片记录
2. **检查partnerCode**：如果没有partnerCode则跳过推送
3. **检查文件状态**：
   - 检查切片文件是否存在
   - 检查缩略图等图片文件是否存在
4. **重新处理（如需要）**：如果图片文件不存在，重新解析切片文件生成图片
5. **执行推送**：调用现有的三方推送逻辑

## 请求示例

```bash
curl -X POST "http://localhost:8080/api/upload/repush/01HQZK9ABCDEFGHIJKLMNOPQRS" \
  -H "Content-Type: application/json"
```

## 响应示例

### 成功响应

```json
{
  "success": true,
  "data": {
    "id": "01HQZK9ABCDEFGHIJKLMNOPQRS",
    "partnerCode": "partner123",
    "needReprocess": false,
    "pushSuccess": true,
    "status": "success",
    "message": "重新推送成功"
  },
  "message": "操作成功"
}
```

### 需要重新处理的成功响应

```json
{
  "success": true,
  "data": {
    "id": "01HQZK9ABCDEFGHIJKLMNOPQRS",
    "partnerCode": "partner123",
    "needReprocess": true,
    "reprocessReason": "缩略图或其他图片文件不存在，需要重新生成",
    "pushSuccess": true,
    "status": "success",
    "message": "重新推送成功"
  },
  "message": "操作成功"
}
```

### 跳过推送响应

```json
{
  "success": true,
  "data": {
    "id": "01HQZK9ABCDEFGHIJKLMNOPQRS",
    "status": "skipped",
    "message": "该切片没有partnerCode，无需推送"
  },
  "message": "操作成功"
}
```

### 错误响应

#### 切片记录不存在

```json
{
  "success": false,
  "code": "RESOURCE_NOT_FOUND",
  "message": "切片记录不存在"
}
```

#### 推送失败

```json
{
  "success": false,
  "code": "UPLOAD_ERROR",
  "message": "重新推送失败"
}
```

#### 重新解析失败

```json
{
  "success": false,
  "code": "SLIDE_ERROR",
  "message": "重新解析切片文件失败"
}
```

## 注意事项

1. **主键ID**：接口使用的是数据库表的主键ID（`t_c_slice.id`字段），不是`slice_id`字段
2. **partnerCode检查**：只有设置了`partnerCode`的切片记录才会执行推送
3. **文件检查**：接口会检查物理文件是否存在，确保推送的数据完整
4. **异步处理**：重新解析切片文件的过程是异步的，接口会等待完成后再执行推送
5. **复用逻辑**：接口复用了现有的切片解析和推送逻辑，保证了一致性

## 使用场景

- 修复因图片文件丢失导致的推送失败问题
- 重新推送之前失败的切片信息
- 在三方系统出现问题恢复后批量重新推送数据
- 手动触发特定切片的信息推送

## 日志说明

接口执行过程中会产生详细的日志，包括：
- 切片记录查询日志
- 文件存在性检查日志  
- 重新处理过程日志
- 推送结果日志

日志级别为INFO，可以通过查看应用日志来跟踪接口执行情况。 