# ccaa-Slice 部署指南

本文档提供 ccaa-Slice 项目的完整部署指南，包括环境准备、依赖库安装、应用部署和配置说明。

## 一、系统要求

### 硬件要求

- **CPU**: 8核或更高
- **内存**: 16GB或更高（推荐32GB）
- **存储**: 100GB以上SSD存储（取决于切片文件大小）
- **网络**: 千兆网络或更高

### 软件要求

- **操作系统**: CentOS 7/8, RHEL 7/8, Ubuntu 20.04/22.04
- **JDK**: JDK 22（需启用预览特性）
- **Redis**: 7.4.0或更高版本
- **Minio**: RELEASE.2021-04-22T15-44-28Z或更高版本

## 二、系统依赖安装

### CentOS/RHEL系统

```bash
# 安装基础开发工具和编译依赖
sudo yum update
sudo yum install -y gcc gcc-c++ pkgconfig make wget

# 安装OpenSlide依赖
sudo yum install -y zlib-devel libjpeg-devel libpng-devel libtiff-devel openjpeg2-devel
sudo yum install -y glib2-devel cairo-devel libxml2-devel sqlite-devel gdk-pixbuf2-devel
sudo yum install -y epel-release
sudo yum install -y meson ninja-build git

# 安装SqraySlide依赖库
sudo yum install -y libicu speex libva libvdpau ffmpeg-libs mesa-libGL mesa-dri-drivers 

yum install -y numactl-libs

localedef -c -f UTF-8 -i zh_CN zh_CN.UTF-8 
```

### Ubuntu系统

```bash
# 安装基础开发工具和编译依赖
sudo apt update
sudo apt install -y build-essential pkg-config wget

# 安装OpenSlide依赖
sudo apt install -y libz-dev libjpeg-dev libpng-dev libtiff-dev libopenjp2-7-dev
sudo apt install -y libglib2.0-dev libcairo2-dev libxml2-dev libsqlite3-dev libgdk-pixbuf2.0-dev
sudo apt install -y meson ninja-build

# 安装SqraySlide依赖库
sudo apt install -y libicu-dev libspeex-dev libva-dev libvdpau-dev ffmpeg libgl1-mesa-dev mesa-utils
```

## 三、部署SqraySlide库

```bash
# 创建目录
sudo mkdir -p /usr/local/sqrayslide/

# 上传lib和include目录至/usr/local/sqrayslide/目录
# [确保已上传文件到服务器]

# 添加库路径到系统
sudo sh -c 'echo "/usr/local/sqrayslide/lib" > /etc/ld.so.conf.d/sqrayslide.conf'

# 进入库目录
cd /usr/local/sqrayslide/lib

# 为库文件创建符号链接
for lib in $(ls lib*.so.*.*.* 2>/dev/null); do
  base=$(echo $lib | sed 's/\(.*\.so\.[0-9]*\).*/\1/')
  if [ -f "$base" ] && [ ! -L "$base" ]; then
    sudo mv "$base" "${base}.backup"
    sudo ln -sf "$lib" "$base"
    echo "创建链接: $base -> $lib"
  fi
done

# 特殊处理libzip库
if [ -f "libzip.so.5.5" ] && [ -f "libzip.so.5" ] && [ ! -L "libzip.so.5" ]; then
  sudo mv "libzip.so.5" "libzip.so.5.backup"
  sudo ln -sf "libzip.so.5.5" "libzip.so.5"
  echo "创建链接: libzip.so.5 -> libzip.so.5.5"
fi

# 更新动态链接库缓存
sudo ldconfig
```

## 四、安装OpenSlide库

```bash
# 安装最新版SQLite（如果系统版本过低）
cd /tmp
wget https://www.sqlite.org/2023/sqlite-autoconf-3420000.tar.gz
tar -xzf sqlite-autoconf-3420000.tar.gz
cd sqlite-autoconf-3420000
./configure --prefix=/usr/local
make
sudo make install

# 更新SQLite库路径
sudo sh -c 'echo "/usr/local/lib" > /etc/ld.so.conf.d/sqlite3.conf'
sudo ldconfig

# 设置PKG_CONFIG_PATH
export PKG_CONFIG_PATH=/usr/local/lib/pkgconfig:$PKG_CONFIG_PATH

# 下载并编译OpenSlide 4.0.0
cd /tmp
wget https://github.com/openslide/openslide/archive/refs/tags/v4.0.0.tar.gz
tar -zxvf v4.0.0.tar.gz
cd openslide-4.0.0
meson setup builddir
cd builddir
ninja
sudo ninja install

# 添加OpenSlide库路径并创建符号链接
sudo sh -c 'echo "/usr/local/lib64" > /etc/ld.so.conf.d/openslide-lib64.conf'
sudo ldconfig

# 修复可能的符号链接问题
cd /usr/local/lib64
if [ -f "libopenslide.so.1.0.0" ] && [ ! -L "libopenslide.so.1" ]; then
  sudo ln -sf libopenslide.so.1.0.0 libopenslide.so.1
  sudo ldconfig
fi
```

## 五、安装JDK 22

```bash
# 下载JDK 22
cd /tmp
wget https://download.oracle.com/java/22/latest/jdk-22_linux-x64_bin.tar.gz

# 解压到/usr/local
sudo mkdir -p /usr/local/java
sudo tar -zxvf OpenJDK22U-jdk_x64_linux_hotspot_22.0.2_9.tar.gz -C /usr/local/java/

# 设置环境变量
sudo sh -c 'cat > /etc/profile.d/jdk.sh << EOF
export JAVA_HOME=/usr/local/java/jdk-22.0.2+9
export PATH=$JAVA_HOME/bin:$PATH
EOF'

# 使环境变量生效
source /etc/profile.d/jdk.sh

# 验证JDK版本
java --version
```

## 六、安装Redis

```bash
# 安装Redis
## CentOS/RHEL
sudo yum install -y redis

## Ubuntu
sudo apt install -y redis-server

# 配置Redis
sudo cp /etc/redis/redis.conf /etc/redis/redis.conf.backup
sudo sed -i 's/bind 127.0.0.1/bind 0.0.0.0/g' /etc/redis/redis.conf  # 允许远程连接，生产环境建议设置密码
sudo sed -i 's/# requirepass foobared/requirepass 1qaz!QAZ0.0/g' /etc/redis/redis.conf  # 设置密码

# 启动Redis并设置开机自启
sudo systemctl start redis
sudo systemctl enable redis

# 验证Redis是否正常运行
redis-cli -a "1qaz!QAZ0.0" ping  # 应返回PONG
```

## 七、安装Minio

```bash
# 下载Minio
wget https://dl.min.io/server/minio/release/linux-amd64/archive/minio.RELEASE.2021-04-22T15-44-28Z -O minio
chmod +x minio
sudo mv minio /usr/local/bin/

# 创建存储目录
sudo mkdir -p /data/minio

# 创建Minio服务文件
sudo sh -c 'cat > /etc/systemd/system/minio.service << EOF
[Unit]
Description=MinIO
Documentation=https://docs.min.io
Wants=network-online.target
After=network-online.target

[Service]
User=root
Group=root
Environment="MINIO_ROOT_USER=admin"
Environment="MINIO_ROOT_PASSWORD=3cu8rt00"
ExecStart=/usr/local/bin/minio server /data/minio --console-address ":9001"
Restart=always
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF'

# 启动Minio并设置开机自启
sudo systemctl daemon-reload
sudo systemctl start minio
sudo systemctl enable minio

# 验证Minio是否正常运行
curl -I http://localhost:9000  # 应返回HTTP/1.1 403 Forbidden
```

## 八、部署ccaa-Slice应用

### 1. 准备应用目录

```bash
# 创建应用目录
sudo mkdir -p /mitr/server/ccaa-slice
sudo mkdir -p /mitr/server/ccaa-slice/logs
sudo mkdir -p /mitr/server/ccaa-slice/temp
```

### 2. 上传应用文件

将编译好的JAR文件上传到服务器：

```bash
# 假设JAR文件已上传到当前目录
sudo cp ccaa-slice.jar /mitr/server/ccaa-slice/
```

### 3. 创建配置文件

```bash
sudo sh -c 'cat > /mitr/server/ccaa-slice/application.yml << EOF
# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /slice

# Spring相关配置
spring:
  application:
    name: ccaa-slice

  # 允许Bean覆盖（解决Bean名称冲突问题）
  main:
    allow-bean-definition-overriding: true

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB

  # Redis配置
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      password: 1qaz!QAZ0.0
      database: 0
      timeout: 5000ms
      connect-timeout: 5000ms
      client-name: ccaa-slice

      # 连接池配置
      lettuce:
        pool:
          max-active: 16
          max-idle: 8
          min-idle: 2
          max-wait: 3000ms
          time-between-eviction-runs: 10000ms

# 存储服务配置
storage:
  type: minio
  # 文件配置
  file:
    enabled: false     # 禁用本地存储
    upload-dir: slides
    max-size: 1073741824
    allowed-types: [svs, ndpi, sdpc]

  # Minio配置
  minio:
    endpoint: http://127.0.0.1:9000
    access-key: admin
    secret-key: 3cu8rt00
    bucket: slice
    # 分块上传大小（字节）
    part-size: 10485760

  # 临时文件配置
  temp:
    base-dir: ./temp   # 默认为应用目录下的temp文件夹

# 切片相关配置
slide:
  # OpenSlide库配置
  openslide:
    library-path: /usr/local/lib64/libopenslide.so.1

  # SqraySlide库配置
  sqrayslide:
    enabled: true                    # 如果库不可用，可以设置为false禁用SqraySlide解析器
    library-path: /usr/local/sqrayslide/lib/libsqrayslideservice.so
    jpeg-quality: 90

  # 切片缓存特定配置
  cache:
    format-max-size: 1000            # 格式缓存最大条目数
    thumbnail-max-size: 500          # 缩略图缓存最大条目数
    tile-max-size: 10000             # 瓦片缓存最大条目数
    metadata-max-size: 1000          # 元数据缓存最大条目数
    region-max-size: 500             # 区域缓存最大条目数
    expire-after-minutes: 60         # 缓存过期时间（分钟）

# 缓存配置
cache:
  enabled: true                      # 是否启用缓存
  maximum-size: 10000                # 最大缓存条目数（非权重型缓存）
  maximum-weight-m-b: 8192           # 最大缓存权重，8GB（权重型缓存）
  expire-after-write-minutes: 120    # 写入后过期时间（120分钟）
  expire-after-access-minutes: 240   # 访问后过期时间（240分钟）
  record-stats: true                 # 记录缓存统计

  # 文件缓存清理配置
  file:
    retention-hours: 48              # 缓存文件保留时间（小时）

  temp:
    retention-hours: 72              # 临时文件保留时间（小时）

# 日志配置
logging:
  level:
    root: info
    '[cn.ccaa.slice]': debug
    '[cn.ccaa.slice.service.storage.MinioStorageService]': debug
    '[cn.ccaa.slice.service.analysis.impl.SliceAnalysisServiceImpl]': debug
    '[cn.ccaa.slice.parsers.sqrayslide]': info
    '[org.springframework.cache]': info
    '[org.springframework.data.redis]': info
  file:
    name: logs/ccaa-slice.log
    max-size: 100MB
    max-history: 30
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'
EOF'
```

### 4. 创建服务文件

```bash
sudo sh -c 'cat > /etc/systemd/system/ccaa-slice.service << EOF
[Unit]
Description=CCA Slice Service
After=network.target redis.service minio.service

[Service]
User=root
Group=root
WorkingDirectory=/mitr/server/ccaa-slice
ExecStart=/usr/local/java/jdk-22/bin/java --enable-native-access=ALL-UNNAMED -Xms1g -Xmx4g -jar ccaa-slice.jar
Restart=always

[Install]
WantedBy=multi-user.target
EOF'

# 重新加载systemd配置
sudo systemctl daemon-reload
```

### 5. 启动应用

```bash
# 启动应用
sudo systemctl start ccaa-slice

# 设置开机自启
sudo systemctl enable ccaa-slice

# 查看应用状态
sudo systemctl status ccaa-slice

# 查看应用日志
sudo journalctl -u ccaa-slice -f
```

## 九、验证安装

### 1. 验证依赖库

```bash
# 验证动态库是否正确安装
ldconfig -p | grep "sqrayslide"
ldconfig -p | grep "openslide"

# 验证依赖库是否满足
ldd /usr/local/sqrayslide/lib/libsqrayslideservice.so
ldd /usr/local/lib64/libopenslide.so.1
```

### 2. 验证应用

```bash
# 检查应用是否正常运行
curl -I http://localhost:8080/slice/actuator/health

# 应返回HTTP/1.1 200 OK
```

## 十、Minio存储桶配置

1. 访问Minio控制台：http://服务器IP:9001
2. 使用配置的用户名和密码登录（admin/3cu8rt00）
3. 创建名为"slice"的存储桶
4. 设置存储桶访问策略为"public"

## 十一、常见问题排查

### 1. 应用无法启动

检查日志文件：
```bash
tail -f /mitr/server/ccaa-slice/logs/ccaa-slice.log
```

常见问题：
- JDK版本不匹配：确保使用JDK 22并启用预览特性
- 依赖库路径错误：检查OpenSlide和SqraySlide库路径配置
- Redis连接失败：检查Redis服务是否正常运行
- Minio连接失败：检查Minio服务是否正常运行

### 2. 无法解析切片文件

- 检查OpenSlide和SqraySlide库是否正确安装
- 检查文件格式是否受支持
- 检查文件权限是否正确

### 3. 性能问题

- 增加JVM堆内存：修改服务文件中的-Xmx参数
- 优化缓存配置：调整缓存大小和过期时间
- 检查磁盘IO性能：使用iostat工具监控磁盘性能

## 十二、备份与恢复

### 1. 配置备份

```bash
# 备份配置文件
sudo cp /mitr/server/ccaa-slice/application.yml /mitr/server/ccaa-slice/application.yml.backup
```

### 2. Minio数据备份

```bash
# 备份Minio数据
sudo cp -r /data/minio /data/minio.backup
```

### 3. 应用升级

```bash
# 停止应用
sudo systemctl stop ccaa-slice

# 备份旧版本
sudo mv /mitr/server/ccaa-slice/ccaa-slice.jar /mitr/server/ccaa-slice/ccaa-slice.jar.old

# 上传新版本
sudo cp new-ccaa-slice.jar /mitr/server/ccaa-slice/ccaa-slice.jar

# 启动应用
sudo systemctl start ccaa-slice
```
