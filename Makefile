CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g
TRON_SDK_PATH = /Users/<USER>/Desktop/tronsdk.c.rust.1.1.1-linux.glibc2.23
TRON_LIB_PATH = $(TRON_SDK_PATH)/c/lib
TRON_INCLUDE_PATH = $(TRON_SDK_PATH)/c/headers

# 目标文件
TARGET = test_tronsdk
SOURCE = test_tronsdk.c

# 编译规则
$(TARGET): $(SOURCE)
	$(CC) $(CFLAGS) -I$(TRON_INCLUDE_PATH) -L$(TRON_LIB_PATH) -o $(TARGET) $(SOURCE) -ltronc -Wl,-rpath,$(TRON_LIB_PATH)

# 清理规则
clean:
	rm -f $(TARGET) test_*.dat

# 检查TronSDK库
check:
	@echo "检查TronSDK库文件..."
	@ls -la $(TRON_LIB_PATH)/libtronc.so
	@echo "检查TronSDK头文件..."
	@ls -la $(TRON_INCLUDE_PATH)/tronc.h
	@echo "检查库依赖..."
	@file $(TRON_LIB_PATH)/libtronc.so
	@echo "检查库符号..."
	@nm -D $(TRON_LIB_PATH)/libtronc.so | grep tron_open || echo "未找到tron_open符号"

# 帮助信息
help:
	@echo "可用的make目标:"
	@echo "  $(TARGET)  - 编译测试程序"
	@echo "  clean      - 清理生成的文件"
	@echo "  check      - 检查TronSDK库文件"
	@echo "  help       - 显示此帮助信息"
	@echo ""
	@echo "使用方法:"
	@echo "  make $(TARGET)                    # 编译程序"
	@echo "  ./$(TARGET) <tron文件路径>        # 运行测试"

.PHONY: clean check help 