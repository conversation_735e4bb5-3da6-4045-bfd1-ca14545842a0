#include "../headers/tronc.h"
#include <stdio.h>

int main()
{
    //* Open a tron file with tron_open().
    Handle *handle_ptr = tron_open("non-existed.tron");
    if (!handle_ptr)
    {
        //* If anything wrong happens while opening the file, tron_open() will return nullptr.
        //* Use tron_get_last_error() to retrive the error code.
        //* Several other APIs generate error codes too, so if anything goes wrong, try to
        //* find out with the error code.
        //* The error code definitions are in tronc.h, by the #define constants beginning with
        //* TRON_.
        int32_t last_error = tron_get_last_error();
        //* For example, this tron file does not exist, TRON_IO_ERROR is returned.
        printf("Cannot open 'non-existed.tron', error code: %d\n", last_error);
    }

    //* This file we are sure that exists.
    handle_ptr = tron_open("test.tron");

    //* Some APIs tries to retieve string content. Here is the convention of these APIs: you
    //* provide a buffer of char array, and the max length of the buffer. If the buffer has
    //* enough capacity to carry the string (determined by the max length you specified),
    //* the string will be copied to this buffer; otherwise nothing will be copied, and a
    //* last error code of TRON_INSUFFICIENT_LENGTH will be set. Either way the API returns
    //* the total length of the string content, plus one for the ending \0. So if a number
    //* larger than the buffer length you specified returned, you know to allocate a larger
    //* buffer to retrieve the string content.
    //* It's also possible to start with a zero-length buffer to retrieve the length, and
    //* create a buffer accordingly. However, since most string content won't be too long,
    //* a reasonalby large buffer, like 256 bytes, is sufficient for most cases.
    char buffer[256] = {0};

    //* The name of the slide.
    tron_get_name(handle_ptr, buffer, 256);
    printf("Name: %s\n", buffer);

    //* The quick hash value of the slide. Could be used to identify slides.
    tron_get_quick_hash(handle_ptr, buffer, 256);
    printf("Quick Hash: %s\n", buffer);

    //* The vendor of the scanner which generates the slide.
    tron_get_vendor(handle_ptr, buffer, 256);
    printf("Vendor: %s\n", buffer);

    //* The version of the slide file format. It consists of a major and a minor component.
    //* If either component was not specified in the slide, it will be zero.
    TronVersion version = tron_get_version(handle_ptr);
    printf("Version: %d.%d\n", version.major, version.minor);

    //* The comments which user input before scanning this slide.
    tron_get_comments(handle_ptr, buffer, 256);
    printf("Comments: %s\n", buffer);

    //* The range of LOD levels. Tron archive is in a hierachical structure, divided into
    //* several LOD (level of detail). LOD with a larger number has a more macro view, i.e.
    //* "zoomed out", while LOD0 has the most micro view, pixel exact to the image generated
    //* by the scanner.
    TronLodLevelRange lod_level_range = tron_get_lod_level_range(handle_ptr);
    printf("LOD Levels: %d to %d\n", lod_level_range.minimum, lod_level_range.maximum);

    //* The (Z-)layer count of this slide. A slide may contain multiple layers, each scanned
    //* in a different depth; and it may have all these layers fused together, known as the
    //* "merged layer". The merged layer will always have a layer index of 0; other layers
    //* have indices starting from 1.
    int32_t layer_count = tron_get_layer_count(handle_ptr);
    printf("Layer Count: %d\n", layer_count);

    //* The index of the representative (Z-)layer. The reperesentative layer is meant to be used
    //* to render or analyze by default. Generally speaking, it's 1 for single-layered slide, or
    //* 0 for multi-layered slide, which is the merged layer.
    int32_t representative_layer_index = tron_get_representative_layer_index(handle_ptr);
    printf("Representative Layer Index: %d\n", representative_layer_index);

    //* LOD gaps are scale ratios between adjacent LOD levels. Currently these values are all
    //* 2.0, which means LOD1 is scaled out by the factor of 1/2 comparing to LOD0, and LOD2
    //* has the half size of LOD1, so forth. However this value can be changed in the future
    //* model of scanners, so it's always a good practise to get the gap value from the API.
    printf("LOD Gaps: ");
    for (int32_t i = 0; i < lod_level_range.maximum - 1; ++i)
    {
        //* This retrieves the LOD gap between LOD{i} and LOD{i+1}.
        float lod_gap = tron_get_lod_gap_of(handle_ptr, i);
        printf("%f ", lod_gap);
    }
    printf("\n");

    //* Gets the maximum zoom level of this side, which is the actual zoom level of LOD0.
    int32_t maximum_zoom_level = tron_get_maximum_zoom_level(handle_ptr);
    printf("Maximum zoom level: %d\n", maximum_zoom_level);

    //* Pixel size of each tile.
    TronTileSize tile_size = tron_get_tile_size(handle_ptr);
    printf("Tile Size: %d * %d\n", tile_size.width, tile_size.height);

    //* The rectangular region of the content area, i.e. non-empty area.
    //* Typically we don't scan the entire slide, because most of it is empty. The scanner
    //* detects the areas with content and scan accordingly, while still keep them in the
    //* slide's coordinate space.
    TronContentRegion content_region = tron_get_content_region(handle_ptr);
    printf("Content Region: x=%d, y=%d, w=%d, h=%d\n", content_region.left, content_region.top, content_region.width, content_region.height);

    //* The resolution of the slide, in micrometers per pixel. This can be used to calculate
    //* the real world size of features in the slide, from its pixel size.
    TronResolution resolution = tron_get_resolution(handle_ptr);
    printf("Resolution: x=%f, y=%f\n", resolution.horizontal, resolution.vertical);

    //* The background color which should be used to fill the non-content areas.
    TronBackgroundColor background_color = tron_get_background_color(handle_ptr);
    printf("Background Color: rgb(%d, %d, %d)\n", background_color.red, background_color.green, background_color.blue);

    //* Get information of the macro image. A slide may contain several "named images", whose
    //* names are commonly known as "label", "macro", "sample" and "thumbnail". Tron archives
    //* typically have all of them, and more named images may be added in the future. To make
    //* the demo compact, the sample archive only contains a "macro" image.
    //* You can always try to call this API with any arbitrary image name, and check if that
    //* image exists in the returned structure.
    TronImageInfo macro_image_info = tron_get_named_image_info(handle_ptr, "macro");
    if (macro_image_info.existed)
    {
        printf("Macro image: size: %lu * %lu, bytes: %lu\n", macro_image_info.width, macro_image_info.height, macro_image_info.length);

        //* So we know the length of the image, we can allocate a buffer to retrieve its pixel
        //* data.
        //* The pixel data is in BGR24 format, uncompressed. It always has a stride the same as
        //* the image's width.
        u_char *macro_image_buffer = (u_char *)malloc(macro_image_info.length);
        //* The return value can be used to verify the buffer length.
        size_t read_bytes = tron_get_named_image_data(handle_ptr, "macro", macro_image_buffer);
        printf("%lu bytes read from the macro image data\n", read_bytes);
        free(macro_image_buffer);
    }

    //* Similar APIs go to tile images. A tile image can be located with four dimensions:
    //* - lod_level: the LOD as described above
    //* - layer: the z-layer index. Typically 1 if this is a single layered slide. If this is a
    //*          multi-layered slide, use 0 for the stacked layer and 1 to {n} for {n}th layer.
    //* - row: the y coordinate in its LOD plane.
    //* - column: the x coordinate in its LOD plane.
    TronImageInfo tile_image_info = tron_get_tile_image_info(handle_ptr, 0, 1, 25, 25);
    if (tile_image_info.existed)
    {
        //* A tile image may not exist, even if it's within the content region. For such tiles,
        //* it is intended that the corresponding area should rendered as blank.
        //* To make the demo compact, the sample archive only contains a few tile images.
        printf("Tile image: size: %lu * %lu, bytes: %lu\n", tile_image_info.width, tile_image_info.height, tile_image_info.length);

        u_char *tile_image_buffer = (u_char *)malloc(tile_image_info.length);
        size_t read_bytes = tron_get_tile_image_data(handle_ptr, 0, 1, 25, 25, tile_image_buffer);
        printf("%lu bytes read from the tile image data\n", read_bytes);
        free(tile_image_buffer);
    }

    {
        //* Use tron_read_region to read a rectangular area of a slide. This function mimics the 
        //* behavior of openslide's read_region function.
        //* Note: unlike openslide, this function returns Bgr24 image (consistent with all the 
        //* image reading APIs), rather than Bgra32 image as openslide does.

        //* The following example reads a region of 1536 * 1536 square pixels from the archive,
        //* which spans across 9 tiles (from the bottom-right of (25, 25) to the top-left of 
        //* (27, 27)).

        //* The required buffer size is always width * height * 3 bytes.
        u_char *read_region_buffer = (u_char *)malloc(1536 * 1536 * 3);
        size_t read_bytes = tron_read_region(handle_ptr, 0, 1, 25 * 1024 + 768, 25 * 1024 + 786, 1536, 1536, read_region_buffer);
        printf("%lu bytes read a region of the slide\n", read_bytes);
        free(read_region_buffer);
    }

    //* Now that's all of it!
    //* ALWAYS remember to close a tron archive handle after using it, to release the archive
    //* file and prevent memory leak.
    tron_close(handle_ptr);
}