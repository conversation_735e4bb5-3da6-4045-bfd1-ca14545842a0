# ccaa-Slice

基于Spring Boot 3.4.3和OpenSlide库的数字病理切片处理服务，提供Web API接口，支持多种数字病理切片格式的读取、处理和展示。

## 主要特性

- 支持JDK 22
- 基于Spring Boot 3.4.3
- 集成OpenSlide 0.13.0库和SqraySlide库
- 支持Minio对象存储
- 提供完整的病理切片处理Web API
- 支持多种病理切片格式，包括SVS、NDPI、SDPC等
- 集成Redis和Caffeine本地缓存，提高性能
- 支持文件上传与异步处理
- 支持切片区域、瓦片、缩略图提取

## 项目简介

本项目是一个Spring Boot应用，集成了OpenSlide库和SqraySlide库来处理数字病理切片，支持查看切片信息、获取切片尺寸、提取切片区域、生成瓦片图像等功能。通过Minio对象存储支持文件上传和存储。

## 技术栈

- JDK 22
- Spring Boot 3.4.3
- OpenSlide 0.13.0
- SqraySlide
- Redis缓存
- Caffeine本地缓存
- Minio对象存储

## 环境要求

1. 安装JDK 22
2. 安装OpenSlide库
3. 安装SqraySlide库
4. 安装Redis服务
5. 安装Minio服务

### OpenSlide安装

#### macOS

```bash
brew install openslide
```

#### Ubuntu/Debian

```bash
apt-get install openslide-tools
```

#### Windows

从[OpenSlide官网](https://openslide.org/download/)下载预编译的Windows二进制文件。


不同系统的OpenSlide库路径示例：
- macOS: `/usr/local/lib/libopenslide.dylib`
- Linux: `/usr/lib/x86_64-linux-gnu/libopenslide.so`
- Windows: `C:\\Path\\To\\openslide-win64\\bin\\libopenslide-0.dll`

## API接口说明

### 切片相关接口

#### 检测文件格式
```
GET /api/slides/detect?filePath={filePath}
```

#### 获取切片信息
```
GET /api/slides/info?filePath={filePath}
```

#### 获取切片层级信息
```
GET /api/slides/level?filePath={filePath}&level={level}
```

#### 获取切片瓦片
```
GET /api/slides/tile?filePath={filePath}&x={x}&y={y}&level={level}
```

#### 获取切片缩略图
```
GET /api/slides/thumbnail?filePath={filePath}
```

#### 获取切片标签图
```
GET /api/slides/label?filePath={filePath}
```

#### 获取切片元数据
```
GET /api/slides/metadata?filePath={filePath}
```

#### 获取切片区域
```
GET /api/slides/region?filePath={filePath}&x={x}&y={y}&width={width}&height={height}&level={level}
```

### 文件上传相关接口

#### 创建上传任务
```
POST /api/upload/task
```

#### 上传文件
```
POST /api/upload/file
```

#### 获取上传进度
```
GET /api/upload/progress/{taskId}
```

#### 批量获取上传进度
```
POST /api/upload/progress/batch
```

#### 取消上传
```
DELETE /api/upload/cancel/{taskId}
```

## 构建与运行

### 构建

```bash
mvn clean package -DskipTests
```

### 运行

```bash
java --enable-native-access=ALL-UNNAMED -jar target/ccaa-slice-0.0.1-SNAPSHOT.jar
```

### Docker部署

```bash
# 构建镜像
docker build -t ccaa-slice:latest .

# 运行容器
docker run -p 8080:8080 -v /path/to/data:/app/data ccaa-slice:latest
```

## 注意事项

1. 确保正确安装OpenSlide库，并在配置文件中指定正确的库路径
2. 确保切片文件目录存在并有读写权限
3. 对于大文件上传，请调整最大文件大小配置
4. 使用Redis缓存可以提升性能
5. 对于不同切片格式，可能需要安装额外的解析器库（如SqraySlide） 
