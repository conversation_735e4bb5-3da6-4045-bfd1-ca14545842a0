FROM ccaa-slice-template:ubuntu22.04-x86_64

# 安装TronSDK图像提取器
# 复制TronSDK库文件和头文件
COPY tronsdk.c.rust.1.1.1-linux.glibc2.23/c/lib/libtronc.so /usr/local/lib/
COPY tronsdk.c.rust.1.1.1-linux.glibc2.23/c/headers/tronc.h /usr/local/include/

# 更新动态链接库缓存
RUN ldconfig

# 复制图像提取器源码并编译
COPY tron_image_extractor.c /tmp/
RUN cd /tmp && \
    gcc -Wall -Wextra -std=c99 -O2 -o tron_image_extractor tron_image_extractor.c -ltronc && \
    cp tron_image_extractor /usr/local/bin/ && \
    chmod +x /usr/local/bin/tron_image_extractor && \
    rm -f /tmp/tron_image_extractor.c /tmp/tron_image_extractor

# 验证图像提取器安装
RUN ldd /usr/local/bin/tron_image_extractor

# 拷贝应用文件
COPY target/ccaa-slice.jar /mitr/server/ccaa-slice/
COPY src/main/resources/application.yml /mitr/server/ccaa-slice/application.yml
COPY entrypoint.sh /mitr/server/ccaa-slice/

# 设置执行权限和目录
RUN chmod +x /mitr/server/ccaa-slice/entrypoint.sh && \
    mkdir -p /mitr/server/ccaa-slice/temp && \
    mkdir -p /mitr/server/ccaa-slice/logs

# 工作目录
WORKDIR /mitr/server/ccaa-slice

# 应用端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["/mitr/server/ccaa-slice/entrypoint.sh"]
