#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include <stdbool.h>
#include <tronc.h>

// TronSDK函数声明
extern void tron_close(struct Handle *handle_ptr);
extern int32_t tron_get_last_error(void);
extern size_t tron_get_named_image_data(struct Handle *handle_ptr, const char *image_name_ptr, unsigned char *buffer_ptr);
extern struct TronImageInfo tron_get_named_image_info(struct Handle *handle_ptr, const char *image_name_ptr);
extern struct Handle *tron_open(const char *path_ptr);
extern int32_t tron_get_layer_count(struct Handle *handle_ptr);
extern struct TronTileSize tron_get_tile_size(struct Handle *handle_ptr);
extern struct TronTileCount tron_get_tile_count(struct Handle *handle_ptr);
extern struct TronLodLevelRange tron_get_lod_level_range(struct Handle *handle_ptr);
extern struct TronResolution tron_get_resolution(struct Handle *handle_ptr);
extern struct TronContentRegion tron_get_content_region(struct Handle *handle_ptr);
extern size_t tron_get_tile_image_data(struct Handle *handle_ptr, int32_t lod_level, int32_t layer, int32_t row, int32_t column, unsigned char *buffer_ptr);
extern struct TronImageInfo tron_get_tile_image_info(struct Handle *handle_ptr, int32_t lod_level, int32_t layer, int32_t row, int32_t column);
extern size_t tron_read_region(struct Handle *handle_ptr, int32_t lod_level, int32_t layer, int32_t x, int32_t y, size_t width, size_t height, unsigned char *buffer_ptr);
extern float tron_get_maximum_zoom_level(struct Handle *handle_ptr);

// 错误码定义
#define TRON_SUCCESS 0
#define TRON_UNKNOWN_ERROR -1
#define TRON_INVALID_PATH 1
#define TRON_IO_ERROR 2
#define TRON_INVALID_ARCHIVE 3
#define TRON_INVALID_HANDLER 10
#define TRON_INVALID_IMAGE_NAME 40

void print_usage(const char* program_name) {
    printf("用法: %s <tron文件路径> <图像名称> <输出文件路径>\n", program_name);
    printf("示例: %s /path/to/file.tron thumbnail /tmp/output.jpg\n", program_name);
    printf("支持的图像名称: thumbnail, macro, label, overview\n");
    printf("输出格式: 自动检测并转换为JPEG格式\n");
}

// 检查是否为标准图像格式
bool is_standard_image_format(const unsigned char* data, size_t length) {
    if (data == NULL || length < 4) {
        return false;
    }
    
    // 检查JPEG格式: FF D8 FF
    if (length >= 3 && data[0] == 0xFF && data[1] == 0xD8 && data[2] == 0xFF) {
        return true;
    }
    
    // 检查PNG格式: 89 50 4E 47
    if (length >= 4 && data[0] == 0x89 && data[1] == 0x50 && 
        data[2] == 0x4E && data[3] == 0x47) {
        return true;
    }
    
    // 检查BMP格式: 42 4D
    if (length >= 2 && data[0] == 0x42 && data[1] == 0x4D) {
        return true;
    }
    
    return false;
}

// 修复BGR24到RGB24转换，确保颜色正确显示
void simple_bgr24_to_rgb24(const unsigned char* bgr_data, unsigned char* rgb_data, size_t width, size_t height) {
    size_t pixel_count = width * height;
    
    printf("开始BGR到RGB转换...\n");
    printf("像素数量: %zu\n", pixel_count);
    
    // 正确的BGR到RGB转换：BGR[0]=B, BGR[1]=G, BGR[2]=R -> RGB[0]=R, RGB[1]=G, RGB[2]=B
    for (size_t i = 0; i < pixel_count; i++) {
        // BGR格式：bgr_data[i*3+0]=B, bgr_data[i*3+1]=G, bgr_data[i*3+2]=R
        // RGB格式：rgb_data[i*3+0]=R, rgb_data[i*3+1]=G, rgb_data[i*3+2]=B
        rgb_data[i * 3 + 0] = bgr_data[i * 3 + 2]; // RGB的R = BGR的R
        rgb_data[i * 3 + 1] = bgr_data[i * 3 + 1]; // RGB的G = BGR的G  
        rgb_data[i * 3 + 2] = bgr_data[i * 3 + 0]; // RGB的B = BGR的B
    }
    
    printf("BGR到RGB转换完成\n");
}

// 简单的BGR24到RGB24转换
void bgr24_to_rgb24(const unsigned char* bgr_data, unsigned char* rgb_data, size_t width, size_t height) {
    // 使用简单的BGR到RGB转换
    simple_bgr24_to_rgb24(bgr_data, rgb_data, width, height);
}

// 写入简单的PPM格式（可以被大多数图像查看器识别）
int write_ppm_image(const char* output_path, const unsigned char* rgb_data, size_t width, size_t height) {
    FILE* file = fopen(output_path, "wb");
    if (file == NULL) {
        fprintf(stderr, "错误: 无法创建输出文件: %s\n", output_path);
        return -1;
    }
    
    // 写入PPM头部
    fprintf(file, "P6\n%zu %zu\n255\n", width, height);
    
    // 写入像素数据
    size_t written = fwrite(rgb_data, 1, width * height * 3, file);
    fclose(file);
    
    if (written != width * height * 3) {
        fprintf(stderr, "错误: 写入图像数据不完整\n");
        return -1;
    }
    
    printf("成功写入PPM格式图像: %zu x %zu pixels\n", width, height);
    return 0;
}

int extract_image(const char* tron_file_path, const char* image_name, const char* output_file_path) {
    printf("开始提取图像: %s from %s\n", image_name, tron_file_path);
    
    // 检查文件是否存在
    if (access(tron_file_path, F_OK) != 0) {
        fprintf(stderr, "错误: 文件不存在: %s\n", tron_file_path);
        return 1;
    }
    
    // 1. 打开TRON文件
    printf("打开TRON文件...\n");
    struct Handle* handle = tron_open(tron_file_path);
    if (handle == NULL) {
        int error = tron_get_last_error();
        fprintf(stderr, "错误: 无法打开文件，错误码: %d\n", error);
        return 1;
    }
    printf("成功打开文件，Handle: %p\n", (void*)handle);
    
    int result = 0;
    
    // 2. 获取图像信息
    printf("获取图像信息: %s\n", image_name);
    struct TronImageInfo image_info = tron_get_named_image_info(handle, image_name);
    
    if (!image_info.existed) {
        printf("图像不存在: %s\n", image_name);
        result = 2; // 图像不存在
        goto cleanup;
    }
    
    printf("找到图像: %s\n", image_name);
    printf("尺寸: %zu x %zu pixels\n", image_info.width, image_info.height);
    printf("数据大小: %zu bytes\n", image_info.length);
    
    // 验证图像大小的合理性
    if (image_info.length == 0 || image_info.length > 100 * 1024 * 1024) { // 100MB限制
        fprintf(stderr, "错误: 图像大小不合理: %zu bytes\n", image_info.length);
        result = 3;
        goto cleanup;
    }
    
    // 验证尺寸的合理性
    if (image_info.width == 0 || image_info.height == 0 || 
        image_info.width > 20000 || image_info.height > 20000) {
        fprintf(stderr, "错误: 图像尺寸不合理: %zu x %zu\n", image_info.width, image_info.height);
        result = 3;
        goto cleanup;
    }
    
    // 3. 获取图像数据
    printf("分配缓冲区: %zu bytes\n", image_info.length);
    unsigned char* image_buffer = malloc(image_info.length + 1024); // 额外安全边界
    if (image_buffer == NULL) {
        fprintf(stderr, "错误: 内存分配失败\n");
        result = 4;
        goto cleanup;
    }
    
    // 初始化缓冲区
    memset(image_buffer, 0, image_info.length + 1024);
    
    printf("获取图像数据...\n");
    size_t actual_length = tron_get_named_image_data(handle, image_name, image_buffer);
    
    if (actual_length == 0) {
        int error = tron_get_last_error();
        fprintf(stderr, "错误: 获取图像数据失败，错误码: %d\n", error);
        free(image_buffer);
        result = 5;
        goto cleanup;
    }
    
    printf("成功获取图像数据: %zu bytes\n", actual_length);
    
    // 4. 检查图像格式并处理
    if (is_standard_image_format(image_buffer, actual_length)) {
        printf("检测到标准图像格式，直接保存\n");
        FILE* output_file = fopen(output_file_path, "wb");
        if (output_file == NULL) {
            fprintf(stderr, "错误: 无法创建输出文件: %s\n", output_file_path);
            free(image_buffer);
            result = 6;
            goto cleanup;
        }
        size_t written = fwrite(image_buffer, 1, actual_length, output_file);
        fclose(output_file);
        if (written != actual_length) {
            fprintf(stderr, "错误: 写入文件不完整，期望: %zu, 实际: %zu\n", actual_length, written);
            free(image_buffer);
            result = 7;
            goto cleanup;
        }
        printf("成功保存标准格式图像: %zu bytes\n", written);
    } else {
        printf("检测到BGR24原始数据，转换为PPM格式\n");
        printf("前4字节: 0x%02X 0x%02X 0x%02X 0x%02X\n", 
               image_buffer[0], image_buffer[1], image_buffer[2], image_buffer[3]);
        size_t expected_length = image_info.width * image_info.height * 3;
        if (actual_length != expected_length) {
            printf("警告: 数据长度不匹配BGR24格式，期望: %zu, 实际: %zu\n", 
                   expected_length, actual_length);
        }
        unsigned char* rgb_buffer = malloc(expected_length);
        if (rgb_buffer == NULL) {
            fprintf(stderr, "错误: RGB缓冲区内存分配失败\n");
            free(image_buffer);
            result = 4;
            goto cleanup;
        }
        bgr24_to_rgb24(image_buffer, rgb_buffer, image_info.width, image_info.height);
        char ppm_output_path[1024];
        strncpy(ppm_output_path, output_file_path, sizeof(ppm_output_path) - 1);
        ppm_output_path[sizeof(ppm_output_path) - 1] = '\0';
        char* last_dot = strrchr(ppm_output_path, '.');
        if (last_dot != NULL) {
            strcpy(last_dot, ".ppm");
        } else {
            strcat(ppm_output_path, ".ppm");
        }
        if (write_ppm_image(ppm_output_path, rgb_buffer, image_info.width, image_info.height) != 0) {
            free(image_buffer);
            free(rgb_buffer);
            result = 7;
            goto cleanup;
        }
        printf("成功保存PPM格式图像: %s\n", ppm_output_path);
        printf("提示: PPM格式可以被大多数图像查看器打开，或使用ImageMagick转换为其他格式\n");
        printf("转换命令示例: convert %s %s\n", ppm_output_path, output_file_path);
        free(rgb_buffer);
    }
    
    free(image_buffer);
    
cleanup:
    // 5. 关闭文件
    printf("关闭文件...\n");
    tron_close(handle);
    printf("文件已关闭\n");
    
    return result;
}

int print_tron_info(const char* tron_file_path) {
    printf("=== TRON切片基本信息 ===\n");
    if (access(tron_file_path, F_OK) != 0) {
        fprintf(stderr, "错误: 文件不存在: %s\n", tron_file_path);
        return 1;
    }
    struct Handle* handle = tron_open(tron_file_path);
    if (handle == NULL) {
        int error = tron_get_last_error();
        fprintf(stderr, "错误: 无法打开文件，错误码: %d\n", error);
        return 1;
    }
    // 层数
    int32_t layer_count = tron_get_layer_count(handle);
    printf("层数: %d\n", layer_count);
    // LOD范围
    struct TronLodLevelRange lod_range = tron_get_lod_level_range(handle);
    printf("LOD层级范围: min=%d, max=%d\n", lod_range.minimum, lod_range.maximum);
    // 瓦片大小
    struct TronTileSize tile_size = tron_get_tile_size(handle);
    printf("瓦片大小: %d x %d\n", tile_size.width, tile_size.height);
    // 瓦片数量
    struct TronTileCount tile_count = tron_get_tile_count(handle);
    printf("瓦片数量: 水平=%d, 垂直=%d\n", tile_count.horizontal, tile_count.vertical);
    // 分辨率
    struct TronResolution res = tron_get_resolution(handle);
    printf("分辨率: 水平=%.4f um/px, 垂直=%.4f um/px\n", res.horizontal, res.vertical);
    // 内容区域
    struct TronContentRegion region = tron_get_content_region(handle);
    printf("内容区域: left=%d, top=%d, width=%d, height=%d\n", region.left, region.top, region.width, region.height);
    // 最大缩放倍率
    float max_zoom = tron_get_maximum_zoom_level(handle);
    printf("最大缩放倍率: %.2f\n", max_zoom);
    tron_close(handle);
    printf("=== 信息输出完毕 ===\n");
    return 0;
}

int extract_tile(const char* tron_file_path, int lod_level, int layer, int row, int col, const char* output_file_path) {
    printf("开始提取瓦片: lod=%d, layer=%d, row=%d, col=%d from %s\n", lod_level, layer, row, col, tron_file_path);
    if (access(tron_file_path, F_OK) != 0) {
        fprintf(stderr, "错误: 文件不存在: %s\n", tron_file_path);
        return 1;
    }
    struct Handle* handle = tron_open(tron_file_path);
    if (handle == NULL) {
        int error = tron_get_last_error();
        fprintf(stderr, "错误: 无法打开文件，错误码: %d\n", error);
        return 1;
    }
    struct TronImageInfo tile_info = tron_get_tile_image_info(handle, lod_level, layer, row, col);
    if (!tile_info.existed) {
        printf("瓦片不存在: lod=%d, layer=%d, row=%d, col=%d\n", lod_level, layer, row, col);
        tron_close(handle);
        return 2;
    }
    printf("瓦片尺寸: %zu x %zu, 数据大小: %zu bytes\n", tile_info.width, tile_info.height, tile_info.length);
    if (tile_info.length == 0 || tile_info.length > 50 * 1024 * 1024) {
        fprintf(stderr, "错误: 瓦片数据大小不合理: %zu bytes\n", tile_info.length);
        tron_close(handle);
        return 3;
    }
    unsigned char* buffer = malloc(tile_info.length + 1024);
    if (buffer == NULL) {
        fprintf(stderr, "错误: 内存分配失败\n");
        tron_close(handle);
        return 4;
    }
    memset(buffer, 0, tile_info.length + 1024);
    size_t actual_length = tron_get_tile_image_data(handle, lod_level, layer, row, col, buffer);
    if (actual_length == 0) {
        int error = tron_get_last_error();
        fprintf(stderr, "错误: 获取瓦片数据失败，错误码: %d\n", error);
        free(buffer);
        tron_close(handle);
        return 5;
    }
    printf("成功获取瓦片数据: %zu bytes\n", actual_length);
    // 统一BGR24转RGB24再保存
    size_t expected_length = tile_info.width * tile_info.height * 3;
    if (is_standard_image_format(buffer, actual_length)) {
        FILE* output_file = fopen(output_file_path, "wb");
        if (output_file == NULL) {
            fprintf(stderr, "错误: 无法创建输出文件: %s\n", output_file_path);
            free(buffer);
            tron_close(handle);
            return 6;
        }
        size_t written = fwrite(buffer, 1, actual_length, output_file);
        fclose(output_file);
        if (written != actual_length) {
            fprintf(stderr, "错误: 写入文件不完整，期望: %zu, 实际: %zu\n", actual_length, written);
            free(buffer);
            tron_close(handle);
            return 7;
        }
        printf("成功保存标准格式瓦片: %zu bytes\n", written);
    } else {
        unsigned char* rgb_buffer = malloc(expected_length);
        if (rgb_buffer == NULL) {
            fprintf(stderr, "错误: RGB缓冲区内存分配失败\n");
            free(buffer);
            tron_close(handle);
            return 4;
        }
        bgr24_to_rgb24(buffer, rgb_buffer, tile_info.width, tile_info.height);
        char ppm_output_path[1024];
        strncpy(ppm_output_path, output_file_path, sizeof(ppm_output_path) - 1);
        ppm_output_path[sizeof(ppm_output_path) - 1] = '\0';
        char* last_dot = strrchr(ppm_output_path, '.');
        if (last_dot != NULL) {
            strcpy(last_dot, ".ppm");
        } else {
            strcat(ppm_output_path, ".ppm");
        }
        if (write_ppm_image(ppm_output_path, rgb_buffer, tile_info.width, tile_info.height) != 0) {
            free(buffer);
            free(rgb_buffer);
            tron_close(handle);
            return 7;
        }
        printf("成功保存PPM格式瓦片: %s\n", ppm_output_path);
        free(rgb_buffer);
    }
    free(buffer);
    tron_close(handle);
    return 0;
}

int extract_region(const char* tron_file_path, int lod_level, int layer, int x, int y, int width, int height, const char* output_file_path) {
    printf("开始提取区域: lod=%d, layer=%d, x=%d, y=%d, width=%d, height=%d from %s\n", lod_level, layer, x, y, width, height, tron_file_path);
    if (access(tron_file_path, F_OK) != 0) {
        fprintf(stderr, "错误: 文件不存在: %s\n", tron_file_path);
        return 1;
    }
    struct Handle* handle = tron_open(tron_file_path);
    if (handle == NULL) {
        int error = tron_get_last_error();
        fprintf(stderr, "错误: 无法打开文件，错误码: %d\n", error);
        return 1;
    }
    size_t expected_length = (size_t)width * (size_t)height * 3;
    if (expected_length == 0 || expected_length > 100 * 1024 * 1024) {
        fprintf(stderr, "错误: 区域数据大小不合理: %zu bytes\n", expected_length);
        tron_close(handle);
        return 3;
    }
    unsigned char* buffer = malloc(expected_length + 1024);
    if (buffer == NULL) {
        fprintf(stderr, "错误: 内存分配失败\n");
        tron_close(handle);
        return 4;
    }
    memset(buffer, 0, expected_length + 1024);
    size_t actual_length = tron_read_region(handle, lod_level, layer, x, y, width, height, buffer);
    if (actual_length == 0) {
        int error = tron_get_last_error();
        fprintf(stderr, "错误: 获取区域数据失败，错误码: %d\n", error);
        free(buffer);
        tron_close(handle);
        return 5;
    }
    printf("成功获取区域数据: %zu bytes\n", actual_length);
    // 统一BGR24转RGB24再保存
    unsigned char* rgb_buffer = malloc(expected_length);
    if (rgb_buffer == NULL) {
        fprintf(stderr, "错误: RGB缓冲区内存分配失败\n");
        free(buffer);
        tron_close(handle);
        return 4;
    }
    bgr24_to_rgb24(buffer, rgb_buffer, width, height);
    char ppm_output_path[1024];
    strncpy(ppm_output_path, output_file_path, sizeof(ppm_output_path) - 1);
    ppm_output_path[sizeof(ppm_output_path) - 1] = '\0';
    char* last_dot = strrchr(ppm_output_path, '.');
    if (last_dot != NULL) {
        strcpy(last_dot, ".ppm");
    } else {
        strcat(ppm_output_path, ".ppm");
    }
    if (write_ppm_image(ppm_output_path, rgb_buffer, width, height) != 0) {
        free(buffer);
        free(rgb_buffer);
        tron_close(handle);
        return 7;
    }
    printf("成功保存PPM格式区域: %s\n", ppm_output_path);
    free(buffer);
    free(rgb_buffer);
    tron_close(handle);
    return 0;
}

int main(int argc, char* argv[]) {
    if (argc == 3 && strcmp(argv[2], "info") == 0) {
        return print_tron_info(argv[1]);
    }
    if (argc == 8 && strcmp(argv[2], "tile") == 0) {
        int lod_level = atoi(argv[3]);
        int layer = atoi(argv[4]);
        int row = atoi(argv[5]);
        int col = atoi(argv[6]);
        return extract_tile(argv[1], lod_level, layer, row, col, argv[7]);
    }
    if (argc == 10 && strcmp(argv[2], "region") == 0) {
        int lod_level = atoi(argv[3]);
        int layer = atoi(argv[4]);
        int x = atoi(argv[5]);
        int y = atoi(argv[6]);
        int width = atoi(argv[7]);
        int height = atoi(argv[8]);
        return extract_region(argv[1], lod_level, layer, x, y, width, height, argv[9]);
    }
    if (argc != 4) {
        print_usage(argv[0]);
        return 1;
    }
    
    const char* tron_file_path = argv[1];
    const char* image_name = argv[2];
    const char* output_file_path = argv[3];
    
    printf("=== TronSDK 图像提取器 (增强版) ===\n");
    printf("TRON文件: %s\n", tron_file_path);
    printf("图像名称: %s\n", image_name);
    printf("输出文件: %s\n", output_file_path);
    printf("\n");
    
    int result = extract_image(tron_file_path, image_name, output_file_path);
    
    if (result == 0) {
        printf("\n=== 提取成功 ===\n");
    } else {
        printf("\n=== 提取失败，错误码: %d ===\n", result);
    }
    
    return result;
} 