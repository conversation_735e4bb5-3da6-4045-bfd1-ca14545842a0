package cn.ccaa.slice.service.dzi;

import cn.ccaa.slice.core.exception.SlideException;
import cn.ccaa.slice.core.model.ColorCorrectionParams;
import java.util.Map;

/**
 * DZI服务接口
 * 提供处理Deep Zoom Images相关的业务逻辑
 * 
 * <AUTHOR>
 */
public interface DziService {
    
    /**
     * 获取DZI描述文件内容
     * 
     * @param identifier 切片标识符
     * @return DZI描述文件内容（XML格式）
     * @throws SlideException 如果无法生成DZI描述文件
     */
    String getDziDescriptor(String identifier) throws SlideException;
    
    /**
     * 获取DZI描述文件内容（支持 cname 参数）
     * 
     * @param identifier 切片标识符
     * @param cname 切片名称（对应数据库 partner_code 字段）
     * @return DZI描述文件内容（XML格式）
     * @throws SlideException 如果无法生成DZI描述文件
     */
    String getDziDescriptor(String identifier, String cname) throws SlideException;
    
    /**
     * 获取DZI瓦片数据
     * 
     * @param identifier 切片标识符
     * @param level DZI层级
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @return 瓦片图像数据
     * @throws SlideException 如果无法获取瓦片数据
     */
    byte[] getDziTile(String identifier, int level, int x, int y) throws SlideException;
    
    /**
     * 获取DZI瓦片数据（支持颜色校正）
     * 
     * @param identifier 切片标识符
     * @param level DZI层级
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @param colorParams 颜色校正参数
     * @return 调色后的瓦片图像数据
     * @throws SlideException 如果无法获取瓦片数据
     */
    byte[] getDziTileWithColor(String identifier, int level, int x, int y, ColorCorrectionParams colorParams) throws SlideException;
    
    /**
     * 通过标识符获取文件路径
     * 
     * @param identifier 切片标识符（主键id）
     * @return 处理后的文件路径
     * @throws SlideException 如果无法找到有效的文件
     */
    String getFilePathByIdentifier(String identifier) throws SlideException;
    
    /**
     * 获取与DZI视图一致的缩略图
     * 这个方法确保缩略图与DZI查看器中的图像方向一致
     *
     * @param identifier 切片标识符
     * @return 缩略图数据
     * @throws SlideException 如果无法获取缩略图
     */
    byte[] getThumbnailForDzi(String identifier) throws SlideException;
    
    /**
     * 根据文件路径获取切片信息
     * 
     * @param filePath 切片文件路径
     * @return 切片信息Map
     * @throws SlideException 如果无法获取切片信息
     */
    Map<String, Object> getSlideInfoFromPath(String filePath) throws SlideException;
    
    /**
     * 根据文件路径获取指定区域的JPEG图像数据
     * 
     * @param filePath 切片文件路径
     * @param x 区域左上角X坐标
     * @param y 区域左上角Y坐标
     * @param width 区域宽度
     * @param height 区域高度
     * @param level 层级索引
     * @return JPEG格式的区域图像数据
     * @throws SlideException 如果无法获取图像数据
     */
    byte[] getRegionJpegFromPath(String filePath, int x, int y, int width, int height, int level) throws SlideException;
} 
