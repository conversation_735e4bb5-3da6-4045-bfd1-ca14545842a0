package cn.ccaa.slice.service.dzi.impl;

import cn.ccaa.slice.config.StorageConfig;
import cn.ccaa.slice.config.SlideParserConfig;
import cn.ccaa.slice.core.exception.SlideException;
import cn.ccaa.slice.core.model.ColorCorrectionParams;
import cn.ccaa.slice.core.model.SliceEntity;
import cn.ccaa.slice.core.parser.SlideParser;
import cn.ccaa.slice.core.parser.SlideParserFactory;
import cn.ccaa.slice.parsers.common.SlideImageWrapper;
import cn.ccaa.slice.service.FilePathService;
import cn.ccaa.slice.service.UnifiedSlideService;
import cn.ccaa.slice.service.dzi.DziService;
import cn.ccaa.slice.service.storage.StorageService;
import cn.ccaa.slice.service.SliceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.plugins.jpeg.JPEGImageWriteParam;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.ccaa.slice.parsers.tronsdk.service.TronSdkImageExtractor;

/**
 * DZI服务实现类
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class DziServiceImpl implements DziService, InitializingBean, DisposableBean {

    /**
     * 默认瓦片大小
     * 使用较小的瓦片尺寸可以提高加载速度和减少网络传输量
     */
    private static final int TILE_SIZE = 256;
    
    /**
     * 瓦片重叠像素
     * 设置为0表示瓦片之间没有重叠
     */
    private static final int TILE_OVERLAP = 0;
    
    /**
     * JPEG品质系数
     * 使用接近无损压缩以保证图像清晰度
     */
    private static final float JPEG_QUALITY = 0.98f; // 提高到0.98，确保sdpc文件瓦片显示清晰
    
    // 新增：瓦片大小限制配置（30KB目标）
    /**
     * 目标瓦片最大大小（字节）
     * 用于控制网络传输性能 - 优化：增加SDPC文件的瓦片大小限制
     */
    private static final int TARGET_TILE_MAX_SIZE = 50 * 1024; // 提高到50KB，改善SDPC显示质量
    
    /**
     * 每个处理器分配的线程数 - 优化：减少线程数量
     * 降低线程开销，避免过多并发任务
     */
    private static final int THREADS_PER_PROCESSOR = 2; // 从4减少到2
    
    /**
     * SDPC文件专用的序列化处理队列 - 新增：解决并发过多导致的瓦片加载问题
     */
    private static final int SDPC_MAX_CONCURRENT_TILES = 6; // 拖拽优化：提升SDPC并发数以改善体验
    
    /**
     * 支持的切片文件格式
     */
    private static final String[] PREFERRED_FORMATS = {".ndpi", ".svs", ".sdpc"};
    
    /**
     * 瓦片处理线程池
     */
    private ExecutorService tileProcessorPool;
    
    /**
     * 活跃会话跟踪 - 新增
     * 用于跟踪活跃的会话，页面关闭时可以停止相关任务
     */
    private final Set<String> activeSessions = ConcurrentHashMap.newKeySet();
    

    
    /**
     * 正在执行的异步任务 - 新增
     * 用于管理和取消正在执行的任务
     */
    private final Map<String, CompletableFuture<Void>> runningTasks = new ConcurrentHashMap<>();
    
    /**
     * 内存瓦片缓存 - 优化：增加缓存容量，提高SDPC流畅性
     * 增加到30000，提高大文件缓存命中率
     */
    private final Map<String, byte[]> tileCache = new ConcurrentHashMap<>(30000);
    
    /**
     * 瓦片坐标锁定缓存 - 新增：防止瓦片抖动
     * 缓存瓦片的精确坐标和尺寸，确保每次请求返回完全相同的结果
     */
    private final Map<String, TileCoordinates> tileCoordinatesCache = new ConcurrentHashMap<>();
    
    /**
     * 瓦片一致性验证缓存 - 新增：验证瓦片内容一致性
     */
    private final Map<String, String> tileVerificationCache = new ConcurrentHashMap<>();
    
    /**
     * SDPC文件并发控制信号量 - 新增：限制SDPC文件的并发瓦片处理
     */
    private final java.util.concurrent.Semaphore sdpcConcurrencyControl = 
            new java.util.concurrent.Semaphore(SDPC_MAX_CONCURRENT_TILES);
    
    /**
     * 瓦片坐标数据结构
     */
    private static class TileCoordinates {
        final int srcX, srcY, srcW, srcH;
        final int readW, readH;
        final long timestamp;
        
        TileCoordinates(int srcX, int srcY, int srcW, int srcH, int readW, int readH) {
            this.srcX = srcX;
            this.srcY = srcY;
            this.srcW = srcW;
            this.srcH = srcH;
            this.readW = readW;
            this.readH = readH;
            this.timestamp = System.currentTimeMillis();
        }
    }
    
    /**
     * 瓦片大小统计 - 用于监控优化效果
     */
    private static class TileSizeStats {
        private long totalTiles = 0;
        private long totalSize = 0;
        private long tilesOverLimit = 0;
        private long maxSize = 0;
        private double averageSize = 0;
        
        public synchronized void addTile(int size) {
            totalTiles++;
            totalSize += size;
            averageSize = (double) totalSize / totalTiles;
            if (size > TARGET_TILE_MAX_SIZE) {
                tilesOverLimit++;
            }
            if (size > maxSize) {
                maxSize = size;
            }
        }
        
        public synchronized String getStats() {
            double overLimitPercentage = totalTiles > 0 ? (double) tilesOverLimit / totalTiles * 100 : 0;
            return String.format("瓦片统计 - 总数: %d, 平均大小: %.1fKB, 最大: %dKB, 超限比例: %.1f%%", 
                               totalTiles, averageSize / 1024, maxSize / 1024, overLimitPercentage);
        }
    }
    
    private final TileSizeStats tileSizeStats = new TileSizeStats();
    
    /**
     * 获取瓦片大小统计信息
     * @return 统计信息字符串
     */
    public String getTileSizeStats() {
        return tileSizeStats.getStats();
    }

    @Autowired
    private UnifiedSlideService unifiedSlideService;

    @Autowired
    private FilePathService filePathService;
    
    @Autowired
    private StorageConfig storageConfig;
    
    @Autowired
    private StorageService storageService;
    
    @Autowired
    private SliceService sliceService;
    
    @Autowired
    private SlideParserFactory parserFactory;
    
    @Autowired
    private SlideParserConfig parserConfig;
    
    // SDPC内容区域缓存，key为filePath，value为int[]{minX, minY, maxX, maxY}
    private final ConcurrentHashMap<String, int[]> sdpcContentRegionCache = new ConcurrentHashMap<>();
    
    @Autowired
    private TronSdkImageExtractor tronSdkImageExtractor; // 注入Tron命令行图像提取器
    
    /**
     * 初始化线程池
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        // 创建固定大小的线程池，大小为可用处理器数量的3倍，提高并行处理能力
        int processors = Runtime.getRuntime().availableProcessors();
        tileProcessorPool = Executors.newFixedThreadPool(processors * THREADS_PER_PROCESSOR);
        log.info("DZI瓦片处理线程池已初始化，核心线程数: {}", processors * THREADS_PER_PROCESSOR);
        
        // 尝试创建临时文件缓存目录
        try {
            java.io.File tempDir = new java.io.File(System.getProperty("java.io.tmpdir"), "dzi-cache");
            if (!tempDir.exists()) {
                tempDir.mkdirs();
                log.info("创建DZI临时缓存目录: {}", tempDir.getAbsolutePath());
            }
        } catch (Exception e) {
            log.warn("创建DZI临时缓存目录失败: {}", e.getMessage());
        }
        
        // 确保图像读写器正确初始化
        try {
            ImageIO.scanForPlugins();
            // log.debug("已扫描并注册所有图像I/O插件");
        } catch (Exception e) {
            log.warn("扫描图像I/O插件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 释放资源
     */
    @Override
    public void destroy() throws Exception {
        if (tileProcessorPool != null && !tileProcessorPool.isShutdown()) {
            tileProcessorPool.shutdown();
            log.info("DZI瓦片处理线程池已关闭");
        }
    }
    
    /**
     * 停止指定标识符的所有异步任务 - 新增
     * 用于页面关闭时停止相关的预加载任务
     */
    public void stopAsyncTasksForIdentifier(String identifier) {
        log.info("停止标识符 {} 的所有异步任务", identifier);
        
        // 停止相关的活跃会话
        Set<String> sessionsToStop = activeSessions.stream()
                .filter(session -> session.startsWith(identifier + "_"))
                .collect(java.util.stream.Collectors.toSet());
        
        for (String session : sessionsToStop) {
            activeSessions.remove(session);
            CompletableFuture<Void> task = runningTasks.get(session);
            if (task != null && !task.isDone()) {
                task.cancel(true); // 尝试中断任务
                runningTasks.remove(session);
                log.debug("已取消异步任务: {}", session);
            }
        }
        
        log.info("已停止 {} 个异步任务", sessionsToStop.size());
    }
    
    /**
     * 停止所有异步任务 - 新增
     */
    public void stopAllAsyncTasks() {
        log.info("停止所有异步任务，当前活跃会话数: {}", activeSessions.size());
        
        // 清理所有活跃会话
        activeSessions.clear();
        
        // 取消所有正在运行的任务
        for (Map.Entry<String, CompletableFuture<Void>> entry : runningTasks.entrySet()) {
            CompletableFuture<Void> task = entry.getValue();
            if (task != null && !task.isDone()) {
                task.cancel(true);
                log.debug("已取消异步任务: {}", entry.getKey());
            }
        }
        runningTasks.clear();
        
        log.info("所有异步任务已停止");
    }
    
    @Override
    @Cacheable(value = "dziDescriptor", key = "#identifier", unless = "#result == null")
    public String getDziDescriptor(String identifier) throws SlideException {
        log.info("开始生成DZI描述文件: {}", identifier);
        try {
            String filePath = getFilePathByIdentifier(identifier);
            log.info("获取到文件路径: {}", filePath);
            if (!storageService.fileExists(filePath)) {
                log.error("文件不存在: {}", filePath);
                throw new SlideException("文件不存在: " + filePath);
            }
            Map<String, Object> info = unifiedSlideService.getSlideInfo(filePath);
            validateSlideInfo(info);
            
            // 新增：针对SDPC格式简化处理，直接使用原图尺寸，避免内容区域导致的坐标映射问题
            boolean isSdpcFormat = filePath.toLowerCase().endsWith(".sdpc");
            int width, height;
            
            if (isSdpcFormat) {
                // SDPC格式：直接使用原图尺寸，不使用内容区域
                // 这样可以避免复杂的坐标转换，确保瓦片拼接的准确性
                width = ((Number) info.get("width")).intValue();
                height = ((Number) info.get("height")).intValue();
                log.info("SDPC格式使用原图尺寸: {}x{}", width, height);
            } else {
                // 其他格式：优先用contentRegion宽高
                width = info.containsKey("contentWidth") ? ((Number) info.get("contentWidth")).intValue() : ((Number) info.get("width")).intValue();
                height = info.containsKey("contentHeight") ? ((Number) info.get("contentHeight")).intValue() : ((Number) info.get("height")).intValue();
            }
            
            String dziXml = buildDziXml(width, height);
            log.debug("生成DZI描述文件成功: {}, 尺寸: {}x{}", identifier, width, height);
            return dziXml;
        } catch (Exception e) {
            log.error("生成DZI描述文件失败: {}", e.getMessage());
            throw new SlideException("生成DZI描述文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Cacheable(value = "dziDescriptor", key = "#identifier + '_' + #cname", unless = "#result == null")
    public String getDziDescriptor(String identifier, String cname) throws SlideException {
        log.info("开始生成DZI描述文件: {}, cname: {}", identifier, cname);
        try {
            String filePath = getFilePathByIdentifierAndPartnerCode(identifier, cname);
            log.info("获取到文件路径: {}", filePath);
            if (!storageService.fileExists(filePath)) {
                log.error("文件不存在: {}", filePath);
                throw new SlideException("文件不存在: " + filePath);
            }
            Map<String, Object> info = unifiedSlideService.getSlideInfo(filePath);
            validateSlideInfo(info);
            
            // 新增：针对SDPC格式简化处理，直接使用原图尺寸，避免内容区域导致的坐标映射问题
            boolean isSdpcFormat = filePath.toLowerCase().endsWith(".sdpc");
            int width, height;
            
            if (isSdpcFormat) {
                // SDPC格式：直接使用原图尺寸，不使用内容区域
                // 这样可以避免复杂的坐标转换，确保瓦片拼接的准确性
                width = ((Number) info.get("width")).intValue();
                height = ((Number) info.get("height")).intValue();
                log.info("SDPC格式使用原图尺寸: {}x{}", width, height);
            } else {
                // 其他格式：优先用contentRegion宽高
                width = info.containsKey("contentWidth") ? ((Number) info.get("contentWidth")).intValue() : ((Number) info.get("width")).intValue();
                height = info.containsKey("contentHeight") ? ((Number) info.get("contentHeight")).intValue() : ((Number) info.get("height")).intValue();
            }
            
            String dziXml = buildDziXml(width, height);
            log.debug("生成DZI描述文件成功: {}, cname: {}, 尺寸: {}x{}", identifier, cname, width, height);
            return dziXml;
        } catch (Exception e) {
            log.error("生成DZI描述文件失败: {}", e.getMessage());
            // 如果是"找不到切片文件"错误，直接返回简化的错误信息
            if (e.getMessage() != null && e.getMessage().contains("找不到切片文件")) {
                throw new SlideException("找不到切片文件");
            }
            throw new SlideException("生成DZI描述文件失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public byte[] getDziTile(String identifier, int level, int x, int y) throws SlideException {
        // 强制输出日志，确认方法被调用
        log.debug("=== getDziTile被调用: 标识符={}, 层级={}, 坐标=({}, {}) ===", identifier, level, x, y);
        
        try {
            // 修复：统一所有瓦片的处理路径，避免路径不一致导致的抖动
            // 为所有格式创建默认颜色参数，确保处理路径一致
            ColorCorrectionParams defaultParams = new ColorCorrectionParams();
            
            // 检测是否为NDPI格式文件，如果是则应用特定的默认颜色参数
            try {
                String filePath = getFilePathByIdentifier(identifier);
                if (filePath != null && filePath.toLowerCase().endsWith(".ndpi")) {
                    log.debug("检测到NDPI格式文件，应用默认颜色参数: 亮度=0.1, 伽马=1.1, 饱和度=1.1");
                    
                    // NDPI格式的默认颜色参数
                    defaultParams.setBrightness(0.0f);    // 亮度
                    defaultParams.setGamma(1.8f);         // 伽马调整到1.8
                    defaultParams.setSaturation(1.0f);    // 饱和度
                    defaultParams.setContrast(1.0f);      // 保持默认对比度
                    defaultParams.setHue(0.0f);           // 保持默认色调
                    defaultParams.setRedGain(1.0f);       // 保持默认红色增益
                    defaultParams.setGreenGain(1.0f);     // 保持默认绿色增益
                    defaultParams.setBlueGain(1.0f);      // 保持默认蓝色增益
                    defaultParams.setSharpen(0);          // 保持默认锐化
                    defaultParams.setColorStyle(0);       // 保持默认颜色风格
                } else {
                    log.debug("非NDPI格式文件，使用标准默认颜色参数");
                    // 其他格式使用标准默认参数
                    defaultParams.setBrightness(0.0f);
                    defaultParams.setContrast(1.0f);
                    defaultParams.setGamma(1.0f);
                    defaultParams.setSaturation(1.0f);
                    defaultParams.setHue(0.0f);
                    defaultParams.setRedGain(1.0f);
                    defaultParams.setGreenGain(1.0f);
                    defaultParams.setBlueGain(1.0f);
                    defaultParams.setSharpen(0);
                    defaultParams.setColorStyle(0);
                }
            } catch (Exception e) {
                // 如果检测文件格式失败，使用标准默认参数
                log.debug("检测文件格式时出错，使用标准默认参数: {}", e.getMessage());
                defaultParams.setBrightness(0.0f);
                defaultParams.setContrast(1.0f);
                defaultParams.setGamma(1.0f);
                defaultParams.setSaturation(1.0f);
                defaultParams.setHue(0.0f);
                defaultParams.setRedGain(1.0f);
                defaultParams.setGreenGain(1.0f);
                defaultParams.setBlueGain(1.0f);
                defaultParams.setSharpen(0);
                defaultParams.setColorStyle(0);
            }
            
            // 验证参数范围
            defaultParams.validate();
            
            // 统一使用颜色校正路径，确保处理一致性
            return getDziTileWithSoftwareColorCorrection(identifier, level, x, y, defaultParams);
            
        } catch (SlideException se) {
            throw se;
        } catch (Exception e) {
            log.error("获取DZI瓦片时发生未知错误: 标识符={}, 层级={}, 坐标=({}, {}), 错误: {}", 
                     identifier, level, x, y, e.getMessage(), e);
            throw new SlideException("获取DZI瓦片失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 内部方法：获取DZI瓦片的核心实现，避免循环调用
     */
    private byte[] getDziTileInternal(String identifier, int level, int x, int y) throws SlideException {
        // 检查是否为SDPC文件，如果是则进行并发控制
        boolean isSDPCFile = identifier != null && identifier.toLowerCase().contains("sdpc");
        
        if (isSDPCFile) {
            try {
                // 拖拽优化：使用更短的超时时间，快速响应拖拽请求
                boolean acquired = sdpcConcurrencyControl.tryAcquire(200, java.util.concurrent.TimeUnit.MILLISECONDS);
                if (!acquired) {
                    log.debug("SDPC瓦片等待超时，跳过: ({}, {}, {})", level, x, y);
                    // 拖拽时如果等待太久，返回透明瓦片而不是阻塞
                    return createTransparentTile(256); // 假设默认瓦片大小为256
                }
                log.debug("SDPC瓦片获得处理许可: ({}, {}, {})", level, x, y);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new SlideException("SDPC瓦片处理被中断");
            }
        }
        
        try {
            // 只在DEBUG级别记录瓦片请求信息，减少日志输出
            if (log.isDebugEnabled()) {
                log.debug("请求瓦片: 标识符={}, 层级={}, 坐标=({}, {})", identifier, level, x, y);
            }
            
            // 检查缓存中是否有错误记录，避免重复处理已知的错误
            String errorCacheKey = "error_" + identifier;
            if (tileCache.containsKey(errorCacheKey)) {
                // 返回一个静态错误瓦片，而不是不断尝试
                if (log.isDebugEnabled()) {
                    log.debug("该标识符已知错误，返回静态错误瓦片: {}", identifier);
                }
                return createErrorTileForFormat(TILE_SIZE, "");
            }
            
            // 构造缓存键，增加会话标识，便于缓存隔离和管理
            String sessionKey = Thread.currentThread().getName(); // 使用线程名作为简易会话标识
            String cacheKey = identifier + "_" + level + "_" + x + "_" + y + "_" + sessionKey.hashCode();
            
            // 修复：使用统一缓存键，确保无论色彩参数如何，坐标锁定都保持一致
            cacheKey = "tile_unified_" + identifier + "_" + level + "_" + x + "_" + y;
            
            // 特殊处理：SDPC文件使用永久缓存策略
            if (identifier.toLowerCase().contains(".sdpc")) {
                log.debug("SDPC文件使用永久缓存策略: {}", cacheKey);
                
                // 检查预热缓存
                String preloadCacheKey = identifier + "_" + level + "_" + x + "_" + y + "_preload";
                byte[] preloadTile = tileCache.get(preloadCacheKey);
                if (preloadTile != null && preloadTile.length > 0) {
                    log.debug("SDPC文件使用预热缓存: level={}, x={}, y={}", level, x, y);
                    return preloadTile;
                }
            }
            
            // 尝试从缓存获取
            byte[] cachedTile = tileCache.get(cacheKey);
            if (cachedTile != null) {
                // 缓存命中时不记录日志，减少I/O开销
                // 优化：缓存命中时不再触发异步预加载，避免不必要的后台任务
                // 只在实际需要加载瓦片时才考虑预加载
                return cachedTile;
            }
            
            // 获取文件路径
            String filePath;
            try {
                filePath = getFilePathByIdentifier(identifier);
                if (filePath == null || filePath.isEmpty()) {
                    log.error("无法找到有效的文件路径: {}", identifier);
                    // 缓存错误，避免重复处理
                    tileCache.put(errorCacheKey, new byte[0]);
                    return createErrorTileForFormat(TILE_SIZE, "");
                }
                
                // 验证文件是否存在
                java.io.File file = new java.io.File(filePath);
                if (!file.exists() || !file.isFile()) {
                    log.error("文件不存在或不是有效文件: {}", filePath);
                    tileCache.put(errorCacheKey, new byte[0]);
                    return createErrorTileForFormat(TILE_SIZE, "");
                }
            } catch (Exception e) {
                log.error("获取文件路径失败: {}, 错误: {}", identifier, e.getMessage());
                tileCache.put(errorCacheKey, new byte[0]);
                return createErrorTileForFormat(TILE_SIZE, "");
            }
            
            // 修复：使用SlideImageWrapper缓存机制，避免重复打开文件
            SlideImageWrapper wrapper;
            Map<String, Object> info;
            try {
                // 使用UnifiedSlideService的openSlide方法，它会缓存切片对象
                wrapper = unifiedSlideService.openSlide(filePath);
                if (wrapper == null) {
                    log.error("无法打开切片文件: {}", filePath);
                    tileCache.put(errorCacheKey, new byte[0]);
                    return createErrorTileForFormat(TILE_SIZE, "");
                }
                
                // 从wrapper中获取已缓存的切片信息，避免重复调用getSlideInfo
                info = wrapper.getSlideInfo();
                if (info == null || info.isEmpty()) {
                    log.error("切片信息为空: {}", filePath);
                    tileCache.put(errorCacheKey, new byte[0]);
                    return createErrorTileForFormat(TILE_SIZE, "");
                }
                
                validateSlideInfo(info);
            } catch (Exception e) {
                log.error("打开切片或获取信息失败: {}, 错误: {}", filePath, e.getMessage());
                // 缓存错误，避免重复处理
                tileCache.put(errorCacheKey, new byte[0]);
                return createErrorTileForFormat(TILE_SIZE, filePath != null ? new java.io.File(filePath).getName().toLowerCase() : "");
            }
            
            // 安全获取宽高
            Object widthObj = info.get("width");
            Object heightObj = info.get("height");
            int width = (widthObj instanceof Number) ? ((Number) widthObj).intValue() : 0;
            int height = (heightObj instanceof Number) ? ((Number) heightObj).intValue() : 0;
            
            if (width <= 0 || height <= 0) {
                log.error("获取瓦片时图像尺寸无效: {}x{}", width, height);
                return null;
            }

            // 简化：对所有格式统一使用原图尺寸，确保与DZI描述文件一致
            // 移除SDPC格式的内容区域调整，避免坐标映射复杂性
            log.debug("使用原图尺寸进行瓦片处理: {}x{}", width, height);

            // 只在DEBUG级别记录详细的请求信息
            if (log.isDebugEnabled()) {
                log.debug("开始处理DZI瓦片请求: 文件路径={}, 层级={}, 坐标=({}, {}), 原图尺寸={}x{}", 
                       filePath, level, x, y, width, height);
            }

            // DZI参数
            int overlap = TILE_OVERLAP; // 使用常量
            int tileSize = TILE_SIZE;
            
            // 修复：使用与getDziDescriptor相同的层级计算方法，确保一致性
            int maxDim = Math.max(width, height);
            int maxDziLevel = 0;
            if (maxDim > 1) {
                maxDziLevel = (int) Math.ceil(Math.log(maxDim) / Math.log(2));
            }
            
            // 只在DEBUG级别记录层级计算信息
            if (log.isDebugEnabled()) {
                log.debug("计算最大层级: maxDim={}, maxDziLevel={}, 使用位运算", maxDim, maxDziLevel);
            }
            
            // 确保不超出最大层级
            if (level > maxDziLevel) {
                log.warn("请求的层级 {} 超过了最大层级 {}, 使用最大层级代替", level, maxDziLevel);
                level = maxDziLevel;
            }
            
            // 修正DZI层级计算：层级0是最小分辨率，最高层级是原图分辨率
            // DZI标准：level 0 = 最小分辨率，level maxDziLevel = 原图分辨率
            // 关键修复：正确计算各层级的实际尺寸
            int scale = 1 << (maxDziLevel - level);
            int levelWidth = Math.max(1, (width + scale - 1) / scale);  // 向上取整，确保覆盖完整图像
            int levelHeight = Math.max(1, (height + scale - 1) / scale); // 向上取整，确保覆盖完整图像
            
            // 强制输出层级映射信息用于调试
            log.debug("DZI层级映射: maxDziLevel={}, 当前层级={}, 层级尺寸={}x{}, 缩放比例={}", 
                   maxDziLevel, level, levelWidth, levelHeight, scale);
            
            // 复查宽高比
            double originalAspectRatio = (double) width / height;
            double levelAspectRatio = (double) levelWidth / levelHeight;
            
            if (Math.abs(originalAspectRatio - levelAspectRatio) > 0.01) {
                log.warn("层级缩放后宽高比不一致: 原始={}({}/{}), 当前层级={}({}/{}), 差异={}",
                        originalAspectRatio, width, height, 
                        levelAspectRatio, levelWidth, levelHeight,
                        Math.abs(originalAspectRatio - levelAspectRatio));
            }

            // 计算瓦片起点和实际尺寸
            int tileX = x * tileSize;
            int tileY = y * tileSize;
            
            // 边界检查 - 修正：考虑overlap的影响，允许合理的边界扩展
            int maxTileX = (levelWidth + tileSize - 1) / tileSize;
            int maxTileY = (levelHeight + tileSize - 1) / tileSize;
            
            // 修复SDPC格式边界瓦片问题：对SDPC格式采用更严格的边界检查
            boolean isSdpcFormat = filePath.toLowerCase().endsWith(".sdpc");
            boolean isOutOfBounds = false;
            
            if (isSdpcFormat) {
                // SDPC格式：严格边界检查，不允许超出有效范围
                if (x >= maxTileX || y >= maxTileY) {
                    isOutOfBounds = true;
                    log.debug("SDPC严格边界检查: 瓦片索引({}, {})超出范围，最大瓦片索引({}, {})", 
                             x, y, maxTileX - 1, maxTileY - 1);
                }
                
                // 额外检查：确保瓦片起始位置不超出图像边界
                if (tileX >= levelWidth || tileY >= levelHeight) {
                    isOutOfBounds = true;
                    log.debug("SDPC像素位置检查: 瓦片像素坐标({}, {})超出图像边界({}, {})", 
                             tileX, tileY, levelWidth, levelHeight);
                }
            } else {
                // 对于DZI标准，由于overlap的存在，允许边界瓦片稍微超出理论范围
                // 但瓦片的实际像素区域不能完全超出图像边界
                
                // 更宽松的边界检查：只有当瓦片完全超出合理范围时才认为越界
                if (x >= maxTileX + 2 || y >= maxTileY + 2) {
                    // 完全超出合理范围（增加2个瓦片的容错）
                    isOutOfBounds = true;
                } else if (x >= maxTileX || y >= maxTileY) {
                    // 在扩展边界内，检查实际像素区域是否有效
                    if (tileX >= levelWidth + tileSize && tileY >= levelHeight + tileSize) {
                        // 瓦片完全超出图像边界（增加一个瓦片大小的容错）
                        isOutOfBounds = true;
                    } else {
                        // 部分重叠或在容错范围内，允许处理
                        log.debug("DZI边界瓦片（容错范围内）: 瓦片索引=({}, {}), 层级尺寸={}x{}, 瓦片数量={}x{}, 像素坐标=({}, {})", 
                                x, y, levelWidth, levelHeight, maxTileX, maxTileY, tileX, tileY);
                    }
                }
            }
            
            if (isOutOfBounds) {
                log.warn("DZI瓦片索引超出边界: 瓦片索引=({}, {}), 层级尺寸={}x{}, 瓦片数量={}x{}", 
                        x, y, levelWidth, levelHeight, maxTileX, maxTileY);
                
                // 修正：对于小尺寸层级，如果请求的是(0,0)瓦片，返回整个层级图像
                // 如果请求的是其他瓦片，返回空白瓦片
                if (levelWidth <= tileSize && levelHeight <= tileSize) {
                    if (x == 0 && y == 0) {
                        log.info("小尺寸层级，返回(0,0)瓦片作为整个层级图像: 层级={}, 尺寸={}x{}", level, levelWidth, levelHeight);
                        // 继续处理，返回整个层级图像
                    } else {
                        log.info("小尺寸层级，非(0,0)瓦片返回空白: 瓦片索引=({}, {}), 层级={}, 尺寸={}x{}", 
                                x, y, level, levelWidth, levelHeight);
                        return createErrorTileForFormat(tileSize, filePath != null ? new java.io.File(filePath).getName().toLowerCase() : "");
                    }
                } else {
                    // 修复SDPC边界瓦片问题：对于SDPC格式，超出边界时返回透明瓦片
                    if (isSdpcFormat) {
                        log.debug("SDPC格式超出边界，返回透明瓦片: 瓦片索引=({}, {}), 层级={}", x, y, level);
                        return createTransparentTile(tileSize);
                    } else {
                        return createErrorTileForFormat(tileSize, filePath != null ? new java.io.File(filePath).getName().toLowerCase() : "");
                    }
                }
            }
            
            // 计算瓦片的有效尺寸 - 修正：对于边界瓦片，确保至少有部分有效区域
            int tileW, tileH;
            
            // 更宽松的坐标检查
            if (tileX >= levelWidth + tileSize) {
                // 瓦片X坐标完全超出图像边界（增加容错）
                log.debug("瓦片X坐标超出边界（容错后）: tileX={}, levelWidth={}", tileX, levelWidth);
                return createErrorTileForFormat(tileSize, "");
            } else if (tileY >= levelHeight + tileSize) {
                // 瓦片Y坐标完全超出图像边界（增加容错）
                log.debug("瓦片Y坐标超出边界（容错后）: tileY={}, levelHeight={}", tileY, levelHeight);
                return createErrorTileForFormat(tileSize, "");
            } else {
                // 正常计算瓦片尺寸，允许超出边界的情况
                if (tileX >= levelWidth) {
                    // 如果瓦片起始位置超出边界，但在容错范围内，创建空白瓦片
                    log.debug("瓦片起始X坐标超出边界，创建空白瓦片: tileX={}, levelWidth={}", tileX, levelWidth);
                    return createErrorTileForFormat(tileSize, "");
                }
                if (tileY >= levelHeight) {
                    // 如果瓦片起始位置超出边界，但在容错范围内，创建空白瓦片
                    log.debug("瓦片起始Y坐标超出边界，创建空白瓦片: tileY={}, levelHeight={}", tileY, levelHeight);
                    return createErrorTileForFormat(tileSize, "");
                }
                
                tileW = Math.min(tileSize, Math.max(1, levelWidth - tileX));
                tileH = Math.min(tileSize, Math.max(1, levelHeight - tileY));
                
                // 确保尺寸有效
                if (tileW <= 0 || tileH <= 0) {
                    log.debug("DZI瓦片尺寸无效，创建空白瓦片: 瓦片尺寸={}x{}, 瓦片坐标=({}, {}), 层级尺寸={}x{}", 
                            tileW, tileH, tileX, tileY, levelWidth, levelHeight);
                    return createErrorTileForFormat(tileSize, "");
                }
            }
            
            // 计算overlap区域，并确保不超出图像边界
            int left = (x == 0) ? 0 : overlap;
            int top = (y == 0) ? 0 : overlap;
            int right = ((tileX + tileW) >= levelWidth) ? 0 : overlap;
            int bottom = ((tileY + tileH) >= levelHeight) ? 0 : overlap;
            
            // 确保有足够空间给overlap
            if (tileX + tileW + right > levelWidth) right = levelWidth - (tileX + tileW);
            if (tileY + tileH + bottom > levelHeight) bottom = levelHeight - (tileY + tileH);
            
            // 确保不为负
            if (right < 0) right = 0;
            if (bottom < 0) bottom = 0;
            
            int readW = tileW + left + right;
            int readH = tileH + top + bottom;

            // 修复：使用瓦片坐标锁定机制，确保每次请求返回完全相同的坐标
            // 使用统一缓存键，确保无论色彩参数如何，坐标计算都保持一致
            String coordinatesKey = filePath + "_unified_" + level + "_" + x + "_" + y;
            TileCoordinates lockedCoords = tileCoordinatesCache.get(coordinatesKey);
            
            int srcX, srcY, srcW, srcH;
            
            if (lockedCoords != null) {
                // 使用已锁定的坐标
                srcX = lockedCoords.srcX;
                srcY = lockedCoords.srcY;
                srcW = lockedCoords.srcW;
                srcH = lockedCoords.srcH;
                readW = lockedCoords.readW;
                readH = lockedCoords.readH;
                
                log.debug("使用锁定坐标: 瓦片({},{}) -> 坐标({},{},{}x{})", 
                         x, y, srcX, srcY, srcW, srcH);
            } else {
                // 计算新坐标并锁定
                srcX = (tileX - left) * scale;
                srcY = (tileY - top) * scale;
                srcW = readW * scale;
                srcH = readH * scale;
                
                // 锁定坐标，防止后续计算差异
                TileCoordinates newCoords = new TileCoordinates(srcX, srcY, srcW, srcH, readW, readH);
                tileCoordinatesCache.put(coordinatesKey, newCoords);
                
                log.debug("新建锁定坐标: 瓦片({},{}) -> 坐标({},{},{}x{})", 
                         x, y, srcX, srcY, srcW, srcH);
            }
            
            // 重要：确保原始坐标不会超出图像边界
            if (srcX < 0) srcX = 0;
            if (srcY < 0) srcY = 0;
            if (srcX + srcW > width) srcW = width - srcX;
            if (srcY + srcH > height) srcH = height - srcY;
            
            // 使用缓存的解析器和切片对象，避免重复打开
            Object slide = wrapper.getSlide();
            String parserName = wrapper.getParserName();
            SlideParser parser = parserFactory.getParserByName(parserName);
            
            if (parser == null) {
                log.error("无法获取解析器: {}", parserName);
                return createErrorTileForFormat(tileSize, new java.io.File(filePath).getName().toLowerCase());
            }
            
            // 修复：根据实际使用的解析器选择正确的坐标映射逻辑
            String fileName = new java.io.File(filePath).getName().toLowerCase();
            final String fileNameForError = fileName; // 用于错误瓦片的文件名
            boolean isTronFormat = fileName.endsWith(".tron");
            boolean isOpenSlideFormat = fileName.endsWith(".svs") || fileName.endsWith(".ndpi") ||
                                      fileName.endsWith(".vms") || fileName.endsWith(".vmu") ||
                                      fileName.endsWith(".scn") || fileName.endsWith(".mrxs") ||
                                      fileName.endsWith(".tiff") || fileName.endsWith(".tif");
            boolean isSqraySlideFormat = fileName.endsWith(".sdpc");

            byte[] tileData = null;
            
            try {
                if (isOpenSlideFormat) {
                    // OpenSlide 坐标映射逻辑
                    // OpenSlide：层级0=最高分辨率，层级越高分辨率越低
                    // DZI：层级0=最低分辨率，层级越高分辨率越高
                    // 需要进行层级转换
                    Object levelCountObj = info.get("levelCount");
                    int levelCount = (levelCountObj instanceof Number) ? ((Number) levelCountObj).intValue() : 1;
                    
                    // 修复SVS格式的层级映射问题：确保正确的层级转换
                    // 对于OpenSlide格式（包括SVS），DZI最高层级（maxDziLevel）对应OpenSlide层级0（最高分辨率）
                    // DZI层级越小，分辨率越高，对应的OpenSlide层级也越小
                    int openSlideLevel = Math.max(0, Math.min(levelCount - 1, maxDziLevel - level));
                    
                    // OpenSlide.readRegion 方法的参数要求：
                    // - x, y: 必须是层级0（最高分辨率）坐标系统中的坐标
                    // - width, height: 必须是目标层级坐标系统中的尺寸
                    
                    // 修复SVS坐标映射：确保正确计算层级0坐标
                    // 对于SVS格式，需要将DZI坐标正确映射到OpenSlide坐标系统
                    int level0X = srcX;  // srcX 已经通过 scale 缩放到原图坐标
                    int level0Y = srcY;  // srcY 已经通过 scale 缩放到原图坐标
                    
                    // 计算目标层级的下采样比例 - 修复：使用OpenSlide实际的下采样比例
                    double downsample = Math.pow(2.0, openSlideLevel);  // 默认值
                    
                    try {
                        // 通过 parser 获取精确的下采样比例
                        Map<String, Object> levelInfo = parser.getLevelInfo(slide, openSlideLevel);
                        if (levelInfo.containsKey("downsample")) {
                            downsample = ((Number) levelInfo.get("downsample")).doubleValue();
                            log.debug("从levelInfo获取到下采样比例: {}", downsample);
                        } else {
                            log.debug("levelInfo中无下采样比例，使用默认计算值: {}", downsample);
                        }
                    } catch (Exception e) {
                        log.warn("获取OpenSlide层级{}下采样比例失败，使用默认值: {}, 错误: {}", openSlideLevel, downsample, e.getMessage());
                    }
                    
                    // 修复SVS格式的尺寸计算：确保正确计算目标层级尺寸
                    // OpenSlide.readRegion 的 width 和 height 参数是目标层级的像素尺寸
                    int targetLevelW = (int) Math.max(1, Math.ceil((double) readW * scale / downsample));
                    int targetLevelH = (int) Math.max(1, Math.ceil((double) readH * scale / downsample));
                    
                    // 边界检查和调整 - 修复SVS边界问题
                    if (level0X + srcW > width) {
                        srcW = width - level0X;
                        // 重新计算目标层级尺寸
                        targetLevelW = (int) Math.max(1, Math.ceil((double) srcW / downsample));
                    }
                    if (level0Y + srcH > height) {
                        srcH = height - level0Y;
                        // 重新计算目标层级尺寸
                        targetLevelH = (int) Math.max(1, Math.ceil((double) srcH / downsample));
                    }
                    
                    // 修复SVS空白问题：添加额外的边界检查
                    if (level0X < 0) level0X = 0;
                    if (level0Y < 0) level0Y = 0;
                    if (targetLevelW <= 0) targetLevelW = 1;
                    if (targetLevelH <= 0) targetLevelH = 1;
                    
                    log.debug("OpenSlide瓦片请求: DZI层级={} -> OpenSlide层级={}, 层级0坐标=({}, {}), 目标尺寸={}x{}, 下采样={}", 
                             level, openSlideLevel, level0X, level0Y, targetLevelW, targetLevelH, downsample);
                    
                    // 使用缓存的slide对象直接获取区域数据
                    tileData = parser.getRegionJpeg(slide, level0X, level0Y, targetLevelW, targetLevelH, openSlideLevel);
                    
                } else if (isSqraySlideFormat) {
                    // 简化SDPC格式的坐标映射逻辑
                    // 由于DZI描述文件已改为使用原图尺寸，这里可以直接进行坐标映射，无需内容区域偏移
                    
                    Object levelCountObj = info.get("levelCount");
                    int levelCount = (levelCountObj instanceof Number) ? ((Number) levelCountObj).intValue() : 1;
                    
                    // 选择合适的SDPC层级
                    int useLevel = 0;
                    for (int l = 1; l < levelCount; l++) {
                        int levelScale = 1 << l;
                        if (levelScale * 1.5 > scale) break;
                        useLevel = l;
                    }
                    
                    // 关键修复：SDPC格式相邻瓦片边界对齐机制
                    // 确保相邻瓦片的边界坐标完全匹配，避免拼接缝隙
                    int dziTileSize = TILE_SIZE;
                    
                    // 精确计算瓦片边界，确保像素级对齐
                    int dziPx = x * dziTileSize;
                    int dziPy = y * dziTileSize;
                    
                    // 计算瓦片的精确边界（向下取整到像素边界）
                    int alignedDziPx = (dziPx / 4) * 4; // 4像素对齐，确保边界稳定
                    int alignedDziPy = (dziPy / 4) * 4;
                    
                    // 直接映射到原图坐标（基于原图尺寸的DZI）
                    int origPx = alignedDziPx * scale;
                    int origPy = alignedDziPy * scale;
                    int origW = Math.min(dziTileSize * scale, width - alignedDziPx * scale);
                    int origH = Math.min(dziTileSize * scale, height - alignedDziPy * scale);
                    
                    // 新增：SDPC瓦片边界锁定缓存 - 使用统一缓存键
                    String boundaryKey = filePath + "_unified_boundary_" + level + "_" + x + "_" + y;
                    String cachedBoundary = tileVerificationCache.get(boundaryKey);
                    String currentBoundary = origPx + "," + origPy + "," + origW + "," + origH;
                    
                    if (cachedBoundary != null && !cachedBoundary.equals(currentBoundary)) {
                        log.warn("SDPC瓦片边界不一致: 瓦片({},{}), 缓存={}, 当前={}", 
                                x, y, cachedBoundary, currentBoundary);
                        // 使用缓存的边界值，确保一致性
                        String[] parts = cachedBoundary.split(",");
                        origPx = Integer.parseInt(parts[0]);
                        origPy = Integer.parseInt(parts[1]);
                        origW = Integer.parseInt(parts[2]);
                        origH = Integer.parseInt(parts[3]);
                    } else {
                        tileVerificationCache.put(boundaryKey, currentBoundary);
                    }
                    
                    log.debug("SDPC精确坐标映射: DZI({}, {}) -> 对齐({}, {}) -> 原图({}, {}), 尺寸: {}x{}, 缩放: {}", 
                             dziPx, dziPy, alignedDziPx, alignedDziPy, origPx, origPy, origW, origH, scale);
                    
                    // 边界检查
                    if (origPx >= width || origPy >= height || origW <= 0 || origH <= 0) {
                        log.debug("SDPC坐标超出边界，返回透明瓦片: 原图坐标({}, {}), 尺寸{}x{}", 
                                 origPx, origPy, origW, origH);
                        tileData = createTransparentTile(dziTileSize);
                    } else {
                        // 确保不会超出原图边界
                        if (origPx + origW > width) {
                            origW = width - origPx;
                        }
                        if (origPy + origH > height) {
                            origH = height - origPy;
                        }
                        
                        // 转换到SDPC层级坐标
                        int sdpcPx = origPx / (1 << useLevel);
                        int sdpcPy = origPy / (1 << useLevel);
                        int sdpcW = Math.max(1, origW / (1 << useLevel));
                        int sdpcH = Math.max(1, origH / (1 << useLevel));
                        
                        log.debug("SDPC层级{}坐标: ({}, {}), 尺寸: {}x{}", useLevel, sdpcPx, sdpcPy, sdpcW, sdpcH);
                        
                        byte[] region = parser.getRegionJpeg(slide, sdpcPx, sdpcPy, sdpcW, sdpcH, useLevel);
                        if (region == null || region.length == 0) {
                            tileData = createTransparentTile(dziTileSize);
                        } else {
                            try {
                                BufferedImage regionImg = ImageIO.read(new ByteArrayInputStream(region));
                                if (regionImg != null) {
                                    // 修复：正确计算DZI瓦片的目标尺寸
                                    // 基于当前层级的缩放因子，计算实际应该显示的瓦片尺寸
                                    int actualTileW = Math.min(dziTileSize, (width - dziPx) / scale);
                                    int actualTileH = Math.min(dziTileSize, (height - dziPy) / scale);
                                    
                                    // 确保尺寸为正数
                                    actualTileW = Math.max(1, actualTileW);
                                    actualTileH = Math.max(1, actualTileH);
                                    
                                    // 创建标准256x256的DZI瓦片
                                    BufferedImage dziTileImg = new BufferedImage(dziTileSize, dziTileSize, BufferedImage.TYPE_INT_RGB);
                                    Graphics2D g2d = dziTileImg.createGraphics();
                                    
                                    // 设置高质量渲染
                                    g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                                    g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                                    g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                                    
                                    // 填充白色背景
                                    g2d.setColor(Color.WHITE);
                                    g2d.fillRect(0, 0, dziTileSize, dziTileSize);
                                    
                                    // 修复：将SDPC区域图像精确缩放到DZI瓦片尺寸
                                    // 使用精确的缩放比例，确保瓦片拼接准确
                                    double scaleX = (double) actualTileW / regionImg.getWidth();
                                    double scaleY = (double) actualTileH / regionImg.getHeight();
                                    
                                    // 使用统一的缩放比例，保持宽高比
                                    double uniformScale = Math.min(scaleX, scaleY);
                                    int scaledW = (int) Math.round(regionImg.getWidth() * uniformScale);
                                    int scaledH = (int) Math.round(regionImg.getHeight() * uniformScale);
                                    
                                    // 居中绘制，确保瓦片对齐
                                    int offsetX = 0; // 左对齐，不居中
                                    int offsetY = 0; // 上对齐，不居中
                                    
                                    g2d.drawImage(regionImg, offsetX, offsetY, scaledW, scaledH, null);
                                    g2d.dispose();
                                    
                                    ByteArrayOutputStream out = new ByteArrayOutputStream();
                                    ImageIO.write(dziTileImg, "JPEG", out);
                                    tileData = out.toByteArray();
                                    
                                    log.debug("SDPC瓦片生成成功: 原始{}x{} -> 缩放{}x{} -> DZI{}x{}, 缩放比例={}", 
                                             regionImg.getWidth(), regionImg.getHeight(), scaledW, scaledH, 
                                             actualTileW, actualTileH, uniformScale);
                                } else {
                                    tileData = createTransparentTile(dziTileSize);
                                }
                            } catch (Exception e) {
                                log.warn("SDPC瓦片图像处理失败: level={}, x={}, y={}, 错误: {}", 
                                        level, x, y, e.getMessage());
                                tileData = createTransparentTile(dziTileSize);
                            }
                        }
                    }
                } else if (isTronFormat) {
                    // Tron格式DZI瓦片：改为命令行调用 tron_image_extractor
                    log.debug("Tron格式DZI瓦片请求，直接调用 tronSdkImageExtractor: level={}, x={}, y={}", level, x, y);
                    // 直接调用命令行工具获取瓦片
                    tileData = tronSdkImageExtractor.extractTile(filePath, x, y, level);
                } else {
                    // 默认处理逻辑：尝试使用区域获取
                    log.debug("默认瓦片请求: 层级={}, 坐标=({}, {}), 区域=({}, {}, {}, {})", 
                             level, x, y, srcX, srcY, srcW, srcH);
                    
                    // 使用缓存的slide对象直接获取区域数据
                    tileData = parser.getRegionJpeg(slide, srcX, srcY, srcW, srcH, level);
                }
                
                // 验证瓦片数据
                if (tileData == null || tileData.length == 0) {
                    log.warn("获取瓦片数据为空: 层级={}, 坐标=({}, {})", level, x, y);
                    return createErrorTileForFormat(tileSize, fileNameForError);
                }
                
                // 缓存瓦片数据
                tileCache.put(cacheKey, tileData);
                
                // 新增：对SDPC文件进行瓦片一致性验证
                if (filePath.toLowerCase().endsWith(".sdpc")) {
                    String verifyKey = "verify_unified_" + identifier + "_" + level + "_" + x + "_" + y;
                    String tileHash = String.valueOf(tileData.length + "_" + (tileData.length > 0 ? (tileData[0] & 0xFF) : 0));
                    
                    String existingHash = tileVerificationCache.get(verifyKey);
                    if (existingHash != null && !existingHash.equals(tileHash)) {
                        log.warn("SDPC瓦片一致性检查失败: 瓦片({},{}) 哈希不匹配: {} vs {}", 
                                x, y, existingHash, tileHash);
                    } else {
                        tileVerificationCache.put(verifyKey, tileHash);
                        log.debug("SDPC瓦片一致性验证通过: 瓦片({},{}) 哈希: {}", x, y, tileHash);
                    }
                }
                
                // 记录瓦片大小统计
                tileSizeStats.addTile(tileData.length);
                
                // 只在DEBUG级别记录成功信息
                if (log.isDebugEnabled()) {
                    log.debug("瓦片获取成功: 层级={}, 坐标=({}, {}), 大小={}KB", 
                             level, x, y, tileData.length / 1024);
                }
                
                return tileData;
                
            } catch (Exception e) {
                log.error("获取瓦片数据失败: 层级={}, 坐标=({}, {}), 错误: {}", level, x, y, e.getMessage(), e);
                return createErrorTileForFormat(tileSize, fileNameForError);
            }
            
            // 注意：不要在这里关闭slide，因为它是缓存的对象，会被其他请求复用
            
        } catch (SlideException e) {
            // 只在DEBUG级别记录详细错误信息，减少日志噪音
            if (log.isDebugEnabled()) {
                log.debug("获取DZI瓦片失败: {}, 层级: {}, 坐标: ({}, {}), 错误: {}", 
                         identifier, level, x, y, e.getMessage());
            }
            throw e;
        } catch (Exception e) {
            // 系统错误仍然记录为ERROR级别
            log.error("获取DZI瓦片失败，系统内部错误: {}", e.getMessage(), e);
            throw new SlideException("获取DZI瓦片失败: " + e.getMessage(), e);
        } finally {
            // 释放SDPC文件的并发控制信号量
            if (isSDPCFile) {
                sdpcConcurrencyControl.release();
                log.debug("SDPC瓦片释放处理许可: ({}, {}, {})", level, x, y);
            }
        }
    }
    
    /**
     * 判断像素是否为黑色边框像素
     * 
     * @param rgb RGB像素值
     * @return 是否为黑色边框像素
     */
    private boolean isBlackBorderPixel(int rgb) {
        int red = (rgb >> 16) & 0xFF;
        int green = (rgb >> 8) & 0xFF;
        int blue = rgb & 0xFF;
        
        // 黑色及近黑色检测 - 与SqraySlideService保持一致
        if (red < 35 && green < 35 && blue < 35) {
            return true;
        }
        
        // 深灰色检测
        int maxChannel = Math.max(Math.max(red, green), blue);
        int minChannel = Math.min(Math.min(red, green), blue);
        if (maxChannel - minChannel < 25 && maxChannel < 85) {
            return true;
        }
        
        // 近黑色像素检测
        if (red < 60 && green < 60 && blue < 60) {
            return true;
        }
        
        // 平均值检测
        int avgValue = (red + green + blue) / 3;
        if (avgValue < 25) {
            return true;
        }
        
        return false;
    }

    /**
     * 分块处理大区域图像（支持指定层级）
     */
    private byte[] processLargeRegionInBlocks(String filePath, int srcX, int srcY, int srcW, int srcH, int targetWidth, int targetHeight, int level) {
        String fileName = new java.io.File(filePath).getName().toLowerCase();
        boolean isTronFormat = fileName.endsWith(".tron");
        if (isTronFormat) {
            // Tron格式大区域请求：改为命令行调用 tron_image_extractor
            log.debug("Tron格式大区域请求，直接调用 tronSdkImageExtractor: x={}, y={}, w={}, h={}, level={}", srcX, srcY, srcW, srcH, level);
            byte[] regionData = tronSdkImageExtractor.extractRegion(filePath, srcX, srcY, srcW, srcH, level);
            if (regionData != null && regionData.length > 0) {
                return regionData;
            } else {
                log.warn("tronSdkImageExtractor未返回有效区域数据: {}", filePath);
                return null;
            }
        }
        try {
            // log.debug("处理大区域图像: 原尺寸={}x{}, 目标尺寸={}x{}", srcW, srcH, targetWidth, targetHeight);
            
            // 修改1: 优化块大小，增大以减少拼接痕迹
            int blockSize = 4096; // 使用更大的块尺寸，减少拼接缝隙
            
            // 创建目标图像
            BufferedImage fullImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = fullImage.createGraphics();
            
            // 设置更高质量的渲染参数
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
            g.setRenderingHint(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
            g.setRenderingHint(RenderingHints.KEY_DITHERING, RenderingHints.VALUE_DITHER_ENABLE);
            
            // 计算缩放比例 - 使用精确的浮点数计算
            double scaleX = (double) targetWidth / srcW;
            double scaleY = (double) targetHeight / srcH;
            
            // log.debug("开始分块处理大区域: 原始区域={}x{}, 目标尺寸={}x{}, 缩放比例={}x{}", 
            //        srcW, srcH, targetWidth, targetHeight, scaleX, scaleY);
            
            // 修改2: 调整块边界计算方式，确保无缝拼接
            // 计算块数，使用Math.floor确保块边界计算精确
            int xBlocks = (int) Math.ceil((double) srcW / blockSize);
            int yBlocks = (int) Math.ceil((double) srcH / blockSize);
            
            // 优先处理视野中心区域的块
            List<int[]> blockOrder = new ArrayList<>(xBlocks * yBlocks);
            
            // 计算中心块
            int centerBlockX = xBlocks / 2;
            int centerBlockY = yBlocks / 2;
            
            // 按照到中心块的距离排序处理
            for (int blockY = 0; blockY < yBlocks; blockY++) {
                for (int blockX = 0; blockX < xBlocks; blockX++) {
                    blockOrder.add(new int[]{blockX, blockY});
                }
            }
            
            // 使用欧几里得距离排序
            Collections.sort(blockOrder, (a, b) -> {
                double distA = Math.sqrt(Math.pow(a[0] - centerBlockX, 2) + Math.pow(a[1] - centerBlockY, 2));
                double distB = Math.sqrt(Math.pow(b[0] - centerBlockX, 2) + Math.pow(b[1] - centerBlockY, 2));
                return Double.compare(distA, distB);
            });
            
            // log.debug("分块处理顺序已优化，将优先处理中心区域");
            
            // 使用有限并行处理，避免过多同时请求阻塞系统
            int maxConcurrentBlocks = Math.min(Runtime.getRuntime().availableProcessors(), 4);
            
            // 创建任务列表
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            
            // 修改3: 添加块处理间的重叠区域，减少拼接痕迹
            int blockOverlap = 8; // 块间重叠像素数，改善拼接效果
            
            // 分批处理所有块，优先处理中心区域
            for (int[] block : blockOrder) {
                int blockX = block[0];
                int blockY = block[1];
                
                // 修改4: 精确计算块的边界位置，注意添加重叠区
                // 计算当前块的原始坐标和大小
                int blockSrcX = srcX + blockX * (blockSize - blockOverlap);
                int blockSrcY = srcY + blockY * (blockSize - blockOverlap);
                int blockSrcW = Math.min(blockSize, srcX + srcW - blockSrcX);
                int blockSrcH = Math.min(blockSize, srcY + srcH - blockSrcY);
                
                // 检查块是否有效
                if (blockSrcW <= 0 || blockSrcH <= 0) {
                    continue;
                }
                
                // 创建处理任务
                final int finalBlockX = blockX;
                final int finalBlockY = blockY;
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        // 请求当前块数据，使用指定层级
                        byte[] blockJpeg = unifiedSlideService.getRegionJpeg(
                            filePath, blockSrcX, blockSrcY, blockSrcW, blockSrcH, level);
                        
                        if (blockJpeg != null && blockJpeg.length > 0) {
                            // 解码块数据
                            BufferedImage blockImg = ImageIO.read(new ByteArrayInputStream(blockJpeg));
                            
                            if (blockImg != null) {
                                // 修改5: 精确计算块在目标图像中的位置，考虑重叠区
                                int targetBlockX = (int) Math.floor((blockSrcX - srcX) * scaleX);
                                int targetBlockY = (int) Math.floor((blockSrcY - srcY) * scaleY);
                                int targetBlockW = (int) Math.ceil(blockSrcW * scaleX);
                                int targetBlockH = (int) Math.ceil(blockSrcH * scaleY);
                                
                                // 确保边界块不会超出目标图像范围
                                targetBlockW = Math.min(targetBlockW, targetWidth - targetBlockX);
                                targetBlockH = Math.min(targetBlockH, targetHeight - targetBlockY);
                                
                                // 使用同步块确保绘制操作是线程安全的
                                synchronized (g) {
                                    // 修改6: 使用高质量绘制，在有重叠区域时需要特殊处理以平滑过渡
                                    // 对于非第一个块，使用透明度渐变来平滑拼接
                                    if (blockX > 0 || blockY > 0) {
                                        // 绘制时采用完整尺寸，依赖渲染提示确保质量
                                        g.drawImage(blockImg, 
                                                   targetBlockX, targetBlockY, 
                                                   targetBlockX + targetBlockW, targetBlockY + targetBlockH,
                                                   0, 0, blockImg.getWidth(), blockImg.getHeight(), 
                                                   null);
                                    } else {
                                        // 第一个块直接绘制
                                        g.drawImage(blockImg, 
                                                   targetBlockX, targetBlockY, 
                                                   targetBlockW, targetBlockH, 
                                                   null);
                                    }
                                }
                                blockImg.flush(); // 释放块图像资源
                            }
                        }
                    } catch (Exception e) {
                        log.warn("处理块 [{}, {}] 失败: {}", finalBlockX, finalBlockY, e.getMessage());
                    }
                }, tileProcessorPool);
                
                futures.add(future);
                
                // 限制并发块处理数量
                if (futures.size() >= maxConcurrentBlocks) {
                    // 等待所有当前任务完成
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                    futures.clear();
                }
            }
            
            // 等待所有剩余任务完成
            if (!futures.isEmpty()) {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            }
            
            g.dispose();
            
            // 修改7: 使用无损或极高质量JPEG输出
            byte[] result = optimizeJpegQuality(fullImage);
            fullImage.flush(); // 释放全图资源
            
            return result;
        } catch (Exception e) {
            log.error("分块处理大区域图像失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 为特定文件格式创建错误瓦片
     */
    private byte[] createErrorTileForFormat(int tileSize, String fileName) {
        try {
            // 针对SDPC格式，创建完全透明的错误瓦片，避免显示任何背景色
            if (fileName != null && fileName.endsWith(".sdpc")) {
                // 创建透明瓦片，不填充任何颜色
                BufferedImage errorTile = new BufferedImage(tileSize, tileSize, BufferedImage.TYPE_INT_ARGB);
                java.awt.Graphics2D g = errorTile.createGraphics();
                // 设置完全透明
                g.setComposite(AlphaComposite.Clear);
                g.fillRect(0, 0, tileSize, tileSize);
                g.dispose();
                return optimizeJpegQuality(errorTile);
            } else {
                // 其他格式保持原有的浅灰色背景
                BufferedImage errorTile = new BufferedImage(tileSize, tileSize, BufferedImage.TYPE_INT_RGB);
                java.awt.Graphics2D g = errorTile.createGraphics();
                g.setColor(new java.awt.Color(245, 245, 245));  // 其他格式使用浅灰色背景
                g.fillRect(0, 0, tileSize, tileSize);
                g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g.dispose();
                return optimizeJpegQuality(errorTile);
            }
        } catch (Exception e) {
            log.error("创建错误瓦片失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 优化JPEG质量 - 动态调整以控制瓦片大小
     */
    private byte[] optimizeJpegQuality(BufferedImage image) {
        return optimizeJpegQuality(image, JPEG_QUALITY, -1);
    }
    
    /**
     * 计算最优JPEG质量以控制瓦片大小
     * @param baseQuality 基础质量
     * @param level DZI层级
     * @param pixelCount 像素数量
     * @return 优化后的质量值
     */
    private float calculateOptimalQuality(float baseQuality, int level, int pixelCount) {
        // 针对SDPC文件优化：保持更高的质量标准
        if (level <= 2) {
            // 低层级（高分辨率）保持最高质量，确保细节清晰
            return Math.max(baseQuality, 0.96f);
        } else if (level <= 5) {
            // 中等层级，适度优化质量
            return Math.max(baseQuality * 0.97f, 0.93f);
        } else if (level <= 8) {
            // 较高层级，平衡质量和大小
            return Math.max(baseQuality * 0.95f, 0.90f);
        } else {
            // 最高层级（缩略图级别），优先考虑加载速度
            return Math.max(baseQuality * 0.90f, 0.85f);
        }
    }
    
    /**
     * 优化JPEG质量 - 支持层级感知的动态质量调整
     * @param image 待压缩的图像
     * @param baseQuality 基础质量
     * @param level DZI层级（-1表示使用默认）
     */
    private byte[] optimizeJpegQuality(BufferedImage image, float baseQuality, int level) {
        try {
            // 根据层级动态调整质量
            float quality = calculateOptimalQuality(baseQuality, level, image.getWidth() * image.getHeight());
            
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            
            // 设置JPEG压缩参数
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpeg");
            if (!writers.hasNext()) {
                // 没有JPEG编码器，使用标准方法
                ImageIO.write(image, "JPEG", output);
                return output.toByteArray();
            }
            
            ImageWriter writer = writers.next();
            ImageWriteParam param = writer.getDefaultWriteParam();
            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            // 使用动态计算的质量
            param.setCompressionQuality(quality);
            
            // 添加其他质量提升参数
            if (param instanceof JPEGImageWriteParam) {
                // 使用优质编码
                ((JPEGImageWriteParam) param).setOptimizeHuffmanTables(true);
            }
            
            // 创建带有高质量渲染提示的图像
            BufferedImage optimizedImage = image;
            
            // 如果不是RGB类型，则转换为RGB类型以获得更好的质量
            if (image.getType() != BufferedImage.TYPE_INT_RGB) {
                optimizedImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
                Graphics2D g2d = optimizedImage.createGraphics();
                
                // 修改2: 设置更全面的高质量渲染提示
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
                g2d.setRenderingHint(RenderingHints.KEY_DITHERING, RenderingHints.VALUE_DITHER_ENABLE);
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                
                // 绘制原始图像
                g2d.drawImage(image, 0, 0, null);
                g2d.dispose();
            }
            
            // 修改3: 使用更高精度的IO流和写入参数
            ImageOutputStream ios = ImageIO.createImageOutputStream(output);
            writer.setOutput(ios);
            
            // 添加子采样控制，使用无色度抽样压缩以提高边缘清晰度
            if (param instanceof JPEGImageWriteParam) {
            }
            
            writer.write(null, new IIOImage(optimizedImage, null, null), param);
            writer.dispose();
            ios.close();
            
            // 如果创建了新图像，清理资源
            if (optimizedImage != image) {
                optimizedImage.flush();
            }
            
            return output.toByteArray();
        } catch (Exception e) {
            log.error("JPEG优化失败: {}", e.getMessage());
            // 回退到标准方法
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            try {
                // 修改4: 提高备用方法的质量
                javax.imageio.ImageWriteParam iwp = 
                    javax.imageio.ImageIO.getImageWritersByFormatName("jpeg").next().getDefaultWriteParam();
                iwp.setCompressionMode(javax.imageio.ImageWriteParam.MODE_EXPLICIT);
                iwp.setCompressionQuality(0.99f);  // 提高到0.99，确保几乎无损压缩
                
                javax.imageio.ImageWriter iw = 
                    javax.imageio.ImageIO.getImageWritersByFormatName("jpeg").next();
                javax.imageio.stream.ImageOutputStream ios = 
                    javax.imageio.ImageIO.createImageOutputStream(output);
                iw.setOutput(ios);
                iw.write(null, new javax.imageio.IIOImage(image, null, null), iwp);
                iw.dispose();
                ios.close();
                
                return output.toByteArray();
            } catch (Exception ex) {
                log.error("标准JPEG编码也失败: {}", ex.getMessage());
                try {
                    // 最后的回退方法，尝试无损PNG
                    try {
                        output.reset();
                        ImageIO.write(image, "PNG", output);
                        byte[] pngData = output.toByteArray();
                        if (pngData != null && pngData.length > 0) {
                            // log.debug("使用PNG格式作为回退: {}字节", pngData.length);
                            return pngData;
                        }
                    } catch (Exception e2) {
                        // log.debug("PNG编码失败，回退到默认JPEG");
                    }
                    
                    // 最简单的回退方法
                    output.reset();
                    ImageIO.write(image, "JPEG", output);
                    return output.toByteArray();
                } catch (Exception exc) {
                    log.error("所有编码方法都失败: {}", exc.getMessage());
                    return null;
                }
            }
        }
    }

    
    @Override
    public String getFilePathByIdentifier(String identifier) throws SlideException {
        if (identifier == null || identifier.trim().isEmpty()) {
            throw new SlideException("标识符不能为空");
        }
        identifier = identifier.trim();
        
        log.debug("开始通过数据库查询获取文件路径: id={}", identifier);
        
        try {
            // 修改：直接通过主键id查询t_c_slice表获取physic_path
            SliceEntity sliceEntity = sliceService.getById(identifier);
            if (sliceEntity == null) {
                log.warn("未找到对应的切片记录: id={}, 切片已被删除或不存在", identifier);
                // 修改：不再自动使用默认文件，直接抛出异常
                // 这样可以避免删除后仍能通过默认文件访问的问题
                throw new SlideException("切片文件不存在或已被删除: " + identifier);
            }
            
            String physicPath = sliceEntity.getPhysicPath();
            if (physicPath == null || physicPath.trim().isEmpty()) {
                log.error("切片记录中physic_path为空: id={}", identifier);
                throw new SlideException("切片记录中physic_path为空: " + identifier);
            }
            
            // 2. 拼接local-slice-dir + physic_path构建完整文件路径
            String localSliceDir = storageConfig.getLocalSliceDir();
            String fullPath = filePathService.processFilePath(physicPath);
            log.debug("构建完整文件路径: localSliceDir={}, physicPath={}, fullPath={}", 
                     localSliceDir, physicPath, fullPath);
            
            // 3. 验证文件是否存在
            java.io.File file = new java.io.File(fullPath);
            if (!file.exists()) {
                log.error("文件不存在: {}", fullPath);
                // 修改：文件不存在时直接抛出异常，不再使用默认文件
                throw new SlideException("切片文件不存在: " + fullPath);
            }
            
            log.info("成功获取文件路径: id={}, path={}", identifier, fullPath);
            return fullPath;
            
        } catch (SlideException se) {
            // 直接重新抛出SlideException
            throw se;
        } catch (Exception e) {
            log.error("查询数据库获取文件路径失败: id={}, error={}", identifier, e.getMessage(), e);
            throw new SlideException("查询数据库获取文件路径失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查找任何可用的切片文件（测试环境使用）
     * 已废弃：删除回退机制，避免删除后仍能访问默认文件
     */
    // 已删除 findAnySlideFile 方法

    /**
     * 递归收集目录中的所有文件
     * 已废弃：删除回退机制相关方法
     */
    // 已删除 collectFiles 方法

    /**
     * 获取与DZI视图一致的缩略图
     * 这个方法确保缩略图与DZI查看器中的图像方向一致
     *
     * @param identifier 切片标识符
     * @return 缩略图数据
     * @throws SlideException 如果无法获取缩略图
     */
    public byte[] getThumbnailForDzi(String identifier) throws SlideException {
        // 获取文件路径
        String filePath = getFilePathByIdentifier(identifier);
        String fileName = new java.io.File(filePath).getName().toLowerCase();
        boolean isTronFormat = fileName.endsWith(".tron");
        if (isTronFormat) {
            // Tron格式DZI缩略图：改为命令行调用 tron_image_extractor
            log.debug("Tron格式DZI缩略图请求，直接调用 tronSdkImageExtractor");
            byte[] thumbData = tronSdkImageExtractor.extractThumbnail(filePath);
            if (thumbData != null && thumbData.length > 0) {
                return thumbData;
            } else {
                log.warn("tronSdkImageExtractor未返回有效缩略图: {}", identifier);
                throw new SlideException("无法获取Tron格式缩略图数据");
            }
        }
        // 若未获取到，走原有本地生成逻辑
        try {
            // 获取切片信息（filePath 已在方法开头定义）
            Map<String, Object> info = unifiedSlideService.getSlideInfo(filePath);
            validateSlideInfo(info);
            int width = ((Number) info.get("width")).intValue();
            int height = ((Number) info.get("height")).intValue();
            int maxThumbSize = 600;
            double scale = Math.min((double) maxThumbSize / width, (double) maxThumbSize / height);
            int thumbWidth = (int) Math.floor(width * scale);
            int thumbHeight = (int) Math.floor(height * scale);
            int levelCount = 0;
            if (info.get("levelCount") != null) {
                levelCount = ((Number) info.get("levelCount")).intValue();
            }
            byte[] thumbnailJpeg;
            int useLevel = 0;
            if (width > 10000 || height > 10000) {
                log.info("图像尺寸过大({}x{})，使用分块处理获取缩略图", width, height);
                int bestLevel = findBestLevel(width, height, info);
                thumbnailJpeg = processLargeRegionInBlocks(filePath, 0, 0, width, height, thumbWidth, thumbHeight, bestLevel);
            } else {
                thumbnailJpeg = unifiedSlideService.getRegionJpeg(filePath, 0, 0, width, height, useLevel);
            }
            if (thumbnailJpeg == null || thumbnailJpeg.length == 0) {
                log.warn("无法获取缩略图数据，尝试使用标准缩略图方法");
                thumbnailJpeg = unifiedSlideService.getThumbnailJpeg(filePath);
                if (thumbnailJpeg == null || thumbnailJpeg.length == 0) {
                    throw new SlideException("无法获取缩略图数据");
                }
            }
            try {
                BufferedImage fullImg = ImageIO.read(new ByteArrayInputStream(thumbnailJpeg));
                if (fullImg == null) {
                    throw new SlideException("解码缩略图失败");
                }
                BufferedImage thumbnail = new BufferedImage(thumbWidth, thumbHeight, BufferedImage.TYPE_INT_RGB);
                Graphics2D g2d = thumbnail.createGraphics();
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
                g2d.drawImage(fullImg, 0, 0, thumbWidth, thumbHeight, 0, 0, fullImg.getWidth(), fullImg.getHeight(), null);
                g2d.dispose();
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpeg");
                if (writers.hasNext()) {
                    ImageWriter writer = writers.next();
                    ImageWriteParam param = writer.getDefaultWriteParam();
                    param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                    param.setCompressionQuality(JPEG_QUALITY);
                    if (param instanceof JPEGImageWriteParam) {
                        ((JPEGImageWriteParam) param).setOptimizeHuffmanTables(true);
                    }
                    ImageOutputStream ios = ImageIO.createImageOutputStream(out);
                    writer.setOutput(ios);
                    writer.write(null, new IIOImage(thumbnail, null, null), param);
                    writer.dispose();
                    ios.close();
                } else {
                    ImageIO.write(thumbnail, "JPEG", out);
                }
                return out.toByteArray();
            } catch (Exception e) {
                log.error("生成DZI缩略图失败: {}", e.getMessage(), e);
                throw new SlideException("生成DZI缩略图失败: " + e.getMessage(), e);
            }
        } catch (SlideException se) {
            throw se;
        } catch (Exception e) {
            log.error("获取DZI缩略图失败: {}", e.getMessage(), e);
            throw new SlideException("处理图像区域时内容不足，请查试较小的区域或较低的分辨率", e);
        }
    }
    
    /**
     * 根据图像尺寸查找最佳层级
     * @param width 图像宽度
     * @param height 图像高度
     * @param info 切片信息
     * @return 最佳层级
     */
    private int findBestLevel(int width, int height, Map<String, Object> info) {
        // 获取可用层级
        Object levelCountObj = info.get("levelCount");
        int levelCount = (levelCountObj instanceof Number) ? ((Number) levelCountObj).intValue() : 1;
        
        if (levelCount <= 1) {
            return 0; // 只有一个层级，直接返回0
        }
        
        // 根据图像尺寸选择合适的层级
        // 尺寸非常大时使用较高层级，避免内存问题
        int maxDim = Math.max(width, height);
        
        if (maxDim > 30000) return Math.min(3, levelCount - 1);
        if (maxDim > 15000) return Math.min(2, levelCount - 1);
        if (maxDim > 8000) return Math.min(1, levelCount - 1);
        return 0; // 默认使用最高分辨率
    }

    /**
     * 验证切片信息是否有效
     */
    private void validateSlideInfo(Map<String, Object> info) throws SlideException {
        if (info.get("width") == null || info.get("height") == null) {
            throw new SlideException("无法获取切片尺寸信息");
        }
    }
    
    /**
     * 构建DZI XML描述文件
     */
    private String buildDziXml(int width, int height) {
        // 检查宽高是否正常
        if (width <= 0 || height <= 0) {
            log.warn("构建DZI XML时检测到无效的宽高: {}x{}, 使用默认值", width, height);
            width = Math.max(1, width);
            height = Math.max(1, height);
        }
        
        // 确保宽高是2的幂的倍数，这对于某些查看器更友好
        // 特别是对于SDPC这样的特殊格式，确保尺寸值适合DZI查看器
        // 对于OpenSeadragon，它期望层级之间的尺寸是2的幂缩放关系
        int adjustedWidth = width;
        int adjustedHeight = height;
        
        // 对于超过65536的尺寸，考虑进行一些缩放以避免OpenSeadragon的性能问题
        if (width > 65536 || height > 65536) {
            log.info("检测到超大图像尺寸: {}x{}, 应用保护性缩放", width, height);
        }
        
        // 确保尺寸值是有效的
        adjustedWidth = Math.max(1, adjustedWidth);
        adjustedHeight = Math.max(1, adjustedHeight);
        
        // 确保长宽比保持不变
        double aspectRatio = (double)width / height;
        if (Math.abs(adjustedWidth - width) > 10 || Math.abs(adjustedHeight - height) > 10) {
            log.info("应用尺寸调整: 原始={}x{}, 调整后={}x{}, 长宽比={}", 
                    width, height, adjustedWidth, adjustedHeight, aspectRatio);
        }
        
        // 计算最大层级，确保与getDziDescriptor和getDziTile使用相同的计算方法
        int maxDim = Math.max(adjustedWidth, adjustedHeight);
        int maxLevel = 0;
        if (maxDim > 1) {
            maxLevel = (int) Math.ceil(Math.log(maxDim) / Math.log(2));
        }
        
        // 记录DZI XML构建信息
        log.debug("构建DZI XML: 原始尺寸={}x{}, 调整后尺寸={}x{}, 最大层级={}, 瓦片大小={}, 重叠={}", 
                 width, height, adjustedWidth, adjustedHeight, maxLevel, TILE_SIZE, TILE_OVERLAP);
        
        // 保持与OpenSeadragon兼容的XML格式，使用TILE_OVERLAP常量
        return String.format(
            "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
            "<Image TileSize=\"%d\" Overlap=\"%d\" Format=\"jpg\" xmlns=\"http://schemas.microsoft.com/deepzoom/2008\">\n" +
            "    <Size Width=\"%d\" Height=\"%d\"/>\n" +
            "</Image>", TILE_SIZE, TILE_OVERLAP, adjustedWidth, adjustedHeight);
    }

    /**
     * 在文件系统中搜索匹配标识符的文件
     */
    // 已删除 searchInFileSystem 方法

    @Override
    public byte[] getDziTileWithColor(String identifier, int level, int x, int y, ColorCorrectionParams colorParams) throws SlideException {
        log.debug("获取调色DZI瓦片: 标识符={}, 层级={}, 层级={}, 坐标=({}, {}), 颜色参数={}", 
                 identifier, level, x, y, colorParams != null ? "已设置" : "未设置");

        // 统一使用软件实现颜色校正，无论切片格式，不再区分硬件/软件实现
        // by 赵总需求
        if (colorParams == null || isDefaultColorParams(colorParams)) {
            log.debug("使用默认颜色参数，调用普通瓦片获取");
            return getDziTile(identifier, level, x, y);
        }
        return getDziTileWithSoftwareColorCorrection(identifier, level, x, y, colorParams);
    }

    /**
     * 使用软件方式实现颜色校正
     * 适用于不支持硬件颜色校正的解析器（如OpenSlide、TronSDK）
     */
    private byte[] getDziTileWithSoftwareColorCorrection(String identifier, int level, int x, int y, ColorCorrectionParams colorParams) throws SlideException {
        log.debug("使用软件颜色校正实现");
        
        // 优化：如果颜色参数为默认值，直接调用内部方法，避免不必要的颜色处理
        if (isDefaultColorParams(colorParams)) {
            log.debug("颜色参数为默认值，直接获取原始瓦片");
            return getDziTileInternal(identifier, level, x, y);
        }
        
        // 首先获取原始瓦片 - 避免循环调用，直接调用内部方法
        byte[] originalTile = getDziTileInternal(identifier, level, x, y);
        if (originalTile == null || originalTile.length == 0) {
            return originalTile;
        }
        
        try {
            // 将JPEG转换为BufferedImage
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(originalTile));
            if (image == null) {
                log.warn("无法解析瓦片图像数据");
                return originalTile;
            }
            
            // 应用颜色校正
            BufferedImage correctedImage = applyColorCorrection(image, colorParams);
            
            // 转换回JPEG
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(correctedImage, "JPEG", baos);
            byte[] result = baos.toByteArray();
            
            log.debug("软件颜色校正完成，原始大小: {}KB, 调色后大小: {}KB", 
                     originalTile.length / 1024, result.length / 1024);
            
            return result;
            
        } catch (Exception e) {
            log.error("软件颜色校正失败: {}", e.getMessage(), e);
            return originalTile; // 回退到原始瓦片
        }
    }

    /**
     * 应用颜色校正到BufferedImage
     */
    private BufferedImage applyColorCorrection(BufferedImage image, ColorCorrectionParams params) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        // 预计算色调调整的三角函数值（如果需要色调调整）
        float hueRadians = (float) Math.toRadians(params.getHue());
        float cosHue = (float) Math.cos(hueRadians);
        float sinHue = (float) Math.sin(hueRadians);
        boolean needHueAdjust = params.getHue() != 0.0f;
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int r = (rgb >> 16) & 0xFF;
                int g = (rgb >> 8) & 0xFF;
                int b = rgb & 0xFF;
                
                // 应用亮度调整
                r = clamp((int)(r + params.getBrightness() * 255), 0, 255);
                g = clamp((int)(g + params.getBrightness() * 255), 0, 255);
                b = clamp((int)(b + params.getBrightness() * 255), 0, 255);
                
                // 应用对比度调整
                r = clamp((int)((r - 128) * params.getContrast() + 128), 0, 255);
                g = clamp((int)((g - 128) * params.getContrast() + 128), 0, 255);
                b = clamp((int)((b - 128) * params.getContrast() + 128), 0, 255);
                
                // 应用伽马校正
                if (params.getGamma() != 1.0f) {
                    r = clamp((int)(255 * Math.pow(r / 255.0, 1.0 / params.getGamma())), 0, 255);
                    g = clamp((int)(255 * Math.pow(g / 255.0, 1.0 / params.getGamma())), 0, 255);
                    b = clamp((int)(255 * Math.pow(b / 255.0, 1.0 / params.getGamma())), 0, 255);
                }
                
                // 应用色调调整（在HSV色彩空间中进行）
                if (needHueAdjust) {
                    // 转换RGB到HSV
                    float rf = r / 255.0f;
                    float gf = g / 255.0f;
                    float bf = b / 255.0f;
                    
                    float max = Math.max(rf, Math.max(gf, bf));
                    float min = Math.min(rf, Math.min(gf, bf));
                    float delta = max - min;
                    
                    if (delta > 0) { // 只对有颜色的像素进行色调调整
                        float h = 0;
                        if (max == rf) {
                            h = ((gf - bf) / delta) % 6;
                        } else if (max == gf) {
                            h = (bf - rf) / delta + 2;
                        } else {
                            h = (rf - gf) / delta + 4;
                        }
                        h = h * 60; // 转换为度
                        
                        // 应用色调调整
                        h += params.getHue();
                        while (h < 0) h += 360;
                        while (h >= 360) h -= 360;
                        
                        // 转换HSV回RGB
                        float s = max > 0 ? delta / max : 0;
                        float v = max;
                        
                        float c = v * s;
                        float hsvX = c * (1 - Math.abs((h / 60) % 2 - 1));
                        float m = v - c;
                        
                        float r1 = 0, g1 = 0, b1 = 0;
                        int hSector = (int)(h / 60);
                        switch (hSector) {
                            case 0: r1 = c; g1 = hsvX; b1 = 0; break;
                            case 1: r1 = hsvX; g1 = c; b1 = 0; break;
                            case 2: r1 = 0; g1 = c; b1 = hsvX; break;
                            case 3: r1 = 0; g1 = hsvX; b1 = c; break;
                            case 4: r1 = hsvX; g1 = 0; b1 = c; break;
                            case 5: r1 = c; g1 = 0; b1 = hsvX; break;
                        }
                        
                        r = clamp((int)((r1 + m) * 255), 0, 255);
                        g = clamp((int)((g1 + m) * 255), 0, 255);
                        b = clamp((int)((b1 + m) * 255), 0, 255);
                    }
                }
                
                // 应用饱和度调整
                if (params.getSaturation() != 1.0f) {
                    int gray = (int)(0.299 * r + 0.587 * g + 0.114 * b);
                    r = clamp((int)(gray + (r - gray) * params.getSaturation()), 0, 255);
                    g = clamp((int)(gray + (g - gray) * params.getSaturation()), 0, 255);
                    b = clamp((int)(gray + (b - gray) * params.getSaturation()), 0, 255);
                }
                
                // 应用RGB通道增益调整
                if (params.getRedGain() != 1.0f || params.getGreenGain() != 1.0f || params.getBlueGain() != 1.0f) {
                    r = clamp((int)(r * params.getRedGain()), 0, 255);
                    g = clamp((int)(g * params.getGreenGain()), 0, 255);
                    b = clamp((int)(b * params.getBlueGain()), 0, 255);
                }
                
                // 应用颜色反转（如果需要）
                if (params.isInvertColors()) {
                    r = 255 - r;
                    g = 255 - g;
                    b = 255 - b;
                }
                
                // 重新组合RGB
                int newRgb = (r << 16) | (g << 8) | b;
                result.setRGB(x, y, newRgb);
            }
        }
        
        // 应用锐化滤波（如果需要）
        if (params.getSharpen() > 0) {
            result = applySharpenFilter(result, params.getSharpen());
        }
        
        return result;
    }

    /**
     * 应用锐化滤波到BufferedImage
     * 
     * @param image 原图像
     * @param level 锐化级别 (1=轻度, 2=强度)
     * @return 锐化后的图像
     */
    private BufferedImage applySharpenFilter(BufferedImage image, int level) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        // 定义不同级别的锐化内核
        float[][] kernel;
        switch (level) {
            case 1: // 轻度锐化
                kernel = new float[][] {
                    {0, -0.5f, 0},
                    {-0.5f, 3, -0.5f},
                    {0, -0.5f, 0}
                };
                break;
            case 2: // 强度锐化
                kernel = new float[][] {
                    {-1, -1, -1},
                    {-1, 9, -1},
                    {-1, -1, -1}
                };
                break;
            default:
                return image; // 无锐化
        }
        
        // 应用卷积滤波
        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                float r = 0, g = 0, b = 0;
                
                // 应用3x3内核
                for (int ky = -1; ky <= 1; ky++) {
                    for (int kx = -1; kx <= 1; kx++) {
                        int rgb = image.getRGB(x + kx, y + ky);
                        float weight = kernel[ky + 1][kx + 1];
                        
                        r += ((rgb >> 16) & 0xFF) * weight;
                        g += ((rgb >> 8) & 0xFF) * weight;
                        b += (rgb & 0xFF) * weight;
                    }
                }
                
                // 限制像素值范围并设置结果
                int newR = clamp((int) r, 0, 255);
                int newG = clamp((int) g, 0, 255);
                int newB = clamp((int) b, 0, 255);
                
                int newRgb = (newR << 16) | (newG << 8) | newB;
                result.setRGB(x, y, newRgb);
            }
        }
        
        // 复制边缘像素（不进行锐化处理）
        for (int x = 0; x < width; x++) {
            result.setRGB(x, 0, image.getRGB(x, 0)); // 顶边
            result.setRGB(x, height - 1, image.getRGB(x, height - 1)); // 底边
        }
        for (int y = 0; y < height; y++) {
            result.setRGB(0, y, image.getRGB(0, y)); // 左边
            result.setRGB(width - 1, y, image.getRGB(width - 1, y)); // 右边
        }
        
        return result;
    }

    /**
     * 检查是否为默认颜色参数
     */
    private boolean isDefaultColorParams(ColorCorrectionParams params) {
        return params.getBrightness() == 0.0f &&
               params.getContrast() == 1.0f &&
               params.getGamma() == 1.0f &&
               params.getSaturation() == 1.0f &&
               params.getHue() == 0.0f &&
               params.getRedGain() == 1.0f &&
               params.getGreenGain() == 1.0f &&
               params.getBlueGain() == 1.0f &&
               params.getSharpen() == 0 &&
               params.getColorStyle() == 0 &&
               !params.isInvertColors();
    }

    /**
     * 限制值在指定范围内
     */
    private int clamp(int value, int min, int max) {
        return Math.max(min, Math.min(max, value));
    }

    /**
     * 从Map中安全获取整数值
     * 
     * @param map 源Map
     * @param key 键名
     * @param defaultValue 默认值
     * @return 整数值
     */
    private int getIntValue(Map<String, Object> map, String key, int defaultValue) {
        Object value = map.get(key);
        if (value == null) {
            return defaultValue;
        }
        
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法解析整数值: key={}, value={}", key, value);
                return defaultValue;
            }
        }
        
        return defaultValue;
    }

    /**
     * 创建透明瓦片（针对SDPC格式的边界处理）
     */
    private byte[] createTransparentTile(int tileSize) {
        try {
            // 针对SDPC格式，创建完全透明的瓦片
            BufferedImage transparentTile = new BufferedImage(tileSize, tileSize, BufferedImage.TYPE_INT_ARGB);
            java.awt.Graphics2D g = transparentTile.createGraphics();
            // 设置完全透明背景
            g.setComposite(AlphaComposite.Clear);
            g.fillRect(0, 0, tileSize, tileSize);
            g.dispose();
            
            // 转换为JPEG（会自动填充白色背景）
            BufferedImage jpegTile = new BufferedImage(tileSize, tileSize, BufferedImage.TYPE_INT_RGB);
            java.awt.Graphics2D g2 = jpegTile.createGraphics();
            g2.setColor(Color.WHITE);  // 使用白色背景
            g2.fillRect(0, 0, tileSize, tileSize);
            g2.dispose();
            
            return optimizeJpegQuality(jpegTile);
        } catch (Exception e) {
            log.error("创建透明瓦片失败: {}", e.getMessage());
            return createErrorTileForFormat(tileSize, ".sdpc");
        }
    }

    /**
     * 根据标识符和合作伙伴代码获取文件路径
     * 
     * @param identifier 切片标识符（主键id）
     * @param partnerCode 对接产品代码
     * @return 处理后的文件路径
     * @throws SlideException 如果无法找到有效的文件
     */
    public String getFilePathByIdentifierAndPartnerCode(String identifier, String partnerCode) throws SlideException {
        if (identifier == null || identifier.trim().isEmpty()) {
            throw new SlideException("标识符不能为空");
        }
        if (partnerCode == null || partnerCode.trim().isEmpty()) {
            throw new SlideException("cname 参数不能为空");
        }
        
        identifier = identifier.trim();
        partnerCode = partnerCode.trim();
        
        log.debug("开始通过数据库联合查询获取文件路径: id={}, partnerCode={}", identifier, partnerCode);
        
        try {
            // 使用 identifier 和 partnerCode 联合查询
            SliceEntity sliceEntity = sliceService.getByIdentifierAndPartnerCode(identifier, partnerCode);
            if (sliceEntity == null) {
                log.error("找不到切片文件: identifier={}, partnerCode={}", identifier, partnerCode);
                throw new SlideException("找不到切片文件");
            }
            
            String physicPath = sliceEntity.getPhysicPath();
            if (physicPath == null || physicPath.trim().isEmpty()) {
                log.error("切片记录中physic_path为空: id={}, partnerCode={}", identifier, partnerCode);
                throw new SlideException("找不到切片文件");
            }
            
            // 拼接完整文件路径
            String fullPath = filePathService.processFilePath(physicPath);
            log.debug("构建完整文件路径: physicPath={}, fullPath={}", physicPath, fullPath);
            
            // 验证文件是否存在
            java.io.File file = new java.io.File(fullPath);
            if (!file.exists()) {
                log.error("文件不存在: {}", fullPath);
                throw new SlideException("找不到切片文件");
            }
            
            log.info("成功获取文件路径: id={}, partnerCode={}, path={}", identifier, partnerCode, fullPath);
            return fullPath;
            
        } catch (Exception e) {
            log.error("查询数据库获取文件路径失败: id={}, partnerCode={}, error={}", identifier, partnerCode, e.getMessage(), e);
            throw new SlideException("获取文件路径失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Map<String, Object> getSlideInfoFromPath(String filePath) throws SlideException {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new SlideException("文件路径不能为空");
        }
        
        try {
            log.debug("根据文件路径获取切片信息: filePath={}", filePath);
            
            // 验证文件是否存在
            java.io.File file = new java.io.File(filePath);
            if (!file.exists() || !file.isFile()) {
                throw new SlideException("文件不存在或不是有效文件: " + filePath);
            }
            
            // 使用UnifiedSlideService获取切片信息
            Map<String, Object> slideInfo = unifiedSlideService.getSlideInfo(filePath);
            if (slideInfo == null || slideInfo.isEmpty()) {
                throw new SlideException("无法获取切片信息");
            }
            
            log.debug("成功获取切片信息: filePath={}, 尺寸={}x{}", 
                    filePath, slideInfo.get("width"), slideInfo.get("height"));
            
            return slideInfo;
            
        } catch (SlideException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取切片信息失败: filePath={}, error={}", filePath, e.getMessage(), e);
            throw new SlideException("获取切片信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public byte[] getRegionJpegFromPath(String filePath, int x, int y, int width, int height, int level) throws SlideException {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new SlideException("文件路径不能为空");
        }
        
        if (width <= 0 || height <= 0) {
            throw new SlideException("区域宽度和高度必须大于0");
        }
        
        try {
            log.debug("根据文件路径获取区域图像: filePath={}, 坐标=({}, {}), 尺寸={}x{}, 层级={}", 
                    filePath, x, y, width, height, level);
            
            // 验证文件是否存在
            java.io.File file = new java.io.File(filePath);
            if (!file.exists() || !file.isFile()) {
                throw new SlideException("文件不存在或不是有效文件: " + filePath);
            }
            
            // 使用UnifiedSlideService获取区域图像
            byte[] regionData = unifiedSlideService.getRegionJpeg(filePath, x, y, width, height, level);
            if (regionData == null || regionData.length == 0) {
                throw new SlideException("无法获取区域图像数据");
            }
            
            log.debug("成功获取区域图像: filePath={}, 数据大小={}字节", filePath, regionData.length);
            
            return regionData;
            
        } catch (SlideException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取区域图像失败: filePath={}, 坐标=({}, {}), 尺寸={}x{}, 层级={}, error={}", 
                    filePath, x, y, width, height, level, e.getMessage(), e);
            throw new SlideException("获取区域图像失败: " + e.getMessage(), e);
        }
    }
}
