package cn.ccaa.slice.service.async;

import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

/**
 * 异步任务服务接口
 * 提供统一的异步任务处理
 *
 * <AUTHOR>
 */
public interface AsyncTaskService {
    
    /**
     * 提交异步任务
     *
     * @param taskId 任务ID
     * @param task 任务
     * @param <T> 返回值类型
     * @return 异步任务的Future
     */
    <T> CompletableFuture<T> submitTask(String taskId, Supplier<T> task);
    
    /**
     * 提交异步任务，带优先级
     *
     * @param taskId 任务ID
     * @param task 任务
     * @param priority 优先级，值越小优先级越高
     * @param <T> 返回值类型
     * @return 异步任务的Future
     */
    <T> CompletableFuture<T> submitTask(String taskId, Supplier<T> task, int priority);
    
    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @return 如果成功取消返回true，否则返回false
     */
    boolean cancelTask(String taskId);
    
    /**
     * 检查任务是否正在执行
     *
     * @param taskId 任务ID
     * @return 如果正在执行返回true，否则返回false
     */
    boolean isTaskRunning(String taskId);
    
    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    TaskStatus getTaskStatus(String taskId);
    
    /**
     * 任务状态枚举
     */
    enum TaskStatus {
        /**
         * 未知
         */
        UNKNOWN,
        
        /**
         * 等待中
         */
        WAITING,
        
        /**
         * 执行中
         */
        RUNNING,
        
        /**
         * 已完成
         */
        COMPLETED,
        
        /**
         * 已取消
         */
        CANCELLED,
        
        /**
         * 执行失败
         */
        FAILED
    }
}
