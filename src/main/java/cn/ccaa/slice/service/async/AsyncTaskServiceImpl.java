package cn.ccaa.slice.service.async;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * 异步任务服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AsyncTaskServiceImpl implements AsyncTaskService {
    
    /**
     * 任务执行器
     */
    private final Executor taskExecutor;
    
    /**
     * 上传执行器
     */
    private final Executor uploadExecutor;
    
    /**
     * 任务Future映射
     */
    private final Map<String, CompletableFuture<?>> taskFutures = new ConcurrentHashMap<>();
    
    /**
     * 任务取消标志映射
     */
    private final Map<String, AtomicBoolean> cancelFlags = new ConcurrentHashMap<>();
    
    /**
     * 任务状态映射
     */
    private final Map<String, TaskStatus> taskStatuses = new ConcurrentHashMap<>();
    
    /**
     * 构造函数
     *
     * @param taskExecutor 任务执行器
     * @param uploadExecutor 上传执行器
     */
    public AsyncTaskServiceImpl(
            @Qualifier("taskExecutor") Executor taskExecutor,
            @Qualifier("uploadExecutor") Executor uploadExecutor) {
        this.taskExecutor = taskExecutor;
        this.uploadExecutor = uploadExecutor;
    }
    
    @Override
    public <T> CompletableFuture<T> submitTask(String taskId, Supplier<T> task) {
        return submitTask(taskId, task, 0);
    }
    
    @Override
    public <T> CompletableFuture<T> submitTask(String taskId, Supplier<T> task, int priority) {
        log.info("提交异步任务: taskId={}, priority={}", taskId, priority);
        
        // 创建取消标志
        AtomicBoolean cancelFlag = new AtomicBoolean(false);
        cancelFlags.put(taskId, cancelFlag);
        
        // 更新任务状态为等待中
        taskStatuses.put(taskId, TaskStatus.WAITING);
        
        // 创建异步任务
        CompletableFuture<T> future = CompletableFuture.supplyAsync(() -> {
            // 如果任务已取消，直接返回null
            if (cancelFlag.get()) {
                log.info("任务已取消，跳过执行: taskId={}", taskId);
                taskStatuses.put(taskId, TaskStatus.CANCELLED);
                return null;
            }
            
            // 更新任务状态为执行中
            taskStatuses.put(taskId, TaskStatus.RUNNING);
            
            try {
                log.info("开始执行异步任务: taskId={}", taskId);
                T result = task.get();
                
                // 更新任务状态为已完成
                taskStatuses.put(taskId, TaskStatus.COMPLETED);
                
                log.info("异步任务执行完成: taskId={}", taskId);
                return result;
            } catch (Exception e) {
                // 更新任务状态为执行失败
                taskStatuses.put(taskId, TaskStatus.FAILED);
                
                log.error("异步任务执行失败: taskId={}, 错误: {}", taskId, e.getMessage(), e);
                throw e;
            }
        }, priority > 5 ? uploadExecutor : taskExecutor);
        
        // 保存Future
        taskFutures.put(taskId, future);
        
        // 添加完成回调，清理资源
        future.whenComplete((result, ex) -> {
            if (ex != null) {
                log.error("异步任务异常: taskId={}, 错误: {}", taskId, ex.getMessage(), ex);
                taskStatuses.put(taskId, TaskStatus.FAILED);
            }
            
            // 延迟清理资源，保留一段时间用于查询状态
            scheduleCleanup(taskId);
        });
        
        return future;
    }
    
    @Override
    public boolean cancelTask(String taskId) {
        log.info("取消异步任务: taskId={}", taskId);
        
        // 设置取消标志
        AtomicBoolean cancelFlag = cancelFlags.get(taskId);
        if (cancelFlag != null) {
            cancelFlag.set(true);
        }
        
        // 取消Future
        CompletableFuture<?> future = taskFutures.get(taskId);
        if (future != null && !future.isDone()) {
            boolean cancelled = future.cancel(true);
            if (cancelled) {
                taskStatuses.put(taskId, TaskStatus.CANCELLED);
            }
            return cancelled;
        }
        
        return false;
    }
    
    @Override
    public boolean isTaskRunning(String taskId) {
        CompletableFuture<?> future = taskFutures.get(taskId);
        if (future == null) {
            return false;
        }
        
        return !future.isDone() && !future.isCancelled();
    }
    
    @Override
    public TaskStatus getTaskStatus(String taskId) {
        return taskStatuses.getOrDefault(taskId, TaskStatus.UNKNOWN);
    }
    
    /**
     * 定时清理已完成的任务
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000)
    public void cleanupCompletedTasks() {
        log.info("开始清理已完成的异步任务...");
        
        int cleanedCount = 0;
        for (Map.Entry<String, CompletableFuture<?>> entry : taskFutures.entrySet()) {
            String taskId = entry.getKey();
            CompletableFuture<?> future = entry.getValue();
            
            if (future.isDone() || future.isCancelled()) {
                taskFutures.remove(taskId);
                cancelFlags.remove(taskId);
                taskStatuses.remove(taskId);
                cleanedCount++;
            }
        }
        
        log.info("异步任务清理完成，共清理 {} 个任务", cleanedCount);
    }
    
    /**
     * 安排延迟清理任务
     *
     * @param taskId 任务ID
     */
    private void scheduleCleanup(String taskId) {
        // 创建延迟清理任务，30分钟后执行
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(30 * 60 * 1000);
                
                // 清理资源
                taskFutures.remove(taskId);
                cancelFlags.remove(taskId);
                taskStatuses.remove(taskId);
                
                log.debug("已清理异步任务资源: taskId={}", taskId);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, taskExecutor);
    }
}
