package cn.ccaa.slice.service.storage;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import cn.ccaa.slice.core.exception.StorageException;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * 本地存储服务实现
 * 提供基于本地文件系统的存储服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "storage.type", havingValue = "local")
public class LocalStorageService implements StorageService {
    
    /**
     * 存储根目录
     */
    @Value("${storage.local.root-dir:./storage}")
    private String rootDir;
    
    /**
     * 文件URL前缀
     */
    private static final String FILE_URL_PREFIX = "file://";
    
    /**
     * 存储桶根目录
     */
    private Path bucketRoot;
    
    /**
     * 构造函数
     */
    public LocalStorageService() {
        // 移除构造函数中的初始化，使用@PostConstruct注解的方法进行初始化
    }
    
    /**
     * 初始化
     */
    @PostConstruct
    public void init() throws IOException {
        // 创建存储根目录
        bucketRoot = Paths.get(rootDir).toAbsolutePath().normalize();
        Files.createDirectories(bucketRoot);
        log.info("本地存储根目录: {}", bucketRoot);
    }
    
    @Override
    public String uploadFile(String objectName, InputStream inputStream, String contentType, long size)
            throws StorageException {
        return uploadFile(objectName, inputStream, contentType, size, null);
    }
    
    @Override
    public String uploadFile(String objectName, InputStream inputStream, String contentType, long size,
                           BiConsumer<Long, Long> progressCallback) throws StorageException {
        // 验证输入参数
        Objects.requireNonNull(inputStream, "输入流不能为空");
        
        // 如果对象名为空则生成随机名称
        String actualObjectName = Optional.ofNullable(objectName)
                .filter(name -> !name.trim().isEmpty())
                .orElseGet(() -> UUID.randomUUID().toString());
        
        return executeWithException(() -> {
            try {
                // 创建文件目录
                Path targetPath = resolveFilePath(actualObjectName);
                Files.createDirectories(targetPath.getParent());
                
                // 如果存在进度回调，包装输入流
                InputStream sourceStream = (progressCallback != null && size > 0) 
                        ? new ProgressInputStream(inputStream, size, progressCallback)
                        : inputStream;
                
                // 复制文件内容
                Files.copy(sourceStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
                
                // 返回本地路径
                return targetPath.toString();
            } catch (IOException e) {
                throw new StorageException("上传文件IO异常: " + e.getMessage(), e);
            }
        }, "文件上传失败", actualObjectName);
    }
    
    @Override
    public byte[] downloadFile(String objectName) throws StorageException {
        Objects.requireNonNull(objectName, "对象名称不能为空");
        
        return executeWithException(() -> {
            try {
                Path filePath = resolveFilePath(objectName);
                
                if (!Files.exists(filePath)) {
                    throw new StorageException("文件不存在: " + objectName);
                }
                
                byte[] data = Files.readAllBytes(filePath);
                return data;
            } catch (IOException e) {
                throw new StorageException("下载文件IO异常: " + e.getMessage(), e);
            }
        }, "文件下载失败", objectName);
    }
    
    @Override
    public InputStream getFileInputStream(String objectName) throws StorageException {
        Objects.requireNonNull(objectName, "对象名称不能为空");
        
        return executeWithException(() -> {
            try {
                Path filePath = resolveFilePath(objectName);
                
                if (!Files.exists(filePath)) {
                    throw new StorageException("文件不存在: " + objectName);
                }
                
                return Files.newInputStream(filePath);
            } catch (IOException e) {
                throw new StorageException("获取文件输入流IO异常: " + e.getMessage(), e);
            }
        }, "获取文件输入流失败", objectName);
    }
    
    @Override
    public boolean fileExists(String objectName) {
        if (objectName == null) {
            return false;
        }
        
        try {
            Path filePath = resolveFilePath(objectName);
            return Files.exists(filePath);
        } catch (Exception e) {
            log.error("检查文件是否存在失败: {}, 错误: {}", objectName, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public void deleteFile(String objectName) throws StorageException {
        Objects.requireNonNull(objectName, "对象名称不能为空");
        
        executeWithException(() -> {
            try {
                Path filePath = resolveFilePath(objectName);
                
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                }
                
                return null;
            } catch (IOException e) {
                throw new StorageException("删除文件IO异常: " + e.getMessage(), e);
            }
        }, "文件删除失败", objectName);
    }
    
    @Override
    public String getFileUrl(String objectName, int expirySeconds) throws StorageException {
        Objects.requireNonNull(objectName, "对象名称不能为空");
        
        return executeWithException(() -> {
            Path filePath = resolveFilePath(objectName);
            if (!Files.exists(filePath)) {
                throw new StorageException("文件不存在: " + objectName);
            }
            
            // 本地存储不支持URL生成，返回文件路径
            return FILE_URL_PREFIX + filePath;
        }, "获取文件URL失败", objectName);
    }
    
    @Override
    public List<String> listFiles(String prefix) throws StorageException {
        return executeWithException(() -> {
            try {
                // 处理前缀为空的情况
                String effectivePrefix = Optional.ofNullable(prefix).orElse("");
                
                Path prefixPath = bucketRoot;
                if (!effectivePrefix.isEmpty()) {
                    prefixPath = resolveFilePath(effectivePrefix);
                    if (!Files.exists(prefixPath)) {
                        return new ArrayList<>();
                    }
                }
                
                final String finalPrefix = effectivePrefix;
                List<String> fileNames = Files.walk(prefixPath)
                        .filter(Files::isRegularFile)
                        .map(path -> bucketRoot.relativize(path).toString())
                        .filter(name -> name.startsWith(finalPrefix))
                        .collect(Collectors.toList());
                
                return fileNames;
            } catch (IOException e) {
                throw new StorageException("列出文件IO异常: " + e.getMessage(), e);
            }
        }, "列出文件失败", prefix);
    }
    
    @Override
    public void createBucket(String bucketName) throws StorageException {
        Objects.requireNonNull(bucketName, "存储桶名称不能为空");
        log.debug("创建存储桶: {}", bucketName);
        
        executeWithException(() -> {
            try {
                // 本地存储中"存储桶"就是一个子目录
                Path bucketPath = resolveFilePath(bucketName);
                
                if (!Files.exists(bucketPath)) {
                    Files.createDirectories(bucketPath);
                    log.debug("存储桶创建成功: {}", bucketName);
                } else {
                    log.debug("存储桶已存在: {}", bucketName);
                }
                
                return null;
            } catch (IOException e) {
                throw new StorageException("创建存储桶IO异常: " + e.getMessage(), e);
            }
        }, "创建存储桶失败", bucketName);
    }
    
    @Override
    public boolean bucketExists(String bucketName) {
        if (bucketName == null) {
            return false;
        }
        
        try {
            Path bucketPath = resolveFilePath(bucketName);
            return Files.exists(bucketPath) && Files.isDirectory(bucketPath);
        } catch (Exception e) {
            log.error("检查存储桶是否存在失败: {}, 错误: {}", bucketName, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public String getStorageType() {
        return "local";
    }
    
    /**
     * 解析文件路径
     * 
     * @param relativePath 相对路径
     * @return 完整路径
     */
    private Path resolveFilePath(@NonNull String relativePath) {
        return bucketRoot.resolve(relativePath);
    }
    
    /**
     * 执行操作并统一处理异常
     * 
     * @param <T> 返回类型
     * @param supplier 操作
     * @param errorMessage 错误消息
     * @param objectPath 对象路径
     * @return 操作结果
     * @throws StorageException 存储异常
     */
    private <T> T executeWithException(Supplier<T> supplier, String errorMessage, @Nullable String objectPath) 
            throws StorageException {
        try {
            return supplier.get();
        } catch (StorageException e) {
            throw e; // 直接抛出已存在的StorageException
        } catch (Exception e) {
            log.error("{}: {}, 错误: {}", errorMessage, objectPath, e.getMessage(), e);
            throw new StorageException(errorMessage + ": " + e.getMessage(), e);
        }
    }
    
    /**
     * 进度监控输入流
     */
    private static class ProgressInputStream extends InputStream {
        /**
         * 原始输入流
         */
        private final InputStream in;
        
        /**
         * 总大小
         */
        private final long totalSize;
        
        /**
         * 当前已读取大小
         */
        private long bytesRead;
        
        /**
         * 进度回调
         */
        private final BiConsumer<Long, Long> progressCallback;
        
        /**
         * 上次回调时间
         */
        private long lastCallbackTime;
        
        /**
         * 最小回调间隔(毫秒)
         */
        private static final long MIN_CALLBACK_INTERVAL = 100; // 100ms
        
        /**
         * 构造函数
         * 
         * @param in 原始输入流
         * @param totalSize 总大小
         * @param progressCallback 进度回调
         */
        public ProgressInputStream(InputStream in, long totalSize, BiConsumer<Long, Long> progressCallback) {
            this.in = Objects.requireNonNull(in, "输入流不能为空");
            this.totalSize = totalSize;
            this.progressCallback = Objects.requireNonNull(progressCallback, "进度回调不能为空");
            this.bytesRead = 0;
            this.lastCallbackTime = System.currentTimeMillis();
        }
        
        @Override
        public int read() throws IOException {
            int b = in.read();
            if (b != -1) {
                bytesRead++;
                updateProgress();
            }
            return b;
        }
        
        @Override
        public int read(byte[] b, int off, int len) throws IOException {
            int bytesCount = in.read(b, off, len);
            if (bytesCount != -1) {
                bytesRead += bytesCount;
                updateProgress();
            }
            return bytesCount;
        }
        
        /**
         * 更新进度
         */
        private void updateProgress() {
            long now = System.currentTimeMillis();
            if (now - lastCallbackTime >= MIN_CALLBACK_INTERVAL) {
                progressCallback.accept(bytesRead, totalSize);
                lastCallbackTime = now;
            }
        }
        
        @Override
        public void close() throws IOException {
            // 确保最后回调一次100%进度
            if (bytesRead > 0) {
                progressCallback.accept(bytesRead, totalSize);
            }
            in.close();
        }
        
        @Override
        public int available() throws IOException {
            return in.available();
        }
        
        @Override
        public synchronized void mark(int readlimit) {
            in.mark(readlimit);
        }
        
        @Override
        public boolean markSupported() {
            return in.markSupported();
        }
        
        @Override
        public synchronized void reset() throws IOException {
            in.reset();
        }
        
        @Override
        public long skip(long n) throws IOException {
            long skipped = in.skip(n);
            bytesRead += skipped;
            updateProgress();
            return skipped;
        }
    }
} 
