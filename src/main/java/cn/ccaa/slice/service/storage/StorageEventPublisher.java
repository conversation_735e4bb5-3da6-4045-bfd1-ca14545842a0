package cn.ccaa.slice.service.storage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Component;

import cn.ccaa.slice.core.event.FileDeletedEvent;
import cn.ccaa.slice.core.event.FileUploadedEvent;

/**
 * 存储事件发布器
 * 用于发布存储相关事件
 * 
 * <AUTHOR>
 */
@Component
public class StorageEventPublisher implements ApplicationEventPublisherAware {
    
    private static final Logger log = LoggerFactory.getLogger(StorageEventPublisher.class);
    
    private ApplicationEventPublisher publisher;
    
    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher publisher) {
        this.publisher = publisher;
    }
    
    /**
     * 发布文件上传完成事件
     * 
     * @param objectPath 对象路径
     * @param uploaderId 上传者ID
     */
    public void publishFileUploadedEvent(String objectPath, String uploaderId) {
        if (publisher != null) {
            log.debug("发布文件上传完成事件: {}", objectPath);
            FileUploadedEvent event = new FileUploadedEvent(this, objectPath, uploaderId);
            publisher.publishEvent(event);
        }
    }
    
    /**
     * 发布文件删除事件
     * 
     * @param objectPath 对象路径
     * @param operatorId 操作者ID
     */
    public void publishFileDeletedEvent(String objectPath, String operatorId) {
        if (publisher != null) {
            log.debug("发布文件删除事件: {}", objectPath);
            FileDeletedEvent event = new FileDeletedEvent(this, objectPath, operatorId);
            publisher.publishEvent(event);
        }
    }
} 
