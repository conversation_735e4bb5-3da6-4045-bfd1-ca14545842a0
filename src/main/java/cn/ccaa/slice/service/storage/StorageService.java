package cn.ccaa.slice.service.storage;

import java.io.InputStream;
import java.util.List;
import java.util.function.BiConsumer;

import cn.ccaa.slice.core.exception.StorageException;

/**
 * 存储服务接口
 * 定义存储相关操作
 * <AUTHOR>
 */
public interface StorageService {
    
    /**
     * 上传文件
     * 
     * @param objectName 对象名称
     * @param inputStream 输入流
     * @param contentType 内容类型
     * @param size 文件大小
     * @return 对象的访问URL
     * @throws StorageException 如果上传失败
     */
    String uploadFile(String objectName, InputStream inputStream, String contentType, long size) 
            throws StorageException;
    
    /**
     * 上传文件（带进度回调）
     * 
     * @param objectName 对象名称
     * @param inputStream 输入流
     * @param contentType 内容类型
     * @param size 文件大小
     * @param progressCallback 进度回调函数
     * @return 对象的访问URL
     * @throws StorageException 如果上传失败
     */
    String uploadFile(String objectName, InputStream inputStream, String contentType, long size, 
                     BiConsumer<Long, Long> progressCallback) throws StorageException;
    
    /**
     * 下载文件
     * 
     * @param objectName 对象名称
     * @return 文件数据
     * @throws StorageException 如果下载失败
     */
    byte[] downloadFile(String objectName) throws StorageException;
    
    /**
     * 获取文件输入流
     * 
     * @param objectName 对象名称
     * @return 文件输入流
     * @throws StorageException 如果获取失败
     */
    InputStream getFileInputStream(String objectName) throws StorageException;
    
    /**
     * 检查文件是否存在
     * 
     * @param objectName 对象名称
     * @return 如果文件存在返回true，否则返回false
     */
    boolean fileExists(String objectName);
    
    /**
     * 删除文件
     * 
     * @param objectName 对象名称
     * @throws StorageException 如果删除失败
     */
    void deleteFile(String objectName) throws StorageException;
    
    /**
     * 获取文件URL
     * 
     * @param objectName 对象名称
     * @param expirySeconds URL有效期（秒）
     * @return 文件URL
     * @throws StorageException 如果获取失败
     */
    String getFileUrl(String objectName, int expirySeconds) throws StorageException;
    
    /**
     * 列出指定前缀的所有文件
     * 
     * @param prefix 前缀
     * @return 文件名列表
     * @throws StorageException 如果列出失败
     */
    List<String> listFiles(String prefix) throws StorageException;
    
    /**
     * 创建存储桶
     * 
     * @param bucketName 存储桶名称
     * @throws StorageException 如果创建失败
     */
    void createBucket(String bucketName) throws StorageException;
    
    /**
     * 检查存储桶是否存在
     * 
     * @param bucketName 存储桶名称
     * @return 如果存在返回true，否则返回false
     */
    boolean bucketExists(String bucketName);
    
    /**
     * 获取存储服务类型
     * 
     * @return 存储服务类型
     */
    String getStorageType();
} 
