package cn.ccaa.slice.service;

import cn.ccaa.slice.core.exception.SlideException;
import cn.ccaa.slice.core.parser.SlideParser;
import cn.ccaa.slice.core.parser.SlideParserFactory;
import cn.ccaa.slice.parsers.common.SlideImageWrapper;
import cn.ccaa.slice.parsers.openslide.OpenSlideParserAdapter;
import cn.ccaa.slice.parsers.sqrayslide.SqraySlideParserAdapter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;

/**
 * 统一切片服务
 * 提供对不同类型切片文件的统一访问接口
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UnifiedSlideService {

    /**
     * 解析器工厂
     */
    private final SlideParserFactory parserFactory;

    /**
     * 文件权限配置
     */
    private static final String FILE_PERMISSIONS = "rw-rw----";

    /**
     * 获取所有支持的解析器
     *
     * @return 解析器列表
     */
    public List<SlideParser> getAllParsers() {
        return parserFactory.getAllParsers();
    }

    /**
     * 检测文件格式，返回支持该格式的解析器名称
     *
     * @param filePath 文件路径
     * @return 支持该格式的解析器名称，如果没有找到支持的解析器则返回null
     */
    public String detectFormat(@NonNull String filePath) {
        try {
            // 直接借助解析器工厂根据文件路径选择解析器，然后返回其名称
            SlideParser parser = parserFactory.getParser(filePath);
            if (parser != null) {
                return parser.getParserName();
            }
        } catch (Exception e) {
            log.warn("detectFormat 解析文件格式失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 打开切片文件
     *
     * @param filePath 文件路径
     * @return 包装后的切片对象
     * @throws SlideException 如果无法打开文件
     */
    public SlideImageWrapper openSlide(@NonNull String filePath) {
        log.debug("尝试打开切片文件: {}", filePath);

        return Optional.of(new File(filePath))
                .map(this::findSupportingParser)
                .orElse(null);
    }

    /**
     * 查找支持该文件的解析器
     *
     * @param file 文件对象
     * @return 支持该文件的解析器名称
     */
    private SlideImageWrapper findSupportingParser(File file) {
        try {
            // 使用SlideParserFactory统一的解析器选择逻辑
            log.debug("为文件 {} 查找支持的解析器", file.getAbsolutePath());
            SlideParser parser = parserFactory.getParser(file.getAbsolutePath());
            
            if (parser == null) {
                log.error("未找到支持的解析器: {}", file.getAbsolutePath());
                throw new SlideException("未找到支持的解析器: " + file.getAbsolutePath());
            }
            
            log.debug("使用解析器 {} 打开文件: {}", parser.getParserName(), file.getAbsolutePath());
            Object slide = null;
            
            try {
                slide = parser.openSlide(file.getAbsolutePath());
            } catch (SlideException se) {
                log.error("使用解析器 {} 打开文件失败: {}, 错误: {}", 
                        parser.getParserName(), file.getAbsolutePath(), se.getMessage());
                throw se;
            } catch (Exception e) {
                log.error("使用解析器 {} 打开文件时发生异常: {}, 错误: {}", 
                        parser.getParserName(), file.getAbsolutePath(), e.getMessage());
                throw new SlideException("打开切片文件失败: " + file.getAbsolutePath(), e);
            }

            if (slide == null) {
                log.error("解析器 {} 返回了空对象: {}", parser.getParserName(), file.getAbsolutePath());
                throw new SlideException("打开切片文件失败，解析器返回了空对象: " + file.getAbsolutePath());
            }

            // 获取切片信息
            Map<String, Object> slideInfo = null;
            try {
                slideInfo = parser.getSlideInfo(slide);
            } catch (Exception e) {
                log.error("获取切片信息失败: {}, 错误: {}", file.getAbsolutePath(), e.getMessage(), e);
                
                // 尝试关闭切片以避免资源泄露
                try {
                    parser.closeSlide(slide);
                } catch (Exception closeEx) {
                    log.warn("关闭切片时发生异常: {}", closeEx.getMessage());
                }
                
                throw new SlideException("获取切片信息失败: " + file.getAbsolutePath(), e);
            }
            
            if (slideInfo == null) {
                log.error("获取切片信息返回null: {}", file.getAbsolutePath());
                
                // 尝试关闭切片以避免资源泄露
                try {
                    parser.closeSlide(slide);
                } catch (Exception closeEx) {
                    log.warn("关闭切片时发生异常: {}", closeEx.getMessage());
                }
                
                throw new SlideException("获取切片信息返回null: " + file.getAbsolutePath());
            }
            
            log.debug("成功获取切片信息: {}, 解析器: {}", file.getAbsolutePath(), parser.getParserName());

            return new SlideImageWrapper(slide, parser.getParserName(), slideInfo);
        } catch (SlideException se) {
            throw se;
        } catch (Exception e) {
            log.error("打开切片文件失败: {}, 错误: {}", file.getAbsolutePath(), e.getMessage(), e);
            throw new SlideException("打开切片文件失败: " + file.getAbsolutePath(), e);
        }
    }

    /**
     * 关闭切片
     *
     * @param filePath 文件路径
     */
    public void closeSlide(@NonNull String filePath) {
        // Implementation needed
    }

    /**
     * 获取切片基本信息
     *
     * @param filePath 文件路径
     * @return 切片信息
     */
    public Map<String, Object> getSlideInfo(@NonNull String filePath) {
        SlideImageWrapper wrapper = openSlide(filePath);
        return wrapper.getSlideInfo();
    }

    /**
     * 获取切片层级信息
     *
     * @param filePath 文件路径
     * @param level 层级索引
     * @return 层级信息
     */
    public Map<String, Object> getLevelInfo(@NonNull String filePath, int level) {
        SlideImageWrapper wrapper = openSlide(filePath);
        SlideParser parser = parserFactory.getParserByName(wrapper.getParserName());
        return parser.getLevelInfo(wrapper.getSlide(), level);
    }

    /**
     * 获取指定位置和层级的图块的JPEG数据
     *
     * @param filePath 文件路径
     * @param x 图块X坐标
     * @param y 图块Y坐标
     * @param level 层级索引
     * @return JPEG格式的图块数据
     */
    public byte[] getTileJpeg(@NonNull String filePath, int x, int y, int level) {
        return null; // Implementation needed
    }

    /**
     * 获取切片的缩略图
     *
     * @param filePath 文件路径
     * @return JPEG格式的缩略图数据
     */
    public byte[] getThumbnailJpeg(@NonNull String filePath) {
        try {
            SlideParser parser = findParser(filePath);
            if (parser == null) {
                log.error("获取缩略图失败：找不到支持的解析器，文件：{}", filePath);
                return createEmptyJpeg();
            }

            Object slide = null;
            try {
                slide = parser.openSlide(filePath);
                if (slide == null) {
                    log.error("获取缩略图失败：无法打开文件，文件：{}", filePath);
                    return createEmptyJpeg();
                }
                
                byte[] thumbnailData = parser.getThumbnailJpeg(slide);
                if (thumbnailData == null || thumbnailData.length == 0) {
                    log.error("获取缩略图失败：解析器返回空数据，文件：{}", filePath);
                    return createEmptyJpeg();
                }
                
                return thumbnailData;
            } catch (Exception e) {
                log.error("获取缩略图时发生异常：{}, 文件：{}", e.getMessage(), filePath, e);
                return createEmptyJpeg();
            } finally {
                if (slide != null) {
                    try {
                        parser.closeSlide(slide);
                    } catch (Exception e) {
                        log.warn("关闭切片对象时发生异常：{}", e.getMessage(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取缩略图处理过程中发生异常：{}, 文件：{}", e.getMessage(), filePath, e);
            return createEmptyJpeg();
        }
    }

    /**
     * 获取切片标签图
     *
     * @param filePath 文件路径
     * @return JPEG格式的标签图数据
     */
    public byte[] getLabelJpeg(@NonNull String filePath) {
        try {
            SlideParser parser = findParser(filePath);
            if (parser == null) {
                log.error("获取标签图失败：找不到支持的解析器，文件：{}", filePath);
                return createEmptyJpeg();
            }

            Object slide = null;
            try {
                slide = parser.openSlide(filePath);
                if (slide == null) {
                    log.error("获取标签图失败：无法打开文件，文件：{}", filePath);
                    return createEmptyJpeg();
                }
                
                byte[] labelData = parser.getLabelJpeg(slide);
                if (labelData == null || labelData.length == 0) {
                    log.error("获取标签图失败：解析器返回空数据，文件：{}", filePath);
                    return createEmptyJpeg();
                }
                
                return labelData;
            } catch (Exception e) {
                log.error("获取标签图时发生异常：{}, 文件：{}", e.getMessage(), filePath, e);
                return createEmptyJpeg();
            } finally {
                if (slide != null) {
                    try {
                        parser.closeSlide(slide);
                    } catch (Exception e) {
                        log.warn("关闭切片对象时发生异常：{}", e.getMessage(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取标签图处理过程中发生异常：{}, 文件：{}", e.getMessage(), filePath, e);
            return createEmptyJpeg();
        }
    }

    /**
     * 获取区域图像的JPEG数据
     *
     * @param filePath 文件路径
     * @param x 区域左上角X坐标
     * @param y 区域左上角Y坐标
     * @param width 区域宽度
     * @param height 区域高度
     * @param level 层级索引
     * @return JPEG格式的区域图像数据
     */
    public byte[] getRegionJpeg(@NonNull String filePath, int x, int y, int width, int height, int level) {
        try {
            // 只在DEBUG级别记录详细信息，减少日志输出
            if (log.isDebugEnabled()) {
                log.debug("获取区域图像: 文件={}, 坐标=({}, {}), 尺寸={}x{}, 层级={}", 
                         filePath, x, y, width, height, level);
            }
            
            SlideParser parser = findParser(filePath);
            if (parser == null) {
                log.error("获取区域图像失败：找不到支持的解析器，文件：{}", filePath);
                return createEmptyJpeg();
            }

            Object slide = null;
            try {
                slide = parser.openSlide(filePath);
                if (slide == null) {
                    log.error("获取区域图像失败：无法打开文件，文件：{}", filePath);
                    return createEmptyJpeg();
                }
                
                // 获取区域图像数据
                byte[] regionData = parser.getRegionJpeg(slide, x, y, width, height, level);
                if (regionData == null || regionData.length == 0) {
                    if (log.isDebugEnabled()) {
                        log.debug("获取区域图像失败：解析器返回空数据，文件：{}, 坐标=({}, {}), 尺寸={}x{}, 层级={}",
                                 filePath, x, y, width, height, level);
                    }
                    return createEmptyJpeg();
                }
                
                return regionData;
            } catch (Exception e) {
                log.error("获取区域图像时发生异常：{}, 文件：{}, 坐标=({}, {}), 尺寸={}x{}, 层级={}", 
                         e.getMessage(), filePath, x, y, width, height, level, e);
                return createEmptyJpeg();
            } finally {
                if (slide != null) {
                    try {
                        parser.closeSlide(slide);
                    } catch (Exception e) {
                        log.warn("关闭切片对象时发生异常：{}", e.getMessage(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取区域图像处理过程中发生异常：{}, 文件：{}", e.getMessage(), filePath, e);
            return createEmptyJpeg();
        }
    }

    /**
     * 获取切片元数据
     *
     * @param filePath 文件路径
     * @return 切片元数据
     */
    public Map<String, Object> getMetadata(@NonNull String filePath) {
        return null; // Implementation needed
    }

    /**
     * 释放所有缓存的切片
     */
    public void closeAll() {
        // Implementation needed
    }

    /**
     * 获取切片ID
     *
     * @param filePath 文件路径
     * @return 切片ID
     */
    private String getSlideId(String filePath) {
        return filePath == null ? "" : new File(filePath).getName();
    }

    private SlideParser findParser(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            log.error("查找解析器: 文件路径为空");
            return getAllParsers().get(0); // 返回默认解析器
        }
        
        try {
            // 使用SlideParserFactory的getParser方法，确保使用正确的解析器选择逻辑
            log.debug("使用SlideParserFactory查找解析器: {}", filePath);
            SlideParser parser = parserFactory.getParser(filePath);
            log.info("成功为文件 {} 选择解析器: {}", filePath, parser.getParserName());
            return parser;
        } catch (Exception e) {
            log.error("SlideParserFactory查找解析器失败: {}, 错误: {}", filePath, e.getMessage());
            log.warn("回退到默认解析器（OpenSlide）");
            // 回退到默认解析器（通常是OpenSlide）
            return getAllParsers().get(0);
        }
    }

    /**
     * 创建一个空的JPEG图像
     * 
     * @return 空的JPEG图像数据
     */
    private byte[] createEmptyJpeg() {
        try {
            // 创建一个1x1像素的黑色图像
            BufferedImage emptyImage = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(emptyImage, "jpg", baos);
            return baos.toByteArray();
        } catch (Exception e) {
            log.error("创建空JPEG图像失败：{}", e.getMessage(), e);
            // 返回一个最小的有效JPEG字节数组
            return new byte[] {
                (byte)0xFF, (byte)0xD8, // JPEG开始标记
                (byte)0xFF, (byte)0xD9  // JPEG结束标记
            };
        }
    }

    /**
     * 获取切片玻片图 (整体宏观图)
     *
     * @param filePath 文件路径
     * @return JPEG格式的玻片图数据
     */
    public byte[] getSlideJpeg(@NonNull String filePath) {
        try {
            SlideParser parser = findParser(filePath);
            if (parser == null) {
                log.error("获取玻片图失败：找不到支持的解析器，文件：{}", filePath);
                return createEmptyJpeg();
            }

            Object slide = null;
            try {
                slide = parser.openSlide(filePath);
                if (slide == null) {
                    log.error("获取玻片图失败：无法打开文件，文件：{}", filePath);
                    return createEmptyJpeg();
                }

                // 调用解析器的getSlideJpeg；若无数据直接返回空白图（不再使用label/thumbnail回退）
                byte[] slideData = parser.getSlideJpeg(slide);
                if (slideData == null || slideData.length == 0) {
                    log.warn("解析器未返回宏观图，使用空白图代替，文件：{}", filePath);
                    return createEmptyJpeg();
                }
                return slideData;
            } catch (Exception e) {
                log.error("获取玻片图时发生异常：{}, 文件：{}", e.getMessage(), filePath, e);
                return createEmptyJpeg();
            } finally {
                if (slide != null) {
                    try {
                        parser.closeSlide(slide);
                    } catch (Exception e) {
                        log.warn("关闭切片对象时发生异常：{}", e.getMessage(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取玻片图处理过程中发生异常：{}, 文件：{}", e.getMessage(), filePath, e);
            return createEmptyJpeg();
        }
    }
}
