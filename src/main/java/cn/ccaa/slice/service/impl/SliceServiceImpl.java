package cn.ccaa.slice.service.impl;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.Base64;

import javax.imageio.ImageIO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.ccaa.slice.core.exception.BusinessException;
import cn.ccaa.slice.core.mapper.SliceMapper;
import cn.ccaa.slice.core.model.SliceEntity;
import cn.ccaa.slice.service.FilePathService;
import cn.ccaa.slice.service.SliceService;
import cn.ccaa.slice.service.UnifiedSlideService;
import cn.ccaa.slice.web.dto.SliceCaptureRequestDTO;
import cn.ccaa.slice.web.dto.SliceCaptureResponseDTO;
import cn.ccaa.slice.web.dto.AICaptureBatchRequestDTO;
import cn.ccaa.slice.web.dto.AICaptureBatchResponseDTO;
import cn.ccaa.slice.web.dto.AICaptureItemResponseDTO;

/**
 * 数字切片服务实现
 *
 * <AUTHOR>
 */
@Service
public class SliceServiceImpl extends ServiceImpl<SliceMapper, SliceEntity> implements SliceService {

    private static final Logger logger = LoggerFactory.getLogger(SliceServiceImpl.class);

    @Autowired
    private UnifiedSlideService unifiedSlideService;

    @Autowired
    private FilePathService filePathService;

    @Override
    public SliceEntity createSlice(SliceEntity sliceEntity) {
        save(sliceEntity);
        return sliceEntity;
    }
    
    @Override
    public SliceEntity getSliceById(String sliceId) {
        return getById(sliceId);
    }
    
    @Override
    public boolean updateSliceStatus(String sliceId, String status) {
        logger.info("更新切片状态: sliceId={}, status={}", sliceId, status);
        try {
            // 按 slice_id 查找
            SliceEntity entity = getBySliceId(sliceId);
            if (entity == null) {
                logger.warn("切片不存在，无法更新状态: sliceId={}", sliceId);
                return false;
            }
            // 更新状态字段和最后修改时间
            entity.setQpState(status);
            entity.setLastModifiedTime(LocalDateTime.now());
            return updateById(entity);
        } catch (Exception e) {
            logger.error("更新切片状态失败: sliceId={}, status={}, error={}", 
                       sliceId, status, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public SliceEntity getBySliceId(String sliceId) {
        return lambdaQuery().eq(SliceEntity::getSliceId, sliceId).one();
    }

    @Override
    public List<SliceEntity> getByTaskId(String taskId) {
        return lambdaQuery().eq(SliceEntity::getTaskId, taskId).list();
    }

    @Override
    public SliceEntity getByIdentifierAndPartnerCode(String identifier, String partnerCode) {
        return lambdaQuery()
                .eq(SliceEntity::getId, identifier)
                .eq(SliceEntity::getPartnerCode, partnerCode)
                .one();
    }

    @Override
    public SliceCaptureResponseDTO captureSlice(SliceCaptureRequestDTO request) {
        logger.info("开始处理切片截图请求: fileId={}, 功能选项={}", request.getFileId(), request.getFunctionOption());
        
        try {
            // 1. 根据fileId获取切片实体（fileId是主键ID）
            SliceEntity sliceEntity = getById(request.getFileId());
            if (sliceEntity == null) {
                throw new BusinessException("切片文件不存在: " + request.getFileId());
            }
            
            // 2. 获取切片文件路径
            String filePath = filePathService.processFilePath(sliceEntity.getPhysicPath());
            if (filePath == null || filePath.isEmpty()) {
                throw new BusinessException("切片文件路径无效: " + request.getFileId());
            }
            
            logger.info("切片文件路径: {}", filePath);
            
            // 3. 计算截图区域参数
            // 将double类型的坐标和尺寸转换为int类型
            int x = request.getX().intValue();
            int y = request.getY().intValue();
            int width = request.getW().intValue();
            int height = request.getH().intValue();
            int level = request.getLevel();
            
            logger.info("截图参数: x={}, y={}, width={}, height={}, level={}", x, y, width, height, level);
            
            // 4. 调用UnifiedSlideService获取区域图像数据
            byte[] originalImageData = unifiedSlideService.getRegionJpeg(filePath, x, y, width, height, level);
            if (originalImageData == null || originalImageData.length == 0) {
                throw new BusinessException("无法获取指定区域的图像数据");
            }
            
            // 5. 生成缩略图（将原图缩放为200x200像素，保持宽高比）
            logger.info("开始生成缩略图，原图大小: {} bytes", originalImageData.length);
            byte[] thumbnailImageData = null;
            try {
                thumbnailImageData = generateThumbnail(originalImageData, 200, 200);
                logger.info("缩略图生成完成，大小: {} bytes", thumbnailImageData != null ? thumbnailImageData.length : 0);
            } catch (Exception e) {
                logger.error("缩略图生成失败: {}", e.getMessage(), e);
                // 生成一个空的缩略图作为降级方案
                thumbnailImageData = new byte[0];
            }
            
            // 6. 构建响应DTO
            return SliceCaptureResponseDTO.builder()
                    .fileId(request.getFileId())
                    .zoom(request.getZoom())
                    .csize(request.getCsize())
                    .level(request.getLevel())
                    .content(request.getContent())
                    .w(request.getW())
                    .h(request.getH())
                    .x(request.getX())
                    .y(request.getY())
                    .rotate(request.getRotate())
                    .cutImageOri(originalImageData)
                    .cutImage(thumbnailImageData)
                    .functionOption(request.getFunctionOption())
                    .caseId(request.getCaseId())
                    .coords(request.getCoords().stream()
                            .map(coord -> SliceCaptureResponseDTO.Coordinate.builder()
                                    .x(coord.getX())
                                    .y(coord.getY())
                                    .build())
                            .collect(Collectors.toList()))
                    .build();
                    
        } catch (BusinessException e) {
            logger.error("切片截图业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("切片截图处理失败: fileId={}, error={}", request.getFileId(), e.getMessage(), e);
            throw new BusinessException("切片截图处理失败: " + e.getMessage());
        }
    }

    /**
     * 生成缩略图
     * 
     * @param originalImageData 原始图像数据
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @return 缩略图数据
     */
    private byte[] generateThumbnail(byte[] originalImageData, int maxWidth, int maxHeight) {
        try {
            logger.info("generateThumbnail: 输入数据大小={} bytes, 目标尺寸={}x{}", originalImageData.length, maxWidth, maxHeight);
            
            // 验证输入数据
            if (originalImageData == null || originalImageData.length == 0) {
                logger.error("generateThumbnail: 输入图像数据为空");
                throw new RuntimeException("输入图像数据为空");
            }
            
            // 检查数据是否是有效的JPEG格式
            if (originalImageData.length < 4) {
                logger.error("generateThumbnail: 图像数据太小，可能不是有效的图片格式");
                throw new RuntimeException("图像数据太小");
            }
            
            // 检查JPEG文件头
            boolean isJpeg = (originalImageData[0] & 0xFF) == 0xFF && (originalImageData[1] & 0xFF) == 0xD8;
            logger.info("generateThumbnail: 输入数据JPEG格式检查: {}, 前4字节: [{}, {}, {}, {}]", 
                    isJpeg, 
                    originalImageData[0] & 0xFF, 
                    originalImageData[1] & 0xFF, 
                    originalImageData[2] & 0xFF, 
                    originalImageData[3] & 0xFF);
            
            // 读取原始图像
            ByteArrayInputStream inputStream = new ByteArrayInputStream(originalImageData);
            BufferedImage originalImage = ImageIO.read(inputStream);
            
            if (originalImage == null) {
                logger.error("generateThumbnail: 无法解析原始图像数据，可能数据已损坏");
                throw new RuntimeException("无法读取原始图像数据");
            }
            
            // 智能计算缩放比例，参考C#算法思路
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();
            logger.info("generateThumbnail: 原图尺寸={}x{}", originalWidth, originalHeight);
            
            // 智能缩放算法：优先保证主要维度不超限
            int thumbnailWidth, thumbnailHeight;
            double scaleRatio;
            
            if (originalWidth > originalHeight) {
                // 宽图：以宽度为主导
                if (originalWidth > maxWidth) {
                    scaleRatio = (double) maxWidth / originalWidth;
                    thumbnailWidth = maxWidth;
                    thumbnailHeight = (int) (originalHeight * scaleRatio);
                    
                    // 如果高度仍然超限，再次调整
                    if (thumbnailHeight > maxHeight) {
                        scaleRatio = (double) maxHeight / originalHeight;
                        thumbnailHeight = maxHeight;
                        thumbnailWidth = (int) (originalWidth * scaleRatio);
                    }
                } else {
                    // 原图宽度已经在范围内，检查高度
                    scaleRatio = Math.min((double) maxWidth / originalWidth, (double) maxHeight / originalHeight);
                    thumbnailWidth = (int) (originalWidth * scaleRatio);
                    thumbnailHeight = (int) (originalHeight * scaleRatio);
                }
            } else {
                // 高图：以高度为主导
                if (originalHeight > maxHeight) {
                    scaleRatio = (double) maxHeight / originalHeight;
                    thumbnailHeight = maxHeight;
                    thumbnailWidth = (int) (originalWidth * scaleRatio);
                    
                    // 如果宽度仍然超限，再次调整
                    if (thumbnailWidth > maxWidth) {
                        scaleRatio = (double) maxWidth / originalWidth;
                        thumbnailWidth = maxWidth;
                        thumbnailHeight = (int) (originalHeight * scaleRatio);
                    }
                } else {
                    // 原图高度已经在范围内，检查宽度
                    scaleRatio = Math.min((double) maxWidth / originalWidth, (double) maxHeight / originalHeight);
                    thumbnailWidth = (int) (originalWidth * scaleRatio);
                    thumbnailHeight = (int) (originalHeight * scaleRatio);
                }
            }
            
            // 确保最小尺寸
            thumbnailWidth = Math.max(1, thumbnailWidth);
            thumbnailHeight = Math.max(1, thumbnailHeight);
            
            logger.info("generateThumbnail: 缩放比例={}, 缩略图尺寸={}x{}", scaleRatio, thumbnailWidth, thumbnailHeight);
            
            // 创建缩略图 - 使用最高质量设置
            BufferedImage thumbnailImage = new BufferedImage(thumbnailWidth, thumbnailHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = thumbnailImage.createGraphics();
            
            // 设置白色背景，避免透明区域显示为黑色
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, thumbnailWidth, thumbnailHeight);
            
            // 最高质量缩放设置 - 参考C#的高质量渲染
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
            
            // 使用平滑缩放绘制图像
            g2d.drawImage(originalImage, 0, 0, thumbnailWidth, thumbnailHeight, null);
            g2d.dispose();
            
            logger.debug("generateThumbnail: 图像缩放完成");
            
            // 转换为高质量JPEG字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            // 使用高质量JPEG编码器
            javax.imageio.ImageWriter jpegWriter = ImageIO.getImageWritersByFormatName("JPEG").next();
            javax.imageio.ImageWriteParam jpegParams = jpegWriter.getDefaultWriteParam();
            jpegParams.setCompressionMode(javax.imageio.ImageWriteParam.MODE_EXPLICIT);
            jpegParams.setCompressionQuality(0.95f); // 高质量压缩
            
            javax.imageio.stream.ImageOutputStream imageOutputStream = ImageIO.createImageOutputStream(outputStream);
            jpegWriter.setOutput(imageOutputStream);
            jpegWriter.write(null, new javax.imageio.IIOImage(thumbnailImage, null, null), jpegParams);
            jpegWriter.dispose();
            imageOutputStream.close();
            
            logger.debug("generateThumbnail: 高质量JPEG编码完成");
            
            byte[] result = outputStream.toByteArray();
            logger.info("generateThumbnail: 缩略图生成成功，大小={} bytes", result.length);
            
            return result;
            
        } catch (Exception e) {
            logger.error("生成缩略图失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成缩略图失败: " + e.getMessage());
        }
    }

    @Override
    public AICaptureBatchResponseDTO captureAIBatch(List<AICaptureBatchRequestDTO> requests) {
        logger.info("开始处理AI批量截图请求，批次数量: {}", requests.size());
        
        List<AICaptureItemResponseDTO> resultList = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        
        // 逐个处理截图请求
        for (AICaptureBatchRequestDTO request : requests) {
            AICaptureItemResponseDTO item = processAICapture(request);
            resultList.add(item);
            
            if (item.getImage() != null && !item.getImage().isEmpty()) {
                successCount++;
            } else {
                failureCount++;
            }
        }
        
        // 构建响应
        String status = (failureCount == 0) ? "success" : "partial_success";
        String msg = String.format("批量截图完成，成功: %d, 失败: %d", successCount, failureCount);
        
        logger.info("AI批量截图处理完成: {}", msg);
        
        return AICaptureBatchResponseDTO.builder()
                .status(status)
                .data(resultList)
                .msg(msg)
                .build();
    }

    /**
     * 处理单个AI截图请求
     * 
     * @param request 单个截图请求
     * @return 截图结果
     */
    private AICaptureItemResponseDTO processAICapture(AICaptureBatchRequestDTO request) {
        String cutimgid = request.getCutimgid();
        
        try {
            logger.debug("处理AI截图: cutimgid={}, fileId={}, 坐标=({}, {}), 尺寸={}x{}", 
                        cutimgid, request.getFileId(), request.getX(), request.getY(), 
                        request.getW(), request.getH());
            
            // 1. 根据fileId获取切片实体
            SliceEntity sliceEntity = getById(request.getFileId());
            if (sliceEntity == null) {
                return AICaptureItemResponseDTO.builder()
                        .cutimgid(cutimgid)
                        .image("")
                        .msg("切片文件不存在: " + request.getFileId())
                        .build();
            }
            
            // 2. 获取切片文件路径
            String filePath = filePathService.processFilePath(sliceEntity.getPhysicPath());
            if (filePath == null || filePath.isEmpty()) {
                return AICaptureItemResponseDTO.builder()
                        .cutimgid(cutimgid)
                        .image("")
                        .msg("切片文件路径无效: " + request.getFileId())
                        .build();
            }
            
            // 3. 直接使用传入的坐标进行截图（不进行坐标转换）
            int x = request.getX();
            int y = request.getY();
            int width = request.getW();
            int height = request.getH();
            
            logger.debug("AI截图使用原始坐标: 坐标=({}, {}), 尺寸={}x{}", x, y, width, height);
            
            // 4. 针对NDPI格式进行特殊处理，应用颜色增强
            byte[] imageData = getEnhancedRegionForNdpi(filePath, x, y, width, height);
            
            if (imageData == null || imageData.length == 0) {
                return AICaptureItemResponseDTO.builder()
                        .cutimgid(cutimgid)
                        .image("")
                        .msg("无法获取指定区域的图像数据")
                        .build();
            }
            
            // 5. 转换为Base64编码（不进行放大处理）
            String base64Image = Base64.getEncoder().encodeToString(imageData);
            
            logger.debug("AI截图成功: cutimgid={}, 图像大小={} bytes, Base64长度={}", 
                        cutimgid, imageData.length, base64Image.length());
            
            return AICaptureItemResponseDTO.builder()
                    .cutimgid(cutimgid)
                    .image(base64Image)
                    .msg("成功")
                    .build();
            
        } catch (Exception e) {
            logger.error("AI截图失败: cutimgid={}, error={}", cutimgid, e.getMessage(), e);
            return AICaptureItemResponseDTO.builder()
                    .cutimgid(cutimgid)
                    .image("")
                    .msg("截图失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 获取针对NDPI格式优化的区域图像
     * 对NDPI格式应用颜色增强处理，提升截图效果
     * 
     * @param filePath 文件路径
     * @param x 左上角X坐标
     * @param y 左上角Y坐标
     * @param width 宽度
     * @param height 高度
     * @return 增强处理后的JPEG图像数据
     */
    private byte[] getEnhancedRegionForNdpi(String filePath, int x, int y, int width, int height) {
        try {
            // 获取原始区域图像数据
            byte[] originalImageData = unifiedSlideService.getRegionJpeg(filePath, x, y, width, height, 0);
            
            if (originalImageData == null || originalImageData.length == 0) {
                logger.warn("无法获取原始区域图像数据");
                return originalImageData;
            }
            
            // 检查是否为NDPI文件
            boolean isNdpiFile = filePath.toLowerCase().endsWith(".ndpi");
            if (!isNdpiFile) {
                // 非NDPI文件，直接返回原始数据
                logger.debug("非NDPI文件，返回原始图像数据");
                return originalImageData;
            }
            
            logger.info("检测到NDPI文件，开始应用伽马值1.8f的颜色增强处理");
            
            // 解码原始JPEG图像
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(originalImageData));
            if (originalImage == null) {
                logger.warn("无法解码原始图像数据，返回原始数据");
                return originalImageData;
            }
            
            // 应用简化的伽马校正处理
            BufferedImage enhancedImage = applyGammaCorrection(originalImage, 1.8f);
            
            // 转换为高质量JPEG
            byte[] enhancedImageData = convertToHighQualityJpeg(enhancedImage);
            
            logger.info("NDPI伽马校正完成，原始大小: {} bytes, 增强后大小: {} bytes", 
                       originalImageData.length, enhancedImageData.length);
            
            return enhancedImageData;
            
        } catch (Exception e) {
            logger.error("NDPI颜色增强处理失败，回退到原始数据: {}", e.getMessage(), e);
            // 失败时回退到原始方法
            return unifiedSlideService.getRegionJpeg(filePath, x, y, width, height, 0);
        }
    }

    /**
     * 应用简化的伽马校正处理
     * 只进行伽马校正，不做其他颜色调整
     * 
     * @param originalImage 原始图像
     * @param gamma 伽马值
     * @return 校正后的图像
     */
    private BufferedImage applyGammaCorrection(BufferedImage originalImage, float gamma) {
        int width = originalImage.getWidth();
        int height = originalImage.getHeight();
        BufferedImage enhancedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        logger.debug("开始应用伽马校正，伽马值={}, 图像尺寸: {}x{}", gamma, width, height);
        
        // 预计算伽马查找表以提高性能
        int[] gammaLUT = new int[256];
        for (int i = 0; i < 256; i++) {
            gammaLUT[i] = clamp((int)(255 * Math.pow(i / 255.0, 1.0 / gamma)), 0, 255);
        }
        
        // 应用伽马校正
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = originalImage.getRGB(x, y);
                int r = (rgb >> 16) & 0xFF;
                int g = (rgb >> 8) & 0xFF;
                int b = rgb & 0xFF;
                
                // 使用查找表应用伽马校正
                r = gammaLUT[r];
                g = gammaLUT[g];
                b = gammaLUT[b];
                
                // 组合最终颜色
                int finalRgb = (r << 16) | (g << 8) | b;
                enhancedImage.setRGB(x, y, finalRgb);
            }
        }
        
        logger.debug("伽马校正处理完成");
        return enhancedImage;
    }

    /**
     * 转换为高质量JPEG格式
     * 使用最高质量设置，确保颜色保真度
     * 
     * @param image 待转换的图像
     * @return JPEG图像数据
     */
    private byte[] convertToHighQualityJpeg(BufferedImage image) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        // 获取JPEG写入器
        javax.imageio.ImageWriter jpegWriter = ImageIO.getImageWritersByFormatName("JPEG").next();
        javax.imageio.ImageWriteParam jpegParams = jpegWriter.getDefaultWriteParam();
        
        // 设置高质量参数
        jpegParams.setCompressionMode(javax.imageio.ImageWriteParam.MODE_EXPLICIT);
        jpegParams.setCompressionQuality(0.98f); // 98%质量，确保颜色保真度
        
        // 优化编码设置
        if (jpegParams instanceof javax.imageio.plugins.jpeg.JPEGImageWriteParam) {
            javax.imageio.plugins.jpeg.JPEGImageWriteParam jpegSpecificParams = 
                (javax.imageio.plugins.jpeg.JPEGImageWriteParam) jpegParams;
            jpegSpecificParams.setOptimizeHuffmanTables(true);
        }
        
        // 确保使用RGB颜色空间
        BufferedImage rgbImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = rgbImage.createGraphics();
        
        // 设置高质量渲染提示
        g2d.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();
        
        // 写入高质量JPEG
        javax.imageio.stream.ImageOutputStream imageOutputStream = ImageIO.createImageOutputStream(outputStream);
        jpegWriter.setOutput(imageOutputStream);
        jpegWriter.write(null, new javax.imageio.IIOImage(rgbImage, null, null), jpegParams);
        jpegWriter.dispose();
        imageOutputStream.close();
        
        return outputStream.toByteArray();
    }



    /**
     * 数值范围限制工具方法
     * 
     * @param value 待限制的值
     * @param min 最小值
     * @param max 最大值
     * @return 限制后的值
     */
    private int clamp(int value, int min, int max) {
        return Math.max(min, Math.min(max, value));
    }
} 
