package cn.ccaa.slice.service.impl;

import cn.ccaa.slice.core.mapper.PartnerPlatformMapper;
import cn.ccaa.slice.core.model.PartnerPlatformEntity;
import cn.ccaa.slice.service.PartnerPlatformService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PartnerPlatformServiceImpl implements PartnerPlatformService {
    @Autowired
    private PartnerPlatformMapper partnerPlatformMapper;

    @Override
    public PartnerPlatformEntity getByPartnerCode(String partnerCode) {
        return partnerPlatformMapper.selectOne(
            new LambdaQueryWrapper<PartnerPlatformEntity>()
                .eq(PartnerPlatformEntity::getPartnerCode, partnerCode)
        );
    }
} 