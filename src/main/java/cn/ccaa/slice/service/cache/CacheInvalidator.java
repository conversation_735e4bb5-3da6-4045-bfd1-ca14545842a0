package cn.ccaa.slice.service.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import cn.ccaa.slice.core.event.FileDeletedEvent;
import cn.ccaa.slice.core.event.FileUploadedEvent;

/**
 * 缓存失效监听器
 * 监听文件变更事件，自动失效相关缓存
 * 
 * <AUTHOR>
 */
@Component
public class CacheInvalidator {
    
    private static final Logger log = LoggerFactory.getLogger(CacheInvalidator.class);
    
    /**
     * 生成切片ID，与UnifiedSlideService保持一致
     * 
     * @param filePath 文件路径
     * @return 切片ID
     */
    private String getSlideId(String filePath) {
        return String.valueOf(filePath.hashCode());
    }
    
    /**
     * 监听文件上传事件
     * 
     * @param event 文件上传事件
     */
    @EventListener
    public void onFileUploaded(FileUploadedEvent event) {
        String filePath = event.getFilePath();
        String slideId = getSlideId(filePath);
        
        log.info("文件上传事件触发缓存失效: {}", filePath);
        
        // 失效格式缓存
        // cacheService.invalidateFormat(filePath);
        
        // 失效切片相关所有缓存
        // cacheService.invalidateSlide(slideId);
    }
    
    /**
     * 监听文件删除事件
     * 
     * @param event 文件删除事件
     */
    @EventListener
    public void onFileDeleted(FileDeletedEvent event) {
        String filePath = event.getFilePath();
        String slideId = getSlideId(filePath);
        
        log.info("文件删除事件触发缓存失效: {}", filePath);
        
        // 失效格式缓存
        // cacheService.invalidateFormat(filePath);
        
        // 失效切片相关所有缓存
        // cacheService.invalidateSlide(slideId);
    }
} 
