package cn.ccaa.slice.service.cache;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 缓存服务接口
 * 提供统一的缓存操作
 *
 * <AUTHOR>
 */
public interface CacheService {
    
    /**
     * 设置缓存
     *
     * @param key 缓存键
     * @param value 缓存值
     */
    void set(String key, Object value);
    
    /**
     * 设置缓存，带过期时间
     *
     * @param key 缓存键
     * @param value 缓存值
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void set(String key, Object value, long timeout, TimeUnit unit);
    
    /**
     * 获取缓存
     *
     * @param key 缓存键
     * @param clazz 返回值类型
     * @param <T> 返回值泛型
     * @return 缓存值，如果不存在则返回null
     */
    <T> T get(String key, Class<T> clazz);
    
    /**
     * 删除缓存
     *
     * @param key 缓存键
     * @return 如果删除成功返回true，否则返回false
     */
    boolean delete(String key);
    
    /**
     * 批量删除缓存
     *
     * @param keys 缓存键列表
     * @return 删除的键数量
     */
    long deleteMulti(List<String> keys);
    
    /**
     * 设置哈希缓存
     *
     * @param key 缓存键
     * @param field 字段
     * @param value 值
     */
    void hSet(String key, String field, Object value);
    
    /**
     * 批量设置哈希缓存
     *
     * @param key 缓存键
     * @param map 字段值映射
     */
    void hSetAll(String key, Map<String, Object> map);
    
    /**
     * 获取哈希缓存
     *
     * @param key 缓存键
     * @param field 字段
     * @param clazz 返回值类型
     * @param <T> 返回值泛型
     * @return 缓存值，如果不存在则返回null
     */
    <T> T hGet(String key, String field, Class<T> clazz);
    
    /**
     * 获取哈希缓存的所有字段和值
     *
     * @param key 缓存键
     * @return 字段值映射
     */
    Map<String, Object> hGetAll(String key);
    
    /**
     * 删除哈希缓存的字段
     *
     * @param key 缓存键
     * @param fields 字段列表
     * @return 删除的字段数量
     */
    long hDelete(String key, String... fields);
    
    /**
     * 检查键是否存在
     *
     * @param key 缓存键
     * @return 如果存在返回true，否则返回false
     */
    boolean hasKey(String key);
    
    /**
     * 设置过期时间
     *
     * @param key 缓存键
     * @param timeout 过期时间
     * @param unit 时间单位
     * @return 如果设置成功返回true，否则返回false
     */
    boolean expire(String key, long timeout, TimeUnit unit);
    
    /**
     * 获取过期时间
     *
     * @param key 缓存键
     * @param unit 时间单位
     * @return 过期时间，如果永不过期返回-1，如果键不存在返回-2
     */
    long getExpire(String key, TimeUnit unit);
    
    /**
     * 递增
     *
     * @param key 缓存键
     * @param delta 增量
     * @return 增加后的值
     */
    long increment(String key, long delta);
    
    /**
     * 递减
     *
     * @param key 缓存键
     * @param delta 减量
     * @return 减少后的值
     */
    long decrement(String key, long delta);
}
