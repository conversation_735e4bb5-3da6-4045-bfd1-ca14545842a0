package cn.ccaa.slice.service.cache;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * Redis缓存服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RedisCacheService implements CacheService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 构造函数
     *
     * @param redisTemplate Redis模板
     */
    public RedisCacheService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    @Override
    public void set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            log.debug("设置缓存成功: key={}", key);
        } catch (Exception e) {
            log.error("设置缓存失败: key={}, 错误: {}", key, e.getMessage(), e);
        }
    }
    
    @Override
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
            log.debug("设置缓存成功: key={}, timeout={}, unit={}", key, timeout, unit);
        } catch (Exception e) {
            log.error("设置缓存失败: key={}, timeout={}, unit={}, 错误: {}", 
                    key, timeout, unit, e.getMessage(), e);
        }
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value == null) {
                return null;
            }
            
            if (clazz.isInstance(value)) {
                return (T) value;
            } else {
                log.warn("缓存值类型不匹配: key={}, expectedType={}, actualType={}", 
                        key, clazz.getName(), value.getClass().getName());
                return null;
            }
        } catch (Exception e) {
            log.error("获取缓存失败: key={}, 错误: {}", key, e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public boolean delete(String key) {
        try {
            Boolean result = redisTemplate.delete(key);
            log.debug("删除缓存: key={}, result={}", key, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("删除缓存失败: key={}, 错误: {}", key, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public long deleteMulti(List<String> keys) {
        try {
            Long count = redisTemplate.delete(keys);
            log.debug("批量删除缓存: keys={}, count={}", keys, count);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("批量删除缓存失败: keys={}, 错误: {}", keys, e.getMessage(), e);
            return 0;
        }
    }
    
    @Override
    public void hSet(String key, String field, Object value) {
        try {
            redisTemplate.opsForHash().put(key, field, value);
            log.debug("设置哈希缓存成功: key={}, field={}", key, field);
        } catch (Exception e) {
            log.error("设置哈希缓存失败: key={}, field={}, 错误: {}", key, field, e.getMessage(), e);
        }
    }
    
    @Override
    public void hSetAll(String key, Map<String, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            log.debug("批量设置哈希缓存成功: key={}, fields={}", key, map.keySet());
        } catch (Exception e) {
            log.error("批量设置哈希缓存失败: key={}, fields={}, 错误: {}", 
                    key, map.keySet(), e.getMessage(), e);
        }
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T> T hGet(String key, String field, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForHash().get(key, field);
            if (value == null) {
                return null;
            }
            
            if (clazz.isInstance(value)) {
                return (T) value;
            } else {
                log.warn("哈希缓存值类型不匹配: key={}, field={}, expectedType={}, actualType={}", 
                        key, field, clazz.getName(), value.getClass().getName());
                return null;
            }
        } catch (Exception e) {
            log.error("获取哈希缓存失败: key={}, field={}, 错误: {}", key, field, e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public Map<String, Object> hGetAll(String key) {
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
            if (entries.isEmpty()) {
                return new HashMap<>();
            }
            
            // 转换键类型
            Map<String, Object> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                result.put(entry.getKey().toString(), entry.getValue());
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取哈希缓存所有字段失败: key={}, 错误: {}", key, e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    @Override
    public long hDelete(String key, String... fields) {
        try {
            Long count = redisTemplate.opsForHash().delete(key, (Object[]) fields);
            log.debug("删除哈希缓存字段: key={}, fields={}, count={}", key, fields, count);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("删除哈希缓存字段失败: key={}, fields={}, 错误: {}", 
                    key, fields, e.getMessage(), e);
            return 0;
        }
    }
    
    @Override
    public boolean hasKey(String key) {
        try {
            Boolean result = redisTemplate.hasKey(key);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("检查键是否存在失败: key={}, 错误: {}", key, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean expire(String key, long timeout, TimeUnit unit) {
        try {
            Boolean result = redisTemplate.expire(key, timeout, unit);
            log.debug("设置过期时间: key={}, timeout={}, unit={}, result={}", 
                    key, timeout, unit, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("设置过期时间失败: key={}, timeout={}, unit={}, 错误: {}", 
                    key, timeout, unit, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public long getExpire(String key, TimeUnit unit) {
        try {
            Long expireTime = redisTemplate.getExpire(key, unit);
            return expireTime != null ? expireTime : -2;
        } catch (Exception e) {
            log.error("获取过期时间失败: key={}, unit={}, 错误: {}", key, unit, e.getMessage(), e);
            return -2;
        }
    }
    
    @Override
    public long increment(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().increment(key, delta);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("递增失败: key={}, delta={}, 错误: {}", key, delta, e.getMessage(), e);
            return 0;
        }
    }
    
    @Override
    public long decrement(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().decrement(key, delta);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("递减失败: key={}, delta={}, 错误: {}", key, delta, e.getMessage(), e);
            return 0;
        }
    }
}
