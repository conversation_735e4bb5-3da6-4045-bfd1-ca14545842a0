package cn.ccaa.slice.service;

import cn.ccaa.slice.config.StorageConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 文件路径服务
 * 只处理本地磁盘路径
 * <AUTHOR>
 */
@Service
public class FilePathService {

    private static final Logger log = LoggerFactory.getLogger(FilePathService.class);

    @Autowired
    private StorageConfig storageConfig;

    /**
     * 处理文件路径，将相对路径转换为本地路径
     * 只支持本地磁盘路径
     * @param filePath 文件路径
     * @return 本地文件路径
     */
    public String processFilePath(String filePath) {
        if (filePath == null) {
            return null;
        }
        String finalFilePath = filePath.trim();
        File file = new File(finalFilePath);
        
        // 如果是真正的绝对路径（已经是完整系统路径），直接返回
        if (file.isAbsolute() && file.exists()) {
            return finalFilePath;
        }
        
        // 对于数据库中以"/"开头的相对路径，需要拼接localSliceDir
        String localSliceDir = storageConfig.getLocalSliceDir();
        String localPath;
        
        if (finalFilePath.startsWith("/")) {
            // 去掉开头的"/"，然后拼接
            localPath = localSliceDir + finalFilePath;
        } else {
            // 普通相对路径，添加分隔符后拼接
            localPath = localSliceDir + File.separator + finalFilePath;
        }
        
        log.debug("转换本地切片路径: {} -> {}", filePath, localPath);
        return localPath;
    }
}
