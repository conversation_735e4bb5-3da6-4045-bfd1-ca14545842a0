package cn.ccaa.slice.service;

import cn.ccaa.slice.core.model.SliceEntity;
import cn.ccaa.slice.web.dto.SliceCaptureRequestDTO;
import cn.ccaa.slice.web.dto.SliceCaptureResponseDTO;
import cn.ccaa.slice.web.dto.AICaptureBatchRequestDTO;
import cn.ccaa.slice.web.dto.AICaptureBatchResponseDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * 数字切片服务接口
 *
 * <AUTHOR>
 */
public interface SliceService extends IService<SliceEntity> {

    /**
     * 创建切片记录
     *
     * @param sliceEntity 切片实体
     * @return 创建的切片实体
     */
    SliceEntity createSlice(SliceEntity sliceEntity);
    
    /**
     * 根据ID获取切片记录
     *
     * @param sliceId 切片ID
     * @return 切片实体，如果不存在则返回null
     */
    SliceEntity getSliceById(String sliceId);
    
    /**
     * 更新切片状态
     *
     * @param sliceId 切片ID
     * @param status 状态值
     * @return 是否更新成功
     */
    boolean updateSliceStatus(String sliceId, String status);

    /**
     * 根据 slice_id 获取切片记录
     * @param sliceId 切片ID
     * @return 切片实体
     */
    SliceEntity getBySliceId(String sliceId);

    /**
     * 根据 task_id 获取切片记录列表
     * @param taskId 任务ID
     * @return 切片实体列表
     */
    List<SliceEntity> getByTaskId(String taskId);

    /**
     * 根据 identifier 和 partner_code 联合查询切片记录
     * @param identifier 切片标识符（主键id）
     * @param partnerCode 对接产品代码
     * @return 切片实体，如果不存在则返回null
     */
    SliceEntity getByIdentifierAndPartnerCode(String identifier, String partnerCode);

    /**
     * 切片截图功能
     * 根据参数生成切片截图
     *
     * @param request 截图请求参数
     * @return 截图响应数据
     */
    SliceCaptureResponseDTO captureSlice(SliceCaptureRequestDTO request);

    /**
     * AI批量截图功能
     * 根据AI返回坐标批量生成图片
     *
     * @param requests 批量截图请求参数列表
     * @return 批量截图响应数据
     */
    AICaptureBatchResponseDTO captureAIBatch(List<AICaptureBatchRequestDTO> requests);
} 
