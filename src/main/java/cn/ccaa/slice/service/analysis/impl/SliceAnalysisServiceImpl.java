package cn.ccaa.slice.service.analysis.impl;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import cn.ccaa.slice.config.StorageConfig;
import cn.ccaa.slice.config.TempDirConfig;
import cn.ccaa.slice.core.model.SliceEntity;
import cn.ccaa.slice.core.result.Result;
import cn.ccaa.slice.core.upload.UploadStatus;
import cn.ccaa.slice.core.upload.UploadTask;
import cn.ccaa.slice.service.SliceService;
import cn.ccaa.slice.service.dzi.DziService;
import cn.ccaa.slice.service.upload.SliceUploadService;
import cn.ccaa.slice.service.analysis.SliceAnalysisService;
import cn.ccaa.slice.service.storage.StorageService;
import cn.ccaa.slice.service.upload.FileUploadService;
import cn.ccaa.slice.service.UnifiedSlideService;
import lombok.Builder;
import lombok.Data;

/**
 * 切片解析服务实现类
 *
 * <AUTHOR>
 */
@Service
public class SliceAnalysisServiceImpl implements SliceAnalysisService {

    private static final Logger log = LoggerFactory.getLogger(SliceAnalysisServiceImpl.class);

    private static final String UPLOAD_PROGRESS_KEY = "upload:progress:";

    /**
     * 进度阶段枚举
     */
    private enum ProgressStage {
        /**
         * 下载切片文件占总进度的1%
         */
        DOWNLOAD(0.1),
        
        /**
         * 解析切片文件占总进度的3%
         */
        ANALYZE(0.3),
        
        /**
         * 生成图片占总进度的3%
         */
        GENERATE_IMAGES(0.3),
        
        /**
         * 上传图片占总进度的2%
         */
        UPLOAD_IMAGES(0.2),
        
        /**
         * 完成任务占总进度的1%
         */
        COMPLETE(0.1);

        private final double percentage;

        ProgressStage(double percentage) {
            this.percentage = percentage;
        }

        public double getPercentage() {
            return percentage;
        }
    }

    private final TempDirConfig tempDirConfig;
    private final StorageConfig storageConfig;
    private final SliceService sliceService;
    private final DziService dziService;
    private final SliceUploadService sliceUploadService;
    private final UnifiedSlideService unifiedSlideService;
    private final StringRedisTemplate stringRedisTemplate;
    private final Executor analyzerExecutor;
    private final Executor taskExecutor;

    // 任务取消标志映射
    private final Map<String, AtomicBoolean> cancelFlags = new ConcurrentHashMap<>();

    /**
     * 构造函数
     * 注入所需的依赖服务
     *
     * @param sliceService 切片服务
     * @param dziService DZI服务
     * @param sliceUploadService 上传服务
     * @param storageConfig 存储配置
     * @param unifiedSlideService 统一切片服务
     * @param stringRedisTemplate Redis模板
     * @param tempDirConfig 临时目录配置
     * @param analyzerExecutor 分析器执行器
     * @param taskExecutor 任务执行器
     */
    public SliceAnalysisServiceImpl(
            SliceService sliceService,
            DziService dziService,
            SliceUploadService sliceUploadService,
            StorageConfig storageConfig,
            UnifiedSlideService unifiedSlideService,
            StringRedisTemplate stringRedisTemplate,
            TempDirConfig tempDirConfig,
            @Qualifier("sliceAnalyzerExecutor") Executor analyzerExecutor,
            @Qualifier("taskExecutor") Executor taskExecutor) {
        this.sliceService = sliceService;
        this.dziService = dziService;
        this.sliceUploadService = sliceUploadService;
        this.storageConfig = storageConfig;
        this.unifiedSlideService = unifiedSlideService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.tempDirConfig = tempDirConfig;
        this.analyzerExecutor = analyzerExecutor;
        this.taskExecutor = taskExecutor;
    }

    /**
     * 切片解析结果类
     */
    @Data
    @Builder
    private static class SliceAnalysisResult {
        private String thumbnailFileName;
        private String labelFileName;
    }

    @Override
    public Result<?> asyncAnalyzeSliceByTaskId(String taskId, String crterId, String crterName) {
        log.debug("开始异步解析切片文件: taskId={}, crterId={}, crterName={}", taskId, crterId, crterName);

        if (taskId == null || taskId.trim().isEmpty()) {
            log.error("任务ID为空");
            return Result.fail("任务ID不能为空");
        }

        // 注释掉不存在的方法调用
        // UploadTask task = sliceUploadService.getUploadTask(taskId);
        // Result<?> validationResult = validateTask(task, taskId);
        // if (validationResult != null) {
        //     return validationResult;
        // }

        // 创建取消标志
        final AtomicBoolean cancelFlag = new AtomicBoolean(false);
        cancelFlags.put(taskId, cancelFlag);

        // 设置解析状态为进行中
        String redisKey = UPLOAD_PROGRESS_KEY + taskId;
        stringRedisTemplate.opsForHash().put(redisKey, "sliceAnalyzing", true);

        // 提交异步任务
        CompletableFuture.runAsync(() -> 
            processSliceTask(taskId, null, cancelFlag, redisKey), taskExecutor);

        // 返回任务已提交的结果
        return Result.success(Map.of(
            "taskId", taskId,
            "status", "PROCESSING",
            "message", "切片解析任务已提交，正在处理中"
        ));
    }

    /**
     * 验证任务是否有效且满足处理条件
     * 
     * @param task 任务对象
     * @param taskId 任务ID
     * @return 如果验证失败则返回错误结果，否则返回null
     */
    private Result<?> validateTask(UploadTask task, String taskId) {
        if (task == null) {
            log.error("上传任务不存在: taskId={}", taskId);
            return Result.fail("上传任务不存在");
        }

        boolean isCompleted = task.getStatus() == UploadStatus.COMPLETED;
        boolean isNearlyCompleted = task.getStatus() == UploadStatus.UPLOADING && task.getUploadPercentage() >= 90;
        
        if (!isCompleted && !isNearlyCompleted) {
            log.error("任务状态不满足解析条件: taskId={}, status={}, percentage={}", 
                    taskId, task.getStatus(), task.getUploadPercentage());
            return Result.fail("任务状态不满足解析条件");
        }
        
        return null;
    }
    
    /**
     * 安全设置任务元数据，忽略异常
     * 
     * @param task 任务对象
     * @param key 元数据键
     * @param value 元数据值
     */
    private void safelySetTaskMetadata(UploadTask task, String key, String value) {
        try {
            if (value != null) {
                log.info("设置任务元数据{}: {}", key, value);
                task.setMetadata(key, value);
            }
        } catch (Exception e) {
            log.warn("设置任务元数据{}时出错，继续执行: {}", key, e.getMessage());
        }
    }
    
    /**
     * 处理切片任务
     * 
     * @param taskId 任务ID
     * @param task 任务对象
     * @param cancelFlag 取消标志
     * @param redisKey Redis键
     */
    private void processSliceTask(String taskId, UploadTask task, AtomicBoolean cancelFlag, String redisKey) {
        if (cancelFlag.get()) {
            log.info("切片解析任务已被取消: taskId={}", taskId);
            return;
        }

        try {
            log.info("开始执行切片解析任务: taskId={}", taskId);
            SliceAnalysisResult result = analyzeSliceFile(task);
            
            // 更新完成状态
            safelySetTaskMetadata(task, "sliceAnalyzed", "true");
            
            // 更新Redis中的切片解析标记
            stringRedisTemplate.opsForHash().put(redisKey, "sliceAnalyzed", true);
            stringRedisTemplate.opsForHash().put(redisKey, "progress", 100);
            
            log.info("切片解析任务完成: taskId={}", taskId);
        } catch (Exception e) {
            log.error("切片解析任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            markTaskAsFailed(taskId, e.getMessage());
        } finally {
            // 清除取消标志
            cancelFlags.remove(taskId);
            stringRedisTemplate.opsForHash().put(redisKey, "sliceAnalyzing", false);
        }
    }

    /**
     * 取消切片解析任务
     *
     * @param taskId 任务ID
     * @return 如果成功取消返回true，否则返回false
     */
    @Override
    public boolean cancelSliceAnalysis(String taskId) {
        log.info("尝试取消切片解析任务: {}", taskId);
        AtomicBoolean cancelFlag = cancelFlags.get(taskId);
        
        if (cancelFlag != null) {
            cancelFlag.set(true);
            log.info("成功设置取消标志: {}", taskId);
            
            // 更新Redis中的状态
            String redisKey = UPLOAD_PROGRESS_KEY + taskId;
            stringRedisTemplate.opsForHash().put(redisKey, "sliceAnalyzing", false);
            stringRedisTemplate.opsForHash().put(redisKey, "canceled", true);
            
            return true;
        } else {
            log.info("无法取消任务，任务可能不存在或已完成: {}", taskId);
            return false;
        }
    }

    /**
     * 解析切片文件
     *
     * @param task 上传任务
     * @return 解析结果
     * @throws IOException 如果解析过程中发生IO异常
     */
    private SliceAnalysisResult analyzeSliceFile(UploadTask task) throws IOException {
        String taskId = task.getTaskId();
        
        // 下载切片文件
        File sliceFile = downloadSliceFile(task);
        updateProgress(taskId, ProgressStage.DOWNLOAD);

        try {
            // 解析切片文件
            Map<String, Object> metadata = unifiedSlideService.getSlideInfo(sliceFile.getAbsolutePath());
            log.debug("切片文件解析完成: taskId={}, metadata={}", taskId, metadata);
            
            // 预处理原始元数据，确保包含magnification和resolution字段
            processMetadata(metadata);
            updateProgress(taskId, ProgressStage.ANALYZE);

            // 准备文件名
            String baseFileName = sanitizeFileName(task.getFileName());
            String thumbnailFileName = "thumb_" + baseFileName + ".jpeg";
            String labelFileName = "label_" + baseFileName + ".jpeg";
            
            // 生成缩略图和标签图
            File thumbnailFile = createThumbnailImage(sliceFile, thumbnailFileName);
            File labelFile = createLabelImage(sliceFile, labelFileName);
            updateProgress(taskId, ProgressStage.GENERATE_IMAGES);

            // 上传图片到Minio
            String thumbnailUrl = safeUploadImage(taskId, thumbnailFile, "thumb");
            String labelUrl = safeUploadImage(taskId, labelFile, "label");
            updateProgress(taskId, ProgressStage.UPLOAD_IMAGES);

            // 更新任务元数据
            updateTaskMetadata(task, metadata, thumbnailUrl, labelUrl);
            
            // 更新数据库中的t_c_slice表
            updateSliceDatabase(taskId, metadata, thumbnailFileName, labelFileName, sliceFile);
            updateProgress(taskId, ProgressStage.COMPLETE);

            return SliceAnalysisResult.builder()
                    .thumbnailFileName(thumbnailFileName)
                    .labelFileName(labelFileName)
                    .build();
        } finally {
            // 清理临时文件
            safeDeleteFile(sliceFile);
        }
    }
    
    /**
     * 安全地上传图像文件，捕获异常但不中断流程
     * 
     * @param taskId 任务ID
     * @param imageFile 图像文件
     * @param type 图像类型
     * @return 图像URL，如果上传失败则返回null
     */
    private String safeUploadImage(String taskId, File imageFile, String type) {
        try {
            return uploadImageToMinio(taskId, imageFile, type);
        } catch (Exception e) {
            log.error("上传{}图失败: {}", type, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 安全删除文件，不抛出异常
     * 
     * @param file 要删除的文件
     */
    private void safeDeleteFile(File file) {
        try {
            if (file != null && file.exists()) {
                Files.delete(file.toPath());
            }
        } catch (IOException e) {
            log.warn("删除临时文件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 清理并规范化文件名
     * 
     * @param fileName 原始文件名
     * @return 规范化后的文件名
     */
    private String sanitizeFileName(String fileName) {
        String baseFileName = FilenameUtils.getBaseName(fileName);
        // 移除可能的前缀
        if (baseFileName.startsWith("thumb_")) {
            baseFileName = baseFileName.substring(6);
        }
        if (baseFileName.startsWith("label_")) {
            baseFileName = baseFileName.substring(6);
        }
        return baseFileName;
    }
    
    /**
     * 更新任务元数据
     * 
     * @param task 任务
     * @param metadata 元数据
     * @param thumbnailUrl 缩略图URL
     * @param labelUrl 标签图URL
     */
    private void updateTaskMetadata(UploadTask task, Map<String, Object> metadata, 
                                  String thumbnailUrl, String labelUrl) {
        try {
            Optional.ofNullable(thumbnailUrl).ifPresent(url -> task.setMetadata("thumbnailUrl", url));
            Optional.ofNullable(labelUrl).ifPresent(url -> task.setMetadata("labelUrl", url));
            task.setMetadata("metadata", metadata);
        } catch (Exception e) {
            log.warn("更新任务元数据时出错: {}", e.getMessage());
        }
    }
    
    /**
     * 处理元数据，确保包含必要的字段
     * 
     * @param metadata 元数据
     */
    private void processMetadata(Map<String, Object> metadata) {
        log.debug("开始处理元数据，确保包含必要字段");
        
        // 处理扫描倍率 (magnification)
        if (!metadata.containsKey("magnification")) {
            processMagnification(metadata);
        }
        
        // 处理分辨率 (resolution)
        if (!metadata.containsKey("resolution") && !metadata.containsKey("mpp")) {
            processResolution(metadata);
        }
    }
    
    /**
     * 处理扫描倍率元数据
     * 
     * @param metadata 元数据
     */
    private void processMagnification(Map<String, Object> metadata) {
        // 尝试从openslide.objective-power获取扫描倍率
        tryParseMetadataValue(metadata, "openslide.objective-power", "magnification", value -> {
            int magnification = Integer.parseInt(value.trim());
            log.debug("从openslide.objective-power获取扫描倍率: {}", magnification);
            return magnification;
        });
        
        // 如果还是没有magnification，尝试从aperio.AppMag获取
        if (!metadata.containsKey("magnification")) {
            tryParseMetadataValue(metadata, "aperio.AppMag", "magnification", value -> {
                int magnification = Integer.parseInt(value.trim());
                log.debug("从aperio.AppMag获取扫描倍率: {}", magnification);
                return magnification;
            });
        }
        
        // 如果两种方式都没有找到
        if (!metadata.containsKey("magnification")) {
            log.warn("元数据中没有找到扫描倍率相关的字段");
        }
    }
    
    /**
     * 处理分辨率元数据
     * 
     * @param metadata 元数据
     */
    private void processResolution(Map<String, Object> metadata) {
        // 尝试从mpp-x和mpp-y获取分辨率
        if (metadata.containsKey("openslide.mpp-x") && metadata.containsKey("openslide.mpp-y")) {
            Object mppX = metadata.get("openslide.mpp-x");
            Object mppY = metadata.get("openslide.mpp-y");
            
            if (mppX != null && mppY != null) {
                try {
                    double mppXValue = Double.parseDouble(mppX.toString().trim());
                    double mppYValue = Double.parseDouble(mppY.toString().trim());
                    
                    double avgMpp = (mppXValue + mppYValue) / 2.0;
                    String resolutionStr = String.format("%.15f", avgMpp);
                    metadata.put("resolution", resolutionStr);
                    log.debug("从mpp-x/mpp-y平均得到分辨率: {}", resolutionStr);
                    
                    // 存储平均MPP值
                    metadata.put("mpp", avgMpp);
                    log.debug("计算平均MPP值: {}", avgMpp);
                    return; // 成功设置分辨率后返回
                } catch (NumberFormatException e) {
                    log.warn("无法解析mpp值: x={}, y={}", mppX, mppY);
                }
            }
        }
        
        // 尝试从aperio.MPP获取分辨率
        tryParseMetadataValue(metadata, "aperio.MPP", value -> {
            double mppValue = Double.parseDouble(value.trim());
            
            String resolutionStr = String.format("%.15f", mppValue);
            metadata.put("resolution", resolutionStr);
            log.debug("从aperio.MPP生成分辨率: {}", resolutionStr);
            
            // 存储MPP值
            metadata.put("mpp", mppValue);
            log.debug("从aperio.MPP设置mpp值: {}", mppValue);
        });
        
        // 如果上述方法都没有找到分辨率
        if (!metadata.containsKey("resolution") && !metadata.containsKey("mpp")) {
            log.warn("元数据中没有找到分辨率相关的字段");
        }
    }
    
    /**
     * 尝试解析元数据字段值并添加到元数据映射中
     *
     * @param metadata 元数据映射
     * @param sourceKey 源键
     * @param targetKey 目标键
     * @param parser 解析函数
     */
    private <T> void tryParseMetadataValue(Map<String, Object> metadata, String sourceKey, String targetKey, 
                                        Function<String, T> parser) {
        Optional.ofNullable(metadata.get(sourceKey))
            .map(Object::toString)
            .ifPresent(value -> {
                try {
                    T result = parser.apply(value);
                    metadata.put(targetKey, result);
                } catch (Exception e) {
                    log.warn("无法解析{}值: {}", sourceKey, value);
                }
            });
    }
    
    /**
     * 尝试解析元数据字段值并执行操作
     *
     * @param metadata 元数据映射
     * @param sourceKey 源键
     * @param consumer 消费函数
     */
    private void tryParseMetadataValue(Map<String, Object> metadata, String sourceKey, 
                                     Consumer<String> consumer) {
        Optional.ofNullable(metadata.get(sourceKey))
            .map(Object::toString)
            .ifPresent(value -> {
                try {
                    consumer.accept(value);
                } catch (Exception e) {
                    log.warn("无法解析{}值: {}", sourceKey, value);
                }
            });
    }

    /**
     * 创建缩略图
     *
     * @param sliceFile 切片文件
     * @param fileName 输出文件名
     * @return 生成的缩略图文件
     * @throws IOException 如果生成失败
     */
    private File createThumbnailImage(File sliceFile, String fileName) throws IOException {
        return createImage(sliceFile, fileName, "thumb", 
            () -> unifiedSlideService.getThumbnailJpeg(sliceFile.getAbsolutePath()),
            "缩略图");
    }

    /**
     * 创建标签图
     *
     * @param sliceFile 切片文件
     * @param fileName 输出文件名
     * @return 生成的标签图文件
     * @throws IOException 如果生成失败
     */
    private File createLabelImage(File sliceFile, String fileName) throws IOException {
        return createImage(sliceFile, fileName, "label", 
            () -> unifiedSlideService.getLabelJpeg(sliceFile.getAbsolutePath()),
            "标签图");
    }
    
    /**
     * 创建图像文件
     *
     * @param sliceFile 切片文件
     * @param fileName 输出文件名
     * @param cacheType 缓存类型
     * @param imageDataSupplier 图像数据提供器
     * @param imageTypeName 图像类型名称（用于日志）
     * @return 生成的图像文件
     * @throws IOException 如果生成失败
     */
    private File createImage(File sliceFile, String fileName, String cacheType,
                          Supplier<byte[]> imageDataSupplier, String imageTypeName) throws IOException {
        log.debug("开始生成{}: {}", imageTypeName, fileName);

        if (sliceFile == null || !sliceFile.exists()) {
            throw new IOException(String.format("%s文件无效或不存在", imageTypeName));
        }

        Path outputPath = sliceFile.getParentFile().toPath().resolve(fileName);
        File outputFile = outputPath.toFile();

        try {
            // 尝试从缓存中获取图像
            String cacheKey = cacheType + ":" + sliceFile.getAbsolutePath().hashCode();
            log.debug("{}缓存查找: {}", imageTypeName, cacheKey);
            
            byte[] imageData = null;
            try {
                imageData = imageDataSupplier.get();
                if (imageData == null || imageData.length == 0) {
                    throw new IOException(String.format("生成的%s数据为空", imageTypeName));
                }
            } catch (Exception e) {
                log.error("生成{}失败: {}", imageTypeName, e.getMessage());
                throw new IOException(String.format("生成%s失败: %s", imageTypeName, e.getMessage()), e);
            }
            
            Files.write(outputPath, imageData);
            log.debug("{}生成完成: {}, 大小: {} 字节", imageTypeName, outputFile.getAbsolutePath(), outputFile.length());
            return outputFile;
        } catch (Exception e) {
            log.error("生成{}失败: {}", imageTypeName, e.getMessage(), e);
            throw new IOException(String.format("生成%s失败: %s", imageTypeName, e.getMessage()), e);
        }
    }

    /**
     * 下载切片文件
     *
     * @param task 上传任务
     * @return 下载的切片文件
     * @throws IOException 如果下载过程中发生IO异常
     */
    private File downloadSliceFile(UploadTask task) throws IOException {
        // 注释掉minio相关的下载逻辑，因为storageService不存在
        log.warn("文件下载功能暂时禁用，因为存储服务不可用");
        throw new IOException("文件下载功能暂时禁用");
        
        // TODO: 实现本地文件系统的文件下载逻辑
    }

    /**
     * 更新数据库中的t_c_slice表
     *
     * @param taskId 任务ID
     * @param metadata 解析的元数据
     * @param thumbnailFileName 缩略图文件名
     * @param labelFileName 标签图文件名
     * @param sliceFile 切片文件
     */
    private void updateSliceDatabase(String taskId, Map<String, Object> metadata, 
                                    String thumbnailFileName, String labelFileName, File sliceFile) {
        try {
            log.info("开始更新数据库切片信息: taskId={}", taskId);
            
            // 从数据库读取切片信息
            SliceEntity sliceEntity = sliceService.getBySliceId(taskId);
            if (sliceEntity == null) {
                log.warn("未找到切片记录，无法更新: taskId={}", taskId);
                return;
            }

            // 设置时间和用户信息
            LocalDateTime now = LocalDateTime.now();
            sliceEntity.setUploadTime(now);
            sliceEntity.setLastModifiedTime(now);

            // 设置原始像素宽高与分辨率
            if (metadata != null && !metadata.isEmpty()) {
                try {
                    // 宽
                    Object wObj = metadata.get("width");
                    if (wObj != null) {
                        try {
                            int width = Integer.parseInt(wObj.toString().trim());
                            sliceEntity.setOrigPixWidth(width);
                        } catch (NumberFormatException ignore) {
                            log.warn("无法解析像素宽度: {}", wObj);
                        }
                    }

                    // 高
                    Object hObj = metadata.get("height");
                    if (hObj != null) {
                        try {
                            int height = Integer.parseInt(hObj.toString().trim());
                            sliceEntity.setOrigPixHeight(height);
                        } catch (NumberFormatException ignore) {
                            log.warn("无法解析像素高度: {}", hObj);
                        }
                    }

                    // 分辨率
                    Object resObj = metadata.get("resolution");
                    if (resObj != null) {
                        sliceEntity.setResolution(resObj.toString());
                    }

                    // 新增：层级数量
                    Object levelCountObj = metadata.get("levelCount");
                    if (levelCountObj != null) {
                        try {
                            int levelCount = Integer.parseInt(levelCountObj.toString().trim());
                            sliceEntity.setLevelsCnt(levelCount);
                            log.info("成功提取层级数量: taskId={}, levelCount={}", taskId, levelCount);
                        } catch (NumberFormatException ignore) {
                            log.warn("无法解析层级数量: {}", levelCountObj);
                        }
                    } else {
                        log.warn("未能从metadata中获取到层级数量: taskId={}", taskId);
                    }
                } catch (Exception metaEx) {
                    log.warn("设置像素宽高分辨率失败: {}", metaEx.getMessage());
                }
            }

            // 直接使用实体更新
            boolean updateResult = sliceService.updateById(sliceEntity);
            log.info("更新切片表(t_c_slice)信息结果: {}", updateResult ? "成功" : "失败");
            
        } catch (Exception e) {
            log.error("更新切片表(t_c_slice)失败: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 构建存储对象名称
     *
     * @param taskId 任务ID
     * @param type 对象类型
     * @param fileName 文件名
     * @return 完整的对象名称
     */
    private String buildObjectName(String taskId, String type, String fileName) {
        // 检查文件名是否已经包含前缀
        String prefix = type + "_";
        String finalFileName;

        // 如果文件名已经包含前缀，则直接使用原文件名
        if (fileName.startsWith(prefix)) {
            log.debug("文件名已包含前缀 {}，不再添加前缀: {}", prefix, fileName);
            finalFileName = fileName;
        } else {
            // 如果文件名不包含前缀，则添加前缀
            finalFileName = prefix + fileName;
            log.debug("添加前缀到文件名: {} -> {}", fileName, finalFileName);
        }

        // 使用与切片文件相同的路径，即storageConfig中配置的uploadDir
        String uploadDir = storageConfig.getFile().getUploadDir();
        String fullPath = String.format("%s/%s/%s", uploadDir, taskId, finalFileName);
        log.debug("构建的存储对象路径: {}", fullPath);
        return fullPath;
    }

    /**
     * 更新进度
     *
     * @param taskId 任务ID
     * @param stage 进度阶段
     */
    private void updateProgress(String taskId, ProgressStage stage) {
        try {
            // 注释掉不存在的方法调用
            // UploadTask task = sliceUploadService.getTask(taskId);
            // if (task == null) {
            //     log.warn("无法更新进度，找不到任务: {}", taskId);
            //     return;
            // }

            log.debug("更新任务进度: taskId={}, 阶段={}", taskId, stage.name());

            // 注释掉不存在的方法调用
            // sliceUploadService.updateTaskStatus(
            //     taskId,
            //     progressBytes,
            //     null,
            //     null,
            //     null,
            //     false
            // );
        } catch (Exception e) {
            log.warn("更新进度失败: taskId={}, 错误: {}", taskId, e.getMessage());
        }
    }

    /**
     * 将任务标记为失败
     *
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     */
    private void markTaskAsFailed(String taskId, String errorMessage) {
        try {
            // 注释掉不存在的方法调用
            // sliceUploadService.updateTaskStatus(taskId, 0L, UploadStatus.FAILED.toString(),
            //     null, errorMessage, true);
            log.warn("标记任务失败: taskId={}, error={}", taskId, errorMessage);
        } catch (Exception e) {
            log.error("更新任务状态为失败时出错: taskId={}, 错误: {}", taskId, e.getMessage());
        }
    }

    /**
     * 上传图片到Minio
     *
     * @param taskId 任务ID
     * @param imageFile 图片文件
     * @param type 图片类型（thumb或label）
     * @return 图片URL
     * @throws IOException 如果上传失败
     */
    private String uploadImageToMinio(String taskId, File imageFile, String type) throws IOException {
        // 注释掉minio上传逻辑
        log.warn("图片上传到Minio功能暂时禁用: taskId={}, type={}", taskId, type);
        return null;
        
        // TODO: 实现本地文件系统的图片保存逻辑
    }
}
