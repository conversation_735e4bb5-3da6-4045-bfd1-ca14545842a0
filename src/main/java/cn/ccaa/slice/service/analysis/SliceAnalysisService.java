package cn.ccaa.slice.service.analysis;

import cn.ccaa.slice.core.result.Result;

/**
 * 切片解析服务接口
 * 根据任务ID获取切片的方法
 * 
 * <AUTHOR>
 */
public interface SliceAnalysisService {
    /**
     * 异步解析切片文件，并使用指定的创建者信息更新数据库
     * 在后台线程执行切片解析
     * 1. 从Minio获取切片源文件
     * 2. 解析切片文件获取信息
     * 3. 生成缩略图和标签图
     * 4. 上传缩略图和标签图到Minio
     * 5. 更新任务状态和数据库
     *
     * @param taskId 任务ID
     * @param crterId 创建者ID，可为null
     * @param crterName 创建者姓名，可为null
     * @return 任务提交结果
     */
    Result<?> asyncAnalyzeSliceByTaskId(String taskId, String crterId, String crterName);
    
    /**
     * 取消切片解析任务
     *
     * @param taskId 任务ID
     * @return 如果成功取消返回true，否则返回false
     */
    boolean cancelSliceAnalysis(String taskId);
} 
