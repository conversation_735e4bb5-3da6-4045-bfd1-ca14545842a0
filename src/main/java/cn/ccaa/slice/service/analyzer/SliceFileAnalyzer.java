package cn.ccaa.slice.service.analyzer;

import java.io.File;

import cn.ccaa.slice.core.exception.SliceAnalysisException;
import cn.ccaa.slice.core.model.SliceAnalysisResult;

/**
 * 切片文件解析器接口
 * 用于解析切片文件，提取元数据、缩略图和标签图
 *
 * <AUTHOR>
 */
public interface SliceFileAnalyzer {
    
    /**
     * 解析切片文件
     * 
     * @param file 切片文件
     * @return 解析结果，包含元数据、缩略图和标签图
     * @throws SliceAnalysisException 如果解析失败
     */
    SliceAnalysisResult analyze(File file) throws SliceAnalysisException;
    
    /**
     * 检查文件类型是否支持
     * 
     * @param fileName 文件名
     * @return 如果支持返回true，否则返回false
     */
    boolean supports(String fileName);
} 
