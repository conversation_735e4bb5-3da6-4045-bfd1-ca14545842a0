package cn.ccaa.slice.service.analyzer.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import cn.ccaa.slice.core.exception.SliceAnalysisException;
import cn.ccaa.slice.core.model.SliceAnalysisResult;
import cn.ccaa.slice.service.UnifiedSlideService;
import cn.ccaa.slice.service.analyzer.SliceFileAnalyzer;
import lombok.RequiredArgsConstructor;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;

import cn.ccaa.slice.parsers.tronsdk.service.TronSdkImageExtractor;

/**
 * 通用切片文件解析器实现
 * 提供基础的切片文件解析功能
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class GenericSliceFileAnalyzer implements SliceFileAnalyzer {
    
    private static final Logger log = LoggerFactory.getLogger(GenericSliceFileAnalyzer.class);
    
    /**
     * 支持的文件扩展名列表
     */
    private static final List<String> SUPPORTED_EXTENSIONS = Arrays.asList(
            "svs", "ndpi", "tif", "tiff", "kfb", "sdpc", "tron");
    
    /**
     * 用于识别SqraySlide解析器
     */
    private static final String SQRAYSLIDE_PARSER = "sqrayslide";
    
    /**
     * 文件类型代码映射
     */
    private static final Map<String, Integer> FILE_TYPE_CODES = new HashMap<>();
    
    /**
     * 制造商代码映射
     */
    private static final Map<String, Integer> MANUFACTURER_CODES = new HashMap<>();
    
    /**
     * 制造商名称映射
     */
    private static final Map<String, String> MANUFACTURER_NAMES = new HashMap<>();
    
    /**
     * 文件类型名称映射
     */
    private static final Map<String, String> FILE_TYPE_NAMES = new HashMap<>();
    
    /**
     * JSON对象映射器
     */
    private static final ObjectMapper objectMapper = new ObjectMapper()
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    
    static {
        // 初始化文件类型代码映射
        FILE_TYPE_CODES.put("ndpi", 1);
        FILE_TYPE_CODES.put("sdpc", 2);
        FILE_TYPE_CODES.put("svs", 3);
        FILE_TYPE_CODES.put("tif", 3);
        FILE_TYPE_CODES.put("tiff", 3);
        FILE_TYPE_CODES.put("kfb", 4);
        FILE_TYPE_CODES.put("tron", 5);
        
        // 初始化制造商代码映射
        MANUFACTURER_CODES.put("ndpi", 1); // 滨松
        MANUFACTURER_CODES.put("sdpc", 2); // 江丰
        MANUFACTURER_CODES.put("svs", 3);  // 徕卡
        MANUFACTURER_CODES.put("kfb", 4);  // 生强
        
        // 初始化制造商名称映射
        MANUFACTURER_NAMES.put("1", "滨松");
        MANUFACTURER_NAMES.put("2", "江丰");
        MANUFACTURER_NAMES.put("3", "徕卡");
        MANUFACTURER_NAMES.put("4", "生强");
        
        // 初始化文件类型名称映射
        FILE_TYPE_NAMES.put("1", "Hamamatsu NDPI");
        FILE_TYPE_NAMES.put("2", "江丰 SDPC");
        FILE_TYPE_NAMES.put("3", "Leica SVS/TIF");
        FILE_TYPE_NAMES.put("4", "生强 KFB");
        FILE_TYPE_NAMES.put("5", "TRON");
    }

    @Autowired
    private UnifiedSlideService unifiedSlideService;
    
    @Autowired
    private TronSdkImageExtractor tronSdkImageExtractor;
    
    /**
     * 分析切片文件
     * 
     * @param file 切片文件
     * @return 解析结果
     * @throws SliceAnalysisException 如果解析失败
     */
    @Override
    public SliceAnalysisResult analyze(File file) throws SliceAnalysisException {
        validateFile(file);
        
        String extension = FilenameUtils.getExtension(file.getName()).toLowerCase();
        log.info("开始解析切片文件: {}", file.getName());
        
        try {
            // 使用文件所在的临时目录
            Path tempDir = file.getParentFile().toPath();
            
            // 获取文件路径
            String filePath = file.getAbsolutePath();
            
            // 获取切片信息作为元数据
            Map<String, Object> metadata = null;
            try {
                metadata = unifiedSlideService.getSlideInfo(filePath);
            } catch (Exception e) {
                log.error("获取切片信息失败: {}", e.getMessage(), e);
                metadata = new HashMap<>();
                metadata.put("error", "获取切片信息失败: " + e.getMessage());
                
                // 特殊处理Tron格式：如果UnifiedSlideService获取失败，直接调用TronSdkImageExtractor
                if ("tron".equals(extension)) {
                    log.info("Tron格式文件使用TronSdkImageExtractor获取元数据: {}", filePath);
                    try {
                        Map<String, Object> tronInfo = tronSdkImageExtractor.getInfo(filePath);
                        log.info("[DEBUG] tronInfo内容: {}", tronInfo);
                        if (tronInfo != null && !tronInfo.isEmpty() && !tronInfo.containsKey("error")) {
                            metadata.putAll(tronInfo);
                            metadata.remove("error"); // 移除错误信息，因为Tron解析成功了
                            log.info("成功从TronSdkImageExtractor获取元数据: {}", tronInfo.keySet());
                            // 自动修复：无条件将maximumZoomLevel赋值给objective和magnification，保证ChunkMergeService能识别
                            Object maxZoom = tronInfo.get("maximumZoomLevel");
                            if (maxZoom != null) {
                                metadata.put("objective", maxZoom);
                                metadata.put("magnification", maxZoom);
                                log.info("[自动修复] maximumZoomLevel已赋值给objective和magnification: {}", maxZoom);
                            }
                            log.info("[DEBUG] analyze赋值后metadata: {}", metadata);
                        } else {
                            log.warn("TronSdkImageExtractor未返回有效数据: {}", tronInfo);
                        }
                    } catch (Exception tronEx) {
                        log.error("TronSdkImageExtractor获取元数据失败: {}", tronEx.getMessage(), tronEx);
                        metadata.put("error", "TronSDK获取元数据失败: " + tronEx.getMessage());
                    }
                }
            }
            
            if (metadata == null) {
                metadata = new HashMap<>();
            }
            
            // 获取解析器类型
            String parserType = null;
            try {
                parserType = unifiedSlideService.detectFormat(filePath);
            } catch (Exception e) {
                log.error("检测文件格式失败: {}", e.getMessage(), e);
            }
            
            // 若 detectFormat 返回为空，再根据扩展名兜底
            if (parserType == null || parserType.isEmpty()) {
                if ("tron".equalsIgnoreCase(extension)) {
                    parserType = "tronsdk";
                }
            }
            
            // 根据解析器类型处理元数据
            processMetadata(metadata, parserType);
            
            // 生成缩略图和标签图
            File thumbnailImage = null;
            File labelImage = null;
            File slideImage = null;
            
            try {
                thumbnailImage = createThumbnailImage(tempDir, file);
                log.debug("成功创建缩略图: {}", thumbnailImage != null ? thumbnailImage.getAbsolutePath() : "null");
            } catch (Exception e) {
                log.error("创建缩略图失败: {}", e.getMessage(), e);
                // 创建一个空的缩略图，防止后续NPE
                thumbnailImage = createEmptyImage(tempDir, "thumb");
            }
            
            try {
                labelImage = createLabelImage(tempDir, file);
                log.debug("成功创建标签图: {}", labelImage != null ? labelImage.getAbsolutePath() : "null");
            } catch (Exception e) {
                log.error("创建标签图失败: {}", e.getMessage(), e);
                // 创建一个空的标签图，防止后续NPE
                labelImage = createEmptyImage(tempDir, "label");
            }
            
            try {
                slideImage = createSlideImage(tempDir, file);
                log.debug("成功创建玻片图: {}", slideImage != null ? slideImage.getAbsolutePath() : "null");
            } catch (Exception e) {
                log.error("创建玻片图失败: {}", e.getMessage(), e);
                slideImage = createEmptyImage(tempDir, "slide");
            }
            
            // 确定文件类型和厂商代码
            Integer fileTypeCode = determineFileTypeCode(extension);
            Integer manufacturerCode = determineManufacturerCode(extension);
            
            // 将文件类型和厂商代码加入元数据
            metadata.put("fileTypeCode", fileTypeCode);
            metadata.put("manufacturerCode", manufacturerCode);
            
            // 保证最终metadata有倍率字段（最简明修复）
            Object maxZoom = metadata.get("maximumZoomLevel");
            if (maxZoom != null) {
                metadata.put("objective", maxZoom);
                metadata.put("magnification", maxZoom);
                log.info("构建结果前赋值: maximumZoomLevel={} -> objective/magnification", maxZoom);
            }
            SliceAnalysisResult result = SliceAnalysisResult.builder()
                    .metadata(metadata)
                    .thumbnailImage(thumbnailImage)
                    .labelImage(labelImage)
                    .slideImage(slideImage)
                    .fileTypeCode(fileTypeCode)
                    .manufacturerCode(manufacturerCode)
                    .build();
            
            log.info("切片文件解析完成: {}", file.getName());
            
            // 记录解析结果日志
            logAnalysisResult(result);
            
            return result;
        } catch (Exception e) {
            log.error("解析切片文件时发生异常: {}", e.getMessage(), e);
            throw new SliceAnalysisException("解析切片文件时发生异常", e);
        }
    }
    
    /**
     * 验证文件有效性
     * 
     * @param file 要验证的文件
     * @throws SliceAnalysisException 如果文件无效
     */
    private void validateFile(File file) throws SliceAnalysisException {
        if (file == null || !file.exists()) {
            throw new SliceAnalysisException("文件不存在");
        }
        
        String extension = FilenameUtils.getExtension(file.getName()).toLowerCase();
        if (!SUPPORTED_EXTENSIONS.contains(extension)) {
            throw new SliceAnalysisException("不支持的文件格式: " + extension);
        }
    }
    
    /**
     * 记录解析结果日志
     * 
     * @param result 解析结果
     */
    private void logAnalysisResult(SliceAnalysisResult result) {
        try {
            // 创建一个不包含二进制文件的副本用于日志记录
            SliceAnalysisResult logResult = SliceAnalysisResult.builder()
                    .metadata(result.getMetadata())
                    .fileTypeCode(result.getFileTypeCode())
                    .manufacturerCode(result.getManufacturerCode())
                    .build();
            String jsonResult = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(logResult);
            log.debug("解析结果: {}", jsonResult);
        } catch (Exception e) {
            log.error("转换解析结果为JSON失败: {}", e.getMessage(), e);
            log.debug("解析结果: {}", result);
        }
    }
    
    /**
     * 根据解析器类型处理元数据
     * 
     * @param metadata 原始元数据
     * @param parserType 解析器类型
     */
    private void processMetadata(Map<String, Object> metadata, String parserType) {
        if (SQRAYSLIDE_PARSER.equalsIgnoreCase(parserType)) {
            // 处理 Sqrayslide 解析器的元数据
            processSqrayslideMetadata(metadata);
        } else if ("tronsdk".equalsIgnoreCase(parserType)) {
            // 处理 TronSDK 解析器的元数据
            processTronSdkMetadata(metadata);
        } else {
            // 处理 OpenSlide 解析器的元数据
            processOpenSlideMetadata(metadata);
        }
    }
    
    /**
     * 处理Sqrayslide解析器的元数据
     * 
     * @param metadata 原始元数据
     */
    private void processSqrayslideMetadata(Map<String, Object> metadata) {
        try {
            // 提取objective作为扫描倍率
            extractObjectiveAsMagnification(metadata, "objective");
            
            // 提取mpp处理分辨率
            processMppForResolution(metadata);
            
            // 处理扫描格式
            processScanFormatForSqrayslide(metadata);
            
            // 若仍未获得分辨率，则保持为空，交由上层业务决定处理。
            
        } catch (Exception e) {
            log.error("处理Sqrayslide元数据时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理Sqrayslide的扫描格式
     * 
     * @param metadata 元数据
     */
    private void processScanFormatForSqrayslide(Map<String, Object> metadata) {
        StringBuilder scanFormatBuilder = new StringBuilder();
        
        // 添加类型信息
        if (metadata.containsKey("type")) {
            Object type = metadata.get("type");
            if ("BRIGHTFIELD".equals(type)) {
                scanFormatBuilder.append("明场");
            } else if ("FLUORESCENCE".equals(type)) {
                scanFormatBuilder.append("荧光");
            } else {
                scanFormatBuilder.append(type.toString());
            }
        }
        
        // 获取制造商信息
        if (metadata.containsKey("manufacturerCode")) {
            Object mfrCode = metadata.get("manufacturerCode");
            if (scanFormatBuilder.length() > 0) {
                scanFormatBuilder.append(" ");
            }
            
            String manufacturerName = MANUFACTURER_NAMES.getOrDefault(mfrCode.toString(), "未知厂商");
            scanFormatBuilder.append(manufacturerName);
        }
        
        // 添加倍率信息
        appendMagnificationInfo(metadata, scanFormatBuilder);
        
        if (scanFormatBuilder.length() > 0) {
            metadata.put("scanFormat", scanFormatBuilder.toString());
            log.debug("生成扫描格式: {}", scanFormatBuilder.toString());
        }
    }
    
    /**
     * 处理OpenSlide解析的元数据
     * 特别处理mpp-x和mpp-y值，计算分辨率和设置扫描倍率
     * 
     * @param metadata 原始元数据
     */
    private void processOpenSlideMetadata(Map<String, Object> metadata) {
        try {
            // 提取objective-power作为扫描倍率
            extractObjectiveAsMagnification(metadata, "openslide.objective-power");
            
            // 提取mpp-x和mpp-y计算分辨率
            processOpenSlideMppForResolution(metadata);
            
            // 处理扫描格式
            processScanFormatForOpenSlide(metadata);
            
        } catch (Exception e) {
            log.error("处理OpenSlide元数据时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 从指定字段提取扫描倍率
     * 
     * @param metadata 元数据
     * @param fieldName 字段名
     */
    private void extractObjectiveAsMagnification(Map<String, Object> metadata, String fieldName) {
        if (metadata.containsKey(fieldName)) {
            Object objective = metadata.get(fieldName);
            if (objective != null) {
                try {
                    int scanZoom;
                    if (objective instanceof Number) {
                        scanZoom = ((Number) objective).intValue();
                    } else {
                        scanZoom = Integer.parseInt(objective.toString().trim());
                    }
                    metadata.put("magnification", scanZoom);
                    log.debug("从{}提取扫描倍率: {}", fieldName, scanZoom);
                } catch (NumberFormatException e) {
                    log.warn("无法解析{}值: {}", fieldName, objective);
                }
            }
        }
    }
    
    /**
     * 处理单一mpp值的分辨率计算
     * 
     * @param metadata 元数据
     */
    private void processMppForResolution(Map<String, Object> metadata) {
        if (metadata.containsKey("mpp")) {
            Object mpp = metadata.get("mpp");
            if (mpp != null) {
                try {
                    double mppValue;
                    if (mpp instanceof Number) {
                        mppValue = ((Number) mpp).doubleValue();
                    } else {
                        mppValue = Double.parseDouble(mpp.toString().trim());
                    }
                    
                    // 取单一mpp值作为分辨率(µm/Pixel)，保留15位小数
                    String resolutionStr = String.format("%.15f", mppValue);
                    metadata.put("resolution", resolutionStr);
                    log.debug("从mpp获取分辨率: {}", resolutionStr);
                    
                    // 单独保存mpp值，方便后续使用
                    metadata.put("mppX", String.format("%.6f", mppValue));
                    metadata.put("mppY", String.format("%.6f", mppValue));
                } catch (NumberFormatException e) {
                    log.warn("无法解析mpp值: {}", mpp);
                }
            }
        }
    }
    
    /**
     * 处理OpenSlide的mpp-x和mpp-y计算分辨率
     * 
     * @param metadata 元数据
     */
    private void processOpenSlideMppForResolution(Map<String, Object> metadata) {
        if (metadata.containsKey("openslide.mpp-x") && metadata.containsKey("openslide.mpp-y")) {
            Object mppX = metadata.get("openslide.mpp-x");
            Object mppY = metadata.get("openslide.mpp-y");
            
            if (mppX != null && mppY != null) {
                try {
                    double mppXValue = Double.parseDouble(mppX.toString().trim());
                    double mppYValue = Double.parseDouble(mppY.toString().trim());
                    
                    // 取单一mpp值作为分辨率(µm/Pixel)，保留15位小数
                    double avgMpp = (mppXValue + mppYValue) / 2.0;
                    String resolutionStr = String.valueOf(avgMpp);
                    metadata.put("resolution", resolutionStr);
                    log.debug("从mpp-x/mpp-y平均得到分辨率: {}", resolutionStr);
                    // 保存平均MPP，供其他逻辑使用
                    metadata.put("mpp", avgMpp);
                    log.debug("保存平均MPP值: {}", avgMpp);
                } catch (NumberFormatException e) {
                    log.warn("无法解析mpp值: x={}, y={}", mppX, mppY);
                }
            }
        }
    }
    
    /**
     * 处理OpenSlide的扫描格式
     * 
     * @param metadata 元数据
     */
    private void processScanFormatForOpenSlide(Map<String, Object> metadata) {
        StringBuilder scanFormatBuilder = new StringBuilder();
        
        // 添加制造商信息
        if (metadata.containsKey("tiff.Make")) {
            scanFormatBuilder.append(metadata.get("tiff.Make").toString());
        } else if (metadata.containsKey("openslide.vendor")) {
            scanFormatBuilder.append(metadata.get("openslide.vendor").toString());
        }
        
        // 添加型号信息
        if (metadata.containsKey("tiff.Model")) {
            if (scanFormatBuilder.length() > 0) {
                scanFormatBuilder.append(" ");
            }
            scanFormatBuilder.append(metadata.get("tiff.Model").toString());
        }
        
        // 添加软件版本
        if (metadata.containsKey("tiff.Software")) {
            if (scanFormatBuilder.length() > 0) {
                scanFormatBuilder.append(", ");
            }
            scanFormatBuilder.append(metadata.get("tiff.Software").toString());
        }
        
        // 如果没有找到任何信息，尝试使用文件类型作为基础
        if (scanFormatBuilder.length() == 0 && metadata.containsKey("fileTypeCode")) {
            Object fileTypeCode = metadata.get("fileTypeCode");
            String typeName = FILE_TYPE_NAMES.getOrDefault(fileTypeCode.toString(), "未知格式");
            scanFormatBuilder.append(typeName);
        }
        
        if (scanFormatBuilder.length() > 0) {
            metadata.put("scanFormat", scanFormatBuilder.toString());
            log.debug("生成扫描格式: {}", scanFormatBuilder.toString());
        }
    }
    
    /**
     * 添加倍率信息到扫描格式
     * 
     * @param metadata 元数据
     * @param scanFormatBuilder 扫描格式构建器
     */
    private void appendMagnificationInfo(Map<String, Object> metadata, StringBuilder scanFormatBuilder) {
        if (metadata.containsKey("objective")) {
            if (scanFormatBuilder.length() > 0) {
                scanFormatBuilder.append(" ");
            }
            scanFormatBuilder.append(metadata.get("objective")).append("X");
        }
    }
    
    /**
     * 处理TronSDK解析器的元数据
     * 
     * @param metadata 原始元数据
     */
    private void processTronSdkMetadata(Map<String, Object> metadata) {
        try {
            // TronSDK解析器已经在getSlideInfo中设置了objective和magnification字段
            // 这里只做字段验证和后续处理，不做兜底赋值
            // 提取objective作为扫描倍率
            extractObjectiveAsMagnification(metadata, "objective");
            // 处理MPP分辨率信息
            processTronSdkMppForResolution(metadata);
            // 处理扫描格式
            processScanFormatForTronSdk(metadata);
        } catch (Exception e) {
            log.error("处理TronSDK元数据时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理TronSDK的MPP分辨率信息
     * 
     * @param metadata 元数据
     */
    private void processTronSdkMppForResolution(Map<String, Object> metadata) {
        if (metadata.containsKey("mppX") && metadata.containsKey("mppY")) {
            Object mppX = metadata.get("mppX");
            Object mppY = metadata.get("mppY");
            
            if (mppX != null && mppY != null) {
                try {
                    double mppXValue = Double.parseDouble(mppX.toString().trim());
                    double mppYValue = Double.parseDouble(mppY.toString().trim());
                    
                    // 直接使用水平mpp作为分辨率(µm/Pixel)，保留15位小数
                    String resolutionStr = String.valueOf(mppXValue);
                    metadata.put("resolution", resolutionStr);
                    log.debug("使用mppX作为分辨率: {}", resolutionStr);
                    // 保存水平MPP值
                    metadata.put("mpp", mppXValue);
                    log.debug("保存mpp值: {}", mppXValue);
                } catch (NumberFormatException e) {
                    log.warn("无法解析TronSDK mpp值: x={}, y={}", mppX, mppY);
                }
            }
        }
        // Fallback: 若不存在mppX/mppY但有resolutionH/V
        else if (metadata.containsKey("resolutionH") && metadata.containsKey("resolutionV")) {
            try {
                double resH = Double.parseDouble(metadata.get("resolutionH").toString());
                double resV = Double.parseDouble(metadata.get("resolutionV").toString());
                String resolutionStr = String.valueOf(resH);
                metadata.put("resolution", resolutionStr);
                metadata.put("mpp", resH);
                log.debug("TronSDK fallback 使用resolutionH生成分辨率: {}", resolutionStr);
            } catch (Exception e) {
                log.warn("TronSDK fallback 解析resolutionH/V失败: {}", e.getMessage());
            }
        }
    }
    
    /**
     * 处理TronSDK的扫描格式
     * 
     * @param metadata 元数据
     */
    private void processScanFormatForTronSdk(Map<String, Object> metadata) {
        StringBuilder scanFormatBuilder = new StringBuilder();
        
        // 添加供应商信息
        if (metadata.containsKey("vendor")) {
            Object vendor = metadata.get("vendor");
            if (vendor != null && !vendor.toString().trim().isEmpty()) {
                scanFormatBuilder.append(vendor.toString());
            }
        }
        
        // 如果没有供应商信息，使用默认的TronSDK标识
        if (scanFormatBuilder.length() == 0) {
            scanFormatBuilder.append("TronSDK");
        }
        
        // 添加倍率信息
        appendMagnificationInfo(metadata, scanFormatBuilder);
        
        if (scanFormatBuilder.length() > 0) {
            metadata.put("scanFormat", scanFormatBuilder.toString());
            log.debug("生成TronSDK扫描格式: {}", scanFormatBuilder.toString());
        }
    }
    
    @Override
    public boolean supports(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        
        String extension = FilenameUtils.getExtension(fileName).toLowerCase();
        return SUPPORTED_EXTENSIONS.contains(extension);
    }
    
    /**
     * 使用UnifiedSlideService生成缩略图
     *
     * @param tempDir 临时目录
     * @param file 切片文件
     * @return 缩略图文件
     * @throws IOException 如果生成失败
     */
    private File createThumbnailImage(Path tempDir, File file) throws IOException {
        String extension = FilenameUtils.getExtension(file.getName()).toLowerCase();
        if ("tron".equals(extension)) {
            // Tron格式缩略图：命令行调用 tron_image_extractor
            byte[] thumbData = tronSdkImageExtractor.extractThumbnail(file.getAbsolutePath());
            if (thumbData != null && thumbData.length > 0) {
                File imageFile = tempDir.resolve("thumb_" + FilenameUtils.getBaseName(file.getName()) + ".jpeg").toFile();
                try (FileOutputStream fos = new FileOutputStream(imageFile)) {
                    fos.write(thumbData);
                    fos.flush();
                }
                return imageFile;
            } else {
                return createEmptyImage(tempDir, "thumb");
            }
        }
        // 其他格式保持原有逻辑
        return createImage(tempDir, file, "thumb_", unifiedSlideService::getThumbnailJpeg);
    }
    
    /**
     * 使用UnifiedSlideService生成标签图
     *
     * @param tempDir 临时目录
     * @param file 切片文件
     * @return 标签图文件
     * @throws IOException 如果生成失败
     */
    private File createLabelImage(Path tempDir, File file) throws IOException {
        String extension = FilenameUtils.getExtension(file.getName()).toLowerCase();
        if ("tron".equals(extension)) {
            // Tron格式标签图：命令行调用 tron_image_extractor
            byte[] labelData = tronSdkImageExtractor.extractLabel(file.getAbsolutePath());
            if (labelData != null && labelData.length > 0) {
                File imageFile = tempDir.resolve("label_" + FilenameUtils.getBaseName(file.getName()) + ".jpeg").toFile();
                try (FileOutputStream fos = new FileOutputStream(imageFile)) {
                    fos.write(labelData);
                    fos.flush();
                }
                return imageFile;
            } else {
                return createEmptyImage(tempDir, "label");
            }
        }
        // 其他格式保持原有逻辑
        log.debug("开始创建标签图，切片文件: {}, 临时目录: {}", file.getAbsolutePath(), tempDir);
        
        try {
            // 记录文件类型信息
            log.debug("文件扩展名: {}", extension);
            
            // 生成图像文件名
            String baseName = "label_" + FilenameUtils.getBaseName(file.getName());
            File imageFile = tempDir.resolve(baseName + ".jpeg").toFile();
            log.debug("标签图文件路径: {}", imageFile.getAbsolutePath());
            
            // 获取图像数据
            byte[] imageData = null;
            try {
                log.debug("调用UnifiedSlideService.getLabelJpeg获取标签图数据");
                imageData = unifiedSlideService.getLabelJpeg(file.getAbsolutePath());
                
                if (imageData == null) {
                    log.error("获取标签图数据为null");
                    return createEmptyImage(tempDir, "label");
                }
                
                log.debug("获取到标签图数据，大小: {} 字节", imageData.length);
                
                if (imageData.length <= 100) {
                    log.warn("标签图数据异常小: {} 字节，可能是空图像", imageData.length);
                }
            } catch (Exception e) {
                log.error("获取标签图失败: {}", e.getMessage(), e);
                return createEmptyImage(tempDir, "label");
            }
            
            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(imageFile)) {
                fos.write(imageData);
                fos.flush();
                log.debug("标签图数据已写入文件: {}, 大小: {} 字节", imageFile.getAbsolutePath(), imageData.length);
            } catch (IOException e) {
                log.error("写入标签图文件失败: {}", e.getMessage(), e);
                return createEmptyImage(tempDir, "label");
            }
            
            // 验证生成的图像文件
            if (!imageFile.exists() || imageFile.length() == 0) {
                log.error("标签图文件创建失败或为空: {}", imageFile.getAbsolutePath());
                return createEmptyImage(tempDir, "label");
            }
            
            log.debug("标签图创建成功: {}, 大小: {} 字节", imageFile.getAbsolutePath(), imageFile.length());
            return imageFile;
        } catch (Exception e) {
            log.error("创建标签图失败: {}", e.getMessage(), e);
            return createEmptyImage(tempDir, "label");
        }
    }
    
    /**
     * 使用UnifiedSlideService生成图像的通用方法
     *
     * @param tempDir 临时目录
     * @param file 切片文件
     * @param prefix 文件名前缀
     * @param imageSupplier 图像数据提供者
     * @return 图像文件
     * @throws IOException 如果生成失败
     */
    private File createImage(Path tempDir, File file, String prefix, ImageSupplier imageSupplier) throws IOException {
        try {
            // 生成图像文件名
            String baseName = prefix + FilenameUtils.getBaseName(file.getName());
            File imageFile = tempDir.resolve(baseName + ".jpeg").toFile();
            log.debug("生成{}图像文件路径: {}", prefix, imageFile.getAbsolutePath());
            
            // 获取图像数据
            byte[] imageData = null;
            try {
                log.debug("调用图像提供者获取{}数据", prefix);
                imageData = imageSupplier.getImageJpeg(file.getAbsolutePath());
                if (imageData == null || imageData.length == 0) {
                    log.error("生成{}失败，imageData为空或长度为0: {}", prefix, file.getAbsolutePath());
                    return createEmptyImage(tempDir, prefix);
                }
                log.debug("获取到{}数据，大小: {} 字节", prefix, imageData.length);
            } catch (Exception e) {
                log.error("生成{}失败: {}", prefix, e.getMessage(), e);
                return createEmptyImage(tempDir, prefix);
            }
            
            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(imageFile)) {
                fos.write(imageData);
                fos.flush();
                log.debug("{}数据已写入文件: {}, 大小: {} 字节", prefix, imageFile.getAbsolutePath(), imageData.length);
            } catch (IOException e) {
                log.error("写入{}文件失败: {}", prefix, e.getMessage(), e);
                return createEmptyImage(tempDir, prefix);
            }
            
            // 验证生成的图像文件
            if (!imageFile.exists() || imageFile.length() == 0) {
                log.error("{}文件创建失败或为空: {}", prefix, imageFile.getAbsolutePath());
                return createEmptyImage(tempDir, prefix);
            }
            
            log.debug("{}创建成功: {}, 大小: {} 字节", prefix, imageFile.getAbsolutePath(), imageFile.length());
            return imageFile;
        } catch (Exception e) {
            log.error("生成{}图像失败: {}", prefix, e.getMessage(), e);
            return createEmptyImage(tempDir, prefix);
        }
    }
    
    /**
     * 图像数据提供者接口
     */
    @FunctionalInterface
    private interface ImageSupplier {
        byte[] getImageJpeg(String filePath) throws Exception;
    }
    
    /**
     * 根据文件扩展名确定文件类型代码
     *
     * @param extension 文件扩展名
     * @return 文件类型代码
     */
    private Integer determineFileTypeCode(String extension) {
        return FILE_TYPE_CODES.getOrDefault(extension.toLowerCase(), 0);
    }
    
    /**
     * 根据文件扩展名确定制造商代码
     *
     * @param extension 文件扩展名
     * @return 制造商代码
     */
    private Integer determineManufacturerCode(String extension) {
        return MANUFACTURER_CODES.getOrDefault(extension.toLowerCase(), 0);
    }

    /**
     * 创建一个空的图像文件，用于处理异常情况
     *
     * @param tempDir 临时目录
     * @param prefix 文件前缀
     * @return 创建的空图像文件
     */
    private File createEmptyImage(Path tempDir, String prefix) {
        try {
            // 创建一个1x1像素的空白图像
            BufferedImage emptyImage = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB);
            File emptyFile = tempDir.resolve(prefix + "-empty.jpg").toFile();
            ImageIO.write(emptyImage, "jpg", emptyFile);
            return emptyFile;
        } catch (Exception e) {
            log.error("创建空图像失败: {}", e.getMessage(), e);
            try {
                // 最后的尝试，创建一个空文件
                File emptyFile = tempDir.resolve(prefix + "-empty.jpg").toFile();
                emptyFile.createNewFile();
                return emptyFile;
            } catch (Exception ex) {
                log.error("创建空文件失败: {}", ex.getMessage(), ex);
                return null;
            }
        }
    }

    private File createSlideImage(Path tempDir, File file) throws IOException {
        return createImage(tempDir, file, "slide_", unifiedSlideService::getSlideJpeg);
    }
} 
