package cn.ccaa.slice.service.upload.validation;

import cn.ccaa.slice.core.upload.UploadTask;

/**
 * 文件验证器接口
 * 负责验证上传文件的类型、大小和内容
 *
 * <AUTHOR>
 */
public interface FileValidator {
    
    /**
     * 验证文件名和大小
     *
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @throws cn.ccaa.slice.core.exception.BusinessException 如果验证失败
     */
    void validateFile(String fileName, long fileSize);
    
    /**
     * 验证文件类型
     *
     * @param fileName 文件名
     * @throws cn.ccaa.slice.core.exception.BusinessException 如果验证失败
     */
    void validateFileType(String fileName);
    
    /**
     * 验证文件大小
     *
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @throws cn.ccaa.slice.core.exception.BusinessException 如果验证失败
     */
    void validateFileSize(String fileName, long fileSize);
    
    /**
     * 验证已上传的文件
     * 检查文件是否完整、格式是否正确等
     *
     * @param task 上传任务
     * @return 如果验证通过返回true，否则返回false
     */
    boolean validateUploadedFile(UploadTask task);
}
