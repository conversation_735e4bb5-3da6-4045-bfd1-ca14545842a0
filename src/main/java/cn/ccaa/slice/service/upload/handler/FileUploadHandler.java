package cn.ccaa.slice.service.upload.handler;

import java.io.InputStream;
import java.util.concurrent.CompletableFuture;

import org.springframework.web.multipart.MultipartFile;

import cn.ccaa.slice.core.upload.UploadTask;

/**
 * 文件上传处理器接口
 * 负责处理文件上传逻辑
 *
 * <AUTHOR>
 */
public interface FileUploadHandler {
    
    /**
     * 上传完整文件
     *
     * @param task 上传任务
     * @param file 文件
     * @return 更新后的上传任务
     */
    UploadTask uploadFile(UploadTask task, MultipartFile file);
    
    /**
     * 通过输入流上传文件
     *
     * @param task 上传任务
     * @param inputStream 输入流
     * @param contentType 内容类型
     * @return 更新后的上传任务
     */
    UploadTask uploadFile(UploadTask task, InputStream inputStream, String contentType);
    
    /**
     * 上传文件分块
     *
     * @param task 上传任务
     * @param chunk 分块
     * @param chunkIndex 分块索引
     * @param totalChunks 总分块数
     * @return 更新后的上传任务
     */
    UploadTask uploadFileChunk(UploadTask task, MultipartFile chunk, int chunkIndex, int totalChunks);
    
    /**
     * 异步上传文件
     *
     * @param task 上传任务
     * @param file 文件
     * @return 异步上传的Future
     */
    CompletableFuture<UploadTask> asyncUploadFile(UploadTask task, MultipartFile file);
    
    /**
     * 完成上传
     *
     * @param task 上传任务
     * @return 更新后的上传任务
     */
    UploadTask completeUpload(UploadTask task);
    
    /**
     * 取消上传
     *
     * @param task 上传任务
     */
    void cancelUpload(UploadTask task);
}
