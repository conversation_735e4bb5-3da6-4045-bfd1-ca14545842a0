package cn.ccaa.slice.service.upload.temp;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Path;

import org.springframework.web.multipart.MultipartFile;

/**
 * 临时文件管理器接口
 * 负责管理上传过程中的临时文件
 *
 * <AUTHOR>
 */
public interface TempFileManager {
    
    /**
     * 初始化临时目录
     */
    void initTempDirectory();
    
    /**
     * 获取临时目录路径
     *
     * @return 临时目录路径
     */
    Path getTempDirectory();
    
    /**
     * 创建临时文件
     *
     * @param taskId 任务ID
     * @param fileName 文件名
     * @return 临时文件路径
     */
    Path createTempFile(String taskId, String fileName);
    
    /**
     * 保存MultipartFile到临时文件
     *
     * @param file MultipartFile对象
     * @param taskId 任务ID
     * @return 临时文件路径
     */
    Path saveToTempFile(MultipartFile file, String taskId);
    
    /**
     * 保存InputStream到临时文件
     *
     * @param inputStream 输入流
     * @param taskId 任务ID
     * @param fileName 文件名
     * @return 临时文件路径
     */
    Path saveToTempFile(InputStream inputStream, String taskId, String fileName);
    
    /**
     * 保存文件块到临时文件
     *
     * @param chunk 文件块
     * @param taskId 任务ID
     * @param chunkIndex 块索引
     * @return 临时块文件路径
     */
    Path saveChunkToTempFile(MultipartFile chunk, String taskId, int chunkIndex);
    
    /**
     * 合并文件块
     *
     * @param taskId 任务ID
     * @param fileName 文件名
     * @param totalChunks 总块数
     * @return 合并后的文件路径
     */
    Path mergeChunks(String taskId, String fileName, int totalChunks);
    
    /**
     * 清理任务的临时文件
     *
     * @param taskId 任务ID
     */
    void cleanupTempFiles(String taskId);
    
    /**
     * 定期清理过期的临时文件
     */
    void cleanupExpiredTempFiles();
    
    /**
     * 获取任务的临时目录
     *
     * @param taskId 任务ID
     * @return 任务临时目录
     */
    Path getTaskTempDirectory(String taskId);
    
    /**
     * 检查临时文件是否存在
     *
     * @param taskId 任务ID
     * @param fileName 文件名
     * @return 如果存在返回true，否则返回false
     */
    boolean tempFileExists(String taskId, String fileName);
    
    /**
     * 获取临时文件
     *
     * @param taskId 任务ID
     * @param fileName 文件名
     * @return 临时文件对象
     */
    File getTempFile(String taskId, String fileName);
}
