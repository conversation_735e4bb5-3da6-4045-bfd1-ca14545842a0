package cn.ccaa.slice.service.upload;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import cn.ccaa.slice.config.CallbackConfig;
import cn.ccaa.slice.config.StorageConfig;
import cn.ccaa.slice.core.model.PartnerPlatformEntity;
import cn.ccaa.slice.core.model.SliceEntity;
import cn.ccaa.slice.service.PartnerPlatformService;
import cn.ccaa.slice.service.SliceService;

/**
 * 切片解析信息推送服务
 * 
 * <AUTHOR>
 */
@Service
public class SliceAnalysisCallbackService {
    
    private static final Logger log = LoggerFactory.getLogger(SliceAnalysisCallbackService.class);
    
    @Autowired
    private PartnerPlatformService partnerPlatformService;
    
    @Autowired
    private SliceService sliceService;
    
    @Autowired
    private CallbackConfig callbackConfig;
    
    @Autowired
    private StorageConfig storageConfig;
    
    @Autowired
    private RestTemplate restTemplate;
    
    /**
     * 推送切片解析信息到第三方平台（向后兼容方法）
     * 
     * @param taskId 任务ID（穿线标识，可能是sliceId或主键id）
     * @param partnerCode 对接产品代码
     * @return 推送是否成功
     */
    public boolean pushSliceAnalysisInfo(String taskId, String partnerCode) {
        return pushSliceAnalysisInfo(taskId, partnerCode, null);
    }

    /**
     * 推送切片解析信息到第三方平台
     * 
     * @param taskId 任务ID（穿线标识，可能是sliceId或主键id）
     * @param partnerCode 对接产品代码
     * @param caseId 病例ID（可选，如果提供则推送给第三方）
     * @return 推送是否成功
     */
    public boolean pushSliceAnalysisInfo(String taskId, String partnerCode, String caseId) {
        try {
            log.info("开始推送切片解析信息: taskId={}, partnerCode={}", taskId, partnerCode);
            
            // 修改判断逻辑：只有partnerCode为null或空字符串时才跳过推送
            if (partnerCode == null || partnerCode.trim().isEmpty()) {
                log.info("partnerCode为空，跳过推送: taskId={}", taskId);
                return true; // 返回true表示跳过推送但不算失败
            }
            
            // 获取平台配置
            PartnerPlatformEntity platform = partnerPlatformService.getByPartnerCode(partnerCode);
            if (platform == null || platform.getUrl() == null) {
                log.warn("未找到 partnerCode={} 的平台URL，跳过推送", partnerCode);
                return false;
            }
            
            // 构建回调URL
            String callbackUrl = buildCallbackUrl(platform.getUrl());
            log.info("构建的回调URL: {}", callbackUrl);
            
            // 获取切片信息 - 支持不同场景的查询方式
            SliceEntity slice = null;
            try {
                // 先尝试按sliceId查询（蚂蚁上传场景）
                slice = sliceService.getBySliceId(taskId);
                if (slice == null) {
                    // 如果按sliceId查不到，再按主键id查询（本地上传和切片池上传场景）
                    slice = sliceService.getById(taskId);
                    log.info("按主键id查询到切片信息: 穿线标识(主键id)={}, 实际taskId={}", 
                            taskId, slice != null ? slice.getTaskId() : "null");
                } else {
                    log.info("按sliceId查询到切片信息: 穿线标识(sliceId)={}", taskId);
                }
            } catch (Exception e) {
                log.warn("查询切片记录失败，尝试按主键id查询: 穿线标识={}, error={}", taskId, e.getMessage());
                slice = sliceService.getById(taskId);
            }
            
            if (slice == null) {
                log.error("未找到切片信息: 穿线标识={}", taskId);
                return false;
            }
            
            log.info("获取到切片信息: sliceId={}, physicPath={}, qpName={}, tempAuth={}", 
                    slice.getSliceId(), slice.getPhysicPath(), slice.getQpName(), 
                    slice.getTempAuth() != null ? "有值(长度:" + slice.getTempAuth().length() + ")" : "无值");
            
            // 查找缩略图文件
            File thumbFile = findThumbnailFile(slice);
            if (thumbFile == null || !thumbFile.exists()) {
                log.error("未找到缩略图文件: 穿线标识={}, thumbFile={}", taskId, 
                        thumbFile != null ? thumbFile.getAbsolutePath() : "null");
                return false;
            }
            
            log.info("找到缩略图文件: {}, 大小: {} bytes", thumbFile.getAbsolutePath(), thumbFile.length());
            
            // 查找标签图文件（可选）
            File labelFile = findLabelFile(slice);
            if (labelFile != null && labelFile.exists()) {
                log.info("找到标签图文件: {}, 大小: {} bytes", labelFile.getAbsolutePath(), labelFile.length());
            } else {
                log.warn("未找到标签图文件: 穿线标识={}", taskId);
            }
            
            // 查找玻片图文件（可选）
            File slideFile = findSlideFile(slice);
            if (slideFile != null && slideFile.exists()) {
                log.info("找到玻片图文件: {}, 大小: {} bytes", slideFile.getAbsolutePath(), slideFile.length());
            } else {
                log.warn("未找到玻片图文件: 穿线标识={}", taskId);
            }
            
            // 构建请求参数
            MultiValueMap<String, Object> requestBody = buildRequestBody(slice, thumbFile, labelFile, slideFile, caseId);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            if (slice.getTempAuth() != null && !slice.getTempAuth().isEmpty()) {
                headers.set("Authorization", slice.getTempAuth());
                log.info("设置Authorization头: 长度={}", slice.getTempAuth().length());
            } else {
                log.warn("temp_auth为空，未设置Authorization头: 穿线标识={}", taskId);
            }
            
            // 发送请求
            HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            // 打印Info调用参数（简化格式）
            log.info("Info接口调用 - URL: {}", callbackUrl);
            Map<String, Object> logParams = new HashMap<>();
            for (String key : requestBody.keySet()) {
                Object value = requestBody.getFirst(key);
                if (value instanceof FileSystemResource) {
                    FileSystemResource fileResource = (FileSystemResource) value;
                    logParams.put(key, "[文件]" + fileResource.getPath() + "(" + fileResource.getFile().length() + "bytes)");
                } else {
                    logParams.put(key, value);
                }
            }
            log.info("Info接口调用 - 请求参数: {}", logParams);
            
            ResponseEntity<String> response = restTemplate.postForEntity(callbackUrl, entity, String.class);
            
            log.info("切片解析信息推送响应: url={}, status={}, response={}", 
                    callbackUrl, response.getStatusCode(), response.getBody());
            
            return response.getStatusCode().is2xxSuccessful();
            
        } catch (Exception e) {
            log.error("推送切片解析信息失败: taskId={}, partnerCode={}, error={}", 
                    taskId, partnerCode, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 构建回调URL
     */
    private String buildCallbackUrl(String baseUrl) {
        String callbackUrl = baseUrl;
        String infoPath = callbackConfig.getInfoPath();
        
        if (!callbackUrl.endsWith("/") && (infoPath == null || !infoPath.startsWith("/"))) {
            callbackUrl += "/";
        }
        
        if (infoPath != null) {
            callbackUrl += infoPath;
        } else {
            callbackUrl += "Info";
        }
        
        return callbackUrl;
    }
    
    /**
     * 查找标签图文件
     */
    private File findLabelFile(SliceEntity slice) {
        try {
            log.debug("开始查找标签图文件: sliceId={}", slice.getSliceId());
            
            // 根据physic_path查找标签图文件
            String physicPath = slice.getPhysicPath();
            if (physicPath == null || physicPath.isEmpty()) {
                log.error("physic_path为空，无法查找标签图文件: sliceId={}", slice.getSliceId());
                return null;
            }
            
            log.debug("使用physic_path查找: {}", physicPath);
            
            // 构建完整的切片文件路径
            Path sliceFilePath = Paths.get(storageConfig.getLocalSliceDir(), physicPath);
            Path parentDir = sliceFilePath.getParent();
            
            log.debug("切片文件路径: {}, 父目录: {}", sliceFilePath, parentDir);
            
            if (parentDir == null || !Files.exists(parentDir)) {
                log.error("切片文件父目录不存在: {}", parentDir);
                return null;
            }
            
            // 获取切片文件名（不含扩展名）
            String sliceFileName = sliceFilePath.getFileName().toString();
            String baseName = sliceFileName;
            int dotIdx = sliceFileName.lastIndexOf('.');
            if (dotIdx > 0) {
                baseName = sliceFileName.substring(0, dotIdx);
            }
            
            // 构建标签图文件路径：label_ + 切片文件名（不含扩展名） + .jpeg
            File labelFile = parentDir.resolve("label_" + baseName + ".jpeg").toFile();
            
            log.debug("构建的标签图路径: {}, 存在: {}", labelFile.getAbsolutePath(), labelFile.exists());
            
            if (labelFile.exists()) {
                return labelFile;
            } else {
                log.warn("标签图文件不存在: {}", labelFile.getAbsolutePath());
                return null;
            }
            
        } catch (Exception e) {
            log.error("查找标签图文件失败: sliceId={}, error={}", slice.getSliceId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查找缩略图文件
     */
    private File findThumbnailFile(SliceEntity slice) {
        try {
            log.debug("开始查找缩略图文件: sliceId={}", slice.getSliceId());
            
            // 根据physic_path查找缩略图文件
            String physicPath = slice.getPhysicPath();
            if (physicPath == null || physicPath.isEmpty()) {
                log.error("physic_path为空，无法查找缩略图文件: sliceId={}", slice.getSliceId());
                return null;
            }
            
            log.debug("使用physic_path查找: {}", physicPath);
            
            // 构建完整的切片文件路径
            Path sliceFilePath = Paths.get(storageConfig.getLocalSliceDir(), physicPath);
            Path parentDir = sliceFilePath.getParent();
            
            log.debug("切片文件路径: {}, 父目录: {}", sliceFilePath, parentDir);
            
            if (parentDir == null || !Files.exists(parentDir)) {
                log.error("切片文件父目录不存在: {}", parentDir);
                return null;
            }
            
            // 获取切片文件名（不含扩展名）
            String sliceFileName = sliceFilePath.getFileName().toString();
            String baseName = sliceFileName;
            int dotIdx = sliceFileName.lastIndexOf('.');
            if (dotIdx > 0) {
                baseName = sliceFileName.substring(0, dotIdx);
            }
            
            // 构建缩略图文件路径：thumb_ + 切片文件名（不含扩展名） + .jpeg
            File thumbFile = parentDir.resolve("thumb_" + baseName + ".jpeg").toFile();
            
            log.debug("构建的缩略图路径: {}, 存在: {}", thumbFile.getAbsolutePath(), thumbFile.exists());
            
            if (thumbFile.exists()) {
                return thumbFile;
            } else {
                log.error("缩略图文件不存在: {}", thumbFile.getAbsolutePath());
                return null;
            }
            
        } catch (Exception e) {
            log.error("查找缩略图文件失败: sliceId={}, error={}", slice.getSliceId(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 构建请求体
     */
    private MultiValueMap<String, Object> buildRequestBody(SliceEntity slice, File thumbFile, File labelFile, File slideFile, String caseId) {
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        
        // 获取实际的切片文件大小
        long actualFileSize = 0;
        String slicePhysicPath = slice.getPhysicPath();
        if (slicePhysicPath != null && !slicePhysicPath.isEmpty()) {
            try {
                Path sliceFilePath = Paths.get(storageConfig.getLocalSliceDir(), slicePhysicPath);
                if (Files.exists(sliceFilePath)) {
                    actualFileSize = Files.size(sliceFilePath);
                    log.debug("从实际文件获取大小: {} bytes", actualFileSize);
                } else {
                    log.warn("切片文件不存在，使用数据库中的大小: {}", sliceFilePath);
                    actualFileSize = slice.getQpSize() != null ? slice.getQpSize().longValue() : 0L;
                }
            } catch (Exception e) {
                log.warn("获取切片文件大小失败，使用数据库中的大小: {}", e.getMessage());
                actualFileSize = slice.getQpSize() != null ? slice.getQpSize().longValue() : 0L;
            }
        } else {
            actualFileSize = slice.getQpSize() != null ? slice.getQpSize().longValue() : 0L;
        }
        
        // 根据上传场景确定推送参数
        String pushParam = null;
        String scenarioName = "";
        
        if (slice.getTaskId() != null && !slice.getTaskId().isEmpty()) {
            // 本地上传场景：推送taskId
            pushParam = slice.getTaskId();
            scenarioName = "本地上传";
            body.add("taskId", pushParam);
            log.info("Info推送-本地上传场景，推送实际taskId: {}, 数据库主键id: {}", pushParam, slice.getId());
        } else if (slice.getSliceId() != null && !slice.getSliceId().isEmpty()) {
            // 蚂蚁上传场景：推送sliceId字段
            pushParam = slice.getSliceId();
            scenarioName = "蚂蚁上传";
            body.add("sliceId", pushParam); // 修改：推送sliceId字段，而不是taskId字段
            log.info("Info推送-蚂蚁上传场景，推送sliceId: {}", pushParam);
        } else {
            // 切片池上传场景：只推送fileId，不推送taskId
            pushParam = slice.getId();
            scenarioName = "切片池上传";
            // 不添加taskId字段，只推送fileId
            log.info("Info推送-切片池上传场景，只推送fileId: {}", slice.getId());
        }
        
        body.add("fileId", slice.getId()); // 切片服务给切片文件颁发的唯一id
        body.add("qpName", slice.getQpName()); // 切片名称
        body.add("qpSize", String.valueOf(actualFileSize)); // 文件大小 - 使用实际文件大小
        body.add("enlarge", slice.getEnlarge() != null ? slice.getEnlarge() : ""); // 倍率
        body.add("qpState", "普通上传"); // 切片状态：默认普通上传
        
        // 注释掉病例ID参数 - 根据赵总需求，Info接口调用时不传递caseId
        /*
        if (caseId != null && !caseId.isEmpty()) {
            body.add("caseId", caseId);
            log.info("Info推送-添加病例ID参数: caseId={}", caseId);
        }
        */
        
        // 新增：像素宽高与分辨率
        if (slice.getOrigPixWidth() != null) {
            body.add("origPixWidth", slice.getOrigPixWidth().toString());
        }
        if (slice.getOrigPixHeight() != null) {
            body.add("origPixHeight", slice.getOrigPixHeight().toString());
        }
        if (slice.getResolution() != null && !slice.getResolution().isEmpty()) {
            body.add("resolution", slice.getResolution());
        }
        
        // 新增：切片层级数量
        if (slice.getLevelsCnt() != null) {
            body.add("levelsCnt", slice.getLevelsCnt().toString());
            log.info("Info推送-添加层级数量参数: levelsCnt={}", slice.getLevelsCnt());
        } else {
            log.warn("Info推送-层级数量为空，未添加levelsCnt参数: 穿线标识={}", slice.getId());
        }
        
        // 时间戳
        String timestamp = String.valueOf(System.currentTimeMillis());
        body.add("timestamp", timestamp);
        
        // 物理路径和相对路径
        String physicPath = slice.getPhysicPath();
        if (physicPath != null) {
            // 绝对路径
            String absolutePath = Paths.get(storageConfig.getLocalSliceDir(), physicPath).toString();
            body.add("physicalPath", absolutePath);
            // 相对路径
            body.add("relativePath", physicPath);
        }
        
        // 缩略图文件
        body.add("thumb", new FileSystemResource(thumbFile));
        
        // 标签图文件（可选）
        if (labelFile != null && labelFile.exists()) {
            body.add("label", new FileSystemResource(labelFile));
        }
        
        // 玻片图文件（可选）
        if (slideFile != null && slideFile.exists()) {
            body.add("slideImage", new FileSystemResource(slideFile));
        }
        
        // 生成签名
        String sign = generateSign(slice, timestamp, actualFileSize);
        body.add("sign", sign);
        
        log.info("Info推送参数构建完成: scenario={}, pushParam={}", scenarioName, pushParam);
        
        return body;
    }
    
    /**
     * 生成签名
     * 包含Key：qpViewId，fileId，qpSize，enlarge，qpState，timestamp，appCode,appPrivateKey
     * 参数按照参数名ASCII码从小到大排序（字典序）
     * 排序后 key与value用=连接，每一组用&连接
     * 排序后的字符串，使用SHA256Hash 加密得 sign
     */
    private String generateSign(SliceEntity slice, String timestamp, long actualFileSize) {
        try {
            // 构建签名参数
            TreeMap<String, String> signParams = new TreeMap<>();
            
            // 添加签名参数（按字典序自动排序）
            signParams.put("qpViewId", slice.getSliceId()); // 使用sliceId作为qpViewId
            signParams.put("fileId", slice.getId());
            signParams.put("qpSize", String.valueOf(actualFileSize)); // 使用实际文件大小
            signParams.put("enlarge", slice.getEnlarge() != null ? slice.getEnlarge() : "");
            signParams.put("qpState", "普通上传");
            signParams.put("timestamp", timestamp);
            signParams.put("appCode", "ccaa-slice"); // 默认应用代码
            signParams.put("appPrivateKey", "defaultPrivateKey"); // 默认私钥
            
            // 构建签名字符串
            StringBuilder signStr = new StringBuilder();
            for (Map.Entry<String, String> entry : signParams.entrySet()) {
                if (signStr.length() > 0) {
                    signStr.append("&");
                }
                signStr.append(entry.getKey()).append("=").append(entry.getValue());
            }
            
            log.debug("签名字符串: {}", signStr.toString());
            
            // SHA256加密
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(signStr.toString().getBytes("UTF-8"));
            
            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
            
        } catch (Exception e) {
            log.error("生成签名失败: {}", e.getMessage(), e);
            return "defaultSign"; // 返回默认签名
        }
    }

    /**
     * 查找玻片图文件
     */
    private File findSlideFile(SliceEntity slice) {
        try {
            log.debug("开始查找玻片图文件: sliceId={}", slice.getSliceId());
            String physicPath = slice.getPhysicPath();
            if (physicPath == null || physicPath.isEmpty()) {
                log.error("physic_path为空，无法查找玻片图文件: sliceId={}", slice.getSliceId());
                return null;
            }
            Path sliceFilePath = Paths.get(storageConfig.getLocalSliceDir(), physicPath);
            Path parentDir = sliceFilePath.getParent();
            if (parentDir == null || !Files.exists(parentDir)) {
                log.error("切片文件父目录不存在: {}", parentDir);
                return null;
            }
            String sliceFileName = sliceFilePath.getFileName().toString();
            String baseName = sliceFileName.contains(".") ? sliceFileName.substring(0, sliceFileName.lastIndexOf('.')) : sliceFileName;
            File slideFile = parentDir.resolve("slide_" + baseName + ".jpeg").toFile();
            log.debug("构建的玻片图路径: {}, 存在: {}", slideFile.getAbsolutePath(), slideFile.exists());
            return slideFile.exists() ? slideFile : null;
        } catch (Exception e) {
            log.error("查找玻片图文件失败: sliceId={}, error={}", slice.getSliceId(), e.getMessage(), e);
            return null;
        }
    }
} 