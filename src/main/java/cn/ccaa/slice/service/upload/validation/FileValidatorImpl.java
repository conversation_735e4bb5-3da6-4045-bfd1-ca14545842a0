package cn.ccaa.slice.service.upload.validation;

import java.util.Optional;

import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;

import cn.ccaa.slice.config.StorageConfig;
import cn.ccaa.slice.core.exception.BusinessException;
import cn.ccaa.slice.core.parser.SlideParser;
import cn.ccaa.slice.core.upload.UploadTask;
import cn.ccaa.slice.parsers.openslide.OpenSlideParserAdapter;
import cn.ccaa.slice.service.UnifiedSlideService;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件验证器实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FileValidatorImpl implements FileValidator {
    
    private final StorageConfig storageConfig;
    private final UnifiedSlideService unifiedSlideService;
    
    /**
     * 构造函数
     *
     * @param storageConfig 存储配置
     * @param unifiedSlideService 统一切片服务
     */
    public FileValidatorImpl(StorageConfig storageConfig, UnifiedSlideService unifiedSlideService) {
        this.storageConfig = storageConfig;
        this.unifiedSlideService = unifiedSlideService;
    }
    
    @Override
    public void validateFile(String fileName, long fileSize) {
        validateFileType(fileName);
        validateFileSize(fileName, fileSize);
    }
    
    @Override
    public void validateFileType(String fileName) {
        String extension = FilenameUtils.getExtension(fileName);
        // 支持的文件类型：svs, ndpi, sdpc
        String[] allowedTypes = {"svs", "ndpi", "sdpc"};
        boolean isAllowed = false;
        for (String type : allowedTypes) {
            if (type.equalsIgnoreCase(extension)) {
                isAllowed = true;
                break;
            }
        }
        
        if (!isAllowed) {
            throw new BusinessException("不支持的文件类型：" + extension + "，支持的类型：svs, ndpi, sdpc");
        }
    }
    
    @Override
    public void validateFileSize(String fileName, long fileSize) {
        // 文件大小限制：2GB
        long maxSize = 2L * 1024 * 1024 * 1024; // 2GB
        long maxSizeInMB = maxSize / 1024 / 1024;
        if (fileSize > maxSize) {
            throw new BusinessException("文件大小超过限制，最大允许：" + maxSizeInMB + "MB");
        }
    }
    
    @Override
    public boolean validateUploadedFile(UploadTask task) {
        if (task == null) {
            log.error("无法验证文件，任务为空");
            return false;
        }
        
        String fileName = task.getFileName();
        String storagePath = task.getProgress().getStoragePath();
        
        try {
            log.info("开始验证上传文件: {}", storagePath);
            
            // 如果是切片文件（.svs或.sdpc格式），进行特殊验证
            if (fileName.endsWith(".svs") || fileName.endsWith(".ndpi") ||
                fileName.endsWith(".tif") || fileName.endsWith(".tiff") ||
                fileName.endsWith(".sdpc")) {
                
                log.info("检测到切片文件，进行格式验证: {}", fileName);
                
                // 使用UnifiedSlideService检测文件格式
                String format = unifiedSlideService.detectFormat(storagePath);
                if (format == null) {
                    log.warn("文件格式检测失败，可能不是有效的切片文件: {}", storagePath);
                    return false;
                }
                
                // 如果是openslide格式，进行深度检查
                if ("openslide".equals(format)) {
                    // 获取OpenSlide解析器
                    Optional<SlideParser> parser = unifiedSlideService.getAllParsers().stream()
                            .filter(p -> "openslide".equals(p.getParserName()))
                            .findFirst();
                    
                    if (parser.isPresent() && parser.get() instanceof OpenSlideParserAdapter) {
                        // 使用深度检查方法验证文件
                        OpenSlideParserAdapter openSlideParser = (OpenSlideParserAdapter) parser.get();
                        boolean valid = openSlideParser.deepCheckFile(storagePath);
                        
                        if (!valid) {
                            log.warn("深度检查失败，文件可能损坏或不完整: {}", storagePath);
                            return false;
                        }
                    }
                }
            }
            
            // 文件验证通过
            log.info("文件验证通过: {}", storagePath);
            return true;
        } catch (Exception e) {
            log.error("验证上传文件时发生异常: {}, 错误: {}", task.getProgress().getStoragePath(), e.getMessage(), e);
            return false;
        }
    }
}
