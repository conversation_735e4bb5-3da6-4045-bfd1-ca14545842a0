package cn.ccaa.slice.service.upload.task;

import java.util.List;
import java.util.Map;

import cn.ccaa.slice.core.upload.UploadStatus;
import cn.ccaa.slice.core.upload.UploadTask;

/**
 * 上传任务管理器接口
 * 负责上传任务的创建、获取、更新和删除
 *
 * <AUTHOR>
 */
public interface UploadTaskManager {

    /**
     * 用自定义taskId创建上传任务（如sliceId）
     *
     * @param taskId 任务ID
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @param userId 用户ID
     * @return 上传任务
     */
    UploadTask createTask(String taskId, String fileName, long fileSize, String userId);

    /**
     * 获取上传任务
     *
     * @param taskId 任务ID
     * @return 上传任务，如果不存在则返回null
     */
    UploadTask getTask(String taskId);

    /**
     * 批量获取上传任务
     *
     * @param taskIds 任务ID列表
     * @return 上传任务映射，key为taskId，value为对应的上传任务
     */
    Map<String, UploadTask> batchGetTasks(List<String> taskIds);

    /**
     * 更新任务状态
     *
     * @param task 上传任务
     * @param status 新状态
     */
    void updateTaskStatus(UploadTask task, UploadStatus status);

    /**
     * 更新任务进度
     *
     * @param task 上传任务
     * @param uploadedBytes 已上传字节数
     */
    void updateTaskProgress(UploadTask task, long uploadedBytes);

    /**
     * 更新任务详细信息
     *
     * @param taskId 任务ID
     * @param uploadedBytes 已上传字节数
     * @param status 上传状态
     * @param fileUrl 文件URL（可选）
     * @param errorMessage 错误信息（可选）
     * @param completed 是否完成（可选）
     * @return 更新后的上传任务
     */
    UploadTask updateTaskDetails(String taskId, Long uploadedBytes, String status,
                             String fileUrl, String errorMessage, Boolean completed);

    /**
     * 完成上传任务
     *
     * @param taskId 任务ID
     * @return 更新后的上传任务
     */
    UploadTask completeTask(String taskId);

    /**
     * 取消上传任务
     *
     * @param taskId 任务ID
     */
    void cancelTask(String taskId);

    /**
     * 标记任务为失败
     *
     * @param task 上传任务
     * @param errorMessage 错误信息
     * @return 更新后的上传任务
     */
    UploadTask markTaskAsFailed(UploadTask task, String errorMessage);

    /**
     * 清理过期任务
     */
    void cleanExpiredTasks();

    /**
     * 根据用户ID获取任务列表
     *
     * @param userId 用户ID
     * @return 上传任务列表
     */
    List<UploadTask> getTasksByUserId(String userId);
}
