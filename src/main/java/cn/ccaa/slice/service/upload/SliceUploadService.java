package cn.ccaa.slice.service.upload;

import org.springframework.web.multipart.MultipartFile;

import cn.ccaa.slice.core.model.SliceInfo;
import cn.ccaa.slice.core.result.Result;

/**
 * 切片上传服务接口
 * 用于处理切片文件上传、解析、存储
 *
 * <AUTHOR>
 */
public interface SliceUploadService {
    
    /**
     * 上传并解析切片文件
     * 1. 解析切片文件
     * 2. 提取缩略图和标签图
     * 3. 上传文件到Minio
     * 4. 保存切片信息
     *
     * @param file 原始文件
     * @param taskId 任务ID
     * @return 切片信息
     */
    Result<SliceInfo> uploadAndAnalyzeSlice(MultipartFile file, String taskId);
    
    /**
     * 异步上传并解析切片文件
     * 1. 异步解析切片文件
     * 2. 提取缩略图和标签图
     * 3. 异步上传文件到Minio
     * 4. 保存切片信息
     *
     * @param file 原始文件
     * @param taskId 任务ID
     * @return 立即返回任务已提交的结果
     */
    Result<?> asyncUploadAndAnalyzeSlice(MultipartFile file, String taskId);
    
    /**
     * 异步上传并解析切片文件（支持partnerCode）
     * 1. 异步解析切片文件
     * 2. 提取缩略图和标签图
     * 3. 异步上传文件到Minio
     * 4. 保存切片信息
     * 5. 推送切片解析信息到第三方平台
     *
     * @param file 原始文件
     * @param taskId 任务ID
     * @param partnerCode 对接产品代码
     * @return 立即返回任务已提交的结果
     */
    Result<?> asyncUploadAndAnalyzeSlice(MultipartFile file, String taskId, String partnerCode);

    /**
     * 取消切片处理任务
     *
     * @param taskId 任务ID
     * @return 如果成功取消返回true，否则返回false
     */
    boolean cancelSliceProcessing(String taskId);
} 
