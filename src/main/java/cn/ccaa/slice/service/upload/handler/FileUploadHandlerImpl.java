package cn.ccaa.slice.service.upload.handler;

import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.file.Path;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.BiConsumer;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import cn.ccaa.slice.core.exception.BusinessException;
import cn.ccaa.slice.core.upload.UploadStatus;
import cn.ccaa.slice.core.upload.UploadTask;
import cn.ccaa.slice.service.storage.StorageEventPublisher;
import cn.ccaa.slice.service.storage.StorageService;
import cn.ccaa.slice.service.upload.task.UploadTaskManager;
import cn.ccaa.slice.service.upload.temp.TempFileManager;
import cn.ccaa.slice.service.upload.validation.FileValidator;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件上传处理器实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FileUploadHandlerImpl implements FileUploadHandler {
    
    private final StorageService storageService;
    private final UploadTaskManager taskManager;
    private final TempFileManager tempFileManager;
    private final FileValidator fileValidator;
    private final StorageEventPublisher storageEventPublisher;
    private final Executor uploadExecutor;
    
    /**
     * 构造函数
     *
     * @param storageService 存储服务
     * @param taskManager 任务管理器
     * @param tempFileManager 临时文件管理器
     * @param fileValidator 文件验证器
     * @param storageEventPublisher 存储事件发布器
     * @param uploadExecutor 上传执行器
     */
    public FileUploadHandlerImpl(
            StorageService storageService,
            UploadTaskManager taskManager,
            TempFileManager tempFileManager,
            FileValidator fileValidator,
            StorageEventPublisher storageEventPublisher,
            @Qualifier("uploadExecutor") Executor uploadExecutor) {
        this.storageService = storageService;
        this.taskManager = taskManager;
        this.tempFileManager = tempFileManager;
        this.fileValidator = fileValidator;
        this.storageEventPublisher = storageEventPublisher;
        this.uploadExecutor = uploadExecutor;
    }
    
    @Override
    public UploadTask uploadFile(UploadTask task, MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("上传文件不能为空");
        }
        
        if (task == null) {
            throw new BusinessException("上传任务不能为空");
        }
        
        try {
            // 更新任务状态为上传中
            taskManager.updateTaskStatus(task, UploadStatus.UPLOADING);
            
            // 保存文件到临时目录
            Path tempFilePath = tempFileManager.saveToTempFile(file, task.getTaskId());
            
            // 上传文件到存储服务
            try (InputStream inputStream = new FileInputStream(tempFilePath.toFile())) {
                BiConsumer<Long, Long> progressCallback = (uploadedBytes, totalBytes) -> 
                    taskManager.updateTaskProgress(task, uploadedBytes);
                
                storageService.uploadFile(
                    task.getProgress().getStoragePath(),
                    inputStream,
                    file.getContentType(),
                    file.getSize(),
                    progressCallback);
            }
            
            // 完成上传
            return completeUpload(task);
        } catch (Exception e) {
            log.error("上传文件失败: taskId={}, fileName={}, 错误: {}", 
                    task.getTaskId(), file.getOriginalFilename(), e.getMessage(), e);
            return handleUploadError(task, e);
        } finally {
            // 清理临时文件
            tempFileManager.cleanupTempFiles(task.getTaskId());
        }
    }
    
    @Override
    public UploadTask uploadFile(UploadTask task, InputStream inputStream, String contentType) {
        if (inputStream == null) {
            throw new BusinessException("输入流不能为空");
        }
        
        if (task == null) {
            throw new BusinessException("上传任务不能为空");
        }
        
        try {
            // 更新任务状态为上传中
            taskManager.updateTaskStatus(task, UploadStatus.UPLOADING);
            
            // 直接上传到存储系统
            BiConsumer<Long, Long> progressCallback = (uploadedBytes, totalBytes) -> 
                taskManager.updateTaskProgress(task, uploadedBytes);
            
            storageService.uploadFile(
                task.getProgress().getStoragePath(),
                inputStream,
                contentType,
                task.getFileSize(),
                progressCallback);
            
            // 完成上传
            return completeUpload(task);
        } catch (Exception e) {
            log.error("上传文件失败: taskId={}, 错误: {}", task.getTaskId(), e.getMessage(), e);
            return handleUploadError(task, e);
        }
    }
    
    @Override
    public UploadTask uploadFileChunk(UploadTask task, MultipartFile chunk, int chunkIndex, int totalChunks) {
        if (chunk == null || chunk.isEmpty()) {
            throw new BusinessException("上传分块不能为空");
        }
        
        if (task == null) {
            throw new BusinessException("上传任务不能为空");
        }
        
        try {
            // 更新任务状态为上传中
            taskManager.updateTaskStatus(task, UploadStatus.UPLOADING);
            
            // 保存分块到临时目录
            tempFileManager.saveChunkToTempFile(chunk, task.getTaskId(), chunkIndex);
            
            // 计算已上传的大小
            long chunkSize = chunk.getSize();
            long uploadedSize = (long) chunkIndex * chunkSize;
            taskManager.updateTaskProgress(task, uploadedSize);
            
            // 如果是最后一个分块，合并所有分块并上传
            if (chunkIndex == totalChunks - 1) {
                Path mergedFilePath = tempFileManager.mergeChunks(task.getTaskId(), task.getFileName(), totalChunks);
                
                // 上传合并后的文件
                try (InputStream inputStream = new FileInputStream(mergedFilePath.toFile())) {
                    BiConsumer<Long, Long> progressCallback = (uploadedBytes, totalBytes) -> {
                        // 计算实际进度，考虑到前面已上传的分块
                        long actualProgress = uploadedSize + (long) (uploadedBytes * (1.0 - (double) uploadedSize / task.getFileSize()));
                        taskManager.updateTaskProgress(task, actualProgress);
                    };
                    
                    storageService.uploadFile(
                        task.getProgress().getStoragePath(),
                        inputStream,
                        chunk.getContentType(),
                        task.getFileSize(),
                        progressCallback);
                }
                
                // 完成上传
                return completeUpload(task);
            }
            
            return task;
        } catch (Exception e) {
            log.error("上传文件分块失败: taskId={}, chunkIndex={}, 错误: {}", 
                    task.getTaskId(), chunkIndex, e.getMessage(), e);
            return handleUploadError(task, e);
        }
    }
    
    @Override
    public CompletableFuture<UploadTask> asyncUploadFile(UploadTask task, MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("上传文件不能为空");
        }
        
        if (task == null) {
            throw new BusinessException("上传任务不能为空");
        }
        
        // 更新任务状态为上传中
        taskManager.updateTaskStatus(task, UploadStatus.UPLOADING);
        
        try {
            // 保存文件到临时目录
            Path tempFilePath = tempFileManager.saveToTempFile(file, task.getTaskId());
            
            // 异步上传文件
            return CompletableFuture.supplyAsync(() -> {
                try {
                    // 上传文件到存储服务
                    try (InputStream inputStream = new FileInputStream(tempFilePath.toFile())) {
                        BiConsumer<Long, Long> progressCallback = (uploadedBytes, totalBytes) -> 
                            taskManager.updateTaskProgress(task, uploadedBytes);
                        
                        storageService.uploadFile(
                            task.getProgress().getStoragePath(),
                            inputStream,
                            file.getContentType(),
                            file.getSize(),
                            progressCallback);
                    }
                    
                    // 完成上传
                    return completeUpload(task);
                } catch (Exception e) {
                    log.error("异步上传文件失败: taskId={}, fileName={}, 错误: {}", 
                            task.getTaskId(), file.getOriginalFilename(), e.getMessage(), e);
                    return handleUploadError(task, e);
                } finally {
                    // 清理临时文件
                    tempFileManager.cleanupTempFiles(task.getTaskId());
                }
            }, uploadExecutor);
        } catch (Exception e) {
            log.error("准备异步上传文件失败: taskId={}, fileName={}, 错误: {}", 
                    task.getTaskId(), file.getOriginalFilename(), e.getMessage(), e);
            taskManager.markTaskAsFailed(task, "准备异步上传文件失败: " + e.getMessage());
            throw new BusinessException("准备异步上传文件失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public UploadTask completeUpload(UploadTask task) {
        if (task == null) {
            throw new BusinessException("上传任务不能为空");
        }
        
        // 验证上传的文件
        boolean fileValid = fileValidator.validateUploadedFile(task);
        if (fileValid) {
            // 文件验证通过，更新任务状态为已完成
            taskManager.completeTask(task.getTaskId());
            
            // 发布文件上传完成事件
            storageEventPublisher.publishFileUploadedEvent(task.getProgress().getStoragePath(), task.getUserId());
            
            log.info("文件上传完成: taskId={}, fileName={}", task.getTaskId(), task.getFileName());
        } else {
            // 文件验证失败，标记任务为失败
            taskManager.markTaskAsFailed(task, "文件验证失败，可能损坏或不完整");
            log.warn("文件验证失败: taskId={}, fileName={}", task.getTaskId(), task.getFileName());
        }
        
        return task;
    }
    
    @Override
    public void cancelUpload(UploadTask task) {
        if (task == null) {
            return;
        }
        
        // 更新任务状态为已取消
        taskManager.cancelTask(task.getTaskId());
        
        // 清理临时文件
        tempFileManager.cleanupTempFiles(task.getTaskId());
        
        log.info("取消上传任务: taskId={}, fileName={}", task.getTaskId(), task.getFileName());
    }
    
    /**
     * 处理上传错误
     *
     * @param task 上传任务
     * @param e 异常
     * @return 更新后的上传任务
     */
    private UploadTask handleUploadError(UploadTask task, Exception e) {
        String errorMessage = "文件上传失败: " + e.getMessage();
        return taskManager.markTaskAsFailed(task, errorMessage);
    }
}
