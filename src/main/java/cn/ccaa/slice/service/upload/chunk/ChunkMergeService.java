package cn.ccaa.slice.service.upload.chunk;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.HashMap;
import java.util.Map;
import java.util.HashSet;

import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import cn.ccaa.slice.config.TempDirConfig;
import cn.ccaa.slice.config.StorageConfig;
import cn.ccaa.slice.core.model.SliceEntity;
import cn.ccaa.slice.core.upload.UploadStatus;
import cn.ccaa.slice.service.SliceService;
import cn.ccaa.slice.service.upload.FileUploadService;
import cn.ccaa.slice.service.upload.SliceUploadService;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import cn.ccaa.slice.service.PartnerPlatformService;
import cn.ccaa.slice.core.model.PartnerPlatformEntity;
import cn.ccaa.slice.config.CallbackConfig;
import cn.ccaa.slice.service.analyzer.SliceFileAnalyzer;
import cn.ccaa.slice.service.upload.SliceAnalysisCallbackService;
import cn.ccaa.slice.core.model.SliceAnalysisResult;

/**
 * 分片合并服务
 * 
 * <AUTHOR>
 */
@Service
public class ChunkMergeService {
    private static final Logger log = LoggerFactory.getLogger(ChunkMergeService.class);
    
    @Autowired
    private ChunkStatusManager chunkStatusManager;
    
    @Autowired
    private SliceUploadService sliceUploadService;
    
    @Autowired
    private FileUploadService fileUploadService;
    
    @Autowired
    private TempDirConfig tempDirConfig;
    
    @Autowired
    private SliceService sliceService;
    
    @Autowired
    private StorageConfig storageConfig;
    
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    @Autowired
    private PartnerPlatformService partnerPlatformService;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private CallbackConfig callbackConfig;
    
    @Autowired
    private SliceFileAnalyzer genericSliceFileAnalyzer;
    
    @Autowired
    private SliceAnalysisCallbackService sliceAnalysisCallbackService;
    
    // 文件存储基础目录
    @Value("${storage.base-dir:./data}")
    private String baseDir;
    
    // 临时分片目录保留时长（毫秒），默认24小时
    @Value("${upload.chunk.temp-retain-ms:86400000}")
    private long tempRetainMs;
    
    /**
     * 异步合并分片并处理切片文件
     * 
     * @param taskId 任务ID
     * @param filename 文件名
     * @param totalSize 文件总大小
     * @return 合并结果
     */
    @Async("chunkMergeExecutor")
    public CompletableFuture<Boolean> mergeAndProcess(String taskId, String filename, long totalSize) {
        return mergeAndProcess(taskId, filename, totalSize, null);
    }
    
    /**
     * 异步合并分片并处理切片文件，支持指定partnerCode
     * 
     * @param taskId 任务ID
     * @param filename 文件名
     * @param totalSize 文件总大小
     * @param partnerCode 对接产品代码
     * @return 合并结果
     */
    @Async("chunkMergeExecutor")
    public CompletableFuture<Boolean> mergeAndProcess(String taskId, String filename, long totalSize, String partnerCode) {
        try {
            // 更新任务状态为合并中
            chunkStatusManager.updateTaskStatus(taskId, "MERGING", null);
            
            // 延长Redis键过期时间，确保长时间处理过程中不会丢失状态
            chunkStatusManager.updateTaskExpiry(taskId, 7);
            
            // 创建合并文件的目录
            Path tempDir = tempDirConfig.getTempDir();
            String tempDirStr = tempDir.toString();
            Path mergeDir = Paths.get(tempDirStr).resolve(taskId).resolve("merged");
            Files.createDirectories(mergeDir);
            
            // 合并后的文件路径
            Path mergedFilePath = mergeDir.resolve(filename);
            
            // 合并前检查 Redis 关键 key
            String progressKey = "upload:progress:" + taskId;
            String taskKey = "upload:task:" + taskId;
            log.debug("[updateTaskStatus前] Redis key 检查: progressKey={}, exists={}, taskKey={}, exists={}",
                progressKey, stringRedisTemplate.hasKey(progressKey),
                taskKey, stringRedisTemplate.hasKey(taskKey));
            
            // 执行合并
            mergeChunks(taskId, mergedFilePath);
            
            // 验证合并后的文件大小
            long mergedFileSize = Files.size(mergedFilePath);
            if (mergedFileSize != totalSize) {
                String errorMsg = String.format("合并文件大小不匹配: 预期=%d, 实际=%d", totalSize, mergedFileSize);
                log.error(errorMsg);
                chunkStatusManager.updateTaskStatus(taskId, "FAILED", errorMsg);
                try {
                    fileUploadService.updateTaskStatus(taskId, 0L, UploadStatus.FAILED.toString(),
                        null, errorMsg, true);
                } catch (Exception e) {
                    log.warn("更新任务状态失败，但不影响合并结果: taskId={}, error={}", taskId, e.getMessage());
                }
                return CompletableFuture.completedFuture(false);
            }
            
            // ====== 路径生成规则修改开始 ======
            String fileExtension = FilenameUtils.getExtension(filename);
            String type = fileExtension.toLowerCase(); // 目录全部小写
            LocalDateTime now = LocalDateTime.now();
            String year = now.format(DateTimeFormatter.ofPattern("yyyy"));
            String month = now.format(DateTimeFormatter.ofPattern("MM"));
            String day = now.format(DateTimeFormatter.ofPattern("dd"));
            String timestamp = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
            String newFileName = timestamp + "_" + filename;
            if (partnerCode == null || partnerCode.trim().isEmpty()) {
                partnerCode = "default";
            }
            // 物理存储相对路径（physic_path）
            String relativePath = String.format("/%s/%s/%s/%s/%s/%s", 
                partnerCode, type, year, month, day, newFileName);
            // 物理存储完整路径
            Path finalFilePath = Paths.get(storageConfig.getLocalSliceDir(), partnerCode, type, year, month, day);
            Files.createDirectories(finalFilePath);
            finalFilePath = finalFilePath.resolve(newFileName);
            // ====== 路径生成规则修改结束 ======
            
            // 将合并后的文件移动到最终位置
            Files.move(mergedFilePath, finalFilePath, StandardCopyOption.REPLACE_EXISTING);
            log.info("分片合并完成，文件已移动到最终位置: {}", finalFilePath);
            
            // 先更新physic_path字段
            UpdateWrapper<SliceEntity> wrapper = new UpdateWrapper<>();
            wrapper.eq("slice_id", taskId);
            wrapper.set("physic_path", relativePath);
            boolean updated = sliceService.update(wrapper);
            log.info("已更新切片物理路径: sliceId={}, physicPath={}, updateResult={}", taskId, relativePath, updated);

            try {
                log.info("准备调用updateTaskStatus: taskId={}", taskId);
                fileUploadService.updateTaskStatus(
                    taskId, 
                    totalSize, 
                    UploadStatus.UPLOADING.toString(), 
                    null,
                    "文件合并完成，开始处理切片文件", 
                    false
                );
                log.info("updateTaskStatus调用完成: taskId={}", taskId);
            } catch (Exception e) {
                log.error("updateTaskStatus异常: taskId={}, error={}", taskId, e.getMessage(), e);
                // 记录更详细的异常信息
                log.error("updateTaskStatus异常详情: taskId={}, 异常类型={}, 异常消息={}", 
                         taskId, e.getClass().getSimpleName(), e.getMessage());
                if (e.getCause() != null) {
                    log.error("updateTaskStatus根本原因: taskId={}, 根本原因类型={}, 根本原因消息={}", 
                             taskId, e.getCause().getClass().getSimpleName(), e.getCause().getMessage());
                }
                // 异常不中断后续流程，仅记录日志
            }
            log.debug("[updateTaskStatus后] Redis key 检查: progressKey={}, exists={}",
                progressKey, stringRedisTemplate.hasKey(progressKey));
            
            // 注意：不再在这里重复进行切片解析和图片生成，因为asyncUploadAndAnalyzeSlice已经处理了
            log.info("分片合并和处理完成: taskId={}, fileName={}", taskId, filename);
            
            // 不再调用asyncUploadAndAnalyzeSlice，避免重复处理
            // 只进行必要的状态更新和回调
            
            // 更新Redis中的任务状态为完成
            try {
                chunkStatusManager.updateTaskStatus(taskId, "COMPLETED", null);
                // 确保状态完成后Redis键还能保留一段时间，以便查询
                chunkStatusManager.updateTaskExpiry(taskId, 3);
            } catch (Exception e) {
                log.warn("更新分片状态为完成失败，但不影响处理结果: taskId={}, error={}", taskId, e.getMessage());
            }
            
            // 合并完成后回调外部系统（如果需要）
            boolean callbackSuccess = true;
            if (partnerCode != null && !partnerCode.equals("default")) {
                try {
                    PartnerPlatformEntity platform = partnerPlatformService.getByPartnerCode(partnerCode);
                    if (platform != null && platform.getUrl() != null) {
                        String callbackUrl = platform.getUrl();
                        String basicInfoPath = callbackConfig.getBasicInfoPath();
                        if (!callbackUrl.endsWith("/") && (basicInfoPath == null || !basicInfoPath.startsWith("/"))) {
                            callbackUrl += "/";
                        }
                        if (basicInfoPath != null) {
                            callbackUrl += basicInfoPath;
                        }
                        
                        Map<String, Object> callbackData = Map.of(
                            "sliceId", taskId,
                            "status", "COMPLETED",
                            "fileName", filename,
                            "fileSize", totalSize,
                            "message", "分片合并完成"
                        );
                        
                        restTemplate.postForObject(callbackUrl, callbackData, String.class);
                        log.info("分片合并完成回调成功: taskId={}, callbackUrl={}", taskId, callbackUrl);
                    }
                } catch (Exception e) {
                    log.warn("分片合并完成回调失败: taskId={}, error={}", taskId, e.getMessage());
                    callbackSuccess = false;
                }
            }
            
            log.info("分片合并完成: taskId={}, fileName={}, callbackSuccess={}", taskId, filename, callbackSuccess);
            
            return CompletableFuture.completedFuture(true);
            
        } catch (Exception e) {
            String errorMsg = "合并分片失败: " + e.getMessage();
            log.error("任务{}的分片合并失败: {}", taskId, e.getMessage(), e);
            try {
                chunkStatusManager.updateTaskStatus(taskId, "FAILED", errorMsg);
                fileUploadService.updateTaskStatus(taskId, 0L, UploadStatus.FAILED.toString(),
                    null, errorMsg, true);
            } catch (Exception ex) {
                log.warn("更新任务状态失败: taskId={}, error={}", taskId, ex.getMessage());
            }
            return CompletableFuture.completedFuture(false);
        } finally {
            // 清理临时分片文件
            cleanupChunks(taskId);
        }
    }
    
    /**
     * 合并分片文件
     */
    private void mergeChunks(String taskId, Path targetPath) throws IOException {
        log.info("开始合并分片文件: taskId={}, targetPath={}", taskId, targetPath);
        
        // 获取分片信息并验证
        Set<Integer> uploadedChunks = chunkStatusManager.getUploadedChunks(taskId);
        if (uploadedChunks == null || uploadedChunks.isEmpty()) {
            throw new IOException("未找到任何已上传的分片: taskId=" + taskId);
        }
        
        List<Integer> sortedChunks = new ArrayList<>(uploadedChunks);
        Collections.sort(sortedChunks);
        
        log.info("分片信息验证: taskId={}, 分片数量={}, 分片序号={}", taskId, sortedChunks.size(), sortedChunks);
        
        // 检查分片连续性
        if (!isChunksSequential(sortedChunks)) {
            log.warn("分片序号不连续: taskId={}, sortedChunks={}", taskId, sortedChunks);
        }
        
        // 验证所有分片文件是否存在
        List<String> missingChunks = new ArrayList<>();
        List<String> validChunkPaths = new ArrayList<>();
        long totalChunkSize = 0;
        
        for (Integer chunkNumber : sortedChunks) {
            String chunkPath = chunkStatusManager.getChunkPath(taskId, chunkNumber);
            if (chunkPath == null) {
                missingChunks.add("chunkNumber=" + chunkNumber + ", path=null");
                continue;
            }
            
            File chunkFile = new File(chunkPath);
            if (!chunkFile.exists()) {
                missingChunks.add("chunkNumber=" + chunkNumber + ", path=" + chunkPath + ", exists=false");
                continue;
            }
            
            if (!chunkFile.canRead()) {
                missingChunks.add("chunkNumber=" + chunkNumber + ", path=" + chunkPath + ", readable=false");
                continue;
            }
            
            long chunkSize = chunkFile.length();
            totalChunkSize += chunkSize;
            validChunkPaths.add(chunkPath);
            log.debug("分片文件验证成功: chunkNumber={}, path={}, size={} bytes", chunkNumber, chunkPath, chunkSize);
        }
        
        if (!missingChunks.isEmpty()) {
            String errorMsg = "找不到或无法读取分片文件: taskId=" + taskId + ", missingChunks=" + missingChunks;
            log.error(errorMsg);
            throw new IOException(errorMsg);
        }
        
        log.info("分片文件验证完成: taskId={}, 有效分片数={}, 预计合并后大小={} bytes", 
                taskId, validChunkPaths.size(), totalChunkSize);
        
        // 执行合并
        try (FileChannel outChannel = FileChannel.open(targetPath, 
                StandardOpenOption.CREATE, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING)) {
            
            ByteBuffer buffer = ByteBuffer.allocate(1024 * 1024); // 1MB buffer
            long actualMergedSize = 0;
            
            for (Integer chunkNumber : sortedChunks) {
                String chunkPath = chunkStatusManager.getChunkPath(taskId, chunkNumber);
                log.debug("开始合并分片: taskId={}, chunkNumber={}, chunkPath={}", taskId, chunkNumber, chunkPath);
                
                try (FileChannel inChannel = FileChannel.open(Paths.get(chunkPath), StandardOpenOption.READ)) {
                    long chunkSize = inChannel.size();
                    long chunkMergedSize = 0;
                    
                    while (inChannel.read(buffer) != -1) {
                        buffer.flip();
                        while (buffer.hasRemaining()) {
                            int written = outChannel.write(buffer);
                            chunkMergedSize += written;
                            actualMergedSize += written;
                        }
                        buffer.clear();
                    }
                    
                    log.debug("分片合并完成: taskId={}, chunkNumber={}, 原始大小={}, 合并大小={}", 
                            taskId, chunkNumber, chunkSize, chunkMergedSize);
                    
                    if (chunkSize != chunkMergedSize) {
                        log.warn("分片合并大小不一致: taskId={}, chunkNumber={}, 原始大小={}, 合并大小={}", 
                                taskId, chunkNumber, chunkSize, chunkMergedSize);
                    }
                } catch (Exception e) {
                    String errorMsg = String.format("合并分片失败: taskId=%s, chunkNumber=%d, chunkPath=%s, error=%s", 
                                                   taskId, chunkNumber, chunkPath, e.getMessage());
                    log.error(errorMsg, e);
                    throw new IOException(errorMsg, e);
                }
            }
            
            // 强制刷新到磁盘
            outChannel.force(true);
            
            log.info("分片文件合并完成: taskId={}, 目标文件={}, 预计大小={}, 实际大小={}", 
                    taskId, targetPath, totalChunkSize, actualMergedSize);
            
            // 验证合并后的文件
            if (!Files.exists(targetPath)) {
                throw new IOException("合并文件创建失败: " + targetPath);
            }
            
            long finalFileSize = Files.size(targetPath);
            if (finalFileSize != actualMergedSize) {
                log.warn("合并文件最终大小异常: taskId={}, 实际合并大小={}, 文件系统大小={}", 
                        taskId, actualMergedSize, finalFileSize);
            }
            
        } catch (Exception e) {
            log.error("文件合并过程发生异常: taskId={}, targetPath={}, error={}", taskId, targetPath, e.getMessage(), e);
            // 清理可能生成的不完整文件
            try {
                if (Files.exists(targetPath)) {
                    Files.delete(targetPath);
                    log.info("已清理不完整的合并文件: {}", targetPath);
                }
            } catch (Exception cleanupException) {
                log.warn("清理不完整文件失败: {}, error={}", targetPath, cleanupException.getMessage());
            }
            throw e;
        }
    }
    
    /**
     * 检查分片序号是否连续
     */
    private boolean isChunksSequential(List<Integer> sortedChunks) {
        if (sortedChunks.isEmpty()) {
            return false;
        }
        
        for (int i = 0; i < sortedChunks.size(); i++) {
            if (sortedChunks.get(i) != i + 1) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 清理临时分片文件
     */
    private void cleanupChunks(String taskId) {
        try {
            Path tempDir = tempDirConfig.getTempDir();
            String tempDirStr = tempDir.toString();
            Path chunksDir = Paths.get(tempDirStr).resolve(taskId).resolve("chunks");
            if (Files.exists(chunksDir)) {
                Files.walk(chunksDir)
                    .sorted(Comparator.reverseOrder())
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.warn("删除临时分片文件失败: {}, 错误: {}", path, e.getMessage());
                        }
                    });
                log.info("临时分片文件清理完成: 任务ID={}", taskId);
            }
        } catch (Exception e) {
            log.error("清理临时分片文件失败: 任务ID={}, 错误: {}", taskId, e.getMessage(), e);
        }
    }
    
    /**
     * 基于identifier清理分片临时文件（新方法）
     */
    private void cleanupChunksByIdentifier(String identifier) {
        try {
            log.info("开始清理分片临时文件: identifier={}", identifier);
            
            Path tempDir = tempDirConfig.getTempDir();
            Path taskDir = tempDir.resolve(identifier);
            
            if (Files.exists(taskDir)) {
                // 递归删除目录及其内容
                Files.walk(taskDir)
                    .sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(file -> {
                        if (!file.delete()) {
                            log.warn("无法删除文件或目录: {}", file.getPath());
                        }
                    });
                log.info("分片临时文件清理完成: identifier={}, path={}", identifier, taskDir);
            } else {
                log.debug("分片临时目录不存在，无需清理: identifier={}, path={}", identifier, taskDir);
            }
            
            // 清理Redis中的分片信息
            try {
                chunkStatusManager.cleanupTaskByIdentifier(identifier);
                log.info("Redis分片信息清理完成: identifier={}", identifier);
            } catch (Exception e) {
                log.warn("清理Redis分片信息失败: identifier={}, error={}", identifier, e.getMessage());
            }
            
        } catch (Exception e) {
            log.error("清理分片临时文件失败: identifier={}, error={}", identifier, e.getMessage(), e);
        }
    }
    
    /**
     * 从元数据中提取倍率信息，兼容各个解析器的不同字段名
     * 优先使用TronSDK的MaximumZoomLevel真实倍率（objective字段）
     * 
     * @param metadata 元数据Map
     * @param taskId 任务ID，用于日志输出
     * @return 倍率字符串，如果未找到则返回null
     */
    private String extractMagnificationFromMetadata(Map<String, Object> metadata, String taskId) {
        log.info("[DEBUG] ChunkMergeService收到metadata: {}", metadata);
        if (metadata == null || metadata.isEmpty()) {
            log.debug("元数据为空: sliceId={}", taskId);
            return null;
        }
        
        log.debug("开始从元数据中提取倍率信息: sliceId={}, metadata={}", taskId, metadata);
        
        // 定义可能的倍率字段名，按优先级排序
        String[] magnificationFields = {
            "objective",           // TronSDK和SqraySlide解析器使用，优先级最高（真实倍率）
            "magnification",       // 通用字段名
            "openslide.objective-power",  // OpenSlide解析器使用
            "scan.magnification",  // 其他可能的字段名
            "zoom",               // 其他可能的字段名
            "power"               // 其他可能的字段名
        };
        
        for (String fieldName : magnificationFields) {
            Object magnificationObj = metadata.get(fieldName);
            if (magnificationObj != null) {
                try {
                    String magnificationStr = magnificationObj.toString().trim();
                    log.debug("找到倍率字段: sliceId={}, fieldName={}, value={}", taskId, fieldName, magnificationStr);
                    
                    // 提取数字部分，去除可能的"x"或"X"后缀
                    String numericPart = magnificationStr.replaceAll("[xX]", "").trim();
                    
                    // 验证是否为有效数字
                    if (numericPart.matches("\\d+(\\.\\d+)?")) {
                        // 如果是小数，四舍五入为整数
                        if (numericPart.contains(".")) {
                            double doubleValue = Double.parseDouble(numericPart);
                            numericPart = String.valueOf(Math.round(doubleValue));
                        }
                        log.info("成功提取倍率: sliceId={}, fieldName={}, originalValue={}, extractedValue={}", 
                                taskId, fieldName, magnificationStr, numericPart);
                        return numericPart;
                    } else {
                        log.warn("倍率值格式无效: sliceId={}, fieldName={}, value={}", taskId, fieldName, magnificationStr);
                    }
                } catch (Exception e) {
                    log.warn("解析倍率值时出错: sliceId={}, fieldName={}, value={}, error={}", 
                            taskId, fieldName, magnificationObj, e.getMessage());
                }
            }
        }
        
        log.warn("未找到有效的倍率信息: sliceId={}, availableFields={}", taskId, metadata.keySet());
        return null;
    }
    
    /**
     * 定时清理过期的分片临时目录（每小时执行一次）
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void cleanupExpiredChunkDirs() {
        Path tempDir = tempDirConfig.getTempDir();
        File[] taskDirs = tempDir.toFile().listFiles(File::isDirectory);
        if (taskDirs == null) return;
        long now = System.currentTimeMillis();
        int deleted = 0;
        for (File dir : taskDirs) {
            // 只清理chunks目录
            File chunks = new File(dir, "chunks");
            if (chunks.exists() && chunks.isDirectory()) {
                long lastModified = chunks.lastModified();
                if (now - lastModified > tempRetainMs) {
                    try {
                        org.apache.commons.io.FileUtils.deleteDirectory(dir);
                        deleted++;
                        log.info("定时清理过期分片目录: {}", dir.getAbsolutePath());
                    } catch (Exception e) {
                        log.warn("定时清理分片目录失败: {}，错误: {}", dir.getAbsolutePath(), e.getMessage());
                    }
                }
            }
        }
        if (deleted > 0) {
            log.info("本次定时清理分片目录共删除{}个过期目录", deleted);
        }
    }
    
    /**
     * 自定义MultipartFile实现，用于从文件创建MultipartFile
     */
    private static class CustomMultipartFile implements MultipartFile {
        private final String name;
        private final String originalFilename;
        private final String contentType;
        private final File file;
        
        public CustomMultipartFile(String name, String originalFilename, String contentType, File file) {
            this.name = name;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
            this.file = file;
        }
        
        @Override
        public String getName() {
            return name;
        }
        
        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }
        
        @Override
        public String getContentType() {
            return contentType;
        }
        
        @Override
        public boolean isEmpty() {
            return file.length() == 0;
        }
        
        @Override
        public long getSize() {
            return file.length();
        }
        
        @Override
        public byte[] getBytes() throws IOException {
            return Files.readAllBytes(file.toPath());
        }
        
        @Override
        public InputStream getInputStream() throws IOException {
            return new FileInputStream(file);
        }
        
        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            Files.copy(file.toPath(), dest.toPath(), StandardCopyOption.REPLACE_EXISTING);
        }
    }
    
    /**
     * 异步合并分片并处理切片文件，支持指定partnerCode和场景
     * 
     * @param threadingId 穿线标识（本地上传和切片池上传时为主键id，蚂蚁上传时为sliceId）
     * @param filename 文件名
     * @param totalSize 文件总大小
     * @param partnerCode 对接产品代码
     * @param scenario 上传场景
     * @param caseId 病例ID（可选）
     * @return 合并结果
     */
    @Async("chunkMergeExecutor")
    public CompletableFuture<Boolean> mergeAndProcessWithScenario(String threadingId, String filename, long totalSize, 
                                                             String partnerCode, Object scenario, String caseId) {
        return mergeAndProcessWithScenario(threadingId, filename, totalSize, partnerCode, scenario, caseId, null, null);
    }
    
    /**
     * 异步合并分片并处理切片文件，支持指定partnerCode、场景和sectionId
     * 
     * @param threadingId 穿线标识（本地上传和切片池上传时为主键id，蚂蚁上传时为sliceId）
     * @param filename 文件名
     * @param totalSize 文件总大小
     * @param partnerCode 对接产品代码
     * @param scenario 上传场景
     * @param caseId 病例ID（可选）
     * @param sectionId 章节ID（可选）
     * @return 合并结果
     */
    @Async("chunkMergeExecutor")
    public CompletableFuture<Boolean> mergeAndProcessWithScenario(String threadingId, String filename, long totalSize, 
                                                             String partnerCode, Object scenario, String caseId, String sectionId) {
        return mergeAndProcessWithScenario(threadingId, filename, totalSize, partnerCode, scenario, caseId, sectionId, null);
    }
    
    /**
     * 异步合并分片并处理切片文件，支持指定partnerCode和场景（带identifier支持）
     * 
     * @param threadingId 穿线标识（本地上传和切片池上传时为主键id，蚂蚁上传时为sliceId）
     * @param filename 文件名
     * @param totalSize 文件总大小
     * @param partnerCode 对接产品代码
     * @param scenario 上传场景
     * @param caseId 病例ID（可选）
     * @param sectionId 章节ID（可选）
     * @param identifier 标识符（切片池上传场景使用）
     * @return 合并结果
     */
    @Async("chunkMergeExecutor")
    public CompletableFuture<Boolean> mergeAndProcessWithScenario(String threadingId, String filename, long totalSize, 
                                                             String partnerCode, Object scenario, String caseId, String sectionId, String identifier) {
        try {
            log.info("开始处理分片合并: 穿线标识={}, scenario={}, caseId={}, sectionId={}, identifier={}", threadingId, scenario, caseId, sectionId, identifier);
            
            // 判断是否为切片池上传场景
            boolean isSlicePoolUpload = scenario != null && scenario.toString().contains("SLICE_POOL_UPLOAD");
            String redisKey = isSlicePoolUpload ? 
                ((identifier != null && !identifier.trim().isEmpty()) ? identifier.trim() : "default") : threadingId;
            
            // 更新任务状态为合并中
            if (isSlicePoolUpload) {
                chunkStatusManager.updateTaskStatusByIdentifier(redisKey, "MERGING", null);
                // 延长Redis键过期时间，确保长时间处理过程中不会丢失状态
                chunkStatusManager.updateTaskExpiryByIdentifier(redisKey, 7);
            } else {
                chunkStatusManager.updateTaskStatus(threadingId, "MERGING", null);
                // 延长Redis键过期时间，确保长时间处理过程中不会丢失状态
                chunkStatusManager.updateTaskExpiry(threadingId, 7);
            }
            
            // 验证Redis中的分片信息完整性
            Map<String, Object> initialTaskInfo = isSlicePoolUpload ? 
                chunkStatusManager.getTaskInfoByIdentifier(redisKey) : 
                chunkStatusManager.getTaskInfo(threadingId);
            Set<Integer> initialUploadedChunks = isSlicePoolUpload ? 
                chunkStatusManager.getUploadedChunksByIdentifier(redisKey) : 
                chunkStatusManager.getUploadedChunks(threadingId);
            
            log.info("Redis分片信息验证: threadingId={}, redisKey={}, isSlicePoolUpload={}, taskInfo={}, uploadedChunksCount={}", 
                    threadingId, redisKey, isSlicePoolUpload, initialTaskInfo, initialUploadedChunks.size());
            
            if (initialTaskInfo.isEmpty()) {
                String errorMsg = "Redis中未找到任务信息: threadingId=" + threadingId + ", redisKey=" + redisKey;
                log.error(errorMsg);
                updateDatabaseStatusByScenario(threadingId, "2", errorMsg, scenario);
                return CompletableFuture.completedFuture(false);
            }
            
            if (initialUploadedChunks.isEmpty()) {
                String errorMsg = "Redis中未找到已上传分片信息: threadingId=" + threadingId + ", redisKey=" + redisKey;
                log.error(errorMsg);
                updateDatabaseStatusByScenario(threadingId, "2", errorMsg, scenario);
                return CompletableFuture.completedFuture(false);
            }
            
            // 验证总分片数是否一致
            Object totalChunksObj = initialTaskInfo.get("totalChunks");
            if (totalChunksObj == null) {
                String errorMsg = "Redis中缺少totalChunks信息: threadingId=" + threadingId + ", redisKey=" + redisKey;
                log.error(errorMsg);
                updateDatabaseStatusByScenario(threadingId, "2", errorMsg, scenario);
                return CompletableFuture.completedFuture(false);
            }
            
            int expectedTotalChunks = Integer.parseInt(totalChunksObj.toString());
            int actualUploadedChunks = initialUploadedChunks.size();
            
            if (actualUploadedChunks != expectedTotalChunks) {
                String errorMsg = String.format("分片数量不匹配: threadingId=%s, redisKey=%s, 预期=%d, 实际=%d, uploadedChunks=%s", 
                                               threadingId, redisKey, expectedTotalChunks, actualUploadedChunks, initialUploadedChunks);
                log.error(errorMsg);
                
                // 添加详细的分片状态信息用于诊断
                log.error("分片数量不匹配详细信息: threadingId={}, redisKey={}", threadingId, redisKey);
                log.error("  - 预期总分片数: {}", expectedTotalChunks);
                log.error("  - 实际上传分片数: {}", actualUploadedChunks);
                log.error("  - 已上传分片列表: {}", initialUploadedChunks);
                
                // 分析缺失的分片
                Set<Integer> missingChunks = new HashSet<>();
                for (int i = 1; i <= expectedTotalChunks; i++) {
                    if (!initialUploadedChunks.contains(i)) {
                        missingChunks.add(i);
                    }
                }
                log.error("  - 缺失的分片: {}", missingChunks);
                
                // 检查Redis键是否存在
                String taskKeyToCheck = isSlicePoolUpload ? ("upload:task:" + redisKey) : ("upload:task:" + threadingId);
                String chunksKeyToCheck = isSlicePoolUpload ? ("upload:chunks:" + redisKey) : ("upload:chunks:" + threadingId);
                boolean taskKeyExists = stringRedisTemplate.hasKey(taskKeyToCheck);
                boolean chunksKeyExists = stringRedisTemplate.hasKey(chunksKeyToCheck);
                log.error("  - Redis键状态: taskKey={}({}), chunksKey={}({})", 
                         taskKeyToCheck, taskKeyExists, chunksKeyToCheck, chunksKeyExists);
                
                updateDatabaseStatusByScenario(threadingId, "2", errorMsg, scenario);
                return CompletableFuture.completedFuture(false);
            }
            
            // 验证分片序号连续性
            List<Integer> sortedChunks = new ArrayList<>(initialUploadedChunks);
            Collections.sort(sortedChunks);
            for (int i = 0; i < sortedChunks.size(); i++) {
                if (!sortedChunks.get(i).equals(i + 1)) {
                    String errorMsg = String.format("分片序号不连续: threadingId=%s, redisKey=%s, 预期序号=%d, 实际序号=%d, 所有分片=%s", 
                                                   threadingId, redisKey, i + 1, sortedChunks.get(i), sortedChunks);
                    log.error(errorMsg);
                    updateDatabaseStatusByScenario(threadingId, "2", errorMsg, scenario);
                    return CompletableFuture.completedFuture(false);
                }
            }
            
            // 创建合并文件的目录（使用合适的目录名）
            Path tempDir = tempDirConfig.getTempDir();
            String tempDirStr = tempDir.toString();
            String mergeDir = isSlicePoolUpload ? redisKey : threadingId;
            Path mergeDirPath = Paths.get(tempDirStr).resolve(mergeDir).resolve("merged");
            Files.createDirectories(mergeDirPath);
            
            // 合并后的文件路径
            Path mergedFilePath = mergeDirPath.resolve(filename);
            
            // 执行合并（增加重试机制）
            int maxRetries = 3;
            IOException lastException = null;
            boolean mergeSuccess = false;
            
            for (int attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    log.info("开始合并分片文件 (尝试 {}/{}): threadingId={}, redisKey={}", attempt, maxRetries, threadingId, redisKey);
                    // 使用新的合并方法，支持identifier
                    if (isSlicePoolUpload) {
                        mergeChunksByIdentifier(redisKey, mergedFilePath);
                    } else {
                        mergeChunks(threadingId, mergedFilePath);
                    }
                    mergeSuccess = true;
                    log.info("分片合并成功: threadingId={}, redisKey={}, 尝试次数={}", threadingId, redisKey, attempt);
                    break;
                } catch (IOException e) {
                    lastException = e;
                    log.warn("分片合并失败 (尝试 {}/{}): threadingId={}, redisKey={}, error={}", attempt, maxRetries, threadingId, redisKey, e.getMessage());
                    
                    if (attempt < maxRetries) {
                        // 等待一段时间后重试
                        try {
                            Thread.sleep(1000 * attempt); // 递增等待时间
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new RuntimeException("合并过程被中断", ie);
                        }
                    }
                }
            }
            
            if (!mergeSuccess) {
                String errorMsg = String.format("分片合并失败，已重试%d次: threadingId=%s, redisKey=%s, 最后错误: %s", 
                                               maxRetries, threadingId, redisKey, lastException.getMessage());
                log.error(errorMsg);
                if (isSlicePoolUpload) {
                    chunkStatusManager.updateTaskStatusByIdentifier(redisKey, "FAILED", errorMsg);
                } else {
                    chunkStatusManager.updateTaskStatus(threadingId, "FAILED", errorMsg);
                }
                updateDatabaseStatusByScenario(threadingId, "2", errorMsg, scenario);
                return CompletableFuture.completedFuture(false);
            }
            
            // 验证合并后的文件
            long mergedFileSize = Files.size(mergedFilePath);
            log.info("分片合并验证: threadingId={}, redisKey={}, 预期大小={}, 实际大小={}", threadingId, redisKey, totalSize, mergedFileSize);
            
            // 允许一定的大小差异（比如1%的误差）
            long sizeDifference = Math.abs(mergedFileSize - totalSize);
            double sizeErrorRate = (double) sizeDifference / totalSize;
            boolean sizeValid = sizeErrorRate <= 0.01; // 允许1%的误差
            
            if (!sizeValid) {
                String errorMsg = String.format("合并文件大小不匹配: 预期=%d, 实际=%d, 差异=%d, 错误率=%.4f%%", 
                                               totalSize, mergedFileSize, sizeDifference, sizeErrorRate * 100);
                log.error(errorMsg);
                
                // 获取详细的Redis分片信息用于调试
                Set<Integer> uploadedChunks = isSlicePoolUpload ? 
                    chunkStatusManager.getUploadedChunksByIdentifier(redisKey) : 
                    chunkStatusManager.getUploadedChunks(threadingId);
                Map<String, Object> taskInfo = isSlicePoolUpload ? 
                    chunkStatusManager.getTaskInfoByIdentifier(redisKey) : 
                    chunkStatusManager.getTaskInfo(threadingId);
                log.error("文件大小不匹配调试信息: threadingId={}, redisKey={}, uploadedChunks={}, taskInfo={}", 
                         threadingId, redisKey, uploadedChunks, taskInfo);
                
                if (isSlicePoolUpload) {
                    chunkStatusManager.updateTaskStatusByIdentifier(redisKey, "FAILED", errorMsg);
                } else {
                    chunkStatusManager.updateTaskStatus(threadingId, "FAILED", errorMsg);
                }
                updateDatabaseStatusByScenario(threadingId, "2", errorMsg, scenario);
                return CompletableFuture.completedFuture(false);
            } else if (sizeDifference > 0) {
                log.warn("文件大小有轻微差异但在允许范围内: threadingId={}, redisKey={}, 预期={}, 实际={}, 差异={}", 
                        threadingId, redisKey, totalSize, mergedFileSize, sizeDifference);
            }
            
            // 生成最终文件路径
            String fileExtension = FilenameUtils.getExtension(filename);
            String type = fileExtension.toLowerCase();
            LocalDateTime now = LocalDateTime.now();
            String year = now.format(DateTimeFormatter.ofPattern("yyyy"));
            String month = now.format(DateTimeFormatter.ofPattern("MM"));
            String day = now.format(DateTimeFormatter.ofPattern("dd"));
            String timestamp = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
            String newFileName = timestamp + "_" + filename;
            if (partnerCode == null || partnerCode.trim().isEmpty()) {
                partnerCode = "default";
            }
            
            String relativePath = String.format("/%s/%s/%s/%s/%s/%s", 
                partnerCode, type, year, month, day, newFileName);
            Path finalFilePath = Paths.get(storageConfig.getLocalSliceDir(), partnerCode, type, year, month, day);
            Files.createDirectories(finalFilePath);
            finalFilePath = finalFilePath.resolve(newFileName);
            
            // 将合并后的文件移动到最终位置
            Files.move(mergedFilePath, finalFilePath, StandardCopyOption.REPLACE_EXISTING);
            log.info("分片合并完成，文件已移动到最终位置: {}", finalFilePath);
            
            // 更新数据库的physic_path字段
            updateDatabasePhysicPathByScenario(threadingId, relativePath, scenario);
            
            // 第一步：先调用BasicInfo回调
            boolean basicInfoSuccess = false;
            if (partnerCode != null && !partnerCode.trim().isEmpty()) {
                try {
                    PartnerPlatformEntity platform = partnerPlatformService.getByPartnerCode(partnerCode);
                    if (platform != null && platform.getUrl() != null) {
                        String basicInfoUrl = platform.getUrl();
                        String basicInfoPath = callbackConfig.getBasicInfoPath();
                        if (!basicInfoUrl.endsWith("/") && (basicInfoPath == null || !basicInfoPath.startsWith("/"))) {
                            basicInfoUrl += "/";
                        }
                        if (basicInfoPath != null) {
                            basicInfoUrl += basicInfoPath;
                        } else {
                            basicInfoUrl += "BasicInfo"; // 默认路径
                        }
                        
                        // 根据场景构建BasicInfo回调参数
                        Map<String, Object> basicInfoData = new HashMap<>();
                        String scenarioStr = scenario != null ? scenario.toString() : "";
                        
                        // 获取切片实体以获取主键id
                        SliceEntity slice = null;
                        if (scenarioStr.contains("ANT_UPLOAD")) {
                            slice = sliceService.getBySliceId(threadingId);
                        } else {
                            slice = sliceService.getById(threadingId);
                        }
                        
                        if (slice == null) {
                            log.error("BasicInfo推送失败，未找到切片记录: threadingId={}, scenario={}", threadingId, scenarioStr);
                            basicInfoSuccess = false;
                        } else {
                            // 所有场景都推送fileId（表主键）
                            basicInfoData.put("fileId", slice.getId());
                            
                            if (scenarioStr.contains("LOCAL_UPLOAD")) {
                                // 本地上传场景：推送实际taskId和fileId
                                if (slice.getTaskId() != null) {
                                    basicInfoData.put("taskId", slice.getTaskId());
                                    log.info("BasicInfo推送-本地上传场景，推送实际taskId: {}, fileId: {}, 穿线标识(主键id): {}", 
                                            slice.getTaskId(), slice.getId(), threadingId);
                                } else {
                                    basicInfoData.put("taskId", threadingId);
                                    log.warn("BasicInfo推送-本地上传场景，未找到实际taskId，使用穿线标识: {}, fileId: {}", 
                                            threadingId, slice.getId());
                                }
                            } else if (scenarioStr.contains("ANT_UPLOAD")) {
                                // 蚂蚁上传场景：推送sliceId和fileId
                                basicInfoData.put("sliceId", threadingId);
                                log.info("BasicInfo推送-蚂蚁上传场景，推送sliceId: {}, fileId: {}", threadingId, slice.getId());
                            } else {
                                // 切片池上传场景：只推送fileId（主键id）
                                log.info("BasicInfo推送-切片池上传场景，推送fileId: {}", slice.getId());
                            }
                            
                            basicInfoData.put("status", "COMPLETED");
                            basicInfoData.put("qpName", filename);
                            basicInfoData.put("fileSize", totalSize);
                            basicInfoData.put("message", "分片合并完成");
                            
                            // 添加sectionId参数（如果有值）
                            if (sectionId != null && !sectionId.trim().isEmpty()) {
                                basicInfoData.put("sectionId", sectionId);
                                log.info("BasicInfo推送-添加sectionId参数: {}", sectionId);
                            }
                            
                            // 添加病例ID参数 - 根据赵总需求，BasicInfo接口调用时传递caseId
                            if (caseId != null && !caseId.trim().isEmpty()) {
                                basicInfoData.put("caseId", caseId);
                                log.info("BasicInfo推送-添加病例ID参数: caseId={}", caseId);
                            }
                            
                            // 添加必需的参数，设置默认值
                            basicInfoData.put("timestamp", System.currentTimeMillis()); // 当前时间戳
                            basicInfoData.put("sign", "default_sign_" + System.currentTimeMillis()); // 默认签名
                            
                            // 添加机构相关参数
                            if (slice.getOrgId() != null && !slice.getOrgId().trim().isEmpty()) {
                                basicInfoData.put("orgId", slice.getOrgId());
                                log.info("BasicInfo推送-添加机构ID参数: orgId={}", slice.getOrgId());
                            }
                            if (slice.getAhId() != null && !slice.getAhId().trim().isEmpty()) {
                                basicInfoData.put("areaId", slice.getAhId()); // 注意：ahId映射为areaId
                                log.info("BasicInfo推送-添加院区ID参数: areaId={}", slice.getAhId());
                            }
                            if (slice.getProjId() != null && !slice.getProjId().trim().isEmpty()) {
                                basicInfoData.put("projId", slice.getProjId());
                                log.info("BasicInfo推送-添加项目ID参数: projId={}", slice.getProjId());
                            }
                            
                            log.info("BasicInfo推送参数: {}", basicInfoData);
                            
                            // 设置请求头
                            HttpHeaders headers = new HttpHeaders();
                            headers.setContentType(MediaType.APPLICATION_JSON);
                            
                            if (slice.getTempAuth() != null && !slice.getTempAuth().isEmpty()) {
                                headers.set("Authorization", slice.getTempAuth());
                                log.info("BasicInfo推送设置Authorization头: 长度={}", slice.getTempAuth().length());
                            } else {
                                log.warn("BasicInfo推送未设置Authorization头: 穿线标识={}", threadingId);
                            }
                            
                            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(basicInfoData, headers);
                            
                            // 打印BasicInfo调用参数（JSON格式）
                            log.info("BasicInfo接口调用 - URL: {}", basicInfoUrl);
                            log.info("BasicInfo接口调用 - 请求参数: {}", basicInfoData);
                            
                            ResponseEntity<String> response = restTemplate.postForEntity(basicInfoUrl, entity, String.class);
                            
                            log.info("BasicInfo推送响应: url={}, status={}, response={}", 
                                    basicInfoUrl, response.getStatusCode(), response.getBody());
                            
                            basicInfoSuccess = response.getStatusCode().is2xxSuccessful();
                        }
                    } else {
                        log.warn("未找到 partnerCode={} 的平台URL，跳过BasicInfo推送", partnerCode);
                        basicInfoSuccess = true; // 跳过推送但不算失败
                    }
                } catch (Exception e) {
                    log.error("BasicInfo回调异常: threadingId={}, error={}", threadingId, e.getMessage(), e);
                    basicInfoSuccess = false;
                }
            } else {
                log.info("partnerCode为空或未提供，跳过BasicInfo推送: threadingId={}, partnerCode={}", threadingId, partnerCode);
                basicInfoSuccess = true; // 跳过推送但不算失败
            }
            
            // 第二步：BasicInfo成功后，进行切片解析和图片生成
            if (basicInfoSuccess) {
                try {
                    log.info("BasicInfo回调成功，开始解析切片文件: {}", finalFilePath);
                    
                    // 解析切片文件
                    SliceAnalysisResult analysisResult = genericSliceFileAnalyzer.analyze(finalFilePath.toFile());
                    
                    // 生成缩略图和标签图
                    File tempThumbnailImage = analysisResult.getThumbnailImage();
                    File tempLabelImage = analysisResult.getLabelImage();
                    
                    if (tempThumbnailImage != null && tempLabelImage != null) {
                        // 将生成的图片移动到最终位置（与切片文件同目录）
                        String baseFileName = FilenameUtils.getBaseName(newFileName);
                        Path thumbLocalPath = finalFilePath.getParent().resolve("thumb_" + baseFileName + ".jpeg");
                        Path labelLocalPath = finalFilePath.getParent().resolve("label_" + baseFileName + ".jpeg");
                        
                        // 移动缩略图到最终位置
                        if (!tempThumbnailImage.getAbsolutePath().equals(thumbLocalPath.toString())) {
                            Files.move(tempThumbnailImage.toPath(), thumbLocalPath, StandardCopyOption.REPLACE_EXISTING);
                            log.info("缩略图已移动到最终位置: {}", thumbLocalPath);
                        }
                        
                        // 移动标签图到最终位置
                        if (!tempLabelImage.getAbsolutePath().equals(labelLocalPath.toString())) {
                            Files.move(tempLabelImage.toPath(), labelLocalPath, StandardCopyOption.REPLACE_EXISTING);
                            log.info("标签图已移动到最终位置: {}", labelLocalPath);
                        }
                        
                        // 更新数据库中的切片解析结果
                        updateSliceAnalysisResult(threadingId, analysisResult, scenario, filename);
                        
                        log.info("切片解析和图片生成完成: threadingId={}", threadingId);
                        
                        // 第三步：解析成功后，调用Info接口
                        handleScenarioSpecificCallback(threadingId, filename, partnerCode, scenario, caseId);
                        
                    } else {
                        log.error("缩略图或标签图生成失败: threadingId={}, thumbImage={}, labelImage={}", 
                                threadingId, tempThumbnailImage, tempLabelImage);
                        
                        // 即使图片生成失败，也更新数据库状态
                        updateSliceAnalysisResult(threadingId, analysisResult, scenario, filename);
                        
                        // 仍然调用Info接口，但可能没有图片
                        handleScenarioSpecificCallback(threadingId, filename, partnerCode, scenario, caseId);
                    }
                    
                } catch (Exception e) {
                    log.error("切片解析失败: threadingId={}, error={}", threadingId, e.getMessage(), e);
                    updateDatabaseStatusByScenario(threadingId, "2", "切片解析失败: " + e.getMessage(), scenario);
                }
            } else {
                log.error("BasicInfo回调失败，跳过切片解析: threadingId={}", threadingId);
                updateDatabaseStatusByScenario(threadingId, "2", "BasicInfo回调失败", scenario);
            }
            
            // 最后：清理Redis分片信息（延迟清理）
            try {
                // 延迟清理，确保状态查询接口还能获取到结果
                CompletableFuture.runAsync(() -> {
                    try {
                        Thread.sleep(30000); // 30秒后清理
                        
                        // 清理Redis分片信息
                        chunkStatusManager.cleanupTask(threadingId);
                        
                        // 针对本地上传场景，需要额外清理会话信息
                        String scenarioStr = scenario != null ? scenario.toString() : "";
                        if (scenarioStr.contains("LOCAL_UPLOAD")) {
                            try {
                                // 获取切片信息以清理会话
                                SliceEntity slice = sliceService.getById(threadingId);
                                if (slice != null) {
                                    // 查找所有相关的会话键，但只清理当前threadingId的会话
                                    String sessionPattern = String.format("local_session:%s:%s:%d:*", 
                                                                         slice.getTaskId(), slice.getQpName(), slice.getQpSize().longValue());
                                    Set<String> allKeys = stringRedisTemplate.keys(sessionPattern);
                                    
                                    int deletedCount = 0;
                                    if (allKeys != null && !allKeys.isEmpty()) {
                                        // 只删除与当前threadingId相关的会话
                                        for (String key : allKeys) {
                                            if (key.contains(":" + threadingId)) {
                                                stringRedisTemplate.delete(key);
                                                deletedCount++;
                                            }
                                        }
                                    }
                                    
                                    log.info("清理本地上传会话完成: threadingId={}, taskId={}, qpName={}, qpSize={}, 清理数量={}", 
                                            threadingId, slice.getTaskId(), slice.getQpName(), slice.getQpSize(), deletedCount);
                                } else {
                                    log.warn("未找到切片信息，无法清理会话: threadingId={}", threadingId);
                                }
                            } catch (Exception sessionEx) {
                                log.warn("清理本地上传会话失败: threadingId={}, error={}", threadingId, sessionEx.getMessage());
                            }
                        }
                        
                        log.info("Redis分片信息清理完成: threadingId={}", threadingId);
                    } catch (Exception cleanupException) {
                        log.warn("清理Redis分片信息失败: threadingId={}, error={}", threadingId, cleanupException.getMessage());
                    }
                });
            } catch (Exception e) {
                log.warn("更新分片状态为完成失败，但不影响处理结果: threadingId={}, error={}", threadingId, e.getMessage());
            }
            
            return CompletableFuture.completedFuture(true);
            
        } catch (Exception e) {
            String errorMsg = "合并分片失败: " + e.getMessage();
            log.error("穿线标识{}的分片合并失败: {}", threadingId, e.getMessage(), e);
            try {
                chunkStatusManager.updateTaskStatus(threadingId, "FAILED", errorMsg);
                updateDatabaseStatusByScenario(threadingId, "2", errorMsg, scenario);
            } catch (Exception ex) {
                log.warn("更新任务状态失败: threadingId={}, error={}", threadingId, ex.getMessage());
            }
            return CompletableFuture.completedFuture(false);
        } finally {
            // 清理临时分片文件 - 简化为统一清理，避免变量作用域问题
            try {
                // 判断是否为切片池上传场景
                boolean isSlicePoolUpload = scenario != null && scenario.toString().contains("SLICE_POOL_UPLOAD");
                if (isSlicePoolUpload) {
                    String actualIdentifier = (identifier != null && !identifier.trim().isEmpty()) ? identifier.trim() : "default";
                    cleanupChunksByIdentifier(actualIdentifier);
                } else {
                    cleanupChunks(threadingId);
                }
            } catch (Exception e) {
                log.warn("清理临时文件失败: threadingId={}, error={}", threadingId, e.getMessage());
            }
        }
    }
    
    /**
     * 根据场景更新数据库状态
     */
    private void updateDatabaseStatusByScenario(String threadingId, String status, String errorMsg, Object scenario) {
        try {
            String scenarioStr = scenario != null ? scenario.toString() : "";
            
            if (scenarioStr.contains("ANT_UPLOAD")) {
                // 蚂蚁上传：threadingId就是sliceId，使用sliceId查询
                SliceEntity slice = sliceService.getBySliceId(threadingId);
            if (slice != null) {
                slice.setQpState(status);
                slice.setLastModifiedTime(LocalDateTime.now());
                sliceService.updateById(slice);
                    log.info("已更新数据库状态(蚂蚁上传): sliceId={}, status={}", threadingId, status);
                } else {
                    log.warn("未找到切片记录(蚂蚁上传): sliceId={}", threadingId);
                }
            } else {
                // 本地上传和切片池上传：threadingId是主键id，使用主键id查询
                SliceEntity slice = sliceService.getById(threadingId);
                if (slice != null) {
                    slice.setQpState(status);
                    slice.setLastModifiedTime(LocalDateTime.now());
                    sliceService.updateById(slice);
                    String actualTaskId = slice.getTaskId(); // 获取实际的taskId
                    log.info("已更新数据库状态(本地/切片池上传): 主键id={}, 实际taskId={}, status={}", 
                            threadingId, actualTaskId, status);
                } else {
                    log.warn("未找到切片记录(本地/切片池上传): 主键id={}", threadingId);
                }
            }
        } catch (Exception e) {
            log.error("更新数据库状态失败: threadingId={}, error={}", threadingId, e.getMessage(), e);
        }
    }
    
    /**
     * 根据场景更新数据库的physic_path字段
     */
    private void updateDatabasePhysicPathByScenario(String threadingId, String relativePath, Object scenario) {
        try {
            String scenarioStr = scenario != null ? scenario.toString() : "";
            
            if (scenarioStr.contains("ANT_UPLOAD")) {
                // 蚂蚁上传：threadingId就是sliceId，使用sliceId查询
                SliceEntity slice = sliceService.getBySliceId(threadingId);
            if (slice != null) {
                slice.setPhysicPath(relativePath);
                slice.setLastModifiedTime(LocalDateTime.now());
                sliceService.updateById(slice);
                    log.info("已更新physic_path(蚂蚁上传): sliceId={}, path={}", threadingId, relativePath);
                } else {
                    log.warn("未找到切片记录(蚂蚁上传): sliceId={}", threadingId);
                }
            } else {
                // 本地上传和切片池上传：threadingId是主键id，使用主键id查询
                SliceEntity slice = sliceService.getById(threadingId);
                if (slice != null) {
                    slice.setPhysicPath(relativePath);
                    slice.setLastModifiedTime(LocalDateTime.now());
                    sliceService.updateById(slice);
                    String actualTaskId = slice.getTaskId(); // 获取实际的taskId
                    log.info("已更新physic_path(本地/切片池上传): 主键id={}, 实际taskId={}, path={}", 
                            threadingId, actualTaskId, relativePath);
                } else {
                    log.warn("未找到切片记录(本地/切片池上传): 主键id={}", threadingId);
                }
            }
        } catch (Exception e) {
            log.error("更新physic_path失败: threadingId={}, error={}", threadingId, e.getMessage(), e);
        }
    }
    
    /**
     * 根据场景处理特定的回调逻辑
     */
    private void handleScenarioSpecificCallback(String threadingId, String filename, String partnerCode, Object scenarioObj, String caseId) {
        try {
            String scenario = scenarioObj != null ? scenarioObj.toString() : "";
            log.info("处理场景特定回调: threadingId={}, scenario={}, partnerCode={}, caseId={}", threadingId, scenario, partnerCode, caseId);
            
            // 推送切片解析信息到第三方平台
            // 修改判断逻辑：只要有partnerCode（不为null且不为空字符串）就尝试回调
            if (partnerCode != null && !partnerCode.trim().isEmpty()) {
                try {
                    boolean pushSuccess = sliceAnalysisCallbackService.pushSliceAnalysisInfo(threadingId, partnerCode, caseId);
                    if (pushSuccess) {
                        log.info("切片解析信息推送成功: threadingId={}, partnerCode={}, caseId={}", threadingId, partnerCode, caseId);
                    } else {
                        log.warn("切片解析信息推送失败: threadingId={}, partnerCode={}, caseId={}", threadingId, partnerCode, caseId);
                    }
                } catch (Exception e) {
                    log.error("切片解析信息推送异常: threadingId={}, partnerCode={}, caseId={}, error={}", threadingId, partnerCode, caseId, e.getMessage(), e);
                }
            } else {
                log.debug("partnerCode为空或未提供，跳过Info推送: threadingId={}, partnerCode={}", threadingId, partnerCode);
            }
            
        } catch (Exception e) {
            log.error("处理场景特定回调失败: threadingId={}, error={}", threadingId, e.getMessage(), e);
        }
    }
    
    /**
     * 更新数据库中的切片解析结果
     */
    private void updateSliceAnalysisResult(String threadingId, SliceAnalysisResult analysisResult, Object scenario, String filename) {
        try {
            String scenarioStr = scenario != null ? scenario.toString() : "";
            
            SliceEntity slice = null;
            if (scenarioStr.contains("ANT_UPLOAD")) {
                // 蚂蚁上传：threadingId就是sliceId，使用sliceId查询
                slice = sliceService.getBySliceId(threadingId);
                        } else {
                // 本地上传和切片池上传：threadingId是主键id，使用主键id查询
                slice = sliceService.getById(threadingId);
            }
            
            if (slice != null) {
                // 更新表结构字段
                Map<String, Object> metadata = analysisResult.getMetadata();
                if (metadata.get("sliceNo") != null) {
                    slice.setSliceNo(String.valueOf(metadata.get("sliceNo")));
                }
                
                // 设置扫描格式 - 修改为使用文件扩展名小写
                String fileExtension = getFileExtension(filename); // 从文件名中提取扩展名
                if (fileExtension != null && !fileExtension.isEmpty()) {
                    slice.setScanFormat(fileExtension.toLowerCase()); // 使用小写扩展名
                } else if (analysisResult.getFileTypeCode() != null) {
                    // 如果无法从文件名提取扩展名，则使用数字代码作为备选
                    slice.setScanFormat(String.valueOf(analysisResult.getFileTypeCode()));
                }
                
                // 提取倍率信息
                String magnificationValue = extractMagnificationFromMetadata(metadata, threadingId);
                if (magnificationValue != null) {
                    slice.setEnlarge(magnificationValue);
                    log.info("成功提取倍率信息: threadingId={}, enlarge={}", threadingId, magnificationValue);
                } else {
                    log.warn("未能提取到倍率信息: threadingId={}", threadingId);
                }
                
                // 更新时间和状态
                slice.setLastModifiedTime(LocalDateTime.now());
                slice.setQpState("1"); // 解析成功
                
                // ------ 新增: 像素宽高与分辨率 ------
                try {
                    if (metadata != null && !metadata.isEmpty()) {
                        // 宽度
                        Object wObj = metadata.get("width");
                        if (wObj != null) {
                            try {
                                int width = Integer.parseInt(wObj.toString().trim());
                                slice.setOrigPixWidth(width);
                            } catch (NumberFormatException ignore) {
                                log.warn("无法解析像素宽度: {}", wObj);
                            }
                        }

                        // 高度
                        Object hObj = metadata.get("height");
                        if (hObj != null) {
                            try {
                                int height = Integer.parseInt(hObj.toString().trim());
                                slice.setOrigPixHeight(height);
                            } catch (NumberFormatException ignore) {
                                log.warn("无法解析像素高度: {}", hObj);
                            }
                        }

                        // 分辨率
                        Object resObj = metadata.get("resolution");
                        if (resObj != null) {
                            slice.setResolution(resObj.toString());
                        }

                        // 新增：层级数量
                        Object levelCountObj = metadata.get("levelCount");
                        if (levelCountObj != null) {
                            try {
                                int levelCount = Integer.parseInt(levelCountObj.toString().trim());
                                slice.setLevelsCnt(levelCount);
                                log.info("成功提取层级数量: threadingId={}, levelCount={}", threadingId, levelCount);
                            } catch (NumberFormatException ignore) {
                                log.warn("无法解析层级数量: {}", levelCountObj);
                            }
                        } else {
                            log.warn("未能从metadata中获取到层级数量: threadingId={}", threadingId);
                        }
                    }
                } catch (Exception e) {
                    log.warn("设置像素宽高分辨率字段失败: threadingId={}, error={}", threadingId, e.getMessage());
                }
                
                boolean updateResult = sliceService.updateById(slice);
                if (updateResult) {
                    log.info("成功更新切片表(t_c_slice)信息，状态设置为1（解析成功）: threadingId={}, 主键id={}", 
                            threadingId, slice.getId());
                        } else {
                    log.warn("未能更新切片表(t_c_slice)信息: threadingId={}", threadingId);
                        }
                    } else {
                log.warn("未找到切片记录，无法更新解析结果: threadingId={}", threadingId);
            }
            
        } catch (Exception e) {
            log.error("更新切片解析结果失败: threadingId={}, error={}", threadingId, e.getMessage(), e);
        }
    }
    
    /**
     * 从文件名中提取扩展名
     * 
     * @param fileName 文件名
     * @return 文件扩展名（不包含点号），如果没有扩展名则返回null
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return null;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1);
        }
        
        return null;
    }
    
    /**
     * 基于identifier合并分片文件（新方法）
     */
    private void mergeChunksByIdentifier(String identifier, Path targetPath) throws IOException {
        log.info("开始合并分片文件: identifier={}, targetPath={}", identifier, targetPath);
        
        // 获取分片信息并验证
        Set<Integer> uploadedChunks = chunkStatusManager.getUploadedChunksByIdentifier(identifier);
        if (uploadedChunks == null || uploadedChunks.isEmpty()) {
            throw new IOException("未找到任何已上传的分片: identifier=" + identifier);
        }
        
        List<Integer> sortedChunks = new ArrayList<>(uploadedChunks);
        Collections.sort(sortedChunks);
        
        log.info("分片信息验证: identifier={}, 分片数量={}, 分片序号={}", identifier, sortedChunks.size(), sortedChunks);
        
        // 检查分片连续性
        if (!isChunksSequential(sortedChunks)) {
            log.warn("分片序号不连续: identifier={}, sortedChunks={}", identifier, sortedChunks);
        }
        
        // 验证所有分片文件是否存在
        List<String> missingChunks = new ArrayList<>();
        List<String> validChunkPaths = new ArrayList<>();
        long totalChunkSize = 0;
        
        for (Integer chunkNumber : sortedChunks) {
            String chunkPath = chunkStatusManager.getChunkPathByIdentifier(identifier, chunkNumber);
            if (chunkPath == null) {
                missingChunks.add("chunkNumber=" + chunkNumber + ", path=null");
                continue;
            }
            
            File chunkFile = new File(chunkPath);
            if (!chunkFile.exists()) {
                missingChunks.add("chunkNumber=" + chunkNumber + ", path=" + chunkPath + ", exists=false");
                continue;
            }
            
            if (!chunkFile.canRead()) {
                missingChunks.add("chunkNumber=" + chunkNumber + ", path=" + chunkPath + ", readable=false");
                continue;
            }
            
            long chunkSize = chunkFile.length();
            totalChunkSize += chunkSize;
            validChunkPaths.add(chunkPath);
            log.debug("分片文件验证成功: chunkNumber={}, path={}, size={} bytes", chunkNumber, chunkPath, chunkSize);
        }
        
        if (!missingChunks.isEmpty()) {
            String errorMsg = "找不到或无法读取分片文件: identifier=" + identifier + ", missingChunks=" + missingChunks;
            log.error(errorMsg);
            throw new IOException(errorMsg);
        }
        
        log.info("分片文件验证完成: identifier={}, 有效分片数={}, 预计合并后大小={} bytes", 
                identifier, validChunkPaths.size(), totalChunkSize);
        
        // 执行合并
        try (FileChannel outChannel = FileChannel.open(targetPath, 
                StandardOpenOption.CREATE, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING)) {
            
            ByteBuffer buffer = ByteBuffer.allocate(1024 * 1024); // 1MB buffer
            long actualMergedSize = 0;
            
            for (Integer chunkNumber : sortedChunks) {
                String chunkPath = chunkStatusManager.getChunkPathByIdentifier(identifier, chunkNumber);
                log.debug("开始合并分片: identifier={}, chunkNumber={}, chunkPath={}", identifier, chunkNumber, chunkPath);
                
                try (FileChannel inChannel = FileChannel.open(Paths.get(chunkPath), StandardOpenOption.READ)) {
                    long chunkSize = inChannel.size();
                    long chunkMergedSize = 0;
                    
                    while (inChannel.read(buffer) != -1) {
                        buffer.flip();
                        while (buffer.hasRemaining()) {
                            int written = outChannel.write(buffer);
                            chunkMergedSize += written;
                            actualMergedSize += written;
                        }
                        buffer.clear();
                    }
                    
                    log.debug("分片合并完成: identifier={}, chunkNumber={}, 原始大小={}, 合并大小={}", 
                            identifier, chunkNumber, chunkSize, chunkMergedSize);
                    
                    if (chunkSize != chunkMergedSize) {
                        log.warn("分片合并大小不一致: identifier={}, chunkNumber={}, 原始大小={}, 合并大小={}", 
                                identifier, chunkNumber, chunkSize, chunkMergedSize);
                    }
                } catch (Exception e) {
                    String errorMsg = String.format("合并分片失败: identifier=%s, chunkNumber=%d, chunkPath=%s, error=%s", 
                                                   identifier, chunkNumber, chunkPath, e.getMessage());
                    log.error(errorMsg, e);
                    throw new IOException(errorMsg, e);
                }
            }
            
            // 强制刷新到磁盘
            outChannel.force(true);
            
            log.info("分片文件合并完成: identifier={}, 目标文件={}, 预计大小={}, 实际大小={}", 
                    identifier, targetPath, totalChunkSize, actualMergedSize);
            
            // 验证合并后的文件
            if (!Files.exists(targetPath)) {
                throw new IOException("合并文件创建失败: " + targetPath);
            }
            
            long finalFileSize = Files.size(targetPath);
            if (finalFileSize != actualMergedSize) {
                log.warn("合并文件最终大小异常: identifier={}, 实际合并大小={}, 文件系统大小={}", 
                        identifier, actualMergedSize, finalFileSize);
            }
            
        } catch (Exception e) {
            log.error("文件合并过程发生异常: identifier={}, targetPath={}, error={}", identifier, targetPath, e.getMessage(), e);
            // 清理可能生成的不完整文件
            try {
                if (Files.exists(targetPath)) {
                    Files.delete(targetPath);
                    log.info("已清理不完整的合并文件: {}", targetPath);
                }
            } catch (Exception cleanupException) {
                log.warn("清理不完整文件失败: {}, error={}", targetPath, cleanupException.getMessage());
            }
            throw e;
        }
    }
} 
