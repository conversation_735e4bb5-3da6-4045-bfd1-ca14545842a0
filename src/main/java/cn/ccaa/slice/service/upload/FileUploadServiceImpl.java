package cn.ccaa.slice.service.upload;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import cn.ccaa.slice.core.exception.BusinessException;
import cn.ccaa.slice.core.upload.UploadTask;
import cn.ccaa.slice.service.upload.handler.FileUploadHandler;
import cn.ccaa.slice.service.upload.task.UploadTaskManager;
import cn.ccaa.slice.service.upload.validation.FileValidator;
import lombok.extern.slf4j.Slf4j;
import com.github.f4b6a3.ulid.UlidCreator;

/**
 * 文件上传服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FileUploadServiceImpl implements FileUploadService {

    @Lazy
    @Autowired
    private UploadTaskManager taskManager;
    private final FileUploadHandler uploadHandler;
    private final FileValidator fileValidator;

    /**
     * 构造函数
     *
     * @param taskManager 任务管理器
     * @param uploadHandler 上传处理器
     * @param fileValidator 文件验证器
     */
    public FileUploadServiceImpl(
            UploadTaskManager taskManager,
            FileUploadHandler uploadHandler,
            FileValidator fileValidator) {
        this.taskManager = taskManager;
        this.uploadHandler = uploadHandler;
        this.fileValidator = fileValidator;
    }

    @Override
    public UploadTask createUploadTask(String taskId, String fileName, long fileSize, String userId) {
        // 用自定义taskId（如sliceId）创建上传任务
        return taskManager.createTask(taskId, fileName, fileSize, userId);
    }

    @Override
    public UploadTask createUploadTask(String fileName, Long fileSize, String userId) {
        // 兼容老接口，自动生成taskId
        String taskId = UlidCreator.getMonotonicUlid().toString();
        return createUploadTask(taskId, fileName, fileSize != null ? fileSize : 0L, userId);
    }

    @Override
    public UploadTask uploadFile(MultipartFile file, String userId) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("上传文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        long fileSize = file.getSize();

        // 创建上传任务
        UploadTask task = createUploadTask(fileName, fileSize, userId);

        // 上传文件
        return uploadHandler.uploadFile(task, file);
    }

    @Override
    public UploadTask asyncUploadFile(String taskId, MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("上传文件不能为空");
        }

        // 获取任务
        UploadTask task = getTask(taskId);
        if (task == null) {
            throw new BusinessException("上传任务不存在: " + taskId);
        }

        // 异步上传文件
        CompletableFuture<UploadTask> future = uploadHandler.asyncUploadFile(task, file);

        // 返回任务，不等待异步上传完成
        return task;
    }

    @Override
    public UploadTask uploadFileChunk(String taskId, MultipartFile chunk, int chunkIndex, int totalChunks) {
        if (chunk == null || chunk.isEmpty()) {
            throw new BusinessException("上传分块不能为空");
        }

        // 获取任务
        UploadTask task = getTask(taskId);
        if (task == null) {
            throw new BusinessException("上传任务不存在: " + taskId);
        }

        // 上传分块
        return uploadHandler.uploadFileChunk(task, chunk, chunkIndex, totalChunks);
    }

    @Override
    public UploadTask uploadFile(String fileName, InputStream inputStream, long fileSize, String userId) {
        if (inputStream == null) {
            throw new BusinessException("输入流不能为空");
        }

        // 创建上传任务
        UploadTask task = createUploadTask(fileName, fileSize, userId);

        // 上传文件
        return uploadHandler.uploadFile(task, inputStream, "application/octet-stream");
    }

    @Override
    public void updateProgress(String taskId, long uploadedBytes) {
        UploadTask task = getTask(taskId);
        if (task != null) {
            taskManager.updateTaskProgress(task, uploadedBytes);
        }
    }

    @Override
    public UploadTask updateTaskStatus(String taskId, Long uploadedBytes, String status,
                                     String fileUrl, String errorMessage, Boolean completed) {
        return taskManager.updateTaskDetails(taskId, uploadedBytes, status, fileUrl, errorMessage, completed);
    }

    @Override
    public UploadTask completeUpload(String taskId) {
        UploadTask task = getTask(taskId);
        if (task == null) {
            throw new BusinessException("上传任务不存在: " + taskId);
        }

        return uploadHandler.completeUpload(task);
    }

    @Override
    public void cancelUpload(String taskId) {
        UploadTask task = getTask(taskId);
        if (task != null) {
            uploadHandler.cancelUpload(task);
        }
    }

    @Override
    public UploadTask getUploadTask(String taskId) {
        return taskManager.getTask(taskId);
    }

    @Override
    public Map<String, UploadTask> batchGetUploadTasks(List<String> taskIds) {
        return taskManager.batchGetTasks(taskIds);
    }

    @Override
    public boolean isUploadCompleted(String taskId) {
        UploadTask task = getTask(taskId);
        if (task == null) {
            return false;
        }

        return task.getProgress().isCompleted();
    }

    @Override
    public List<UploadTask> getTasksByUserId(String userId) {
        // 实现根据用户ID获取任务列表的逻辑
        return taskManager.getTasksByUserId(userId);
    }

    @Override
    public void deleteUploadTask(String taskId) {
        // 删除上传任务实现
        log.info("删除上传任务: taskId={}", taskId);
        
        try {
            UploadTask task = getTask(taskId);
            if (task != null) {
                // 取消上传
                uploadHandler.cancelUpload(task);
            }
            
            // 取消任务（相当于删除）
            taskManager.cancelTask(taskId);
        } catch (Exception e) {
            log.error("删除上传任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            throw new BusinessException("删除上传任务失败: " + e.getMessage());
        }
    }
}
