package cn.ccaa.slice.service.upload.chunk;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.stereotype.Service;
import org.springframework.scheduling.annotation.Scheduled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.beans.factory.annotation.Value;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.ccaa.slice.service.upload.FileUploadService;
import cn.ccaa.slice.core.upload.UploadStatus;
import cn.ccaa.slice.core.upload.UploadTask;
import cn.ccaa.slice.service.SliceService;
import cn.ccaa.slice.core.model.SliceEntity;

/**
 * 分片上传状态管理器
 * 使用Redis存储分片上传状态
 *
 * <AUTHOR>
 */
@Service
public class ChunkStatusManager {
    private static final Logger log = LoggerFactory.getLogger(ChunkStatusManager.class);
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Lazy  // 使用懒加载解决循环依赖
    @Autowired
    private FileUploadService fileUploadService;
    
    @Autowired
    private SliceService sliceService;
    
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    // 任务信息哈希表的键前缀
    private static final String TASK_KEY_PREFIX = "upload:task:";
    // 分片集合的键前缀
    private static final String CHUNKS_KEY_PREFIX = "upload:chunks:";
    // 分片路径的键前缀
    private static final String CHUNK_PATH_KEY_PREFIX = "upload:chunk:path:";
    // 键的默认过期时间(24小时)
    private static final long DEFAULT_EXPIRY = 24 * 60 * 60;
    
    // 文件存储基础目录
    @Value("${storage.base-dir:./data}")
    private String baseDir;
    
    // 临时分片目录保留时长（毫秒），默认24小时
    @Value("${upload.chunk.temp-retain-ms:86400000}")
    private long tempRetainMs;
    
    // 分布式锁的键前缀
    private static final String LOCK_KEY_PREFIX = "upload:lock:";
    
    /**
     * 初始化任务信息
     */
    public void initTask(String taskId, String filename, long totalSize, int totalChunks) {
        String taskKey = TASK_KEY_PREFIX + taskId;
        Map<String, Object> taskInfo = new HashMap<>();
        taskInfo.put("filename", filename);
        taskInfo.put("totalSize", totalSize);
        taskInfo.put("totalChunks", totalChunks);
        taskInfo.put("uploadedSize", 0L);
        taskInfo.put("status", "UPLOADING");
        taskInfo.put("startTime", System.currentTimeMillis());
        taskInfo.put("lastUpdateTime", System.currentTimeMillis());
        
        log.info("初始化任务信息: taskId={}, taskKey={}, taskInfo={}", taskId, taskKey, taskInfo);
        
        redisTemplate.opsForHash().putAll(taskKey, taskInfo);
        redisTemplate.expire(taskKey, DEFAULT_EXPIRY, TimeUnit.SECONDS);
        String chunksKey = CHUNKS_KEY_PREFIX + taskId;
        redisTemplate.expire(chunksKey, DEFAULT_EXPIRY, TimeUnit.SECONDS);
        
        // 验证数据是否成功存储
        try {
            Map<Object, Object> storedInfo = redisTemplate.opsForHash().entries(taskKey);
            boolean taskKeyExists = Boolean.TRUE.equals(redisTemplate.hasKey(taskKey));
            boolean chunksKeyExists = Boolean.TRUE.equals(redisTemplate.hasKey(chunksKey));
            log.info("任务初始化验证: taskId={}, taskKeyExists={}, chunksKeyExists={}, storedInfo={}", 
                    taskId, taskKeyExists, chunksKeyExists, storedInfo);
        } catch (Exception e) {
            log.error("任务初始化验证失败: taskId={}, error={}", taskId, e.getMessage());
        }
    }
    
    /**
     * 记录分片上传
     */
    public void saveChunk(String taskId, int chunkNumber, long chunkSize, String tempPath) {
        String taskKey = TASK_KEY_PREFIX + taskId;
        String chunksKey = CHUNKS_KEY_PREFIX + taskId;
        String chunkPathKey = CHUNK_PATH_KEY_PREFIX + taskId + ":" + chunkNumber;
        
        log.info("保存分片信息: taskId={}, chunkNumber={}, chunkSize={}, taskKey={}, chunksKey={}", 
                taskId, chunkNumber, chunkSize, taskKey, chunksKey);
        
        redisTemplate.opsForSet().add(chunksKey, chunkNumber);
        redisTemplate.opsForHash().increment(taskKey, "uploadedSize", chunkSize);
        redisTemplate.opsForHash().put(taskKey, "lastUpdateTime", System.currentTimeMillis());
        redisTemplate.opsForValue().set(chunkPathKey, tempPath);
        
        // 更新过期时间，增加到7天，适应大文件上传场景
        redisTemplate.expire(taskKey, 7, TimeUnit.DAYS);
        redisTemplate.expire(chunksKey, 7, TimeUnit.DAYS);
        redisTemplate.expire(chunkPathKey, 7, TimeUnit.DAYS);
        
        // 验证数据是否成功存储
        try {
            Long chunksCount = redisTemplate.opsForSet().size(chunksKey);
            Set<Object> chunks = redisTemplate.opsForSet().members(chunksKey);
            Object uploadedSize = redisTemplate.opsForHash().get(taskKey, "uploadedSize");
            log.info("分片保存验证: taskId={}, chunkNumber={}, chunksCount={}, chunks={}, uploadedSize={}", 
                    taskId, chunkNumber, chunksCount, chunks, uploadedSize);
        } catch (Exception e) {
            log.error("分片保存验证失败: taskId={}, chunkNumber={}, error={}", taskId, chunkNumber, e.getMessage());
        }
    }
    
    /**
     * 检查任务是否所有分片都已上传
     */
    public boolean isUploadComplete(String taskId) {
        String taskKey = TASK_KEY_PREFIX + taskId;
        String chunksKey = CHUNKS_KEY_PREFIX + taskId;
        Object totalChunksObj = redisTemplate.opsForHash().get(taskKey, "totalChunks");
        if (totalChunksObj == null) {
            return false;
        }
        int totalChunks = Integer.parseInt(totalChunksObj.toString());
        Long uploadedChunks = redisTemplate.opsForSet().size(chunksKey);
        return uploadedChunks != null && uploadedChunks == totalChunks;
    }
    
    /**
     * 获取所有已上传的分片
     */
    public Set<Integer> getUploadedChunks(String taskId) {
        String chunksKey = CHUNKS_KEY_PREFIX + taskId;
        Set<Object> chunks = redisTemplate.opsForSet().members(chunksKey);
        if (chunks == null) {
            return Collections.emptySet();
        }
        return chunks.stream().map(o -> Integer.parseInt(o.toString())).collect(Collectors.toSet());
    }
    
    /**
     * 获取分片临时路径
     */
    public String getChunkPath(String taskId, int chunkNumber) {
        String chunkPathKey = CHUNK_PATH_KEY_PREFIX + taskId + ":" + chunkNumber;
        Object val = redisTemplate.opsForValue().get(chunkPathKey);
        return val == null ? null : val.toString();
    }
    
    /**
     * 更新任务状态
     */
    public void updateTaskStatus(String taskId, String status, String errorMessage) {
        String taskKey = TASK_KEY_PREFIX + taskId;
        redisTemplate.opsForHash().put(taskKey, "status", status);
        redisTemplate.opsForHash().put(taskKey, "lastUpdateTime", System.currentTimeMillis());
        if (errorMessage != null) {
            redisTemplate.opsForHash().put(taskKey, "errorMessage", errorMessage);
        }
        
        // 更新过期时间，确保状态更新后不会过快过期
        redisTemplate.expire(taskKey, 7, TimeUnit.DAYS);
    }
    
    /**
     * 获取任务信息
     */
    public Map<String, Object> getTaskInfo(String taskId) {
        String taskKey = TASK_KEY_PREFIX + taskId;
        Map<Object, Object> entries;
        
        try {
            entries = redisTemplate.opsForHash().entries(taskKey);
        } catch (Exception e) {
            log.warn("获取Redis任务信息异常: taskId={}, error={}", taskId, e.getMessage());
            return Collections.emptyMap();
        }
        
        if (entries.isEmpty()) {
            return Collections.emptyMap();
        }
        
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            String key = entry.getKey() != null ? entry.getKey().toString() : "";
            Object value = entry.getValue();
            
            // 特殊处理值为字符串的情况，避免JSON解析错误
            if (value != null) {
                result.put(key, value);
            } else {
                result.put(key, "");
            }
        }
        
        return result;
    }
    
    /**
     * 清理任务相关的所有Redis键
     */
    public void cleanupTask(String taskId) {
        String taskKey = TASK_KEY_PREFIX + taskId;
        String chunksKey = CHUNKS_KEY_PREFIX + taskId;
        Set<Integer> chunks = getUploadedChunks(taskId);
        for (Integer chunk : chunks) {
            String chunkPathKey = CHUNK_PATH_KEY_PREFIX + taskId + ":" + chunk;
            redisTemplate.delete(chunkPathKey);
        }
        redisTemplate.delete(Arrays.asList(taskKey, chunksKey));
    }
    
    /**
     * 获取所有活跃任务ID
     * @return 活跃任务ID集合
     */
    public Set<String> getAllActiveTasks() {
        Set<String> keys = redisTemplate.keys(TASK_KEY_PREFIX + "*");
        if (keys == null || keys.isEmpty()) {
            return Collections.emptySet();
        }
        
        return keys.stream()
            .map(key -> key.substring(TASK_KEY_PREFIX.length()))
            .collect(Collectors.toSet());
    }
    
    /**
     * 定期同步任务状态，确保ChunkStatusManager和UploadTaskManager的数据一致性
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000)
    public void syncTaskStatus() {
        log.info("开始同步分片上传任务状态...");
        int syncCount = 0;
        int errorCount = 0;
        
        try {
            // 获取所有活跃的分片任务
            Set<String> activeTasks = getAllActiveTasks();
            log.debug("发现{}个活跃分片任务", activeTasks.size());
            
            for (String taskId : activeTasks) {
                try {
                    // 获取分片任务信息
                    Map<String, Object> taskInfo = getTaskInfo(taskId);
                    if (taskInfo.isEmpty()) {
                        continue;
                    }
                    
                    // 尝试从FileUploadService获取上传任务
                    UploadTask uploadTask = null;
                    try {
                        uploadTask = fileUploadService.getUploadTask(taskId);
                    } catch (Exception e) {
                        log.warn("获取上传任务异常，将尝试从数据库恢复: taskId={}, error={}", taskId, e.getMessage());
                    }
                    
                    if (uploadTask == null) {
                        // 如果上传任务不存在，从数据库获取切片信息
                        SliceEntity sliceEntity = null;
                        try {
                            sliceEntity = sliceService.getBySliceId(taskId);
                        } catch (Exception e) {
                            log.warn("查询切片信息失败: taskId={}, error={}", taskId, e.getMessage());
                            continue;
                        }
                        
                        if (sliceEntity != null) {
                            // 构建任务状态信息，安全处理Redis中的数据
                            String filename = sliceEntity.getQpName();
                            long fileSize = sliceEntity.getQpSize() != null ? sliceEntity.getQpSize().longValue() : 0L;
                            
                            // 安全获取状态值，避免类型转换异常
                            String status = "UPLOADING";
                            Object statusObj = taskInfo.get("status");
                            if (statusObj != null) {
                                if (statusObj instanceof String) {
                                    status = (String) statusObj;
                                } else {
                                    status = String.valueOf(statusObj);
                                }
                            }
                            
                            // 同步到UploadTaskManager
                            log.info("正在同步任务状态: taskId={}, status={}", taskId, status);
                            try {
                                fileUploadService.updateTaskStatus(
                                    taskId,
                                    fileSize,
                                    "UPLOADING".equals(status) ? UploadStatus.UPLOADING.toString() : 
                                    "COMPLETED".equals(status) ? UploadStatus.COMPLETED.toString() : 
                                    "FAILED".equals(status) ? UploadStatus.FAILED.toString() : 
                                    UploadStatus.UPLOADING.toString(),
                                    null,
                                    "状态同步: " + status,
                                    "COMPLETED".equals(status)
                                );
                                syncCount++;
                            } catch (Exception e) {
                                log.error("同步任务状态到UploadTaskManager失败: taskId={}, error={}", taskId, e.getMessage());
                                errorCount++;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("同步任务状态失败: taskId={}, error={}", taskId, e.getMessage());
                    errorCount++;
                }
            }
            
            log.info("任务状态同步完成，成功同步{}个任务，失败{}个任务", syncCount, errorCount);
        } catch (Exception e) {
            log.error("任务状态同步过程发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 更新分片任务的Redis键过期时间
     * @param taskId 任务ID
     * @param days 过期天数
     */
    public void updateTaskExpiry(String taskId, int days) {
        String taskKey = TASK_KEY_PREFIX + taskId;
        String chunksKey = CHUNKS_KEY_PREFIX + taskId;
        
        // 检查键是否存在
        if (Boolean.TRUE.equals(redisTemplate.hasKey(taskKey))) {
            redisTemplate.expire(taskKey, days, TimeUnit.DAYS);
            log.debug("更新任务键过期时间: {}, 天数={}", taskKey, days);
        }
        
        if (Boolean.TRUE.equals(redisTemplate.hasKey(chunksKey))) {
            redisTemplate.expire(chunksKey, days, TimeUnit.DAYS);
            log.debug("更新分片集合键过期时间: {}, 天数={}", chunksKey, days);
        }
        
        // 更新所有分片路径键
        Set<Integer> chunks = getUploadedChunks(taskId);
        for (Integer chunk : chunks) {
            String chunkPathKey = CHUNK_PATH_KEY_PREFIX + taskId + ":" + chunk;
            if (Boolean.TRUE.equals(redisTemplate.hasKey(chunkPathKey))) {
                redisTemplate.expire(chunkPathKey, days, TimeUnit.DAYS);
            }
        }
        
        log.debug("已更新任务的所有Redis键过期时间: taskId={}, 天数={}", taskId, days);
    }
    
    /**
     * 批量获取上传进度百分比
     * 通过已上传片数/总片数计算进度
     * 
     * @param taskIds 任务ID列表
     * @return 任务ID到进度百分比的映射，key为taskId，value为进度百分比(0-100)
     */
    public Map<String, Integer> batchGetUploadProgress(List<String> taskIds) {
        if (taskIds == null || taskIds.isEmpty()) {
            return Collections.emptyMap();
        }
        
        long startTime = System.currentTimeMillis();
        Map<String, Integer> progressMap = new HashMap<>();
        
        // 添加调试日志：检查Redis连接和键存在情况
        log.info("开始批量查询上传进度: taskIds={}", taskIds);
        for (String taskId : taskIds) {
            String taskKey = TASK_KEY_PREFIX + taskId;
            String chunksKey = CHUNKS_KEY_PREFIX + taskId;
            boolean taskKeyExists = Boolean.TRUE.equals(redisTemplate.hasKey(taskKey));
            boolean chunksKeyExists = Boolean.TRUE.equals(redisTemplate.hasKey(chunksKey));
            log.info("Redis键检查: taskId={}, taskKey={} exists={}, chunksKey={} exists={}", 
                    taskId, taskKey, taskKeyExists, chunksKey, chunksKeyExists);
            
            // 如果键存在，尝试获取一些基本信息
            if (taskKeyExists) {
                try {
                    Map<Object, Object> taskInfo = redisTemplate.opsForHash().entries(taskKey);
                    log.info("任务信息: taskId={}, taskInfo={}", taskId, taskInfo);
                } catch (Exception e) {
                    log.warn("获取任务信息失败: taskId={}, error={}", taskId, e.getMessage());
                }
            }
            
            if (chunksKeyExists) {
                try {
                    Long chunksCount = redisTemplate.opsForSet().size(chunksKey);
                    Set<Object> chunks = redisTemplate.opsForSet().members(chunksKey);
                    log.info("分片信息: taskId={}, chunksCount={}, chunks={}", taskId, chunksCount, chunks);
                } catch (Exception e) {
                    log.warn("获取分片信息失败: taskId={}, error={}", taskId, e.getMessage());
                }
            }
        }
        
        try {
            // 使用Pipeline批量查询，提高性能
            List<Object> results = redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                for (String taskId : taskIds) {
                    String taskKey = TASK_KEY_PREFIX + taskId;
                    String chunksKey = CHUNKS_KEY_PREFIX + taskId;
                    
                    // 获取任务信息（主要是totalChunks和status）
                    connection.hashCommands().hGetAll(taskKey.getBytes());
                    // 获取已上传分片数量
                    connection.setCommands().sCard(chunksKey.getBytes());
                }
                return null;
            });
            
            log.info("Pipeline查询结果数量: {}, 期望结果数量: {}", results.size(), taskIds.size() * 2);
            
            // 处理Pipeline结果
            for (int i = 0; i < taskIds.size(); i++) {
                String taskId = taskIds.get(i);
                log.info("处理第{}个任务: taskId={}", i + 1, taskId);
                
                try {
                    // 每个taskId对应两个查询结果：任务信息和分片数量
                    int taskInfoIndex = i * 2;
                    int chunksCountIndex = i * 2 + 1;
                    
                    log.info("计算索引: taskInfoIndex={}, chunksCountIndex={}, resultsSize={}", 
                            taskInfoIndex, chunksCountIndex, results.size());
                    
                    if (taskInfoIndex >= results.size() || chunksCountIndex >= results.size()) {
                        log.warn("批量查询结果索引越界: taskId={}, taskInfoIndex={}, chunksCountIndex={}, resultsSize={}", 
                                taskId, taskInfoIndex, chunksCountIndex, results.size());
                        continue;
                    }
                    
                    // 获取任务信息
                    Object taskInfoObj = results.get(taskInfoIndex);
                    Object chunksCountObj = results.get(chunksCountIndex);
                    
                    log.info("原始Pipeline结果: taskId={}, taskInfoObj类型={}, chunksCountObj类型={}", 
                            taskId, 
                            taskInfoObj != null ? taskInfoObj.getClass().getSimpleName() : "null",
                            chunksCountObj != null ? chunksCountObj.getClass().getSimpleName() : "null");
                    
                    log.info("原始Pipeline结果值: taskId={}, taskInfoObj={}, chunksCountObj={}", 
                            taskId, taskInfoObj, chunksCountObj);
                    
                    // 处理Pipeline返回的结果，根据实际类型进行转换
                    Map<String, String> taskInfo = new HashMap<>();
                    Long uploadedChunksCount = null;
                    
                    // 处理任务信息
                    try {
                        if (taskInfoObj instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<Object, Object> rawTaskInfo = (Map<Object, Object>) taskInfoObj;
                            log.info("成功获取任务信息Map: taskId={}, size={}", taskId, rawTaskInfo.size());
                            
                            // 转换为String类型的Map
                            for (Map.Entry<Object, Object> entry : rawTaskInfo.entrySet()) {
                                if (entry.getKey() != null && entry.getValue() != null) {
                                    String key = entry.getKey().toString();
                                    String value = entry.getValue().toString();
                                    taskInfo.put(key, value);
                                    log.debug("转换键值对: {}={}", key, value);
                                }
                            }
                            log.info("任务信息转换完成: taskId={}, taskInfo={}", taskId, taskInfo);
                        } else {
                            log.warn("taskInfoObj不是Map类型: taskId={}, 实际类型={}", taskId, 
                                    taskInfoObj != null ? taskInfoObj.getClass() : "null");
                        }
                    } catch (Exception e) {
                        log.error("taskInfoObj处理失败: taskId={}, error={}", taskId, e.getMessage(), e);
                    }
                    
                    // 处理分片数量
                    try {
                        if (chunksCountObj instanceof Long) {
                            uploadedChunksCount = (Long) chunksCountObj;
                            log.info("成功转换chunksCountObj为Long: taskId={}, value={}", taskId, uploadedChunksCount);
                        } else if (chunksCountObj instanceof Integer) {
                            uploadedChunksCount = ((Integer) chunksCountObj).longValue();
                            log.info("转换chunksCountObj从Integer为Long: taskId={}, value={}", taskId, uploadedChunksCount);
                        } else {
                            log.warn("chunksCountObj不是数字类型: taskId={}, 实际类型={}, 值={}", taskId, 
                                    chunksCountObj != null ? chunksCountObj.getClass() : "null", chunksCountObj);
                        }
                    } catch (Exception e) {
                        log.error("chunksCountObj类型转换失败: taskId={}, error={}", taskId, e.getMessage(), e);
                    }
                    
                    log.info("最终转换结果: taskId={}, taskInfo.size={}, uploadedChunksCount={}", 
                            taskId, taskInfo.size(), uploadedChunksCount);
                    
                    if (taskInfo.isEmpty()) {
                        log.warn("任务信息为空，跳过: taskId={}", taskId);
                        continue;
                    }
                    
                    log.info("转换后的任务信息: taskId={}, taskInfo={}", taskId, taskInfo);
                    
                    // 计算进度
                    int uploadedCount = uploadedChunksCount != null ? uploadedChunksCount.intValue() : 0;
                    log.info("准备计算进度: taskId={}, uploadedCount={}", taskId, uploadedCount);
                    
                    int progress = calculateProgress(taskInfo, uploadedCount);
                    
                    if (progress >= 0) {
                        progressMap.put(taskId, progress);
                        log.info("成功计算进度: taskId={}, progress={}%", taskId, progress);
                    } else {
                        log.warn("进度计算返回负值，跳过: taskId={}, progress={}", taskId, progress);
                    }
                    
                } catch (Exception e) {
                    log.error("处理单个任务进度计算失败: taskId={}, error={}", taskId, e.getMessage(), e);
                    // 单个任务失败不影响其他任务，继续处理
                }
            }
            
        } catch (Exception e) {
            log.error("批量获取上传进度失败: error={}", e.getMessage(), e);
            // 如果批量查询失败，降级为单个查询
            return fallbackToSingleQuery(taskIds);
        }
        
        log.info("批量获取上传进度完成: 请求{}个任务，成功获取{}个任务进度，耗时{}ms", 
                taskIds.size(), progressMap.size(), System.currentTimeMillis() - startTime);
        return progressMap;
    }
    
    /**
     * 计算单个任务的上传进度
     * 
     * @param taskInfo 任务信息
     * @param uploadedChunksCount 已上传分片数量
     * @return 进度百分比(0-100)
     */
    private int calculateProgress(Map<String, String> taskInfo, int uploadedChunksCount) {
        log.info("开始计算进度: taskInfo={}, uploadedChunksCount={}", taskInfo, uploadedChunksCount);
        
        try {
            // 获取总分片数
            String totalChunksStr = taskInfo.get("totalChunks");
            log.info("获取totalChunks字符串: {}", totalChunksStr);
            
            if (totalChunksStr == null || totalChunksStr.isEmpty()) {
                log.warn("任务信息中缺少totalChunks字段");
                return 0;
            }
            
            int totalChunks;
            try {
                totalChunks = Integer.parseInt(totalChunksStr);
                log.info("解析totalChunks成功: {}", totalChunks);
            } catch (NumberFormatException e) {
                log.warn("解析totalChunks失败: {}", totalChunksStr);
                return 0;
            }
            
            if (totalChunks <= 0) {
                log.warn("totalChunks值无效: {}", totalChunks);
                return 0;
            }
            
            // 简单计算：已上传片数 / 总片数 * 100
            int progress = (uploadedChunksCount * 100) / totalChunks;
            log.info("进度计算: {}片/{} = {}%", uploadedChunksCount, totalChunks, progress);
            
            // 确保进度在0-100之间
            progress = Math.max(0, Math.min(100, progress));
            log.info("最终进度: {}%", progress);
            
            return progress;
            
        } catch (Exception e) {
            log.error("计算进度时发生异常: taskInfo={}, uploadedChunksCount={}, error={}", 
                    taskInfo, uploadedChunksCount, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 降级处理：单个查询模式
     * 当批量查询失败时使用
     * 
     * @param taskIds 任务ID列表
     * @return 进度映射
     */
    private Map<String, Integer> fallbackToSingleQuery(List<String> taskIds) {
        log.warn("批量查询失败，降级为单个查询模式");
        Map<String, Integer> progressMap = new HashMap<>();
        
        for (String taskId : taskIds) {
            try {
                // 获取任务信息
                Map<String, Object> taskInfo = getTaskInfo(taskId);
                if (taskInfo.isEmpty()) {
                    continue;
                }
                
                // 获取已上传分片数量
                Set<Integer> uploadedChunks = getUploadedChunks(taskId);
                int uploadedChunksCount = uploadedChunks.size();
                
                // 转换taskInfo为String类型的Map
                Map<String, String> taskInfoStr = new HashMap<>();
                for (Map.Entry<String, Object> entry : taskInfo.entrySet()) {
                    if (entry.getValue() != null) {
                        taskInfoStr.put(entry.getKey(), entry.getValue().toString());
                    }
                }
                
                // 计算进度
                int progress = calculateProgress(taskInfoStr, uploadedChunksCount);
                progressMap.put(taskId, progress);
                
            } catch (Exception e) {
                log.error("单个查询任务进度失败: taskId={}, error={}", taskId, e.getMessage());
            }
        }
        
        return progressMap;
    }
    
    /**
     * 基于identifier初始化任务信息（新方法）
     * 使用分布式锁确保并发安全
     */
    public void initTaskByIdentifier(String identifier, String filename, long totalSize, int totalChunks) {
        String taskKey = TASK_KEY_PREFIX + identifier;
        Map<String, Object> taskInfo = new HashMap<>();
        taskInfo.put("filename", filename);
        taskInfo.put("totalSize", totalSize);
        taskInfo.put("totalChunks", totalChunks);
        taskInfo.put("uploadedSize", 0L);
        taskInfo.put("status", "UPLOADING");
        taskInfo.put("startTime", System.currentTimeMillis());
        taskInfo.put("lastUpdateTime", System.currentTimeMillis());
        
        log.info("基于identifier初始化任务信息: identifier={}, taskKey={}, taskInfo={}", identifier, taskKey, taskInfo);
        
        redisTemplate.opsForHash().putAll(taskKey, taskInfo);
        redisTemplate.expire(taskKey, DEFAULT_EXPIRY, TimeUnit.SECONDS);
        String chunksKey = CHUNKS_KEY_PREFIX + identifier;
        redisTemplate.expire(chunksKey, DEFAULT_EXPIRY, TimeUnit.SECONDS);
        
        // 验证数据是否成功存储
        try {
            Map<Object, Object> storedInfo = redisTemplate.opsForHash().entries(taskKey);
            boolean taskKeyExists = Boolean.TRUE.equals(redisTemplate.hasKey(taskKey));
            boolean chunksKeyExists = Boolean.TRUE.equals(redisTemplate.hasKey(chunksKey));
            log.info("基于identifier的任务初始化验证: identifier={}, taskKeyExists={}, chunksKeyExists={}, storedInfo={}", 
                    identifier, taskKeyExists, chunksKeyExists, storedInfo);
        } catch (Exception e) {
            log.error("基于identifier的任务初始化验证失败: identifier={}, error={}", identifier, e.getMessage());
        }
    }
    
    /**
     * 基于identifier记录分片上传（新方法）
     */
    public void saveChunkByIdentifier(String identifier, int chunkNumber, long chunkSize, String tempPath) {
        String taskKey = TASK_KEY_PREFIX + identifier;
        String chunksKey = CHUNKS_KEY_PREFIX + identifier;
        String chunkPathKey = CHUNK_PATH_KEY_PREFIX + identifier + ":" + chunkNumber;
        
        log.info("基于identifier保存分片信息: identifier={}, chunkNumber={}, chunkSize={}, taskKey={}, chunksKey={}", 
                identifier, chunkNumber, chunkSize, taskKey, chunksKey);
        
        redisTemplate.opsForSet().add(chunksKey, chunkNumber);
        redisTemplate.opsForHash().increment(taskKey, "uploadedSize", chunkSize);
        redisTemplate.opsForHash().put(taskKey, "lastUpdateTime", System.currentTimeMillis());
        redisTemplate.opsForValue().set(chunkPathKey, tempPath);
        
        // 更新过期时间，增加到7天，适应大文件上传场景
        redisTemplate.expire(taskKey, 7, TimeUnit.DAYS);
        redisTemplate.expire(chunksKey, 7, TimeUnit.DAYS);
        redisTemplate.expire(chunkPathKey, 7, TimeUnit.DAYS);
        
        // 验证数据是否成功存储
        try {
            Long chunksCount = redisTemplate.opsForSet().size(chunksKey);
            Set<Object> chunks = redisTemplate.opsForSet().members(chunksKey);
            Object uploadedSize = redisTemplate.opsForHash().get(taskKey, "uploadedSize");
            log.info("基于identifier的分片保存验证: identifier={}, chunkNumber={}, chunksCount={}, chunks={}, uploadedSize={}", 
                    identifier, chunkNumber, chunksCount, chunks, uploadedSize);
        } catch (Exception e) {
            log.error("基于identifier的分片保存验证失败: identifier={}, chunkNumber={}, error={}", identifier, chunkNumber, e.getMessage());
        }
    }
    
    /**
     * 基于identifier检查任务是否所有分片都已上传（新方法）
     */
    public boolean isUploadCompleteByIdentifier(String identifier) {
        String taskKey = TASK_KEY_PREFIX + identifier;
        String chunksKey = CHUNKS_KEY_PREFIX + identifier;
        Object totalChunksObj = redisTemplate.opsForHash().get(taskKey, "totalChunks");
        if (totalChunksObj == null) {
            return false;
        }
        int totalChunks = Integer.parseInt(totalChunksObj.toString());
        Long uploadedChunks = redisTemplate.opsForSet().size(chunksKey);
        return uploadedChunks != null && uploadedChunks == totalChunks;
    }
    
    /**
     * 基于identifier获取所有已上传的分片（新方法）
     */
    public Set<Integer> getUploadedChunksByIdentifier(String identifier) {
        String chunksKey = CHUNKS_KEY_PREFIX + identifier;
        Set<Object> chunks = redisTemplate.opsForSet().members(chunksKey);
        if (chunks == null) {
            return Collections.emptySet();
        }
        return chunks.stream().map(o -> Integer.parseInt(o.toString())).collect(Collectors.toSet());
    }
    
    /**
     * 基于identifier获取分片临时路径（新方法）
     */
    public String getChunkPathByIdentifier(String identifier, int chunkNumber) {
        String chunkPathKey = CHUNK_PATH_KEY_PREFIX + identifier + ":" + chunkNumber;
        Object val = redisTemplate.opsForValue().get(chunkPathKey);
        return val == null ? null : val.toString();
    }
    
    /**
     * 基于identifier更新任务状态（新方法）
     */
    public void updateTaskStatusByIdentifier(String identifier, String status, String errorMessage) {
        String taskKey = TASK_KEY_PREFIX + identifier;
        redisTemplate.opsForHash().put(taskKey, "status", status);
        redisTemplate.opsForHash().put(taskKey, "lastUpdateTime", System.currentTimeMillis());
        if (errorMessage != null) {
            redisTemplate.opsForHash().put(taskKey, "errorMessage", errorMessage);
        }
        
        // 更新过期时间，确保状态更新后不会过快过期
        redisTemplate.expire(taskKey, 7, TimeUnit.DAYS);
    }
    
    /**
     * 基于identifier获取任务信息（新方法）
     */
    public Map<String, Object> getTaskInfoByIdentifier(String identifier) {
        String taskKey = TASK_KEY_PREFIX + identifier;
        Map<Object, Object> entries;
        
        try {
            entries = redisTemplate.opsForHash().entries(taskKey);
        } catch (Exception e) {
            log.warn("获取Redis任务信息异常: identifier={}, error={}", identifier, e.getMessage());
            return Collections.emptyMap();
        }
        
        if (entries.isEmpty()) {
            return Collections.emptyMap();
        }
        
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            String key = entry.getKey() != null ? entry.getKey().toString() : "";
            Object value = entry.getValue();
            
            // 特殊处理值为字符串的情况，避免JSON解析错误
            if (value != null) {
                result.put(key, value);
            } else {
                result.put(key, "");
            }
        }
        
        return result;
    }
    
    /**
     * 基于identifier清理任务相关的所有Redis键（新方法）
     */
    public void cleanupTaskByIdentifier(String identifier) {
        String taskKey = TASK_KEY_PREFIX + identifier;
        String chunksKey = CHUNKS_KEY_PREFIX + identifier;
        Set<Integer> chunks = getUploadedChunksByIdentifier(identifier);
        for (Integer chunk : chunks) {
            String chunkPathKey = CHUNK_PATH_KEY_PREFIX + identifier + ":" + chunk;
            redisTemplate.delete(chunkPathKey);
        }
        redisTemplate.delete(Arrays.asList(taskKey, chunksKey));
    }
    
    /**
     * 基于identifier更新分片任务的Redis键过期时间（新方法）
     */
    public void updateTaskExpiryByIdentifier(String identifier, int days) {
        String taskKey = TASK_KEY_PREFIX + identifier;
        String chunksKey = CHUNKS_KEY_PREFIX + identifier;
        
        // 检查键是否存在
        if (Boolean.TRUE.equals(redisTemplate.hasKey(taskKey))) {
            redisTemplate.expire(taskKey, days, TimeUnit.DAYS);
            log.debug("更新任务键过期时间: {}, 天数={}", taskKey, days);
        }
        
        if (Boolean.TRUE.equals(redisTemplate.hasKey(chunksKey))) {
            redisTemplate.expire(chunksKey, days, TimeUnit.DAYS);
            log.debug("更新分片集合键过期时间: {}, 天数={}", chunksKey, days);
        }
        
        // 更新所有分片路径键
        Set<Integer> chunks = getUploadedChunksByIdentifier(identifier);
        for (Integer chunk : chunks) {
            String chunkPathKey = CHUNK_PATH_KEY_PREFIX + identifier + ":" + chunk;
            if (Boolean.TRUE.equals(redisTemplate.hasKey(chunkPathKey))) {
                redisTemplate.expire(chunkPathKey, days, TimeUnit.DAYS);
            }
        }
        
        log.debug("已更新任务的所有Redis键过期时间: identifier={}, 天数={}", identifier, days);
    }
    
    /**
     * 获取分布式锁
     * @param identifier 标识符
     * @param lockValue 锁值
     * @param expireSeconds 过期秒数
     * @return 是否获取成功
     */
    public boolean acquireLock(String identifier, String lockValue, int expireSeconds) {
        String lockKey = LOCK_KEY_PREFIX + identifier;
        try {
            Boolean result = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, Duration.ofSeconds(expireSeconds));
            log.debug("尝试获取分布式锁: lockKey={}, lockValue={}, result={}", lockKey, lockValue, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("获取分布式锁失败: lockKey={}, error={}", lockKey, e.getMessage());
            return false;
        }
    }
    
    /**
     * 释放分布式锁
     * @param identifier 标识符
     * @param lockValue 锁值
     * @return 是否释放成功
     */
    public boolean releaseLock(String identifier, String lockValue) {
        String lockKey = LOCK_KEY_PREFIX + identifier;
        try {
            // 使用Lua脚本确保原子性：只有持有锁的线程才能释放锁
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            Long result = stringRedisTemplate.execute(
                (org.springframework.data.redis.core.script.RedisScript<Long>) 
                org.springframework.data.redis.core.script.RedisScript.of(script, Long.class),
                Collections.singletonList(lockKey),
                lockValue
            );
            log.debug("尝试释放分布式锁: lockKey={}, lockValue={}, result={}", lockKey, lockValue, result);
            return result != null && result == 1L;
        } catch (Exception e) {
            log.error("释放分布式锁失败: lockKey={}, error={}", lockKey, e.getMessage());
            return false;
        }
    }
} 