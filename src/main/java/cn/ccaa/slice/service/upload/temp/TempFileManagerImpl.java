package cn.ccaa.slice.service.upload.temp;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;

import org.apache.commons.io.FileUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import cn.ccaa.slice.config.StorageConfig;
import cn.ccaa.slice.core.exception.BusinessException;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * 临时文件管理器实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TempFileManagerImpl implements TempFileManager {
    
    private final StorageConfig storageConfig;
    private Path tempDir;
    
    /**
     * 构造函数
     *
     * @param storageConfig 存储配置
     */
    public TempFileManagerImpl(StorageConfig storageConfig) {
        this.storageConfig = storageConfig;
    }
    
    @PostConstruct
    @Override
    public void initTempDirectory() {
        try {
            String tempDirPath = storageConfig.getTemp().getBaseDir();
            tempDir = Paths.get(tempDirPath);
            Files.createDirectories(tempDir);
            log.info("上传临时目录初始化成功: {}", tempDir);
        } catch (Exception e) {
            log.error("初始化上传临时目录失败: {}", e.getMessage(), e);
            fallbackToDefaultTempDir();
        }
    }
    
    @Override
    public Path getTempDirectory() {
        return tempDir;
    }
    
    @Override
    public Path createTempFile(String taskId, String fileName) {
        try {
            Path taskTempDir = getTaskTempDirectory(taskId);
            Files.createDirectories(taskTempDir);
            
            Path tempFilePath = taskTempDir.resolve(fileName);
            if (!Files.exists(tempFilePath)) {
                Files.createFile(tempFilePath);
            }
            
            return tempFilePath;
        } catch (IOException e) {
            log.error("创建临时文件失败: taskId={}, fileName={}, 错误: {}", taskId, fileName, e.getMessage(), e);
            throw new BusinessException("创建临时文件失败: " + e.getMessage());
        }
    }
    
    @Override
    public Path saveToTempFile(MultipartFile file, String taskId) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("上传文件不能为空");
        }
        
        try {
            Path taskTempDir = getTaskTempDirectory(taskId);
            Files.createDirectories(taskTempDir);
            
            Path tempFilePath = taskTempDir.resolve(file.getOriginalFilename());
            file.transferTo(tempFilePath.toFile());
            
            log.debug("文件已保存到临时目录: {}", tempFilePath);
            return tempFilePath;
        } catch (IOException e) {
            log.error("保存文件到临时目录失败: taskId={}, fileName={}, 错误: {}", 
                    taskId, file.getOriginalFilename(), e.getMessage(), e);
            throw new BusinessException("保存文件到临时目录失败: " + e.getMessage());
        }
    }
    
    @Override
    public Path saveToTempFile(InputStream inputStream, String taskId, String fileName) {
        if (inputStream == null) {
            throw new BusinessException("输入流不能为空");
        }
        
        try {
            Path taskTempDir = getTaskTempDirectory(taskId);
            Files.createDirectories(taskTempDir);
            
            Path tempFilePath = taskTempDir.resolve(fileName);
            Files.copy(inputStream, tempFilePath, StandardCopyOption.REPLACE_EXISTING);
            
            log.debug("文件已保存到临时目录: {}", tempFilePath);
            return tempFilePath;
        } catch (IOException e) {
            log.error("保存文件到临时目录失败: taskId={}, fileName={}, 错误: {}", 
                    taskId, fileName, e.getMessage(), e);
            throw new BusinessException("保存文件到临时目录失败: " + e.getMessage());
        }
    }
    
    @Override
    public Path saveChunkToTempFile(MultipartFile chunk, String taskId, int chunkIndex) {
        if (chunk == null || chunk.isEmpty()) {
            throw new BusinessException("上传分块不能为空");
        }
        
        try {
            Path taskChunksDir = getTaskTempDirectory(taskId).resolve("chunks");
            Files.createDirectories(taskChunksDir);
            
            Path chunkFilePath = taskChunksDir.resolve(String.format("chunk_%d", chunkIndex));
            chunk.transferTo(chunkFilePath.toFile());
            
            log.debug("分块已保存到临时目录: {}", chunkFilePath);
            return chunkFilePath;
        } catch (IOException e) {
            log.error("保存分块到临时目录失败: taskId={}, chunkIndex={}, 错误: {}", 
                    taskId, chunkIndex, e.getMessage(), e);
            throw new BusinessException("保存分块到临时目录失败: " + e.getMessage());
        }
    }
    
    @Override
    public Path mergeChunks(String taskId, String fileName, int totalChunks) {
        try {
            Path taskTempDir = getTaskTempDirectory(taskId);
            Path chunksDir = taskTempDir.resolve("chunks");
            
            if (!Files.exists(chunksDir) || !Files.isDirectory(chunksDir)) {
                throw new BusinessException("分块目录不存在");
            }
            
            // 检查所有分块是否存在
            for (int i = 0; i < totalChunks; i++) {
                Path chunkPath = chunksDir.resolve(String.format("chunk_%d", i));
                if (!Files.exists(chunkPath)) {
                    throw new BusinessException("分块文件不完整，缺少分块: " + i);
                }
            }
            
            // 创建合并后的文件
            Path mergedFilePath = taskTempDir.resolve(fileName);
            try (FileOutputStream fos = new FileOutputStream(mergedFilePath.toFile());
                 FileChannel outChannel = fos.getChannel()) {
                
                // 按顺序合并所有分块
                for (int i = 0; i < totalChunks; i++) {
                    Path chunkPath = chunksDir.resolve(String.format("chunk_%d", i));
                    try (FileChannel inChannel = FileChannel.open(chunkPath)) {
                        inChannel.transferTo(0, inChannel.size(), outChannel);
                    }
                }
            }
            
            log.info("分块合并完成: {}", mergedFilePath);
            return mergedFilePath;
        } catch (IOException e) {
            log.error("合并分块失败: taskId={}, fileName={}, 错误: {}", 
                    taskId, fileName, e.getMessage(), e);
            throw new BusinessException("合并分块失败: " + e.getMessage());
        }
    }
    
    @Override
    public void cleanupTempFiles(String taskId) {
        try {
            Path taskTempDir = getTaskTempDirectory(taskId);
            if (Files.exists(taskTempDir)) {
                FileUtils.deleteDirectory(taskTempDir.toFile());
                log.info("已清理任务临时文件: {}", taskId);
            }
        } catch (IOException e) {
            log.error("清理任务临时文件失败: taskId={}, 错误: {}", taskId, e.getMessage(), e);
        }
    }
    
    /**
     * 定时清理过期的临时文件
     * 每天凌晨3点执行
     */
    @Scheduled(cron = "0 0 3 * * ?")
    @Override
    public void cleanupExpiredTempFiles() {
        log.info("开始清理过期临时文件...");
        
        try {
            if (!Files.exists(tempDir)) {
                log.info("临时目录不存在，无需清理");
                return;
            }
            
            LocalDateTime now = LocalDateTime.now();
            
            // 获取所有任务临时目录
            Files.list(tempDir)
                .filter(Files::isDirectory)
                .forEach(taskDir -> {
                    try {
                        // 获取目录的最后修改时间
                        LocalDateTime lastModified = LocalDateTime.ofInstant(
                            Files.getLastModifiedTime(taskDir).toInstant(),
                            java.time.ZoneId.systemDefault());
                        
                        // 如果目录超过3天未修改，则清理
                        if (lastModified.plusDays(3).isBefore(now)) {
                            FileUtils.deleteDirectory(taskDir.toFile());
                            log.info("已清理过期临时目录: {}", taskDir);
                        }
                    } catch (IOException e) {
                        log.error("清理过期临时目录失败: {}, 错误: {}", taskDir, e.getMessage(), e);
                    }
                });
            
            log.info("过期临时文件清理完成");
        } catch (IOException e) {
            log.error("清理过期临时文件失败: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public Path getTaskTempDirectory(String taskId) {
        return tempDir.resolve(taskId);
    }
    
    @Override
    public boolean tempFileExists(String taskId, String fileName) {
        Path filePath = getTaskTempDirectory(taskId).resolve(fileName);
        return Files.exists(filePath) && Files.isRegularFile(filePath);
    }
    
    @Override
    public File getTempFile(String taskId, String fileName) {
        Path filePath = getTaskTempDirectory(taskId).resolve(fileName);
        if (Files.exists(filePath) && Files.isRegularFile(filePath)) {
            return filePath.toFile();
        }
        return null;
    }
    
    /**
     * 回退到默认临时目录
     */
    private void fallbackToDefaultTempDir() {
        tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "ccaa-slice-uploads");
        try {
            Files.createDirectories(tempDir);
            log.info("使用默认临时目录: {}", tempDir);
        } catch (Exception ex) {
            log.error("创建默认临时目录失败: {}", ex.getMessage(), ex);
        }
    }
}
