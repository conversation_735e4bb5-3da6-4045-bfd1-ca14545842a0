package cn.ccaa.slice.service.upload.task;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.github.f4b6a3.ulid.UlidCreator;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.Set;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import cn.ccaa.slice.config.StorageConfig;
import cn.ccaa.slice.core.exception.BusinessException;
import cn.ccaa.slice.core.upload.UploadProgress;
import cn.ccaa.slice.core.upload.UploadStatus;
import cn.ccaa.slice.core.upload.UploadTask;
import cn.ccaa.slice.core.model.SliceEntity;
import cn.ccaa.slice.service.SliceService;
import cn.ccaa.slice.service.upload.chunk.ChunkStatusManager;

/**
 * 上传任务管理器实现
 *
 * <AUTHOR>
 */
@Service
public class UploadTaskManagerImpl implements UploadTaskManager {

    private static final Logger log = LoggerFactory.getLogger(UploadTaskManagerImpl.class);

    /**
     * Redis中上传进度的key前缀
     */
    private static final String UPLOAD_PROGRESS_KEY = "upload:progress:";

    /**
     * 内存中的上传任务缓存
     */
    private final Map<String, UploadTask> uploadTasks = new ConcurrentHashMap<>();

    /**
     * 内存中最大任务数量
     */
    private static final int MAX_TASKS_IN_MEMORY = 1000;

    /**
     * Redis模板
     */
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 存储配置
     */
    @Autowired
    private StorageConfig storageConfig;

    @Autowired
    private SliceService sliceService;

    @Lazy
    @Autowired
    private ChunkStatusManager chunkStatusManager;

    /**
     * 构造函数
     *
     * @param redisTemplate Redis模板
     * @param storageConfig 存储配置
     */
    public UploadTaskManagerImpl(RedisTemplate<String, Object> redisTemplate, StorageConfig storageConfig) {
        this.redisTemplate = redisTemplate;
        this.storageConfig = storageConfig;
    }

    @Override
    public UploadTask createTask(String taskId, String fileName, long fileSize, String userId) {
        // 检查内存和Redis中是否已存在该任务
        UploadTask exist = getTask(taskId);
        if (exist != null) {
            return exist;
        }
        // 使用传入的taskId
        String storagePath = generateStoragePath(taskId, fileName);
        UploadTask task = new UploadTask(taskId, fileName, storagePath, fileSize, userId);
        uploadTasks.put(taskId, task);
        saveTaskToRedis(task);
        log.info("用自定义taskId创建上传任务: taskId={}, fileName={}, fileSize={}", taskId, fileName, fileSize);
        return task;
    }

    // 保留原有自动生成ID的方法，供老接口兼容
    public UploadTask createTask(String fileName, long fileSize, String userId) {
        String taskId = UlidCreator.getMonotonicUlid().toString();
        return createTask(taskId, fileName, fileSize, userId);
    }

    @Override
    public UploadTask getTask(String taskId) {
        // 先从内存缓存中获取
        UploadTask task = uploadTasks.get(taskId);
        if (task != null) {
            return task;
        }

        // 如果内存中没有，尝试从Redis获取
        return getTaskFromRedis(taskId);
    }

    @Override
    public Map<String, UploadTask> batchGetTasks(List<String> taskIds) {
        Map<String, UploadTask> result = new HashMap<>();

        // 先从内存缓存中获取
        for (String taskId : taskIds) {
            UploadTask task = uploadTasks.get(taskId);
            if (task != null) {
                result.put(taskId, task);
            }
        }

        // 找出内存中没有的taskId
        List<String> missingTaskIds = taskIds.stream()
                .filter(id -> !result.containsKey(id))
                .collect(Collectors.toList());

        // 如果有未找到的任务，从Redis批量获取
        if (!missingTaskIds.isEmpty()) {
            Map<String, UploadTask> redisResults = batchGetTasksFromRedis(missingTaskIds);
            result.putAll(redisResults);
        }

        return result;
    }

    @Override
    public void updateTaskStatus(UploadTask task, UploadStatus status) {
        if (task == null) {
            return;
        }

        task.getProgress().setStatus(status);

        // 更新Redis缓存
        String redisKey = UPLOAD_PROGRESS_KEY + task.getTaskId();
        Map<String, Object> updates;

        if (status.isTerminal()) {
            updates = Map.of(
                "status", status.toString(),
                "lastUpdateTime", System.currentTimeMillis(),
                "endTime", System.currentTimeMillis()
            );
        } else {
            updates = Map.of(
                "status", status.toString(),
                "lastUpdateTime", System.currentTimeMillis()
            );
        }

        redisTemplate.opsForHash().putAll(redisKey, updates);

        log.debug("更新任务状态: taskId={}, status={}", task.getTaskId(), status);
    }

    @Override
    public void updateTaskProgress(UploadTask task, long uploadedBytes) {
        if (task == null) {
            return;
        }

        task.getProgress().setUploadedSize(uploadedBytes);
        task.getProgress().setLastUpdateTime(LocalDateTime.now());

        // 更新Redis缓存
        String redisKey = UPLOAD_PROGRESS_KEY + task.getTaskId();
        Map<String, Object> updates = Map.of(
            "uploadedSize", uploadedBytes,
            "lastUpdateTime", System.currentTimeMillis()
        );

        redisTemplate.opsForHash().putAll(redisKey, updates);

        log.debug("更新任务进度: taskId={}, uploadedBytes={}", task.getTaskId(), uploadedBytes);
    }

    @Override
    public UploadTask updateTaskDetails(String taskId, Long uploadedBytes, String status,
                                     String fileUrl, String errorMessage, Boolean completed) {
        UploadTask task = getTask(taskId);
        if (task == null) {
            log.warn("更新任务状态时发现任务不存在（Redis可能已过期），尝试创建临时任务: {}", taskId);
            try {
                // 查询数据库检查任务是否真实存在
                SliceEntity sliceEntity = sliceService.getBySliceId(taskId);
                if (sliceEntity != null) {
                    // 从数据库记录创建临时任务对象
                    task = new UploadTask(taskId, sliceEntity.getQpName(), 
                                         sliceEntity.getPhysicPath(), 
                                         sliceEntity.getQpSize() != null ? sliceEntity.getQpSize().longValue() : 0L, 
                                         "system"); // 使用默认系统用户作为创建者
                    // 将任务重新保存到Redis
                    saveTaskToRedis(task);
                    log.info("已从数据库恢复任务信息并保存到Redis: {}", taskId);
                } else {
                    // 数据库也不存在此任务
                    throw new BusinessException("上传任务不存在: " + taskId);
                }
            } catch (Exception e) {
                log.error("尝试恢复任务失败: {}, 错误: {}", taskId, e.getMessage());
                throw new BusinessException("上传任务不存在: " + taskId);
            }
        }

        // 更新任务状态并保存到Redis
        Map<String, Object> updates = new HashMap<>();

        if (uploadedBytes != null) {
            task.getProgress().setUploadedSize(uploadedBytes);
            updates.put("uploadedSize", uploadedBytes);
        }

        if (status != null) {
            try {
                UploadStatus uploadStatus = UploadStatus.valueOf(status);
                task.getProgress().setStatus(uploadStatus);
                updates.put("status", status);
            } catch (IllegalArgumentException e) {
                log.warn("无效的上传状态: {}", status);
            }
        }

        if (fileUrl != null) {
            // 将fileUrl存储在元数据中
            task.setMetadata("fileUrl", fileUrl);
            updates.put("fileUrl", fileUrl);
        }

        if (errorMessage != null) {
            task.getProgress().setErrorMessage(errorMessage);
            updates.put("errorMessage", errorMessage);
        }

        if (completed != null && completed) {
            task.getProgress().markAsCompleted();
            updates.put("status", task.getStatus().toString());
            updates.put("endTime", System.currentTimeMillis());
            updates.put("uploadedSize", task.getFileSize());
        }

        updates.put("lastUpdateTime", System.currentTimeMillis());

        // 更新Redis
        if (!updates.isEmpty()) {
            try {
                updateRedisAndSetExpiry(taskId, updates);
            } catch (Exception e) {
                log.error("更新Redis失败，但不影响任务状态更新: taskId={}, error={}", taskId, e.getMessage());
                // Redis更新失败不应该影响任务状态的更新，只记录日志
            }
        }

        log.info("更新上传任务详情: taskId={}, status={}, uploadedBytes={}, completed={}",
                taskId, task.getProgress().getStatus(), task.getProgress().getUploadedSize(), completed);

        return task;
    }

    @Override
    public UploadTask completeTask(String taskId) {
        UploadTask task = getTask(taskId);
        if (task == null) {
            throw new BusinessException("上传任务不存在: " + taskId);
        }

        // 使用UploadProgress的markAsCompleted方法
        task.getProgress().markAsCompleted();

        // 更新Redis缓存
        String redisKey = UPLOAD_PROGRESS_KEY + taskId;
        Map<String, Object> updates = Map.of(
            "status", task.getStatus().toString(),
            "uploadedSize", task.getFileSize(),
            "endTime", System.currentTimeMillis(),
            "lastUpdateTime", System.currentTimeMillis()
        );

        redisTemplate.opsForHash().putAll(redisKey, updates);

        log.info("完成上传任务: taskId={}, fileName={}", taskId, task.getFileName());
        return task;
    }

    @Override
    public void cancelTask(String taskId) {
        UploadTask task = getTask(taskId);
        if (task == null) {
            log.warn("取消上传任务失败，任务不存在: {}", taskId);
            return;
        }

        task.cancel(); // 使用UploadTask的cancel方法

        // 更新Redis缓存
        String redisKey = UPLOAD_PROGRESS_KEY + taskId;
        Map<String, Object> updates = Map.of(
            "status", task.getStatus().toString(),
            "endTime", System.currentTimeMillis(),
            "lastUpdateTime", System.currentTimeMillis()
        );

        redisTemplate.opsForHash().putAll(redisKey, updates);

        log.info("取消上传任务: taskId={}, fileName={}", taskId, task.getFileName());
    }

    @Override
    public UploadTask markTaskAsFailed(UploadTask task, String errorMessage) {
        if (task == null) {
            return null;
        }

        // 使用UploadProgress的markAsFailed方法
        task.getProgress().markAsFailed(errorMessage);

        // 更新Redis缓存
        String redisKey = UPLOAD_PROGRESS_KEY + task.getTaskId();
        Map<String, Object> updates = Map.of(
            "status", task.getStatus().toString(),
            "errorMessage", errorMessage,
            "endTime", System.currentTimeMillis(),
            "lastUpdateTime", System.currentTimeMillis()
        );

        redisTemplate.opsForHash().putAll(redisKey, updates);

        log.error("任务状态已更新为失败: taskId={}, error={}", task.getTaskId(), errorMessage);
        return task;
    }

    /**
     * 定时清理过期任务
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @Override
    public void cleanExpiredTasks() {
        log.info("开始清理过期上传任务...");

        // 清理内存中的过期任务
        int memoryTasksBeforeClean = uploadTasks.size();
        uploadTasks.entrySet().removeIf(entry -> {
            UploadTask task = entry.getValue();
            LocalDateTime lastUpdateTime = task.getProgress().getLastUpdateTime();

            // 如果最后更新时间超过7天，则清理
            return lastUpdateTime.plusDays(7).isBefore(LocalDateTime.now());
        });
        int memoryTasksAfterClean = uploadTasks.size();

        log.info("内存中的过期任务清理完成，清理前: {}，清理后: {}", memoryTasksBeforeClean, memoryTasksAfterClean);

        // 清理Redis中的过期任务
        // 注意：Redis的过期是通过TTL设置的，这里不需要额外清理
    }

    /**
     * 检查并在需要时清理任务
     */
    private void checkAndCleanTasksIfNeeded() {
        if (uploadTasks.size() > MAX_TASKS_IN_MEMORY) {
            // 清理最旧的已完成任务，每次清理10%
            cleanOldestCompletedTasks(MAX_TASKS_IN_MEMORY / 10);
        }
    }

    /**
     * 清理最旧的已完成任务
     *
     * @param count 要清理的任务数量
     */
    private void cleanOldestCompletedTasks(int count) {
        log.info("清理最旧的已完成任务，数量: {}", count);

        // 按最后更新时间排序，找出最旧的已完成任务
        List<String> oldestTaskIds = uploadTasks.values().stream()
                .filter(task -> task.getProgress().isCompleted() ||
                               task.getProgress().isFailed() ||
                               task.getProgress().isCanceled())
                .sorted((t1, t2) -> t1.getProgress().getLastUpdateTime().compareTo(t2.getProgress().getLastUpdateTime()))
                .limit(count)
                .map(UploadTask::getTaskId)
                .collect(Collectors.toList());

        // 从内存中移除这些任务
        for (String taskId : oldestTaskIds) {
            uploadTasks.remove(taskId);
            log.debug("从内存中移除旧任务: {}", taskId);
        }

        log.info("清理完成，共移除 {} 个旧任务", oldestTaskIds.size());
    }

    /**
     * 保存任务到Redis
     *
     * @param task 上传任务
     */
    private void saveTaskToRedis(UploadTask task) {
        String redisKey = UPLOAD_PROGRESS_KEY + task.getTaskId();

        Map<String, Object> taskData = new HashMap<>();
        taskData.put("taskId", task.getTaskId());
        taskData.put("fileName", task.getFileName());
        taskData.put("fileSize", task.getFileSize());
        taskData.put("userId", task.getUserId());
        taskData.put("createTime", System.currentTimeMillis());

        UploadProgress progress = task.getProgress();
        taskData.put("status", progress.getStatus().toString());
        taskData.put("uploadedSize", progress.getUploadedSize());
        taskData.put("totalSize", progress.getTotalSize());
        taskData.put("startTime", System.currentTimeMillis());
        taskData.put("lastUpdateTime", System.currentTimeMillis());
        taskData.put("storagePath", progress.getStoragePath());

        redisTemplate.opsForHash().putAll(redisKey, taskData);

        // 设置过期时间，7天
        redisTemplate.expire(redisKey, 7, java.util.concurrent.TimeUnit.DAYS);

        log.debug("任务已保存到Redis: {}", task.getTaskId());
    }

    /**
     * 从Redis获取任务
     *
     * @param taskId 任务ID
     * @return 上传任务，如果不存在则返回null
     */
    private UploadTask getTaskFromRedis(String taskId) {
        // 使用安全的方法获取任务数据
        Map<String, Object> taskData = getUploadProgressFromRedis(taskId);
        if (taskData == null || taskData.isEmpty()) {
            return null;
        }

        try {
            // 安全获取必要字段
            String fileName = getStringValue(taskData, "fileName", "");
            String storagePath = getStringValue(taskData, "storagePath", "");
            long fileSize = getLongValue(taskData, "fileSize", 0L);
            String userId = getStringValue(taskData, "userId", "system");

            // 创建上传任务
            UploadTask task = new UploadTask(taskId, fileName, storagePath, fileSize, userId);

            // 更新任务状态
            UploadProgress progress = task.getProgress();
            String statusStr = getStringValue(taskData, "status", UploadStatus.PENDING.toString());
            try {
                progress.setStatus(UploadStatus.valueOf(statusStr));
            } catch (IllegalArgumentException e) {
                log.warn("无效的上传状态: {}, 使用默认状态PENDING", statusStr);
                progress.setStatus(UploadStatus.PENDING);
            }
            
            progress.setUploadedSize(getLongValue(taskData, "uploadedSize", 0L));

            // 设置时间
            if (taskData.containsKey("lastUpdateTime")) {
                try {
                    long timestamp = getLongValue(taskData, "lastUpdateTime", System.currentTimeMillis());
                    progress.setLastUpdateTime(LocalDateTime.ofInstant(
                        java.time.Instant.ofEpochMilli(timestamp),
                        java.time.ZoneId.systemDefault()));
                } catch (Exception e) {
                    log.warn("解析lastUpdateTime失败: {}", e.getMessage());
                    progress.setLastUpdateTime(LocalDateTime.now());
                }
            }

            if (taskData.containsKey("endTime")) {
                try {
                    long timestamp = getLongValue(taskData, "endTime", 0L);
                    if (timestamp > 0) {
                        progress.setEndTime(LocalDateTime.ofInstant(
                            java.time.Instant.ofEpochMilli(timestamp),
                            java.time.ZoneId.systemDefault()));
                    }
                } catch (Exception e) {
                    log.warn("解析endTime失败: {}", e.getMessage());
                }
            }

            if (taskData.containsKey("errorMessage")) {
                progress.setErrorMessage(getStringValue(taskData, "errorMessage", ""));
            }

            if (taskData.containsKey("fileUrl")) {
                task.setMetadata("fileUrl", taskData.get("fileUrl"));
            }

            // 将任务添加到内存缓存
            uploadTasks.put(taskId, task);

            return task;
        } catch (Exception e) {
            log.error("从Redis解析任务数据失败: {}, 错误: {}", taskId, e.getMessage());
            return null;
        }
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(Map<String, Object> map, String key, String defaultValue) {
        Object value = map.get(key);
        if (value == null) {
            return defaultValue;
        }
        return value.toString();
    }

    /**
     * 安全获取长整型值
     */
    private long getLongValue(Map<String, Object> map, String key, long defaultValue) {
        Object value = map.get(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            if (value instanceof Number) {
                return ((Number) value).longValue();
            }
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法解析{}的值为长整型: {}", key, value);
            return defaultValue;
        }
    }

    /**
     * 批量从Redis获取任务
     *
     * @param taskIds 任务ID列表
     * @return 上传任务映射，key为taskId，value为对应的上传任务
     */
    private Map<String, UploadTask> batchGetTasksFromRedis(List<String> taskIds) {
        Map<String, UploadTask> result = new HashMap<>();

        for (String taskId : taskIds) {
            UploadTask task = getTaskFromRedis(taskId);
            if (task != null) {
                result.put(taskId, task);
            }
        }

        return result;
    }

    /**
     * 更新Redis并设置过期时间
     *
     * @param taskId 任务ID
     * @param updates 更新的字段
     */
    private void updateRedisAndSetExpiry(String taskId, Map<String, Object> updates) {
        String redisKey = UPLOAD_PROGRESS_KEY + taskId;
        try {
            log.debug("开始更新Redis: taskId={}, updates={}", taskId, updates);
            redisTemplate.opsForHash().putAll(redisKey, updates);

            // 重新设置过期时间，7天
            redisTemplate.expire(redisKey, 7, java.util.concurrent.TimeUnit.DAYS);
            log.debug("Redis更新成功: taskId={}", taskId);
        } catch (Exception e) {
            log.error("Redis更新失败: taskId={}, updates={}, error={}", taskId, updates, e.getMessage(), e);
            throw e; // 重新抛出异常，让调用方处理
        }
    }

    /**
     * 生成存储路径
     *
     * @param taskId 任务ID
     * @param fileName 文件名
     * @return 存储路径
     */
    private String generateStoragePath(String taskId, String fileName) {
        String uploadDir = storageConfig.getFile().getUploadDir();
        
        // 使用标准格式: uploadDir/taskId/original_文件名
        return String.format("%s/%s/original_%s", uploadDir, taskId, fileName);
    }

    @Override
    public List<UploadTask> getTasksByUserId(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return Collections.emptyList();
        }

        // 从内存中获取指定用户的任务
        return uploadTasks.values().stream()
                .filter(task -> userId.equals(task.getUserId()))
                .collect(Collectors.toList());
    }

    /**
     * 定期同步UploadTaskManager与ChunkStatusManager数据
     * 每10分钟执行一次，与ChunkStatusManager的同步交错执行
     */
    @Scheduled(fixedRate = 600000, initialDelay = 300000)
    public void syncWithChunkManager() {
        log.info("开始同步上传任务与分片任务状态...");
        int syncCount = 0;
        int errorCount = 0;
        
        try {
            // 获取所有上传任务
            Set<String> taskKeys = redisTemplate.keys(UPLOAD_PROGRESS_KEY + "*");
            if (taskKeys == null || taskKeys.isEmpty()) {
                log.info("没有发现活跃的上传任务");
                return;
            }
            
            List<String> taskIds = taskKeys.stream()
                .map(key -> key.substring(UPLOAD_PROGRESS_KEY.length()))
                .collect(Collectors.toList());
            
            log.debug("发现{}个活跃上传任务", taskIds.size());
            
            for (String taskId : taskIds) {
                try {
                    // 获取上传任务
                    UploadTask task = getTask(taskId);
                    if (task == null) {
                        continue;
                    }
                    
                    // 获取分片任务信息
                    Map<String, Object> chunkTaskInfo = null;
                    try {
                        chunkTaskInfo = chunkStatusManager.getTaskInfo(taskId);
                    } catch (Exception e) {
                        log.warn("获取分片任务信息异常: taskId={}, error={}", taskId, e.getMessage());
                        chunkTaskInfo = Collections.emptyMap();
                    }
                    
                    // 如果分片任务不存在但上传任务存在
                    if (chunkTaskInfo.isEmpty()) {
                        // 根据上传任务状态，初始化或更新分片任务
                        if (task.getStatus() == UploadStatus.UPLOADING || 
                            task.getStatus() == UploadStatus.PENDING) {
                            // 初始化分片任务
                            try {
                                log.info("初始化分片任务: taskId={}, status={}", taskId, task.getStatus());
                                chunkStatusManager.initTask(
                                    taskId,
                                    task.getFileName(),
                                    task.getFileSize(),
                                    1  // 默认只有一个分片
                                );
                                syncCount++;
                            } catch (Exception e) {
                                log.error("初始化分片任务失败: taskId={}, error={}", taskId, e.getMessage());
                                errorCount++;
                            }
                        } else if (task.getStatus() == UploadStatus.COMPLETED) {
                            // 已完成的任务，创建完成状态的分片任务
                            try {
                                log.info("创建完成状态的分片任务: taskId={}", taskId);
                                chunkStatusManager.initTask(
                                    taskId,
                                    task.getFileName(),
                                    task.getFileSize(),
                                    1
                                );
                                chunkStatusManager.updateTaskStatus(taskId, "COMPLETED", null);
                                syncCount++;
                            } catch (Exception e) {
                                log.error("创建完成状态的分片任务失败: taskId={}, error={}", taskId, e.getMessage());
                                errorCount++;
                            }
                        }
                    } else {
                        // 两边都存在，检查状态是否一致
                        try {
                            // 安全获取状态值
                            String chunkStatus = "UNKNOWN";
                            Object statusObj = chunkTaskInfo.get("status");
                            if (statusObj != null) {
                                if (statusObj instanceof String) {
                                    chunkStatus = (String) statusObj;
                                } else {
                                    chunkStatus = String.valueOf(statusObj);
                                }
                            }
                            
                            if ("COMPLETED".equals(chunkStatus) && task.getStatus() != UploadStatus.COMPLETED) {
                                // 分片已完成但上传任务未完成，同步状态
                                log.info("同步上传任务状态为已完成: taskId={}", taskId);
                                completeTask(taskId);
                                syncCount++;
                            } else if ("FAILED".equals(chunkStatus) && task.getStatus() != UploadStatus.FAILED) {
                                // 分片失败但上传任务未失败，同步状态
                                // 安全获取错误信息
                                String errorMsg = "同步自分片任务状态";
                                Object errorObj = chunkTaskInfo.get("errorMessage");
                                if (errorObj != null) {
                                    if (errorObj instanceof String) {
                                        errorMsg = (String) errorObj;
                                    } else {
                                        errorMsg = String.valueOf(errorObj);
                                    }
                                }
                                
                                log.info("同步上传任务状态为失败: taskId={}, error={}", taskId, errorMsg);
                                markTaskAsFailed(task, errorMsg);
                                syncCount++;
                            }
                        } catch (Exception e) {
                            log.error("同步任务状态失败: taskId={}, error={}", taskId, e.getMessage());
                            errorCount++;
                        }
                    }
                } catch (Exception e) {
                    log.error("处理任务同步失败: taskId={}, error={}", taskId, e.getMessage());
                    errorCount++;
                }
            }
            
            log.info("上传任务与分片任务状态同步完成，成功同步{}个任务，失败{}个任务", syncCount, errorCount);
        } catch (Exception e) {
            log.error("上传任务与分片任务状态同步过程发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 从Redis获取任务进度
     *
     * @param taskId 任务ID
     * @return 上传进度信息，如果不存在则返回null
     */
    private Map<String, Object> getUploadProgressFromRedis(String taskId) {
        String redisKey = UPLOAD_PROGRESS_KEY + taskId;
        
        // 检查Redis中是否存在该任务
        if (!Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))) {
            return null;
        }
        
        try {
            Map<Object, Object> progressData = redisTemplate.opsForHash().entries(redisKey);
            if (progressData.isEmpty()) {
                return null;
            }
            
            Map<String, Object> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : progressData.entrySet()) {
                String key = entry.getKey() != null ? entry.getKey().toString() : "";
                Object value = entry.getValue();
                
                // 避免null值
                if (value != null) {
                    result.put(key, value);
                } else {
                    result.put(key, "");
                }
            }
            
            return result;
        } catch (Exception e) {
            log.error("从Redis获取任务进度数据失败: {}, 错误: {}", taskId, e.getMessage());
            return null;
        }
    }
}
