package cn.ccaa.slice.service.upload;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import cn.ccaa.slice.core.upload.UploadTask;

/**
 * 文件上传服务接口
 * 
 * <AUTHOR>
 */
public interface FileUploadService {
    
    /**
     * 创建上传任务
     * 
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @param userId 用户ID
     * @return 上传任务
     */
    UploadTask createUploadTask(String fileName, Long fileSize, String userId);
    
    /**
     * 用自定义taskId创建上传任务（如sliceId）
     *
     * @param taskId 任务ID
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @param userId 用户ID
     * @return 上传任务
     */
    UploadTask createUploadTask(String taskId, String fileName, long fileSize, String userId);
    
    /**
     * 上传完整文件
     * 
     * @param file 文件
     * @param userId 用户ID
     * @return 上传任务
     */
    UploadTask uploadFile(MultipartFile file, String userId);
    
    /**
     * 异步上传文件到存储系统
     * 
     * @param taskId 任务ID
     * @param file 文件
     * @return 上传任务
     */
    UploadTask asyncUploadFile(String taskId, MultipartFile file);
    
    /**
     * 上传文件分块
     * 
     * @param taskId 任务ID
     * @param chunk 分块
     * @param chunkIndex 分块索引
     * @param totalChunks 总分块数
     * @return 上传任务
     */
    UploadTask uploadFileChunk(String taskId, MultipartFile chunk, int chunkIndex, int totalChunks);
    
    /**
     * 通过输入流上传文件
     * 
     * @param fileName 文件名
     * @param inputStream 输入流
     * @param fileSize 文件大小
     * @param userId 用户ID
     * @return 上传任务
     */
    UploadTask uploadFile(String fileName, InputStream inputStream, long fileSize, String userId);
    
    /**
     * 更新上传进度
     * 
     * @param taskId 任务ID
     * @param uploadedBytes 已上传字节数
     */
    void updateProgress(String taskId, long uploadedBytes);
    
    /**
     * 更新上传任务详细信息
     * 
     * @param taskId 任务ID
     * @param uploadedBytes 已上传字节数
     * @param status 上传状态
     * @param fileUrl 文件URL（可选）
     * @param errorMessage 错误信息（可选）
     * @param completed 是否完成（可选）
     * @return 更新后的上传任务
     */
    UploadTask updateTaskStatus(String taskId, Long uploadedBytes, String status, 
                             String fileUrl, String errorMessage, Boolean completed);
    
    /**
     * 完成上传任务
     * 
     * @param taskId 任务ID
     * @return 更新后的上传任务
     */
    UploadTask completeUpload(String taskId);
    
    /**
     * 取消上传任务
     * 
     * @param taskId 任务ID
     */
    void cancelUpload(String taskId);
    
    /**
     * 获取上传任务
     * 
     * @param taskId 任务ID
     * @return 上传任务
     */
    UploadTask getUploadTask(String taskId);
    
    /**
     * 批量获取上传任务
     * 
     * @param taskIds 任务ID列表
     * @return 上传任务映射，key为taskId，value为对应的上传任务
     */
    Map<String, UploadTask> batchGetUploadTasks(List<String> taskIds);
    
    /**
     * 检查文件是否已完成上传
     * 
     * @param taskId 任务ID
     * @return 如果已完成返回true，否则返回false
     */
    boolean isUploadCompleted(String taskId);
    
    /**
     * 获取上传任务（兼容别名，等同于getUploadTask）
     * 
     * @param taskId 任务ID
     * @return 上传任务
     */
    default UploadTask getTask(String taskId) {
        return getUploadTask(taskId);
    }
    
    /**
     * 根据用户ID获取任务列表
     * 
     * @param userId 用户ID
     * @return 上传任务列表
     */
    List<UploadTask> getTasksByUserId(String userId);

    /**
     * 删除上传任务
     * 
     * @param taskId 任务ID
     */
    void deleteUploadTask(String taskId);
} 
