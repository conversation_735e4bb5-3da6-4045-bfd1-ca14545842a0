package cn.ccaa.slice.service.upload;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Date;
import java.util.List;
import java.util.Map;
import com.github.f4b6a3.ulid.UlidCreator;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.HashMap;
import java.util.Optional;
import java.util.stream.Stream;

import jakarta.annotation.PostConstruct;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.beans.factory.annotation.Value;

import cn.ccaa.slice.config.StorageConfig;
import cn.ccaa.slice.config.TempDirConfig;
import cn.ccaa.slice.core.exception.BusinessException;
import cn.ccaa.slice.core.exception.SliceAnalysisException;
import cn.ccaa.slice.core.model.SliceAnalysisResult;
import cn.ccaa.slice.core.model.SliceInfo;
import cn.ccaa.slice.core.result.Result;
import cn.ccaa.slice.core.upload.UploadStatus;
import cn.ccaa.slice.core.upload.UploadTask;
import cn.ccaa.slice.service.analyzer.SliceFileAnalyzer;
import cn.ccaa.slice.service.SliceService;
import cn.ccaa.slice.service.upload.SliceAnalysisCallbackService;

/**
 * 切片上传服务实现类
 *
 * <AUTHOR>
 */
@Service
public class SliceUploadServiceImpl implements SliceUploadService {

    private static final Logger log = LoggerFactory.getLogger(SliceUploadServiceImpl.class);

    private final TempDirConfig tempDirConfig;
    private final StorageConfig storageConfig;
    private final List<SliceFileAnalyzer> sliceAnalyzers;
    private final FileUploadService fileUploadService;
    private final SliceService sliceService;
    private final SliceAnalysisCallbackService sliceAnalysisCallbackService;

    // 任务取消标志映射
    private final Map<String, AtomicBoolean> cancelFlags = new java.util.concurrent.ConcurrentHashMap<>();

    // 本地切片文件保存根目录
    @Value("${storage.local-slice-dir:./local_slices}")
    private String localSliceDir;

    /**
     * 构造函数，使用构造器注入所有依赖
     *
     * @param tempDirConfig 临时目录配置
     * @param storageConfig 存储配置
     * @param sliceAnalyzers 切片分析器列表
     * @param fileUploadService 文件上传服务
     * @param sliceService 切片服务
     * @param sliceAnalysisCallbackService 切片分析回调服务
     */
    public SliceUploadServiceImpl(
            TempDirConfig tempDirConfig,
            StorageConfig storageConfig,
            List<SliceFileAnalyzer> sliceAnalyzers,
            FileUploadService fileUploadService,
            SliceService sliceService,
            SliceAnalysisCallbackService sliceAnalysisCallbackService) {
        this.tempDirConfig = tempDirConfig;
        this.storageConfig = storageConfig;
        this.sliceAnalyzers = sliceAnalyzers;
        this.fileUploadService = fileUploadService;
        this.sliceService = sliceService;
        this.sliceAnalysisCallbackService = sliceAnalysisCallbackService;
    }

    @Override
    public Result<SliceInfo> uploadAndAnalyzeSlice(MultipartFile file, String taskId) {
        long startTime = System.currentTimeMillis();

        if (file == null || file.isEmpty() || taskId == null || taskId.trim().isEmpty()) {
            return Result.fail(file == null || file.isEmpty() ? "上传文件不能为空" : "任务ID不能为空");
        }

        log.info("开始处理切片文件上传: taskId={}, fileName={}, 大小: {}",
                taskId, file.getOriginalFilename(), file.getSize());

        return Optional.ofNullable(processSliceFile(file, taskId))
            .map(sliceInfo -> {
                log.info("切片文件处理完成: taskId={}, fileName={}, 耗时: {}ms",
                        taskId, file.getOriginalFilename(), (System.currentTimeMillis() - startTime));
                return Result.success(sliceInfo);
            })
            .orElseGet(() -> Result.fail("处理切片文件失败"));
    }

    private SliceInfo processSliceFile(MultipartFile file, String taskId) {
        if (file == null || file.isEmpty() || taskId == null || taskId.trim().isEmpty()) {
            return null;
        }

        log.info("开始处理切片文件上传: taskId={}, fileName={}, 大小: {}",
                taskId, file.getOriginalFilename(), file.getSize());

        try {
            // 使用正确的文件存储路径结构：partnerCode/type/year/month/day/timestamp_filename
            String fileExtension = FilenameUtils.getExtension(file.getOriginalFilename());
            String type = fileExtension.toLowerCase(); // 目录全部小写
            java.time.LocalDateTime now = java.time.LocalDateTime.now();
            String year = now.format(java.time.format.DateTimeFormatter.ofPattern("yyyy"));
            String month = now.format(java.time.format.DateTimeFormatter.ofPattern("MM"));
            String day = now.format(java.time.format.DateTimeFormatter.ofPattern("dd"));
            String timestamp = now.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
            String newFileName = timestamp + "_" + file.getOriginalFilename();
            String partnerCode = "default"; // 默认partnerCode
            
            // 物理存储完整路径
            Path localSlicePath = Path.of(localSliceDir, partnerCode, type, year, month, day);
            Files.createDirectories(localSlicePath);
            Path localSliceFile = localSlicePath.resolve(newFileName);
            
            log.info("准备保存切片文件到本地: {}", localSliceFile);
            try {
                Files.copy(file.getInputStream(), localSliceFile, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                log.info("切片文件已成功保存到本地: {} (大小: {} 字节)", localSliceFile, localSliceFile.toFile().length());
            } catch (Exception e) {
                log.error("切片文件保存到本地失败: {}，异常: {}", localSliceFile, e.getMessage(), e);
                // 这里不抛出异常，保证即使保存失败也继续后续流程
            }

            SliceFileAnalyzer analyzer = findSuitableAnalyzer(file.getOriginalFilename());
            SliceAnalysisResult analysisResult = analyzer.analyze(localSliceFile.toFile());

            // 保存缩略图和标签图到本地磁盘（不再上传到Minio）
            String baseFileName = FilenameUtils.getBaseName(newFileName);
            Path thumbLocalPath = localSliceFile.getParent().resolve("thumb_" + baseFileName + ".jpeg");
            Path labelLocalPath = localSliceFile.getParent().resolve("label_" + baseFileName + ".jpeg");
            Path slideLocalPath = localSliceFile.getParent().resolve("slide_" + baseFileName + ".jpeg");
            
            // 保存缩略图到本地
            if (analysisResult.getThumbnailImage() != null && analysisResult.getThumbnailImage().exists()) {
                Files.copy(analysisResult.getThumbnailImage().toPath(), thumbLocalPath, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                log.info("缩略图已保存到本地: {}", thumbLocalPath);
            }
            
            // 保存标签图到本地
            if (analysisResult.getLabelImage() != null && analysisResult.getLabelImage().exists()) {
                Files.copy(analysisResult.getLabelImage().toPath(), labelLocalPath, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                log.info("标签图已保存到本地: {}", labelLocalPath);
            }
            
            // 保存玻片图到本地
            if (analysisResult.getSlideImage() != null && analysisResult.getSlideImage().exists()) {
                Files.copy(analysisResult.getSlideImage().toPath(), slideLocalPath, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                log.info("玻片图已保存到本地: {}", slideLocalPath);
            }

            SliceInfo sliceInfo = createSliceInfo(file, taskId, analysisResult, 
                localSliceFile.toString(), thumbLocalPath.toString(), labelLocalPath.toString(), slideLocalPath.toString());
            logSliceInfo(sliceInfo);

            return sliceInfo;
        } catch (SliceAnalysisException e) {
            log.error("切片解析失败: taskId={}, error={}", taskId, e.getMessage(), e);
            throw e;
        } catch (BusinessException e) {
            log.error("切片上传业务异常: taskId={}, error={}", taskId, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("切片上传过程发生异常: taskId={}, error={}", taskId, e.getMessage(), e);
            throw new BusinessException("处理切片文件失败: " + e.getMessage());
        }
    }

    @Override
    public Result<?> asyncUploadAndAnalyzeSlice(MultipartFile file, String taskId) {
        // 调用支持partnerCode的方法，使用默认partnerCode
        return asyncUploadAndAnalyzeSlice(file, taskId, "default");
    }
    
    @Override
    public Result<?> asyncUploadAndAnalyzeSlice(MultipartFile file, String taskId, String partnerCode) {
        if (file == null || file.isEmpty() || taskId == null || taskId.trim().isEmpty()) {
            return Result.fail(file == null || file.isEmpty() ? "上传文件不能为空" : "任务ID不能为空");
        }

        UploadTask task = Optional.ofNullable(fileUploadService.getTask(taskId))
            .orElseThrow(() -> new BusinessException("上传任务不存在，请先创建上传任务"));

        if (task.getStatus() == UploadStatus.COMPLETED) {
            return Result.fail("该文件已上传完成，请勿重复上传");
        }

        if (task.getStatus() == UploadStatus.UPLOADING) {
            return Result.fail("该文件正在上传中，请勿重复提交");
        }

        log.info("开始同步处理切片文件上传: taskId={}, fileName={}, 大小: {}, partnerCode={}",
                taskId, file.getOriginalFilename(), file.getSize(), partnerCode);

        try {
            fileUploadService.updateTaskStatus(taskId, 0L, UploadStatus.UPLOADING.toString(), null, null, false);

            long totalSize = file.getSize();
            AtomicBoolean cancelFlag = new AtomicBoolean(false);
            cancelFlags.put(taskId, cancelFlag);

            // 直接在最终位置处理文件，避免创建临时目录
            // 生成最终文件路径
            String fileExtension = FilenameUtils.getExtension(file.getOriginalFilename());
            String type = fileExtension.toLowerCase();
            java.time.LocalDateTime now = java.time.LocalDateTime.now();
            String year = now.format(java.time.format.DateTimeFormatter.ofPattern("yyyy"));
            String month = now.format(java.time.format.DateTimeFormatter.ofPattern("MM"));
            String day = now.format(java.time.format.DateTimeFormatter.ofPattern("dd"));
            String timestamp = now.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
            String newFileName = timestamp + "_" + file.getOriginalFilename();
            if (partnerCode == null || partnerCode.trim().isEmpty()) {
                partnerCode = "default";
            }
            
            // 物理存储完整路径
            Path localSlicePath = Path.of(localSliceDir, partnerCode, type, year, month, day);
            Files.createDirectories(localSlicePath);
            Path finalFilePath = localSlicePath.resolve(newFileName);
            
            // 直接保存到最终位置
            file.transferTo(finalFilePath.toFile());
            log.info("切片文件已直接保存到最终位置: {} (大小: {} 字节)", finalFilePath, finalFilePath.toFile().length());

            // 同步处理切片文件
            processSliceFileAsync(file, taskId, finalFilePath, totalSize, cancelFlag, partnerCode);

            return Result.success(Map.of(
                "taskId", taskId,
                "status", "UPLOADING",
                "message", "切片文件处理任务已提交，同步处理中",
                "fileName", file.getOriginalFilename()
            ));

        } catch (Exception e) {
            log.error("同步处理切片文件失败: 穿线标识={}, error={}", taskId, e.getMessage(), e);
            fileUploadService.updateTaskStatus(taskId, 0L, UploadStatus.FAILED.toString(),
                null, "处理切片文件失败: " + e.getMessage(), true);
            return Result.fail("提交切片文件处理任务失败: " + e.getMessage());
        } finally {
            cancelFlags.remove(taskId);
        }
    }

    private void processSliceFileAsync(MultipartFile file, String taskId, Path finalFilePath,
                                     long totalSize, AtomicBoolean cancelFlag, String partnerCode) {
        File tempLabelImage = null;
        File tempThumbnailImage = null;
        File tempSlideImageObj = null;
        UploadTask task = fileUploadService.getTask(taskId);

        try {
            if (cancelFlag.get()) {
                handleTaskCancellation(taskId);
                return;
            }

            fileUploadService.updateTaskStatus(taskId, 0L, UploadStatus.UPLOADING.toString(),
                null, "正在解析切片文件", false);

            // 文件已经在最终位置，直接解析
            log.info("开始解析切片文件: {}", finalFilePath);
            
            // 从finalFilePath计算相对路径，而不是重新生成
            String localSliceDir = this.localSliceDir;
            String absolutePath = finalFilePath.toString();
            String relativePath;
            
            if (absolutePath.startsWith(localSliceDir)) {
                // 去掉localSliceDir前缀，得到相对路径
                relativePath = absolutePath.substring(localSliceDir.length());
                if (!relativePath.startsWith("/")) {
                    relativePath = "/" + relativePath;
                }
            } else {
                // 如果路径不匹配，使用文件名作为fallback
                relativePath = "/" + finalFilePath.getFileName().toString();
            }
            
            log.debug("计算得到的相对路径: {}", relativePath);

            try {
                // 直接在最终位置解析切片文件，生成缩略图、标签图
                SliceFileAnalyzer analyzer = findSuitableAnalyzer(file.getOriginalFilename());
                SliceAnalysisResult analysisResult = analyzer.analyze(finalFilePath.toFile());

                if (cancelFlag.get()) {
                    handleTaskCancellation(taskId);
                    return;
                }

                tempLabelImage = analysisResult.getLabelImage();
                tempThumbnailImage = analysisResult.getThumbnailImage();
                tempSlideImageObj = analysisResult.getSlideImage();

                // 检查缩略图、标签图、玻片图是否生成成功
                if (tempThumbnailImage == null || !tempThumbnailImage.exists() || 
                    tempLabelImage == null || !tempLabelImage.exists() ||
                    tempSlideImageObj == null || !tempSlideImageObj.exists()) {
                    throw new BusinessException("生成缩略图、标签图或玻片图失败");
                }

                fileUploadService.updateTaskStatus(taskId, Math.round(totalSize * 0.4),
                    UploadStatus.UPLOADING.toString(), null, "切片文件解析完成，开始保存图片文件", false);

                // 将生成的图片移动到最终位置（与切片文件同目录）
                String actualFileName = finalFilePath.getFileName().toString();
                String baseFileName = FilenameUtils.getBaseName(actualFileName);
                Path thumbLocalPath = finalFilePath.getParent().resolve("thumb_" + baseFileName + ".jpeg");
                Path labelLocalPath = finalFilePath.getParent().resolve("label_" + baseFileName + ".jpeg");
                Path slideLocalPath = finalFilePath.getParent().resolve("slide_" + baseFileName + ".jpeg");
                
                // 移动缩略图到最终位置
                if (tempThumbnailImage != null && tempThumbnailImage.exists()) {
                    if (!tempThumbnailImage.getAbsolutePath().equals(thumbLocalPath.toString())) {
                        Files.move(tempThumbnailImage.toPath(), thumbLocalPath, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                        log.info("缩略图已移动到最终位置: {}", thumbLocalPath);
                    } else {
                        log.info("缩略图已在最终位置: {}", thumbLocalPath);
                    }
                }
                
                // 移动标签图到最终位置
                if (tempLabelImage != null && tempLabelImage.exists()) {
                    if (!tempLabelImage.getAbsolutePath().equals(labelLocalPath.toString())) {
                        Files.move(tempLabelImage.toPath(), labelLocalPath, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                        log.info("标签图已移动到最终位置: {}", labelLocalPath);
                    } else {
                        log.info("标签图已在最终位置: {}", labelLocalPath);
                    }
                }

                // 移动玻片图到最终位置
                if (tempSlideImageObj != null && tempSlideImageObj.exists()) {
                    if (!tempSlideImageObj.getAbsolutePath().equals(slideLocalPath.toString())) {
                        Files.move(tempSlideImageObj.toPath(), slideLocalPath, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                        log.info("玻片图已移动到最终位置: {}", slideLocalPath);
                    } else {
                        log.info("玻片图已在最终位置: {}", slideLocalPath);
                    }
                }

                fileUploadService.updateTaskStatus(taskId, Math.round(totalSize * 0.85),
                    UploadStatus.UPLOADING.toString(), null, "图片文件保存完成", false);

                // 更新任务元数据
                Map<String, String> metadata = Stream.of(
                    Map.entry("localSlicePath", finalFilePath.toString()),
                    Map.entry("thumbImagePath", thumbLocalPath.toString()),
                    Map.entry("labelImagePath", labelLocalPath.toString()),
                    Map.entry("slideImagePath", slideLocalPath.toString())
                ).collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue()), HashMap::putAll);
                metadata.forEach((key, value) -> task.setMetadata(key, value));

                // 更新t_c_slice表信息
                String fileExtension = FilenameUtils.getExtension(file.getOriginalFilename());
                String scanFormatName = fileExtension.toLowerCase();
                
                // 根据taskId判断是主键id还是sliceId，然后选择合适的查询方式
                cn.ccaa.slice.core.model.SliceEntity sliceEntity = null;
                try {
                    // 先尝试按sliceId查询（蚂蚁上传场景）
                    sliceEntity = sliceService.getBySliceId(taskId);
                    if (sliceEntity == null) {
                        // 如果按sliceId查不到，再按主键id查询（本地上传和切片池上传场景）
                        sliceEntity = sliceService.getById(taskId);
                    }
                } catch (Exception e) {
                    log.warn("查询切片记录失败，尝试按主键id查询: 穿线标识={}, error={}", taskId, e.getMessage());
                    sliceEntity = sliceService.getById(taskId);
                }
                
                if (sliceEntity == null) {
                    log.error("未找到切片记录: 穿线标识={}", taskId);
                    throw new BusinessException("未找到切片记录");
                }
                
                // 更新表结构字段
                Map<String, Object> sliceMetadata = analysisResult.getMetadata();
                if (sliceMetadata.get("sliceNo") != null) {
                    sliceEntity.setSliceNo(String.valueOf(sliceMetadata.get("sliceNo")));
                }
                if (scanFormatName != null) {
                    sliceEntity.setScanFormat(scanFormatName);
                }
                String magnificationValue = extractMagnificationFromMetadata(sliceMetadata, taskId);
                if (magnificationValue != null) {
                    sliceEntity.setEnlarge(magnificationValue);
                }
                
                // 设置相对路径
                sliceEntity.setPhysicPath(relativePath);
                sliceEntity.setQpState("1"); // 解析成功
                sliceEntity.setLastModifiedTime(java.time.LocalDateTime.now());
                
                // 使用实体更新
                sliceService.updateById(sliceEntity);
                
                // 根据场景输出不同的日志
                String actualTaskId = sliceEntity.getTaskId(); // 获取实际的taskId
                if (actualTaskId != null && !actualTaskId.isEmpty()) {
                    // 本地上传场景
                    log.info("本地上传场景：切片解析成功，穿线标识(主键id)={}, 实际taskId={}", taskId, actualTaskId);
                } else if (sliceEntity.getSliceId() != null && !sliceEntity.getSliceId().isEmpty()) {
                    // 蚂蚁上传场景
                    log.info("蚂蚁上传场景：切片解析成功，穿线标识(sliceId)={}", taskId);
                } else {
                    // 切片池上传场景
                    log.info("切片池上传场景：切片解析成功，穿线标识(主键id)={}", taskId);
                }
                
                // 推送切片解析信息
                if (partnerCode != null && !partnerCode.equals("default")) {
                    try {
                        boolean pushSuccess = sliceAnalysisCallbackService.pushSliceAnalysisInfo(taskId, partnerCode);
                        if (pushSuccess) {
                            log.info("切片解析信息推送成功: 穿线标识={}, partnerCode={}", taskId, partnerCode);
                        } else {
                            log.warn("切片解析信息推送失败: 穿线标识={}, partnerCode={}", taskId, partnerCode);
                        }
                    } catch (Exception e) {
                        log.error("切片解析信息推送异常: 穿线标识={}, partnerCode={}, error={}", taskId, partnerCode, e.getMessage(), e);
                    }
                } else {
                    log.debug("partnerCode为default或null，跳过推送: 穿线标识={}, partnerCode={}", taskId, partnerCode);
                }

                fileUploadService.updateTaskStatus(taskId, totalSize, UploadStatus.COMPLETED.toString(),
                    finalFilePath.toString(), "文件处理完成", true);

                log.info("切片文件同步处理完成: 穿线标识={}, fileName={}", taskId, file.getOriginalFilename());

            } catch (Exception e) {
                // 解析失败，状态设为2
                log.error("切片解析失败: 穿线标识={}, error={}", taskId, e.getMessage(), e);
                try {
                    cn.ccaa.slice.core.model.SliceEntity failSliceEntity = null;
                    try {
                        failSliceEntity = sliceService.getBySliceId(taskId);
                        if (failSliceEntity == null) {
                            failSliceEntity = sliceService.getById(taskId);
                        }
                    } catch (Exception queryEx) {
                        log.warn("查询切片记录失败，尝试按主键id查询: 穿线标识={}, error={}", taskId, queryEx.getMessage());
                        failSliceEntity = sliceService.getById(taskId);
                    }
                    
                    if (failSliceEntity != null) {
                        failSliceEntity.setQpState("2");
                        failSliceEntity.setLastModifiedTime(java.time.LocalDateTime.now());
                        sliceService.updateById(failSliceEntity);
                        
                        // 根据场景输出不同的日志
                        String actualTaskId = failSliceEntity.getTaskId(); // 获取实际的taskId
                        if (actualTaskId != null && !actualTaskId.isEmpty()) {
                            // 本地上传场景
                            log.info("本地上传场景：已设置切片状态为2（解析失败），穿线标识(主键id)={}, 实际taskId={}", taskId, actualTaskId);
                        } else if (failSliceEntity.getSliceId() != null && !failSliceEntity.getSliceId().isEmpty()) {
                            // 蚂蚁上传场景
                            log.info("蚂蚁上传场景：已设置切片状态为2（解析失败），穿线标识(sliceId)={}", taskId);
                        } else {
                            // 切片池上传场景
                            log.info("切片池上传场景：已设置切片状态为2（解析失败），穿线标识(主键id)={}", taskId);
                        }
                    } else {
                        log.warn("未找到切片记录，无法更新状态: 穿线标识={}", taskId);
                    }
                } catch (Exception dbEx) {
                    log.error("更新切片状态为失败时出错: 穿线标识={}, error={}", taskId, dbEx.getMessage(), dbEx);
                }
                
                fileUploadService.updateTaskStatus(taskId, 0L, UploadStatus.FAILED.toString(),
                    finalFilePath.toString(), "处理切片文件失败: " + e.getMessage(), true);
            }

        } catch (Exception e) {
            log.error("同步处理切片文件失败: 穿线标识={}, error={}", taskId, e.getMessage(), e);
            fileUploadService.updateTaskStatus(taskId, 0L, UploadStatus.FAILED.toString(),
                null, "处理切片文件失败: " + e.getMessage(), true);
        } finally {
            cancelFlags.remove(taskId);
        }
    }

    private void handleTaskCancellation(String taskId) {
        log.info("切片文件处理任务已取消: 穿线标识={}", taskId);
        fileUploadService.updateTaskStatus(taskId, 0L, UploadStatus.CANCELED.toString(),
            null, "用户取消了上传任务", true);
    }

    @Override
    public boolean cancelSliceProcessing(String taskId) {
        return Optional.ofNullable(cancelFlags.get(taskId))
            .map(flag -> {
                flag.set(true);
                log.info("已设置切片处理任务取消标志: 穿线标识={}", taskId);
                return true;
            })
            .orElse(false);
    }

    private SliceFileAnalyzer findSuitableAnalyzer(String fileName) throws BusinessException {
        return sliceAnalyzers.stream()
            .filter(analyzer -> analyzer.supports(fileName))
            .findFirst()
            .orElseThrow(() -> new BusinessException("不支持的文件格式: " + FilenameUtils.getExtension(fileName)));
    }

    private SliceInfo createSliceInfo(MultipartFile file, String taskId, SliceAnalysisResult analysisResult,
                                     String originalFileUrl, String thumbImageUrl, String labelImageUrl, String slideImageUrl) {
        Map<String, Object> metadata = analysisResult.getMetadata();

        return SliceInfo.builder()
            .id(UlidCreator.getMonotonicUlid().toString())
            .sliceNo(taskId)
            .sliceFileName(file.getOriginalFilename())
            .origPixWidth(getIntegerFromMetadata(metadata, "width"))
            .origPixHeight(getIntegerFromMetadata(metadata, "height"))
            .scanZoom(getIntegerFromMetadata(metadata, "magnification"))
            .fmtCode(analysisResult.getFileTypeCode())
            .sliceMfrCode(analysisResult.getManufacturerCode())
            .originalFilePath(originalFileUrl)
            .thumbImagePath(thumbImageUrl)
            .labelImagePath(labelImageUrl)
            .slideImagePath(slideImageUrl)
            .sliceStas(5)
            .uploadTime(new Date())
            .uploadUserId("system")
            .validFlag(1)
            .crteTime(new Date())
            .crterId("system")
            .crterName("系统")
            .build();
    }

    private Integer getIntegerFromMetadata(Map<String, Object> metadata, String key) {
        return Optional.ofNullable(metadata.get(key))
            .map(value -> {
                if (value instanceof Long) {
                    return ((Long) value).intValue();
                } else if (value instanceof Integer) {
                    return (Integer) value;
                } else if (value instanceof String) {
                    try {
                        return Integer.parseInt((String) value);
                    } catch (NumberFormatException e) {
                        log.warn("无法解析{}: {}", key, value);
                        return 0;
                    }
                }
                return 0;
            })
            .orElse(0);
    }

    /**
     * 从元数据中提取倍率信息，兼容各个解析器的不同字段名
     * 优先使用TronSDK的MaximumZoomLevel真实倍率（objective字段）
     * 
     * @param metadata 元数据Map
     * @param taskId 任务ID，用于日志输出
     * @return 倍率字符串，如果未找到则返回null
     */
    private String extractMagnificationFromMetadata(Map<String, Object> metadata, String taskId) {
        if (metadata == null || metadata.isEmpty()) {
            log.debug("元数据为空: taskId={}", taskId);
            return null;
        }
        
        log.debug("开始从元数据中提取倍率信息: taskId={}, metadata={}", taskId, metadata);
        
        // 定义可能的倍率字段名，按优先级排序
        String[] magnificationFields = {
            "objective",           // TronSDK和SqraySlide解析器使用，优先级最高（真实倍率）
            "magnification",       // 通用字段名
            "openslide.objective-power",  // OpenSlide解析器使用
            "scan.magnification",  // 其他可能的字段名
            "zoom",               // 其他可能的字段名
            "power"               // 其他可能的字段名
        };
        
        for (String fieldName : magnificationFields) {
            Object magnificationObj = metadata.get(fieldName);
            if (magnificationObj != null) {
                try {
                    String magnificationStr = magnificationObj.toString().trim();
                    log.debug("找到倍率字段: taskId={}, fieldName={}, value={}", taskId, fieldName, magnificationStr);
                    
                    // 提取数字部分，去除可能的"x"或"X"后缀
                    String numericPart = magnificationStr.replaceAll("[xX]", "").trim();
                    
                    // 验证是否为有效数字
                    if (numericPart.matches("\\d+(\\.\\d+)?")) {
                        // 如果是小数，四舍五入为整数
                        if (numericPart.contains(".")) {
                            double doubleValue = Double.parseDouble(numericPart);
                            numericPart = String.valueOf(Math.round(doubleValue));
                        }
                        log.info("成功提取倍率: taskId={}, fieldName={}, originalValue={}, extractedValue={}", 
                                taskId, fieldName, magnificationStr, numericPart);
                        return numericPart;
                    } else {
                        log.warn("倍率值格式无效: taskId={}, fieldName={}, value={}", taskId, fieldName, magnificationStr);
                    }
                } catch (Exception e) {
                    log.warn("解析倍率值时出错: taskId={}, fieldName={}, value={}, error={}", 
                            taskId, fieldName, magnificationObj, e.getMessage());
                }
            }
        }
        
        log.warn("未找到有效的倍率信息: taskId={}, availableFields={}", taskId, metadata.keySet());
        return null;
    }

    private void logSliceInfo(SliceInfo sliceInfo) {
        try {
            SliceInfo logInfo = SliceInfo.builder()
                .id(sliceInfo.getId())
                .sliceNo(sliceInfo.getSliceNo())
                .sliceFileName(sliceInfo.getSliceFileName())
                .origPixWidth(sliceInfo.getOrigPixWidth())
                .origPixHeight(sliceInfo.getOrigPixHeight())
                .scanZoom(sliceInfo.getScanZoom())
                .fmtCode(sliceInfo.getFmtCode())
                .sliceMfrCode(sliceInfo.getSliceMfrCode())
                .sliceStas(sliceInfo.getSliceStas())
                .uploadTime(sliceInfo.getUploadTime())
                .uploadUserId(sliceInfo.getUploadUserId())
                .validFlag(sliceInfo.getValidFlag())
                .crteTime(sliceInfo.getCrteTime())
                .crterId(sliceInfo.getCrterId())
                .crterName(sliceInfo.getCrterName())
                .build();

            Stream.of(
                Map.entry("originalFilePath", sliceInfo.getOriginalFilePath()),
                Map.entry("thumbImagePath", sliceInfo.getThumbImagePath()),
                Map.entry("labelImagePath", sliceInfo.getLabelImagePath()),
                Map.entry("slideImagePath", sliceInfo.getSlideImagePath())
            ).forEach(entry -> Optional.ofNullable(entry.getValue())
                .map(this::extractFileNameFromUrl)
                .ifPresent(fileName -> {
                    switch (entry.getKey()) {
                        case "originalFilePath" -> logInfo.setOriginalFilePath(fileName);
                        case "thumbImagePath" -> logInfo.setThumbImagePath(fileName);
                        case "labelImagePath" -> logInfo.setLabelImagePath(fileName);
                        case "slideImagePath" -> logInfo.setSlideImagePath(fileName);
                    }
                }));

            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            objectMapper.setSerializationInclusion(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL);
            objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
            String jsonSliceInfo = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(logInfo);
            log.debug("切片信息: {}", jsonSliceInfo);
        } catch (Exception e) {
            log.error("转换切片信息为JSON失败: {}", e.getMessage(), e);
            log.debug("切片信息: {}", sliceInfo);
        }
    }

    private String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }

        try {
            String path = url.contains("?") ? url.substring(0, url.indexOf("?")) : url;
            int lastSlashIndex = path.lastIndexOf("/");
            String fileName = lastSlashIndex >= 0 && lastSlashIndex < path.length() - 1
                ? path.substring(lastSlashIndex + 1)
                : path;
            
            // 解码URL编码的文件名，恢复原始文件名
            try {
                return java.net.URLDecoder.decode(fileName, "UTF-8");
            } catch (Exception e) {
                log.warn("URL解码文件名失败: {}, 使用原始文件名", e.getMessage());
                return fileName;
            }
        } catch (Exception e) {
            log.warn("提取文件名失败: {}", e.getMessage());
            return url;
        }
    }
}
