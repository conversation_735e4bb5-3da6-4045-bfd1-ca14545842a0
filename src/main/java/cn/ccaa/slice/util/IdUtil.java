package cn.ccaa.slice.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * ID生成工具类
 *
 * <AUTHOR>
 */
public class IdUtil {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * 生成唯一ID
     *
     * @return 唯一ID
     */
    public static String generateId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成基于时间的ID
     *
     * @return 基于时间的ID
     */
    public static String generateTimeBasedId() {
        return LocalDateTime.now().format(FORMATTER) + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
} 
