package cn.ccaa.slice.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

/**
 * Base64转MultipartFile工具类
 * 供其他Java服务调用截图接口后转换响应数据使用
 * 
 * <AUTHOR>
 */
public class Base64ToMultipartUtil {

    /**
     * 将Base64字符串转换为MultipartFile
     * 
     * @param base64Data Base64编码的图片数据
     * @param fileName 文件名
     * @return MultipartFile对象
     */
    public static MultipartFile base64ToMultipartFile(String base64Data, String fileName) {
        if (base64Data == null || base64Data.isEmpty()) {
            return null;
        }
        
        try {
            // 解码Base64数据
            byte[] decodedBytes = Base64.getDecoder().decode(base64Data);
            
            // 创建MultipartFile实现
            return new Base64MultipartFile(decodedBytes, fileName);
            
        } catch (Exception e) {
            throw new RuntimeException("Base64转MultipartFile失败: " + e.getMessage(), e);
        }
    }

    /**
     * MultipartFile的简单实现类
     */
    public static class Base64MultipartFile implements MultipartFile {
        
        private final byte[] content;
        private final String fileName;
        
        public Base64MultipartFile(byte[] content, String fileName) {
            this.content = content;
            this.fileName = fileName;
        }
        
        @Override
        public String getName() {
            return "file";
        }
        
        @Override
        public String getOriginalFilename() {
            return fileName;
        }
        
        @Override
        public String getContentType() {
            return "image/jpeg";
        }
        
        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }
        
        @Override
        public long getSize() {
            return content != null ? content.length : 0;
        }
        
        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }
        
        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(content);
        }
        
        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            // 简单实现，实际使用中可以完善
            throw new UnsupportedOperationException("transferTo not implemented");
        }
    }
}

/**
 * 使用示例：
 * 
 * // 1. 调用截图JSON接口
 * RestTemplate restTemplate = new RestTemplate();
 * ResponseEntity<Result<TCSliceCaptureResponseDTO>> response = restTemplate.postForEntity(
 *     "http://your-server:8080/slice/api/slice/capture/json",
 *     request,
 *     new ParameterizedTypeReference<Result<TCSliceCaptureResponseDTO>>() {}
 * );
 * 
 * // 2. 获取响应数据
 * TCSliceCaptureResponseDTO data = response.getBody().getData();
 * 
 * // 3. 转换Base64为MultipartFile
 * MultipartFile originalImage = Base64ToMultipartUtil.base64ToMultipartFile(
 *     data.getCutImageOriBase64(), 
 *     data.getCutImageOriFileName()
 * );
 * 
 * MultipartFile thumbnailImage = Base64ToMultipartUtil.base64ToMultipartFile(
 *     data.getCutImageBase64(), 
 *     data.getCutImageFileName()
 * );
 * 
 * // 4. 构建TCSliceCaptureDTO
 * TCSliceCaptureDTO dto = new TCSliceCaptureDTO();
 * dto.setFileId(data.getFileId());
 * dto.setZoom(data.getZoom());
 * // ... 其他字段赋值
 * dto.setCutImageOri(originalImage);
 * dto.setCutImage(thumbnailImage);
 * 
 * // 5. 现在可以正常使用包含MultipartFile的DTO对象了
 */ 