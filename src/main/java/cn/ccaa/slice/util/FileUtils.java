package cn.ccaa.slice.util;

import java.io.File;
import java.io.FileOutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.EnumSet;
import java.util.Set;

import org.springframework.stereotype.Component;

import cn.ccaa.slice.config.StorageConfig;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件工具类
 * 提供统一的文件路径处理方法
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FileUtils {

    private static final String DEFAULT_TEMP_DIR = System.getProperty("java.io.tmpdir");
    private static final String POSIX_PERMISSIONS = "rwxrwx---";
    private static final Set<PosixFilePermission> DEFAULT_DIR_PERMS = EnumSet.of(
        PosixFilePermission.OWNER_READ,
        PosixFilePermission.OWNER_WRITE,
        PosixFilePermission.OWNER_EXECUTE,
        PosixFilePermission.GROUP_READ,
        PosixFilePermission.GROUP_WRITE,
        PosixFilePermission.GROUP_EXECUTE
    );
    
    private final StorageConfig storageConfig;
    
    @Getter
    private static String configuredTempDir;
    
    @Getter
    private static String defaultTempDir = DEFAULT_TEMP_DIR;
    
    /**
     * 获取统一的临时目录
     */
    public static String getTempDir() {
        return configuredTempDir != null ? configuredTempDir : defaultTempDir;
    }
    
    @PostConstruct
    public void init() {
        if (storageConfig != null && storageConfig.getTemp() != null) {
            configuredTempDir = storageConfig.getTemp().getBaseDir();
            log.info("已配置临时目录: {}", configuredTempDir);
            
            File tempDir = new File(configuredTempDir);
            if (!initializeTempDirectory(tempDir)) {
                configuredTempDir = defaultTempDir;
                log.info("回退到系统默认临时目录: {}", defaultTempDir);
            }
        } else {
            log.info("使用系统默认临时目录: {}", defaultTempDir);
        }
        
        testTempDirectory();
    }
    
    /**
     * 初始化临时目录
     */
    private boolean initializeTempDirectory(File tempDir) {
        if (!tempDir.exists()) {
            return createTempDirectory(tempDir);
        }
        
        if (!tempDir.isDirectory()) {
            log.warn("配置的临时目录不是一个目录: {}", configuredTempDir);
            return false;
        }
        
        if (!tempDir.canWrite()) {
            log.warn("配置的临时目录不可写: {}", configuredTempDir);
            return setDirectoryPermissions(tempDir) && tempDir.canWrite();
        }
        
        return true;
    }
    
    /**
     * 创建临时目录
     */
    private boolean createTempDirectory(File tempDir) {
        boolean created = tempDir.mkdirs();
        log.info("创建配置的临时目录: {} - {}", configuredTempDir, created ? "成功" : "失败");
        
        if (created) {
            return setDirectoryPermissions(tempDir);
        }
        
        return false;
    }
    
    /**
     * 设置目录权限
     */
    private boolean setDirectoryPermissions(File dir) {
        try {
            Files.setPosixFilePermissions(dir.toPath(), DEFAULT_DIR_PERMS);
            log.info("已设置目录权限: {}", dir.getAbsolutePath());
            return true;
        } catch (Exception e) {
            log.warn("设置目录权限失败: {}, 错误: {}", dir.getAbsolutePath(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试临时目录是否可用
     */
    private void testTempDirectory() {
        String testDir = getTempDir() + "/test_" + System.currentTimeMillis();
        File dir = new File(testDir);
        
        try {
            if (!dir.mkdirs()) {
                log.warn("无法创建测试临时目录，可能存在权限问题");
                return;
            }
            
            File testFile = new File(testDir, "test.txt");
            testFileWriteAndRead(testFile);
            
            cleanupTestFiles(testFile, dir);
        } catch (Exception e) {
            log.error("测试临时目录失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试文件写入和读取
     */
    private void testFileWriteAndRead(File testFile) throws Exception {
        try (FileOutputStream fos = new FileOutputStream(testFile)) {
            fos.write("test".getBytes(StandardCharsets.UTF_8));
        }
        
        log.debug("测试写入文件成功: {}", testFile.getAbsolutePath());
        
        byte[] content = Files.readAllBytes(testFile.toPath());
        if (content.length != 4) {
            log.warn("测试读取文件内容与写入不一致");
        } else {
            log.debug("测试读取文件成功");
        }
    }
    
    /**
     * 清理测试文件
     */
    private void cleanupTestFiles(File testFile, File dir) {
        testFile.delete();
        dir.delete();
    }
    
    /**
     * 构建规范化的临时文件路径
     */
    public static String getNormalizedTempPath(String minioPath) {
        if (minioPath == null || minioPath.isEmpty()) {
            return null;
        }
        
        String[] parts = minioPath.split("/");
        if (parts.length < 2) {
            return null;
        }
        
        String taskId = parts[1];
        String fileName = parts[parts.length - 1];
        String tempPath = getTempDir() + "/detect_" + taskId + "/" + fileName;
        
        try {
            Path dirPath = Paths.get(tempPath).getParent();
            ensureDirectoryExists(dirPath);
        } catch (Exception e) {
            log.warn("确保目录存在失败: {}", e.getMessage());
        }
        
        return tempPath;
    }
    
    /**
     * 确保目录存在，不存在则创建
     */
    public static boolean ensureDirectoryExists(Path dirPath) {
        if (dirPath == null) {
            return false;
        }
        
        try {
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                log.debug("创建目录: {}", dirPath);
                setDirectoryPermissions(dirPath);
            }
            return true;
        } catch (Exception e) {
            log.warn("创建目录失败: {}, 错误: {}", dirPath, e.getMessage());
            return false;
        }
    }
    
    /**
     * 设置目录权限
     */
    private static void setDirectoryPermissions(Path dirPath) {
        try {
            Files.setPosixFilePermissions(dirPath, 
                PosixFilePermissions.fromString(POSIX_PERMISSIONS));
        } catch (Exception e) {
            log.debug("设置目录权限失败（可能不支持POSIX权限）: {}", e.getMessage());
        }
    }
    
    /**
     * 检查文件是否有效
     */
    public static boolean isFileValid(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        
        File file = new File(filePath);
        return file.exists() && file.isFile();
    }
    
    /**
     * 详细检查文件是否有效且可读
     */
    public static FileCheckResult checkFileValidity(String filePath) {
        FileCheckResult result = new FileCheckResult();
        result.setFilePath(filePath);
        
        if (filePath == null || filePath.isEmpty()) {
            return result.setInvalid("文件路径为空");
        }
        
        File file = new File(filePath);
        if (!file.exists()) {
            return result.setInvalid("文件不存在");
        }
        
        if (!file.isFile()) {
            return result.setInvalid("路径不是一个文件");
        }
        
        if (file.length() == 0) {
            return result.setInvalid("文件大小为0");
        }
        
        if (!file.canRead()) {
            return result.setInvalid("文件不可读");
        }
        
        return result.setValid(file.length(), file.canRead(), file.canWrite());
    }
    
    /**
     * 文件检查结果类
     */
    @Getter
    @Setter
    public static class FileCheckResult {
        private String filePath;
        private boolean valid;
        private String reason;
        private long fileSize;
        private boolean canRead;
        private boolean canWrite;
        
        /**
         * 设置无效状态
         */
        public FileCheckResult setInvalid(String reason) {
            this.valid = false;
            this.reason = reason;
            return this;
        }
        
        /**
         * 设置有效状态
         */
        public FileCheckResult setValid(long fileSize, boolean canRead, boolean canWrite) {
            this.valid = true;
            this.fileSize = fileSize;
            this.canRead = canRead;
            this.canWrite = canWrite;
            return this;
        }
        
        @Override
        public String toString() {
            if (valid) {
                return String.format("文件有效: %s, 大小: %d 字节, 可读: %b, 可写: %b", 
                    filePath, fileSize, canRead, canWrite);
            }
            return String.format("文件无效: %s, 原因: %s", filePath, reason);
        }
    }
} 
