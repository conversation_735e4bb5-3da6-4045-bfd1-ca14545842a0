package cn.ccaa.slice.util;

import java.util.Map;
import java.util.HashMap;

/**
 * 文件格式工具类
 *
 * <AUTHOR>
 */
public class FileFormatUtil {

    private static final Map<String, String> FORMAT_CODE_MAP = new HashMap<>();

    static {
        // 初始化文件格式映射
        FORMAT_CODE_MAP.put("ndpi", "1");  // 滨松
        FORMAT_CODE_MAP.put("sdpc", "2");  // 江丰
        FORMAT_CODE_MAP.put("svs", "3");   // 徕卡
        FORMAT_CODE_MAP.put("kfb", "4");   // 生强
        FORMAT_CODE_MAP.put("tron", "5");  // tron
    }

    /**
     * 根据文件名获取文件格式代码
     *
     * @param fileName 文件名
     * @return 格式代码，如果未知则返回null
     */
    public static String getFormatCodeByFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return null;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex < 0 || lastDotIndex == fileName.length() - 1) {
            return null;
        }
        
        String extension = fileName.substring(lastDotIndex + 1).toLowerCase();
        return FORMAT_CODE_MAP.get(extension);
    }
} 
