package cn.ccaa.slice.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * AES加密工具类
 * 
 * <AUTHOR>
 */
public class AESUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(AESUtil.class);
    
    /**
     * 加密密钥，固定值
     */
    private static final String SECRET_KEY = "CCA_SLICE_AES_KEY";
    
    /**
     * 加密算法
     */
    private static final String ALGORITHM = "AES";
    
    /**
     * 使用AES算法加密字符串
     * 
     * @param data 要加密的数据
     * @return 加密后的Base64编码字符串
     */
    public static String encrypt(String data) {
        if (data == null || data.isEmpty()) {
            return "";
        }
        
        try {
            SecretKeySpec secretKey = generateKey();
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            logger.error("AES加密失败", e);
            return "";
        }
    }
    
    /**
     * 使用AES算法解密字符串
     * 
     * @param encryptedData 加密的Base64编码字符串
     * @return 解密后的原始字符串
     */
    public static String decrypt(String encryptedData) {
        if (encryptedData == null || encryptedData.isEmpty()) {
            return "";
        }
        
        try {
            SecretKeySpec secretKey = generateKey();
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decodedBytes = Base64.getDecoder().decode(encryptedData);
            byte[] decryptedBytes = cipher.doFinal(decodedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("AES解密失败", e);
            return "";
        }
    }
    
    /**
     * 生成密钥
     * 
     * @return SecretKeySpec对象
     */
    private static SecretKeySpec generateKey() throws Exception {
        MessageDigest sha = MessageDigest.getInstance("SHA-256");
        byte[] key = sha.digest(SECRET_KEY.getBytes(StandardCharsets.UTF_8));
        return new SecretKeySpec(key, 0, 16, ALGORITHM);
    }
} 
