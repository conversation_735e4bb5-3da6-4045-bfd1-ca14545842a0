package cn.ccaa.slice.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * 图像块处理管理器
 * 用于管理和优化大图像分块处理
 * 
 * <AUTHOR>
 */
@Slf4j
public class BlockManager {
    private List<int[]> blocks = new ArrayList<>();
    private int centerBlockX;
    private int centerBlockY;
    
    /**
     * 创建块管理器
     *
     * @param xBlocks 横向块数
     * @param yBlocks 纵向块数
     */
    public BlockManager(int xBlocks, int yBlocks) {
        // 计算中心块位置
        centerBlockX = xBlocks / 2;
        centerBlockY = yBlocks / 2;
        
        // 初始化所有块
        for (int blockY = 0; blockY < yBlocks; blockY++) {
            for (int blockX = 0; blockX < xBlocks; blockX++) {
                blocks.add(new int[]{blockX, blockY});
            }
        }
    }
    
    /**
     * 获取块数量
     *
     * @return 块数量
     */
    public int getBlockCount() {
        return blocks.size();
    }
    
    /**
     * 优化处理顺序，优先处理中心区域的块
     */
    public void optimizeProcessingOrder() {
        // 使用欧几里得距离排序
        Collections.sort(blocks, (a, b) -> {
            double distA = Math.sqrt(Math.pow(a[0] - centerBlockX, 2) + Math.pow(a[1] - centerBlockY, 2));
            double distB = Math.sqrt(Math.pow(b[0] - centerBlockX, 2) + Math.pow(b[1] - centerBlockY, 2));
            return Double.compare(distA, distB);
        });
    }
    
    /**
     * 获取所有块
     *
     * @return 块列表
     */
    public List<int[]> getBlocks() {
        return blocks;
    }
} 