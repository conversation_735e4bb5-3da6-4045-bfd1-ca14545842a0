package cn.ccaa.slice.task;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.ccaa.slice.config.StorageConfig;
import cn.ccaa.slice.config.TempDirConfig;
import cn.ccaa.slice.service.upload.chunk.ChunkStatusManager;

/**
 * 文件缓存清理任务
 * 定期清理缓存目录和临时目录中的旧文件
 *
 * <AUTHOR>
 */
@Component
public class FileCacheCleanupTask {

    private static final Logger log = LoggerFactory.getLogger(FileCacheCleanupTask.class);

    @Autowired
    private StorageConfig storageConfig;

    @Autowired
    private TempDirConfig tempDirConfig;

    @Lazy
    @Autowired
    private ChunkStatusManager chunkStatusManager;

    /**
     * 缓存文件保留时间（小时）
     * 默认24小时
     */
    @Value("${cache.file.retention-hours:24}")
    private int cacheRetentionHours;

    /**
     * 临时文件保留时间（小时）
     * 默认48小时
     */
    @Value("${cache.temp.retention-hours:48}")
    private int tempRetentionHours;

    /**
     * 每天凌晨2点执行清理任务（临时目录清理周期由配置决定）
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupCacheFiles() {
        log.info("开始执行缓存文件清理任务");
        // 清理缓存目录
        cleanupCacheDirectory(0); // 使用默认保留时间
        // 清理临时目录（按配置的存活时间）
        cleanupTempDirectoryByConfig();
        log.info("缓存文件清理任务完成");
    }

    /**
     * 清理缓存目录
     *
     * @param hours 保留时间（小时），为0时使用配置的默认值
     */
    public void cleanupCacheDirectory(int hours) {
        int retentionHours = hours > 0 ? hours : cacheRetentionHours;
        log.info("清理缓存目录，保留时间: {}\u5c0f时", retentionHours);
        cleanupDirectory("cache", retentionHours);
    }

    /**
     * 按配置的临时文件存活时间清理临时目录和Redis任务信息
     */
    public void cleanupTempDirectoryByConfig() {
        int ttlHours = tempDirConfig.getTempFileTtlHours();
        Path tempDir = tempDirConfig.getTempDir();
        long cutoff = System.currentTimeMillis() - ttlHours * 60L * 60L * 1000L;
        File[] taskDirs = tempDir.toFile().listFiles(File::isDirectory);
        if (taskDirs == null) return;
        int deleted = 0;
        for (File dir : taskDirs) {
            if (dir.lastModified() < cutoff) {
                String taskId = dir.getName();
                // 清理Redis任务信息
                chunkStatusManager.cleanupTask(taskId);
                // 清理临时分片文件
                org.apache.commons.io.FileUtils.deleteQuietly(dir);
                deleted++;
                log.info("定时清理过期临时目录及Redis: {}", dir.getAbsolutePath());
            }
        }
        if (deleted > 0) {
            log.info("本次定时清理临时目录共删除{}个过期目录", deleted);
        }
    }

    /**
     * 清理指定目录中的旧文件
     *
     * @param dirName 目录名称
     * @param retentionHours 保留时间（小时）
     */
    private void cleanupDirectory(String dirName, int retentionHours) {
        try {
            // 获取应用根目录
            String appDir = System.getProperty("user.dir");
            Path dirPath = Paths.get(appDir, dirName);

            if (!Files.exists(dirPath) || !Files.isDirectory(dirPath)) {
                log.warn("目录不存在或不是有效目录: {}", dirPath);
                return;
            }

            log.info("开始清理目录: {}, 保留时间: {}小时", dirPath, retentionHours);

            // 计算截止时间
            Instant cutoffTime = Instant.now().minus(Duration.ofHours(retentionHours));
            AtomicInteger deletedCount = new AtomicInteger(0);
            AtomicInteger failedCount = new AtomicInteger(0);
            AtomicInteger totalCount = new AtomicInteger(0);

            // 递归遍历目录中的所有文件
            try (Stream<Path> pathStream = Files.walk(dirPath)) {
                pathStream
                    .filter(Files::isRegularFile)
                    .forEach(filePath -> {
                        totalCount.incrementAndGet();
                        try {
                            // 获取文件的最后修改时间
                            Instant lastModified = Files.getLastModifiedTime(filePath).toInstant();

                            // 如果文件早于截止时间，则删除
                            if (lastModified.isBefore(cutoffTime)) {
                                Files.delete(filePath);
                                deletedCount.incrementAndGet();

                                if (deletedCount.get() % 100 == 0) {
                                    log.info("已删除{}个文件...", deletedCount.get());
                                }
                            }
                        } catch (Exception e) {
                            log.warn("删除文件失败: {}, 错误: {}", filePath, e.getMessage());
                            failedCount.incrementAndGet();
                        }
                    });
            }

            // 清理空目录
            cleanEmptyDirectories(dirPath);

            log.info("目录清理完成: {}, 总文件数: {}, 已删除: {}, 失败: {}",
                    dirPath, totalCount.get(), deletedCount.get(), failedCount.get());

        } catch (Exception e) {
            log.error("清理目录失败: {}, 错误: {}", dirName, e.getMessage(), e);
        }
    }

    /**
     * 清理空目录
     *
     * @param dirPath 目录路径
     */
    private void cleanEmptyDirectories(Path dirPath) {
        try {
            AtomicInteger deletedCount = new AtomicInteger(0);

            // 从底层目录开始，递归删除空目录
            try (Stream<Path> pathStream = Files.walk(dirPath, Integer.MAX_VALUE)) {
                pathStream
                    .filter(Files::isDirectory)
                    .sorted((p1, p2) -> -p1.compareTo(p2)) // 反向排序，先处理深层目录
                    .forEach(dir -> {
                        if (!dir.equals(dirPath)) { // 不删除根目录
                            try {
                                File[] files = dir.toFile().listFiles();
                                if (files != null && files.length == 0) {
                                    Files.delete(dir);
                                    deletedCount.incrementAndGet();
                                }
                            } catch (Exception e) {
                                log.warn("删除空目录失败: {}, 错误: {}", dir, e.getMessage());
                            }
                        }
                    });
            }

            log.info("已删除{}个空目录", deletedCount.get());
        } catch (Exception e) {
            log.error("清理空目录失败: {}, 错误: {}", dirPath, e.getMessage(), e);
        }
    }
}
