package cn.ccaa.slice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.mybatis.spring.annotation.MapperScan;

import cn.ccaa.slice.config.SlideParserConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * CCA切片工具应用程序入口类
 */
@SpringBootApplication
@EnableScheduling
@EnableConfigurationProperties({SlideParserConfig.class})
@Slf4j
@MapperScan("cn.ccaa.slice.core.mapper")
public class CcaSliceApplication {

    public static void main(String[] args) {
        SpringApplication.run(CcaSliceApplication.class, args);
        log.info("（==========CcaSliceApplication已启动==========）");
    }
}
