package cn.ccaa.slice.web.controller.admin;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.benmanes.caffeine.cache.stats.CacheStats;

import cn.ccaa.slice.core.constant.ApiConstants;

/**
 * 缓存管理控制器
 * 提供缓存监控和管理功能
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(ApiConstants.Base.ADMIN_CACHE)
@Slf4j
public class CacheAdminController {

    /**
     * 缓存类型常量
     */
    public static final class CacheType {
        public static final String FORMAT = "format";
        public static final String THUMBNAIL = "thumbnail";
        public static final String TILE = "tile";
        public static final String METADATA = "metadata";
        public static final String REGION = "region";
        public static final String FILEPATH = "filepath";
        public static final String ALL = "all";
    }

    /**
     * 缓存处理器映射
     */
    private final Map<String, Supplier<String>> cacheHandlers = Map.of(
        CacheType.FORMAT, () -> {
            return "格式缓存已清除";
        },
        CacheType.THUMBNAIL, () -> {
            return "缩略图缓存已清除";
        },
        CacheType.TILE, () -> {
            return "瓦片缓存已清除";
        },
        CacheType.METADATA, () -> {
            return "元数据缓存已清除";
        },
        CacheType.REGION, () -> {
            return "区域缓存已清除";
        },
        CacheType.FILEPATH, () -> {
            return "文件路径缓存已清除";
        },
        CacheType.ALL, () -> {
            return "所有缓存已清除";
        }
    );

    /**
     * 获取所有缓存统计信息
     *
     * @return 缓存统计信息
     */
    @GetMapping(ApiConstants.CacheEndpoints.STATS)
    public ResponseEntity<Map<String, CacheStats>> getCacheStats() {
        Map<String, CacheStats> stats = new HashMap<>();
        return ResponseEntity.ok(stats);
    }

    /**
     * 获取系统缓存情况状态
     *
     * @return 缓存情况状态
     */
    @GetMapping(ApiConstants.CacheEndpoints.STATUS)
    public ResponseEntity<Map<String, Object>> getCacheStatus() {
        Map<String, Object> summary = new HashMap<>();
        Map<String, CacheStats> stats = new HashMap<>();

        // 计算总体命中率
        long totalHits = 0;
        long totalRequests = 0;

        for (Map.Entry<String, CacheStats> entry : stats.entrySet()) {
            CacheStats stat = entry.getValue();
            totalHits += stat.hitCount();
            totalRequests += stat.requestCount();

            // 添加每个缓存的摘要信息
            Map<String, Object> cacheSummary = new HashMap<>();
            cacheSummary.put("hitCount", stat.hitCount());
            cacheSummary.put("missCount", stat.missCount());
            cacheSummary.put("hitRate", stat.hitRate());
            cacheSummary.put("missRate", stat.missRate());
            cacheSummary.put("evictionCount", stat.evictionCount());

            summary.put(entry.getKey(), cacheSummary);
        }

        // 计算总命中率
        double overallHitRate = totalRequests > 0 ? (double) totalHits / totalRequests : 0.0;

        // 添加总体摘要
        Map<String, Object> overall = new HashMap<>();
        overall.put("totalHits", totalHits);
        overall.put("totalRequests", totalRequests);
        overall.put("overallHitRate", overallHitRate);

        summary.put("overall", overall);

        return ResponseEntity.ok(summary);
    }

    /**
     * 清除指定类型的缓存
     *
     * @param type 缓存类型 (format, thumbnail, tile, metadata, region, filepath, all)
     * @return 清除结果
     */
    @PostMapping(ApiConstants.CacheEndpoints.CLEAN)
    public ResponseEntity<Map<String, Object>> clearCache(@RequestParam String type) {
        log.info("清除缓存: type={}", type);

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("type", type.toLowerCase());

        try {
            String lowerType = type.toLowerCase();
            Supplier<String> handler = cacheHandlers.get(lowerType);

            if (handler != null) {
                String message = handler.get();
                result.put("message", message);
                log.info("缓存清除成功: {}", message);
            } else {
                result.put("success", false);
                result.put("message", "不支持的缓存类型: " + type);
                log.warn("不支持的缓存类型: {}", type);
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "清除缓存失败: " + e.getMessage());
            log.error("清除缓存失败: {}", e.getMessage(), e);
        }

        return ResponseEntity.ok(result);
    }
}
