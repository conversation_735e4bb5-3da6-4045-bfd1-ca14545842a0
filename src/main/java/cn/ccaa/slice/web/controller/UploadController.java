package cn.ccaa.slice.web.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.github.f4b6a3.ulid.UlidCreator;

import cn.ccaa.slice.config.TempDirConfig;
import cn.ccaa.slice.core.constant.ApiConstants;
import cn.ccaa.slice.core.constant.ResultCode;
import cn.ccaa.slice.core.exception.BusinessException;
import cn.ccaa.slice.core.model.SliceEntity;
import cn.ccaa.slice.core.result.Result;
import cn.ccaa.slice.core.upload.UploadStatus;
import cn.ccaa.slice.core.upload.UploadTask;
import cn.ccaa.slice.service.SliceService;
import cn.ccaa.slice.service.upload.FileUploadService;
import cn.ccaa.slice.service.upload.SliceUploadService;
import cn.ccaa.slice.service.upload.chunk.ChunkMergeService;
import cn.ccaa.slice.service.upload.chunk.ChunkStatusManager;
import cn.ccaa.slice.service.upload.task.UploadTaskManager;
import cn.ccaa.slice.web.dto.BatchUploadProgressRequestDTO;
import cn.ccaa.slice.web.dto.SimplifiedUploadResponseDTO;
import cn.ccaa.slice.web.dto.UploadRequestDTO;
import cn.ccaa.slice.web.dto.UploadResponseDTO;
import cn.ccaa.slice.service.cache.CacheService;
import cn.ccaa.slice.service.dzi.DziService;
import cn.ccaa.slice.service.upload.SliceAnalysisCallbackService;
import jakarta.validation.Valid;

/**
 * 文件上传控制器
 * 
 * 修改说明：
 * 1. 本地上传场景现在使用主键id作为唯一穿线标识，而不是taskId
 * 2. 允许taskId不唯一，相同文件可多次上传，每次上传创建新记录，但同一上传过程的分片共享一条记录
 * 3. 三种上传场景的穿线标识：
 *    - 本地上传：使用数据库主键id（基于活跃上传会话判断）
 *    - 蚂蚁上传：使用sliceId
 *    - 切片池上传：使用数据库主键id（支持identifier区分同文件多次上传）
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(ApiConstants.Base.UPLOAD)
public class UploadController {

    private static final Logger logger = LoggerFactory.getLogger(UploadController.class);

    /**
     * 支持的切片文件扩展名
     */
    private static final Set<String> SLICE_FILE_EXTENSIONS = Set.of(
            "svs", "ndpi", "tif", "tiff", "kfb", "sdpc", "tron"
    );

    // 添加常量
    private static final int MAX_BATCH_SIZE = ApiConstants.Defaults.DEFAULT_BATCH_SIZE;

    /**
     * 上传进度最大百分比
     * 上传完成后进度为90%，剩余10%用于后端处理（解析缩略图、标签图等）
     */
    private static final int MAX_UPLOAD_PROGRESS_PERCENTAGE = 90;

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private SliceUploadService sliceUploadService;
    
    @Autowired
    private cn.ccaa.slice.config.StorageConfig storageConfig;

    @Autowired
    private SliceService sliceService;
    
    @Autowired
    private TempDirConfig tempDirConfig;
    
    @Autowired
    private ChunkStatusManager chunkStatusManager;
    
    @Autowired
    private ChunkMergeService chunkMergeService;

    @Autowired
    private UploadTaskManager uploadTaskManager;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private DziService dziService;

    @Autowired
    private SliceAnalysisCallbackService sliceAnalysisCallbackService;

    /**
     * 创建上传任务
     *
     * @param request 上传请求
     * @return 任务ID
     */
    @PostMapping(ApiConstants.UploadEndpoints.TASK)
    public Result<Map<String, String>> createUploadTask(@RequestBody @Valid UploadRequestDTO request) {
        logger.info("创建上传任务: {}", request);

        String userId = request.getUserId() != null ? request.getUserId() : "anonymous";

        try {
            // 创建上传任务
            UploadTask task = fileUploadService.createUploadTask(request.getFileName(),
                                                               request.getFileSize(),
                                                               userId);
            
            // 保存到数据库
            saveSliceToDatabase(task, request);
            
            Map<String, String> response = Map.of(
                "sliceId", task.getTaskId(),
                "uploadDir", storageConfig.getFile().getUploadDir(),
                "prefix", "original_"
            );
            return Result.success(response);
        } catch (BusinessException e) {
            logger.warn("创建上传任务失败: {}", e.getMessage());
            return Result.failure(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("创建上传任务发生错误", e);
            return Result.failure(ResultCode.UPLOAD_ERROR, "创建上传任务失败，请稍后重试");
        }
    }

    /**
     * 保存切片信息到数据库
     *
     * @param task 上传任务
     * @param request 上传请求
     */
    private void saveSliceToDatabase(UploadTask task, UploadRequestDTO request) {
        // 构建切片实体，字段严格与表结构一致
        SliceEntity sliceEntity = SliceEntity.builder()
                .id(UlidCreator.getMonotonicUlid().toString())
                .sliceId(task.getTaskId())
                .qpName(request.getFileName())
                .qpSize(request.getFileSize() != null ? request.getFileSize().doubleValue() : null)
                .tempAuth(request.getTempAuth())
                .uploadTime(LocalDateTime.now())
                .qpState("0")
                .crteTime(LocalDateTime.now())
                .lastModifiedTime(LocalDateTime.now())
                // 解析后再补充sliceNo、scanFormat、enlarge、resolution
                .build();
        sliceService.createSlice(sliceEntity);
        logger.info("切片信息已保存到数据库: id={}, sliceId={}, fileName={}", sliceEntity.getId(), task.getTaskId(), request.getFileName());
    }

    /**
     * 分片上传接口，支持大文件分片断点续传
     * 支持三种上传场景：
     * 1. 本地上传：只传taskId，将taskId落库到task_id字段
     * 2. 蚂蚁上传：只传sliceId，按现有业务流程处理
     * 3. 切片池上传：都不传，自动生成sliceId作为穿线标识
     * 
     * @param authorization 请求头，非必填，对接平台生成的token
     * @param totalSize 文件总大小
     * @param filename 文件名称
     * @param sliceId 切片ID，非必填，蚂蚁上传时使用
     * @param taskId 任务ID，非必填，本地上传时使用
     * @param chunkNumber 当前分片序号（从1开始）
     * @param totalChunks 总片数
     * @param currentChunkSize 当前片大小
     * @param file 分片文件
     * @param tempAuth 临时授权token，非必填
     * @param partnerCode 对接产品code，非必填，用于指定存储路径
     * @param identifier 标识符，非必填，用于区分不同的上传实例
     * @param orgId 机构ID，非必填
     * @param ahId 院区ID，非必填
     * @param projId 项目ID，非必填
     * @param sectionId 章节ID，非必填，用于传递到BasicInfo接口
     * @return 上传结果
     */
    @PostMapping(value = "/file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Object uploadFile(
            @RequestHeader(value = "Authorization", required = false) String authorization,
            @RequestParam(value = "totalSize") Long totalSize,
            @RequestParam(value = "filename") String filename,
            @RequestParam(value = "sliceId", required = false) String sliceId,
            @RequestParam(value = "taskId", required = false) String taskId,
            @RequestParam(value = "chunkNumber", required = false) Integer chunkNumber,
            @RequestParam(value = "totalChunks", required = false) Integer totalChunks,
            @RequestParam(value = "currentChunkSize", required = false) Long currentChunkSize,
            @RequestParam(value = "file") MultipartFile file,
            @RequestParam(value = "temp_auth", required = false) String tempAuth,
            @RequestParam(value = "partnerCode", required = false) String partnerCode,
            @RequestParam(value = "identifier", required = false) String identifier,
            @RequestParam(value = "orgId", required = false) String orgId,
            @RequestParam(value = "ahId", required = false) String ahId,
            @RequestParam(value = "projId", required = false) String projId,
            @RequestParam(value = "caseId", required = false) String caseId,
            @RequestParam(value = "sectionId", required = false) String sectionId
    ) {
        // 参数日志 - 添加caseId和sectionId参数记录
        logger.info("文件上传请求: filename={}, totalSize={}, sliceId={}, taskId={}, chunkNumber={}/{}, partnerCode={}, identifier={}, orgId={}, ahId={}, projId={}, caseId={}, sectionId={}", 
                   filename, totalSize, sliceId, taskId, chunkNumber, totalChunks, partnerCode, identifier, orgId, ahId, projId, caseId, sectionId);

        // 基础参数验证
        if (file == null || file.isEmpty()) {
            return Result.failure(ResultCode.PARAM_INVALID, "文件不能为空");
        }

        if (filename == null || filename.trim().isEmpty()) {
            return Result.failure(ResultCode.PARAM_INVALID, "文件名不能为空");
        }

        if (totalSize == null || totalSize <= 0) {
            return Result.failure(ResultCode.PARAM_INVALID, "文件总大小必须大于0");
        }

        // 判断是否为分片上传
        boolean isChunkUpload = chunkNumber != null && totalChunks != null && chunkNumber > 0 && totalChunks > 0;

        // 根据传入参数判断上传场景
        UploadScenario scenario = determineUploadScenario(sliceId, taskId);
        
        // 确定穿线标识（本地上传场景需要特殊处理）
        String threadingId;
        if (scenario == UploadScenario.LOCAL_UPLOAD) {
            // 修改：使用identifier来判断是否是新的文件上传，而不是根据chunkNumber
            threadingId = getOrCreateLocalUploadThreadingId(taskId, filename, totalSize, authorization, identifier, chunkNumber, orgId, ahId, projId, partnerCode, caseId, sectionId);
        } else {
            // 其他场景使用原有逻辑
            threadingId = determineThreadingId(scenario, sliceId, taskId, filename, totalSize, authorization, orgId, ahId, projId, partnerCode, caseId, sectionId, identifier);
        }
        
        logger.info("上传场景识别: scenario={}, threadingId={}, identifier={}", scenario, threadingId, identifier);
        
        try {
            if (isChunkUpload) {
                            // 确定分片检查使用的键（切片池上传使用identifier，其他使用threadingId）
            String chunkCheckKey = (scenario == UploadScenario.SLICE_POOL_UPLOAD) ? 
                ((identifier != null && !identifier.trim().isEmpty()) ? identifier.trim() : "default") : threadingId;
            
            // 检查之前是否已上传了这个分片
            Set<Integer> uploadedChunks = (scenario == UploadScenario.SLICE_POOL_UPLOAD) ? 
                chunkStatusManager.getUploadedChunksByIdentifier(chunkCheckKey) : 
                chunkStatusManager.getUploadedChunks(threadingId);
                
            if (uploadedChunks.contains(chunkNumber)) {
                logger.info("分片已存在，跳过上传: scenario={}, checkKey={}, chunkNumber={}", scenario, chunkCheckKey, chunkNumber);
                // 修改：使用简化的响应格式
                return SimplifiedUploadResponseDTO.success(chunkNumber);
            }
                
                // 处理分片上传
                return handleChunkUploadV4(threadingId, file, filename, totalSize, chunkNumber, totalChunks, 
                                         currentChunkSize, scenario, sliceId, taskId, authorization, partnerCode, orgId, ahId, projId, caseId, sectionId, identifier);
            } else {
                // 非分片完整上传，兼容老逻辑
                logger.info("非分片上传方式: threadingId={}, filename={}, fileSize={}, scenario={}", 
                           threadingId, filename, file.getSize(), scenario);
                
                // 数据库记录已在确定穿线标识时创建，无需重复创建
                
                // 调用原有的上传服务
                return sliceUploadService.asyncUploadAndAnalyzeSlice(file, threadingId, partnerCode);
            }
        } catch (BusinessException e) {
            logger.warn("文件上传业务异常: {}", e.getMessage());
            return Result.failure(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("文件上传失败", e);
            return Result.failure(ResultCode.UPLOAD_ERROR, "文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传场景枚举
     */
    private enum UploadScenario {
        LOCAL_UPLOAD,    // 本地上传：只传taskId
        ANT_UPLOAD,      // 蚂蚁上传：只传sliceId  
        SLICE_POOL_UPLOAD // 切片池上传：都不传
    }

    /**
     * 根据传入参数判断上传场景
     */
    private UploadScenario determineUploadScenario(String sliceId, String taskId) {
        boolean hasSliceId = sliceId != null && !sliceId.trim().isEmpty();
        boolean hasTaskId = taskId != null && !taskId.trim().isEmpty();
        
        if (hasTaskId && !hasSliceId) {
            return UploadScenario.LOCAL_UPLOAD;
        } else if (hasSliceId && !hasTaskId) {
            return UploadScenario.ANT_UPLOAD;
        } else if (!hasSliceId && !hasTaskId) {
            return UploadScenario.SLICE_POOL_UPLOAD;
        } else {
            // 如果两个都传了，优先按蚂蚁上传处理
            logger.warn("同时传入了sliceId和taskId，优先按蚂蚁上传处理: sliceId={}, taskId={}", sliceId, taskId);
            return UploadScenario.ANT_UPLOAD;
        }
    }

    /**
     * 根据上传场景确定穿线标识
     */
    private String determineThreadingId(UploadScenario scenario, String sliceId, String taskId, String filename, long fileSize, String authorization, String orgId, String ahId, String projId, String partnerCode, String caseId, String sectionId, String identifier) {
        switch (scenario) {
            case LOCAL_UPLOAD:
                // 本地上传：每个新的上传过程创建唯一的进程ID和数据库记录
                return getOrCreateDatabaseRecordForLocalUpload(taskId, filename, fileSize, authorization, orgId, ahId, projId, partnerCode, caseId, sectionId);
            case ANT_UPLOAD:
                // 蚂蚁上传：使用sliceId作为穿线标识
                return sliceId;
            case SLICE_POOL_UPLOAD:
                // 切片池上传：创建数据库记录，使用生成的主键id作为穿线标识，支持identifier区分同文件多次上传
                return getOrCreateDatabaseRecordForPoolUpload(filename, fileSize, authorization, orgId, ahId, projId, partnerCode, caseId, sectionId, identifier);
            default:
                throw new IllegalArgumentException("未知的上传场景: " + scenario);
        }
    }

    /**
     * 建立本地上传会话（Redis映射）
     * 将文件特征与threadingId建立映射关系，15分钟过期
     */
    private void establishLocalUploadSession(String taskId, String filename, long fileSize, String threadingId, String identifier) {
        try {
            // 生成会话键：local_session:taskId:filename:fileSize:identifier
            String sessionKey = String.format("local_session:%s:%s:%d:%s", taskId, filename, fileSize, identifier);
            String startTimeKey = sessionKey + ":startTime";
            
            // 直接存储threadingId字符串，15分钟过期
            stringRedisTemplate.opsForValue().set(sessionKey, threadingId, Duration.ofMinutes(15));
            stringRedisTemplate.opsForValue().set(startTimeKey, String.valueOf(System.currentTimeMillis()), Duration.ofMinutes(15));
            
            logger.info("建立本地上传会话: sessionKey={}, threadingId={}, 过期时间=15分钟", sessionKey, threadingId);
            
        } catch (Exception e) {
            logger.error("建立本地上传会话失败: taskId={}, filename={}, threadingId={}, identifier={}, error={}", 
                        taskId, filename, threadingId, identifier, e.getMessage(), e);
        }
    }

    /**
     * 获取或创建本地上传的threadingId
     * 基于identifier来判断是否是新的文件上传，而不是基于chunkNumber
     * 修复：使用Redis原子操作解决并发竞争问题
     * 
     * @param taskId 任务ID
     * @param filename 文件名
     * @param fileSize 文件大小
     * @param authorization 授权信息
     * @param identifier 上传实例标识符
     * @param chunkNumber 分片序号
     * @param sectionId 章节ID
     * @return threadingId
     */
    private String getOrCreateLocalUploadThreadingId(String taskId, String filename, long fileSize, 
                                                   String authorization, String identifier, Integer chunkNumber, String orgId, String ahId, String projId, String partnerCode, String caseId, String sectionId) {
        String sessionKey = String.format("local_session:%s:%s:%d:%s", taskId, filename, fileSize, identifier);
        
        // 使用Redis分布式锁解决并发创建问题
        String lockKey = sessionKey + ":lock";
        String lockValue = UlidCreator.getMonotonicUlid().toString(); // 唯一锁值
        
        try {
            // 尝试获取分布式锁，防止并发创建
            Boolean lockAcquired = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, Duration.ofSeconds(10));
            
            if (Boolean.TRUE.equals(lockAcquired)) {
                try {
                    // 获得锁后，再次检查会话是否已存在
                    String sessionValue = stringRedisTemplate.opsForValue().get(sessionKey);
                    
                    if (sessionValue != null) {
                        String existingThreadingId = extractThreadingIdFromSession(sessionValue);
                        if (existingThreadingId != null) {
                            Set<Integer> uploadedChunks = chunkStatusManager.getUploadedChunks(existingThreadingId);
                            String startTimeKey = sessionKey + ":startTime";
                            String startTimeStr = stringRedisTemplate.opsForValue().get(startTimeKey);
                            long sessionAge = startTimeStr != null ? 
                                System.currentTimeMillis() - Long.parseLong(startTimeStr) : 0;
                            
                            logger.info("本地上传在锁内找到已存在会话: sessionKey={}, threadingId={}, 已上传分片数={}, sessionAge={}ms, chunkNumber={}", 
                                       sessionKey, existingThreadingId, uploadedChunks.size(), sessionAge, chunkNumber);
                            return existingThreadingId;
                        }
                    }
                    
                    // 会话不存在，创建新的上传进程和会话
                    String newThreadingId = createNewLocalUploadProcess(taskId, filename, fileSize, authorization, orgId, ahId, projId, partnerCode, caseId, sectionId);
                    establishLocalUploadSession(taskId, filename, fileSize, newThreadingId, identifier);
                    
                    logger.info("本地上传在锁内创建新会话: sessionKey={}, threadingId={}, chunkNumber={}, identifier={}", 
                               sessionKey, newThreadingId, chunkNumber, identifier);
                    
                    return newThreadingId;
                    
                } finally {
                    // 释放锁（只有锁的拥有者才能释放）
                    String currentLockValue = stringRedisTemplate.opsForValue().get(lockKey);
                    if (lockValue.equals(currentLockValue)) {
                        stringRedisTemplate.delete(lockKey);
                    }
                }
            } else {
                // 未能获得锁，等待并重试
                logger.info("本地上传未能获得锁，等待其他线程创建会话: sessionKey={}, chunkNumber={}", sessionKey, chunkNumber);
                
                // 等待最多3秒，检查会话是否被其他线程创建
                for (int attempt = 1; attempt <= 15; attempt++) {
                    try {
                        Thread.sleep(200); // 每次等待200ms
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                    
                    String sessionValue = stringRedisTemplate.opsForValue().get(sessionKey);
                    if (sessionValue != null) {
                        String existingThreadingId = extractThreadingIdFromSession(sessionValue);
                        if (existingThreadingId != null) {
                            Set<Integer> uploadedChunks = chunkStatusManager.getUploadedChunks(existingThreadingId);
                            String startTimeKey = sessionKey + ":startTime";
                            String startTimeStr = stringRedisTemplate.opsForValue().get(startTimeKey);
                            long sessionAge = startTimeStr != null ? 
                                System.currentTimeMillis() - Long.parseLong(startTimeStr) : 0;
                            
                            logger.info("本地上传等待后找到会话: sessionKey={}, threadingId={}, 已上传分片数={}, sessionAge={}ms, chunkNumber={}, attempt={}", 
                                       sessionKey, existingThreadingId, uploadedChunks.size(), sessionAge, chunkNumber, attempt);
                            return existingThreadingId;
                        }
                    }
                }
                
                // 等待超时，抛出异常
                logger.error("本地上传等待会话超时: sessionKey={}, chunkNumber={}", sessionKey, chunkNumber);
                throw new BusinessException(ResultCode.UPLOAD_ERROR, 
                    String.format("上传会话创建超时，请重试。taskId=%s, filename=%s, chunkNumber=%s", taskId, filename, chunkNumber));
            }
            
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            logger.error("本地上传获取threadingId时发生异常: sessionKey={}, chunkNumber={}, error={}", sessionKey, chunkNumber, e.getMessage(), e);
            throw new BusinessException(ResultCode.UPLOAD_ERROR, "上传处理异常: " + e.getMessage());
        }
    }

    /**
     * 查找活跃的本地上传会话
     * 通过Redis会话管理查找对应的上传任务
     * 修复：增强会话查找机制，防止分片分散到多个threadingId中
     */
    private String findOrWaitForActiveSession(String taskId, String filename, long fileSize, String authorization, String identifier) {
        // 最多重试5次，每次等待300ms（增加重试次数和等待时间）
        int maxRetries = 5;
        int retryDelayMs = 300;
        
        String sessionKey = String.format("local_session:%s:%s:%d:%s", taskId, filename, fileSize, identifier);
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            String sessionValue = stringRedisTemplate.opsForValue().get(sessionKey);
            
            if (sessionValue != null) {
                String actualThreadingId = extractThreadingIdFromSession(sessionValue);
                
                if (actualThreadingId != null) {
                    // 验证会话的有效性
                    Set<Integer> uploadedChunks = chunkStatusManager.getUploadedChunks(actualThreadingId);
                    String startTimeKey = sessionKey + ":startTime";
                    String startTimeStr = stringRedisTemplate.opsForValue().get(startTimeKey);
                    long sessionAge = startTimeStr != null ? 
                        System.currentTimeMillis() - Long.parseLong(startTimeStr) : 0;
                    
                    logger.info("找到活跃的本地上传会话: sessionKey={}, threadingId={}, 已上传分片数={}, sessionAge={}ms", 
                               sessionKey, actualThreadingId, uploadedChunks.size(), sessionAge);
                    logger.info("本地上传非第一分片找到活跃会话: chunkNumber={}, threadingId={}", attempt, actualThreadingId);
                    return actualThreadingId;
                }
            }
            
            // 在重试期间，检查是否有其他分片正在创建会话（通过查找相关的Redis键）
            if (attempt < maxRetries) {
                logger.info("本地上传非第一分片未找到活跃会话，等待{}ms后重试: attempt={}/{}", 
                           retryDelayMs, attempt, maxRetries);
                
                // 检查相同taskId的其他会话键（可能有不同的identifier）
                String searchPattern = String.format("local_session:%s:%s:%d:*", taskId, filename, fileSize);
                Set<String> existingKeys = stringRedisTemplate.keys(searchPattern);
                
                if (existingKeys != null && !existingKeys.isEmpty()) {
                    logger.info("发现相同文件的其他会话键: count={}, pattern={}", existingKeys.size(), searchPattern);
                    
                    // 尝试使用第一个找到的会话
                    for (String key : existingKeys) {
                        String existingSessionValue = stringRedisTemplate.opsForValue().get(key);
                        if (existingSessionValue != null) {
                            String existingThreadingId = extractThreadingIdFromSession(existingSessionValue);
                            if (existingThreadingId != null) {
                                logger.info("复用已存在的会话: originalKey={}, threadingId={}", key, existingThreadingId);
                                
                                // 更新当前会话键指向同一个threadingId
                                stringRedisTemplate.opsForValue().set(sessionKey, existingThreadingId, Duration.ofMinutes(15));
                                stringRedisTemplate.opsForValue().set(sessionKey + ":startTime", 
                                    String.valueOf(System.currentTimeMillis()), Duration.ofMinutes(15));
                                
                                return existingThreadingId;
                            }
                        }
                    }
                }
                
                try {
                    Thread.sleep(retryDelayMs);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        // 重试后仍未找到会话，抛出异常而不是创建新的threadingId
        // 这样可以避免分片分散到多个threadingId的问题
        logger.error("本地上传非第一分片经过{}次重试后仍未找到活跃会话，拒绝创建新threadingId: taskId={}, filename={}, identifier={}", 
                    maxRetries, taskId, filename, identifier);
        
        throw new BusinessException(ResultCode.UPLOAD_ERROR, 
            String.format("上传会话不存在，请确保第一个分片已成功上传。taskId=%s, filename=%s", taskId, filename));
    }
    
    /**
     * 从会话值中提取threadingId（兼容JSON格式和字符串格式）
     */
    private String extractThreadingIdFromSession(String sessionValue) {
        if (sessionValue == null || sessionValue.trim().isEmpty()) {
            return null;
        }
        
        // 如果是JSON格式，解析提取threadingId
        if (sessionValue.trim().startsWith("{")) {
            try {
                com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
                @SuppressWarnings("unchecked")
                Map<String, Object> sessionData = objectMapper.readValue(sessionValue, Map.class);
                return (String) sessionData.get("threadingId");
            } catch (Exception e) {
                logger.warn("解析JSON格式会话数据失败: sessionValue={}, error={}", sessionValue, e.getMessage());
                return null;
            }
        } else {
            // 直接返回字符串格式的threadingId
            return sessionValue;
        }
    }

    /**
     * 检查或创建本地上传进程（每次都创建新的threadingId，不基于文件特征复用）
     * 修改：完全移除基于文件特征的会话复用，每次上传都是独立的
     */
    private String getOrCheckLocalUploadProcess(String taskId, String filename, long fileSize, String authorization, boolean isChunkUpload, Integer chunkNumber, String orgId, String ahId, String projId, String partnerCode, String caseId, String sectionId) {
        // 每次都创建新的上传进程，不再基于文件特征进行复用判断
        String newThreadingId = createNewLocalUploadProcess(taskId, filename, fileSize, authorization, orgId, ahId, projId, partnerCode, caseId, sectionId);
        logger.info("本地上传场景：为每次上传创建独立进程，threadingId={}, taskId={}, filename={}, caseId={}, sectionId={}", 
                   newThreadingId, taskId, filename, caseId, sectionId);
        return newThreadingId;
    }

    /**
     * 创建新的本地上传进程和数据库记录
     */
    private String createNewLocalUploadProcess(String taskId, String filename, long fileSize, String authorization, String orgId, String ahId, String projId, String partnerCode, String caseId, String sectionId) {
        // 生成唯一的上传进程ID
        String uploadProcessId = UlidCreator.getMonotonicUlid().toString();
        
        SliceEntity entity = SliceEntity.builder()
                .id(uploadProcessId)  // 使用上传进程ID作为主键
                .taskId(taskId)       // 存储传入的taskId，允许重复
                .sliceId(null)        // 本地上传不存储sliceId
                .qpSize((double) fileSize)
                .tempAuth(authorization)
                .qpName(filename)
                .uploadTime(LocalDateTime.now())
                .qpState("0") // 初始状态：处理中
                .crteTime(LocalDateTime.now())
                .lastModifiedTime(LocalDateTime.now())
                .orgId(orgId)
                .ahId(ahId)
                .projId(projId)
                .partnerCode(partnerCode)
                // sectionId不再存储到数据库，直接传递给BasicInfo接口
                .build();

        sliceService.createSlice(entity);
        
        logger.info("本地上传场景：创建新上传进程，主键id={}, taskId={}, filename={}, fileSize={}, sectionId={}", 
                   uploadProcessId, taskId, filename, fileSize, sectionId);
        return uploadProcessId;
    }

    /**
     * 为本地上传获取或创建数据库记录（基于当前上传进程判断，不基于文件特征）
     * 修改：通过生成唯一的上传进程ID，每个新的上传过程创建新记录
     */
    private String getOrCreateDatabaseRecordForLocalUpload(String taskId, String filename, long fileSize, String authorization, String orgId, String ahId, String projId, String partnerCode, String caseId, String sectionId) {
        // 这个方法现在只用于非本地上传场景，直接创建新记录
        return createNewLocalUploadProcess(taskId, filename, fileSize, authorization, orgId, ahId, projId, partnerCode, caseId, sectionId);
    }

    /**
     * 为切片池上传获取或创建数据库记录（使用identifier和分布式锁确保并发安全）
     */
    private String getOrCreateDatabaseRecordForPoolUpload(String filename, long fileSize, String authorization, String orgId, String ahId, String projId, String partnerCode, String caseId, String sectionId, String identifier) {
        // 如果identifier为空，使用默认值避免空指针
        String actualIdentifier = (identifier != null && !identifier.trim().isEmpty()) ? identifier.trim() : "default";
        
        // 使用分布式锁确保并发安全
        String lockValue = UlidCreator.getMonotonicUlid().toString();
        
        try {
            // 尝试获取分布式锁，10秒超时
            if (chunkStatusManager.acquireLock(actualIdentifier, lockValue, 10)) {
                try {
                    // 检查Redis中是否已有相同identifier的上传任务
                    String redisKey = "pool_upload:" + actualIdentifier;
                    String existingId = stringRedisTemplate.opsForValue().get(redisKey);
                    
                    if (existingId != null && !existingId.isEmpty()) {
                        // 检查数据库中是否确实存在该记录
                        SliceEntity existingEntity = sliceService.getById(existingId);
                        if (existingEntity != null) {
                            logger.info("切片池上传场景：找到已存在的数据库记录，主键id={}, filename={}, fileSize={}, identifier={}", 
                                       existingId, filename, fileSize, actualIdentifier);
                            return existingId;
                        } else {
                            // Redis中有记录但数据库中没有，清理Redis记录
                            stringRedisTemplate.delete(redisKey);
                            logger.warn("切片池上传场景：Redis中存在记录但数据库中不存在，已清理Redis记录: {}", redisKey);
                        }
                    }
                    
                    // 创建新的数据库记录
                    String primaryId = UlidCreator.getMonotonicUlid().toString();
                    SliceEntity entity = SliceEntity.builder()
                            .id(primaryId)
                            .taskId(null)    // 切片池上传不存储taskId
                            .sliceId(null)   // 切片池上传不存储sliceId
                            .qpSize((double) fileSize)
                            .tempAuth(authorization)
                            .qpName(filename)
                            .uploadTime(LocalDateTime.now())
                            .qpState("0") // 初始状态：处理中
                            .crteTime(LocalDateTime.now())
                            .lastModifiedTime(LocalDateTime.now())
                            .orgId(orgId)
                            .ahId(ahId)
                            .projId(projId)
                            .partnerCode(partnerCode)
                            // sectionId不再存储到数据库，直接传递给BasicInfo接口
                            .build();

                    sliceService.createSlice(entity);
                    
                    // 在Redis中记录这个映射关系，设置过期时间为24小时
                    stringRedisTemplate.opsForValue().set(redisKey, primaryId, 24, TimeUnit.HOURS);
                    
                    logger.info("切片池上传场景：创建数据库记录，主键id={}, taskId=null, sliceId=null, filename={}, fileSize={}, identifier={}, orgId={}, ahId={}, projId={}, partnerCode={}", 
                               primaryId, filename, fileSize, actualIdentifier, orgId, ahId, projId, partnerCode);
                    return primaryId; // 返回主键id作为穿线标识
                    
                } finally {
                    // 释放分布式锁
                    chunkStatusManager.releaseLock(actualIdentifier, lockValue);
                }
            } else {
                // 获取锁失败，可能有其他线程正在处理，等待一段时间后重试
                logger.warn("获取分布式锁失败，等待其他线程处理完成: identifier={}", actualIdentifier);
                Thread.sleep(100); // 等待100ms
                
                // 重试查询是否已有记录
                String redisKey = "pool_upload:" + actualIdentifier;
                String existingId = stringRedisTemplate.opsForValue().get(redisKey);
                if (existingId != null && !existingId.isEmpty()) {
                    SliceEntity existingEntity = sliceService.getById(existingId);
                    if (existingEntity != null) {
                        logger.info("切片池上传场景：重试后找到已存在的数据库记录，主键id={}, identifier={}", existingId, actualIdentifier);
                        return existingId;
                    }
                }
                
                // 如果还是没有记录，抛出异常
                throw new BusinessException("获取分布式锁失败，请稍后重试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BusinessException("处理被中断，请稍后重试");
        } catch (Exception e) {
            logger.error("切片池上传创建数据库记录失败: identifier={}, error={}", actualIdentifier, e.getMessage(), e);
            throw new BusinessException("创建上传记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取已上传的分片信息（支持identifier查询）
     * 修改：适配本地上传场景使用主键id作为穿线标识，支持identifier查询
     *
     * @param taskId 任务ID（对于本地上传场景，这里实际是主键id；也可能是identifier）
     * @return 已上传的分片信息
     */
    @GetMapping(ApiConstants.UploadEndpoints.UPLOADED_CHUNKS + "/{taskId}")
    public Result<Map<String, Object>> getUploadedChunks(@PathVariable String taskId, 
                                                         @RequestParam(value = "identifier", required = false) String identifier) {
        logger.info("获取已上传分片信息: taskId={}, identifier={}", taskId, identifier);
        
        // 优先尝试通过identifier查询（切片池上传场景）
        Map<String, Object> taskInfo = Collections.emptyMap();
        Set<Integer> uploadedChunks = Collections.emptySet();
        
        if (identifier != null && !identifier.trim().isEmpty()) {
            // 使用identifier查询
            String actualIdentifier = identifier.trim();
            taskInfo = chunkStatusManager.getTaskInfoByIdentifier(actualIdentifier);
            uploadedChunks = chunkStatusManager.getUploadedChunksByIdentifier(actualIdentifier);
            logger.debug("通过identifier查询: identifier={}, taskInfo={}, uploadedChunks={}", 
                        actualIdentifier, taskInfo, uploadedChunks.size());
        }
        
        // 如果identifier查询失败，尝试通过taskId查询（本地/蚂蚁上传场景）
        if (taskInfo.isEmpty()) {
            taskInfo = chunkStatusManager.getTaskInfo(taskId);
            uploadedChunks = chunkStatusManager.getUploadedChunks(taskId);
            logger.debug("通过taskId查询: taskId={}, taskInfo={}, uploadedChunks={}", 
                        taskId, taskInfo, uploadedChunks.size());
        }
        
        if (taskInfo.isEmpty()) {
            // 如果Redis中没有找到，检查数据库
            try {
                // 修改：先尝试通过sliceId查询（蚂蚁上传场景）
                SliceEntity sliceEntity = sliceService.getBySliceId(taskId);
                
                // 如果通过sliceId没找到，再尝试通过主键id查询（本地上传场景）
                if (sliceEntity == null) {
                    sliceEntity = sliceService.getById(taskId);
                }
                
                if (sliceEntity != null) {
                    Map<String, Object> responseData = new HashMap<>();
                    responseData.put("taskId", taskId);
                    responseData.put("status", "COMPLETED");
                    responseData.put("totalChunks", 1);
                    responseData.put("uploadedChunks", Set.of(1));
                    responseData.put("filename", sliceEntity.getQpName());
                    responseData.put("totalSize", sliceEntity.getQpSize());
                    responseData.put("uploadedSize", sliceEntity.getQpSize());
                    responseData.put("isComplete", true);
                    responseData.put("sliceInfo", Map.of(
                        "qpName", sliceEntity.getQpName() != null ? sliceEntity.getQpName() : "",
                        "qpSize", sliceEntity.getQpSize() != null ? sliceEntity.getQpSize() : 0,
                        "qpState", sliceEntity.getQpState() != null ? sliceEntity.getQpState() : "0"
                    ));
                    logger.info("从数据库获取切片信息成功: taskId={}, filename={}, fileSize={}", 
                               taskId, sliceEntity.getQpName(), sliceEntity.getQpSize());
                    return Result.success(responseData);
                } else {
                    return Result.failure(ResultCode.TASK_NOT_FOUND, "上传任务不存在或已过期");
                }
            } catch (Exception e) {
                logger.warn("获取切片信息失败: taskId={}, error={}", taskId, e.getMessage());
                return Result.failure(ResultCode.SYSTEM_ERROR, "获取切片信息失败: " + e.getMessage());
            }
        }
        
        // 如果还没有uploadedChunks，再次尝试获取
        if (uploadedChunks.isEmpty()) {
            uploadedChunks = chunkStatusManager.getUploadedChunks(taskId);
        }
        
        // 从任务信息中获取总分片数和文件大小
        int totalChunks = Integer.parseInt(String.valueOf(taskInfo.getOrDefault("totalChunks", "0")));
        long totalSize = Long.parseLong(String.valueOf(taskInfo.getOrDefault("totalSize", "0")));
        long uploadedSize = Long.parseLong(String.valueOf(taskInfo.getOrDefault("uploadedSize", "0")));
        String status = String.valueOf(taskInfo.getOrDefault("status", "UNKNOWN"));
        String filename = String.valueOf(taskInfo.getOrDefault("filename", "unknown"));
        
        // 计算进度百分比
        int progress = totalSize > 0 ? (int)((uploadedSize * 100.0) / totalSize) : 0;
        
        // 构建响应
        Map<String, Object> response = new HashMap<>();
        response.put("taskId", taskId);
        response.put("totalChunks", totalChunks);
        response.put("uploadedChunks", uploadedChunks);
        response.put("remainingChunks", totalChunks - uploadedChunks.size());
        response.put("filename", filename);
        response.put("totalSize", totalSize);
        response.put("uploadedSize", uploadedSize);
        response.put("progress", progress);
        response.put("status", status);
        response.put("isComplete", "COMPLETED".equals(status));
        
        // 可选：添加已上传的分片的详细信息（例如每个分片的大小）
        List<Map<String, Object>> chunkDetails = new ArrayList<>();
        if (!uploadedChunks.isEmpty()) {
            for (Integer chunkNumber : uploadedChunks) {
                Map<String, Object> chunkInfo = new HashMap<>();
                chunkInfo.put("chunkNumber", chunkNumber);
                chunkInfo.put("uploaded", true);
                chunkDetails.add(chunkInfo);
            }
            response.put("chunkDetails", chunkDetails);
        }
        
        return Result.success(response);
    }

    /**
     * 查询上传进度（仅支持蚂蚁上传场景的sliceId查询）
     */
    @GetMapping("/progress/{sliceId}")
    public Result<Map<String, Object>> getUploadProgress(@PathVariable String sliceId) {
        try {
            logger.info("查询上传进度: sliceId={}", sliceId);
            
            // 只支持蚂蚁上传场景的Redis进度查询
            Map<String, Object> taskInfo = chunkStatusManager.getTaskInfo(sliceId);
            if (taskInfo.isEmpty()) {
                return Result.failure(ResultCode.TASK_NOT_FOUND, "未找到上传任务或任务已完成");
            }
            
            Set<Integer> uploadedChunks = chunkStatusManager.getUploadedChunks(sliceId);
            int totalChunks = (Integer) taskInfo.get("totalChunks");
            long totalSize =  ((Integer) taskInfo.get("totalSize")).longValue();
            
            // 计算已上传的总大小（简化版本，基于分片数量估算）
            long uploadedSize = uploadedChunks.size() * (totalSize / totalChunks);
            
            // 计算进度百分比
            double progress = totalSize > 0 ? (double) uploadedSize / totalSize * 100 : 0;
            
            Map<String, Object> result = new HashMap<>();
            result.put("sliceId", sliceId);
            result.put("uploadedChunks", uploadedChunks.size());
            result.put("totalChunks", totalChunks);
            result.put("uploadedSize", uploadedSize);
            result.put("totalSize", totalSize);
            result.put("progress", Math.round(progress * 100.0) / 100.0); // 保留两位小数
            result.put("isComplete", uploadedChunks.size() >= totalChunks);
            
            return Result.success(result);
            
        } catch (Exception e) {
            logger.error("查询上传进度失败: sliceId={}, error={}", sliceId, e.getMessage(), e);
            return Result.failure(ResultCode.PARAM_INVALID, "查询进度失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取上传进度
     * 通过已上传片数/总片数计算进度百分比
     *
     * @param request 批量查询请求，包含sliceIds列表
     * @return 切片ID到进度百分比的映射
     */
    @PostMapping(ApiConstants.UploadEndpoints.PROGRESS_BATCH)
    public Result<Map<String, Integer>> batchGetUploadProgress(@RequestBody @Valid BatchUploadProgressRequestDTO request) {
        logger.info("批量获取上传进度: sliceIds={}", request.getSliceIds());
        
        try {
            // 参数验证
            List<String> sliceIds = request.getSliceIds();
            if (sliceIds == null || sliceIds.isEmpty()) {
                return Result.failure(ResultCode.PARAM_INVALID, "切片ID列表不能为空");
            }
            
            if (sliceIds.size() > MAX_BATCH_SIZE) {
                return Result.failure(ResultCode.PARAM_INVALID, 
                    String.format("批量查询最多支持%d个切片ID，当前请求%d个", MAX_BATCH_SIZE, sliceIds.size()));
            }
            
            // 去重处理
            List<String> uniqueSliceIds = sliceIds.stream().distinct().collect(java.util.stream.Collectors.toList());
            if (uniqueSliceIds.size() != sliceIds.size()) {
                logger.warn("批量查询请求中存在重复的切片ID，原始数量: {}, 去重后数量: {}", sliceIds.size(), uniqueSliceIds.size());
            }
            
            // 调用ChunkStatusManager进行批量查询
            Map<String, Integer> progressMap = chunkStatusManager.batchGetUploadProgress(uniqueSliceIds);
            
            logger.info("批量获取上传进度完成: 请求{}个切片，返回{}个结果", uniqueSliceIds.size(), progressMap.size());
            return Result.success(progressMap);
            
        } catch (Exception e) {
            logger.error("批量获取上传进度失败: error={}", e.getMessage(), e);
            return Result.failure(ResultCode.SYSTEM_ERROR, "批量获取上传进度失败，请稍后重试");
        }
    }
    
    /**
     * 构建分片上传的响应DTO
     */
    private UploadResponseDTO buildChunkUploadResponseDTO(String taskId, Map<String, Object> taskInfo) {
        return UploadResponseDTO.builder()
                .taskId(taskId)
                .status(UploadStatus.UPLOADING.toString())
                .originalFileName((String) taskInfo.get("fileName"))
                .totalSize((Long) taskInfo.get("totalSize"))
                .uploadedSize((Long) taskInfo.get("uploadedSize"))
                .progress((Integer) taskInfo.get("progressPercentage"))
                .uploadDir(storageConfig.getFile().getUploadDir())
                .build();
    }

    /**
     * 处理业务异常
     *
     * @param e 业务异常
     * @return 处理结果
     */
    private Result<?> handleBusinessException(BusinessException e) {
        String errorMsg = e.getMessage();
        logger.warn("文件上传业务逻辑错误: {}", errorMsg);

        // 针对不同错误类型返回错误信息
        if (errorMsg.contains("正在上传中")) {
            return Result.failure(ResultCode.UPLOAD_ERROR, "该文件正在上传中，请勿重复提交");
        } else if (errorMsg.contains("已上传完成")) {
            return Result.failure(ResultCode.UPLOAD_ERROR, "该文件已上传完成，请勿重复上传");
        } else if (errorMsg.contains("上传任务不存在")) {
            return Result.failure(ResultCode.TASK_NOT_FOUND, "上传任务不存在，请先创建上传任务");
        } else {
            // 对于文件名已存在等其他错误，保留原始错误消息
            return Result.failure(e.getCode(), errorMsg);
        }
    }

    /**
     * 判断文件是否为切片文件
     *
     * @param fileName 文件名
     * @return 如果是切片文件返回true，否则返回false
     */
    private boolean isSliceFile(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }

        String extension = getFileExtension(fileName);
        return SLICE_FILE_EXTENSIONS.contains(extension);
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex > 0) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 将UploadTask转换为UploadResponseDTO
     *
     * @param task 上传任务
     * @return 响应DTO
     */
    private UploadResponseDTO convertToResponseDTO(UploadTask task) {
        try {
            return UploadResponseDTO.builder()
                    .taskId(task.getTaskId())
                    .status(task.getStatus().toString())
                    .originalFileName(task.getFileName())
                    .totalSize(task.getTotalSize())
                    .uploadedSize(task.getUploadedSize())
                    .uploadDir(storageConfig.getFile().getUploadDir())
                    .errorMessage(task.getErrorMessage())
                    .progress(task.getUploadPercentage() != null ? task.getUploadPercentage().intValue() : 0)
                    .completed(task.isCompleted())
                    .build();
        } catch (Exception e) {
            logger.error("转换响应DTO时发生错误: taskId={}", task.getTaskId(), e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "响应数据转换失败");
        }
    }

    /**
     * 手动触发清理过期的临时文件
     * 管理员接口，用于维护磁盘空间
     * 
     * @return 清理结果
     */
    @PostMapping("/cleanup-temp-files")
    public Result<Map<String, Object>> cleanupTempFiles() {
        logger.info("手动触发清理临时文件");
        Path tempDir = tempDirConfig.getTempDir();
        File[] taskDirs = tempDir.toFile().listFiles(File::isDirectory);
        
        if (taskDirs == null || taskDirs.length == 0) {
            return Result.success(Map.of(
                "message", "没有找到需要清理的临时目录",
                "cleanedCount", 0
            ));
        }
        
        int deleted = 0;
        long freedSpace = 0;
        List<String> cleanedDirs = new ArrayList<>();
        
        long now = System.currentTimeMillis();
        // 默认清理24小时前的文件
        long retentionPeriod = 24 * 60 * 60 * 1000; 
        
        for (File dir : taskDirs) {
            try {
                // 检查目录最后修改时间
                long lastModified = dir.lastModified();
                if (now - lastModified > retentionPeriod) {
                    // 计算目录大小
                    long dirSize = calculateDirectorySize(dir);
                    
                    // 递归删除目录
                    org.apache.commons.io.FileUtils.deleteDirectory(dir);
                    
                    deleted++;
                    freedSpace += dirSize;
                    cleanedDirs.add(dir.getName());
                    logger.info("已清理临时目录: {}, 大小: {} 字节", dir.getAbsolutePath(), dirSize);
                }
            } catch (Exception e) {
                logger.warn("清理临时目录失败: {}, 错误: {}", dir.getAbsolutePath(), e.getMessage());
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", deleted > 0 ? "成功清理临时文件" : "没有可清理的临时文件");
        result.put("cleanedCount", deleted);
        result.put("freedSpaceBytes", freedSpace);
        result.put("freedSpaceMB", freedSpace / (1024 * 1024));
        result.put("cleanedDirectories", cleanedDirs);
        
        logger.info("临时文件清理完成，共清理{}个目录，释放{}MB空间", deleted, freedSpace / (1024 * 1024));
        
        return Result.success(result);
    }
    
    /**
     * 计算目录大小
     * 
     * @param directory 目录
     * @return 大小（字节）
     */
    private long calculateDirectorySize(File directory) {
        long size = 0;
        File[] files = directory.listFiles();
        
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    size += file.length();
                } else {
                    size += calculateDirectorySize(file);
                }
            }
        }
        
        return size;
    }

    /**
     * 调试接口：检查Redis中的任务数据
     * 
     * @param taskId 任务ID
     * @return Redis中的数据详情
     */
    @GetMapping("/debug/redis/{taskId}")
    public Result<Map<String, Object>> debugRedisData(@PathVariable String taskId) {
        logger.info("调试Redis数据: taskId={}", taskId);
        
        Map<String, Object> debugInfo = new HashMap<>();
        
        try {
            // 检查任务信息
            Map<String, Object> taskInfo = chunkStatusManager.getTaskInfo(taskId);
            debugInfo.put("taskInfo", taskInfo);
            debugInfo.put("taskInfoEmpty", taskInfo.isEmpty());
            
            // 检查已上传分片
            Set<Integer> uploadedChunks = chunkStatusManager.getUploadedChunks(taskId);
            debugInfo.put("uploadedChunks", uploadedChunks);
            debugInfo.put("uploadedChunksCount", uploadedChunks.size());
            
            // 检查Redis键是否存在
            String taskKey = "upload:task:" + taskId;
            String chunksKey = "upload:chunks:" + taskId;
            
            // 使用反射获取RedisTemplate来直接检查键
            try {
                java.lang.reflect.Field field = chunkStatusManager.getClass().getDeclaredField("redisTemplate");
                field.setAccessible(true);
                org.springframework.data.redis.core.RedisTemplate<String, Object> redisTemplate = 
                    (org.springframework.data.redis.core.RedisTemplate<String, Object>) field.get(chunkStatusManager);
                
                boolean taskKeyExists = Boolean.TRUE.equals(redisTemplate.hasKey(taskKey));
                boolean chunksKeyExists = Boolean.TRUE.equals(redisTemplate.hasKey(chunksKey));
                
                debugInfo.put("taskKeyExists", taskKeyExists);
                debugInfo.put("chunksKeyExists", chunksKeyExists);
                debugInfo.put("taskKey", taskKey);
                debugInfo.put("chunksKey", chunksKey);
                
                if (taskKeyExists) {
                    Map<Object, Object> rawTaskInfo = redisTemplate.opsForHash().entries(taskKey);
                    debugInfo.put("rawTaskInfo", rawTaskInfo);
                }
                
                if (chunksKeyExists) {
                    Long chunksCount = redisTemplate.opsForSet().size(chunksKey);
                    Set<Object> chunks = redisTemplate.opsForSet().members(chunksKey);
                    debugInfo.put("rawChunksCount", chunksCount);
                    debugInfo.put("rawChunks", chunks);
                }
                
                // 检查所有相关的Redis键
                Set<String> allKeys = redisTemplate.keys("upload:*" + taskId + "*");
                debugInfo.put("allRelatedKeys", allKeys);
                
            } catch (Exception e) {
                debugInfo.put("redisAccessError", e.getMessage());
            }
            
            // 检查数据库中的记录
            try {
                SliceEntity sliceEntity = sliceService.getBySliceId(taskId);
                debugInfo.put("databaseRecord", sliceEntity != null ? Map.of(
                    "id", sliceEntity.getId(),
                    "sliceId", sliceEntity.getSliceId(),
                    "qpName", sliceEntity.getQpName(),
                    "qpSize", sliceEntity.getQpSize(),
                    "qpState", sliceEntity.getQpState()
                ) : null);
            } catch (Exception e) {
                debugInfo.put("databaseError", e.getMessage());
            }
            
            return Result.success(debugInfo);
            
        } catch (Exception e) {
            logger.error("调试Redis数据失败: taskId={}, error={}", taskId, e.getMessage(), e);
            debugInfo.put("error", e.getMessage());
            return Result.failure(ResultCode.SYSTEM_ERROR, "调试失败: " + e.getMessage());
        }
    }

    /**
     * 简化的单个进度查询接口（用于对比测试）
     * 
     * @param taskId 任务ID
     * @return 简化的进度信息
     */
    @GetMapping("/debug/simple-progress/{taskId}")
    public Result<Map<String, Object>> getSimpleProgress(@PathVariable String taskId) {
        logger.info("简化进度查询: taskId={}", taskId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 直接从ChunkStatusManager获取数据
            Map<String, Object> taskInfo = chunkStatusManager.getTaskInfo(taskId);
            Set<Integer> uploadedChunks = chunkStatusManager.getUploadedChunks(taskId);
            
            result.put("taskId", taskId);
            result.put("taskInfo", taskInfo);
            result.put("uploadedChunks", uploadedChunks);
            result.put("uploadedChunksCount", uploadedChunks.size());
            
            if (!taskInfo.isEmpty()) {
                Object totalChunksObj = taskInfo.get("totalChunks");
                if (totalChunksObj != null) {
                    int totalChunks = Integer.parseInt(totalChunksObj.toString());
                    int uploadedCount = uploadedChunks.size();
                    int progress = (uploadedCount * 100) / totalChunks;
                    
                    result.put("totalChunks", totalChunks);
                    result.put("progress", progress);
                    result.put("calculation", uploadedCount + " / " + totalChunks + " * 100 = " + progress + "%");
                }
            }
            
            return Result.success(result);
            
        } catch (Exception e) {
            logger.error("简化进度查询失败: taskId={}, error={}", taskId, e.getMessage(), e);
            result.put("error", e.getMessage());
            return Result.failure(ResultCode.SYSTEM_ERROR, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 分片上传处理V4，支持三种上传场景的不同处理逻辑
     */
    private Object handleChunkUploadV4(String threadingId, MultipartFile file, String filename,
                                          long totalSize, int chunkNumber, int totalChunks, long currentChunkSize,
                                          UploadScenario scenario, String sliceId, String taskId, String authorization, String partnerCode,
                                          String orgId, String ahId, String projId, String caseId, String sectionId, String identifier) {
        try {
            // 参数校验
            if (chunkNumber < 1 || chunkNumber > totalChunks) {
                return Result.failure(ResultCode.PARAM_INVALID, "无效的分片序号");
            }
            if (currentChunkSize <= 0 || currentChunkSize > totalSize) {
                return Result.failure(ResultCode.PARAM_INVALID, "无效的分片大小");
            }
            
            logger.info("处理分片上传: scenario={}, threadingId={}, chunkNumber={}/{}", 
                       scenario, threadingId, chunkNumber, totalChunks);
            
            // 所有场景都使用Redis进行分片管理
            return handleRedisBasedChunkUpload(threadingId, file, filename, totalSize, chunkNumber, totalChunks, 
                                             currentChunkSize, scenario, sliceId, taskId, authorization, partnerCode, orgId, ahId, projId, caseId, sectionId, identifier);
            
        } catch (Exception e) {
            logger.error("处理分片上传失败: threadingId={}, chunkNumber={}, scenario={}, error={}", 
                       threadingId, chunkNumber, scenario, e.getMessage(), e);
            return Result.failure(ResultCode.UPLOAD_ERROR, "处理文件分片失败: " + e.getMessage());
        }
    }

    /**
     * 处理基于Redis的分片上传（统一处理所有场景）
     */
    private Object handleRedisBasedChunkUpload(String threadingId, MultipartFile file, String filename,
                                                long totalSize, int chunkNumber, int totalChunks, long currentChunkSize,
                                                UploadScenario scenario, String sliceId, String taskId, String authorization, String partnerCode,
                                                String orgId, String ahId, String projId, String caseId, String sectionId, String identifier) throws Exception {
        
        // 🔥 蚂蚁上传场景：每个分片都要先进行sliceId唯一性校验，确保及早发现重复
        if (scenario == UploadScenario.ANT_UPLOAD) {
            Result<?> validationResult = validateAntUploadSliceIdUniqueness(sliceId, chunkNumber);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }
        }
        
        // 确定Redis操作使用的键（切片池上传使用identifier，其他使用threadingId）
        String redisKey = threadingId;
        if (scenario == UploadScenario.SLICE_POOL_UPLOAD) {
            String actualIdentifier = (identifier != null && !identifier.trim().isEmpty()) ? identifier.trim() : "default";
            redisKey = actualIdentifier;
        }
        
        // 检查Redis中的任务信息
        Map<String, Object> taskInfo = (scenario == UploadScenario.SLICE_POOL_UPLOAD) ? 
            chunkStatusManager.getTaskInfoByIdentifier(redisKey) : 
            chunkStatusManager.getTaskInfo(threadingId);
            
        if (taskInfo.isEmpty()) {
            // 根据场景处理数据库记录
            if (scenario == UploadScenario.ANT_UPLOAD) {
                // 蚂蚁上传场景：sliceId唯一性校验已通过，创建数据库记录
                SliceEntity entity = SliceEntity.builder()
                        .id(UlidCreator.getMonotonicUlid().toString())
                        .taskId(null)
                        .sliceId(sliceId)  // 存储传入的sliceId
                        .qpSize((double) totalSize)
                        .tempAuth(authorization)
                        .qpName(filename)
                        .uploadTime(LocalDateTime.now())
                        .qpState("0")
                        .crteTime(LocalDateTime.now())
                        .lastModifiedTime(LocalDateTime.now())
                        .orgId(orgId)
                        .ahId(ahId)
                        .projId(projId)
                        .partnerCode(partnerCode)
                        // sectionId不再存储到数据库，直接传递给BasicInfo接口
                        .build();
                
                try {
                    sliceService.createSlice(entity);
                    logger.info("蚂蚁上传场景：创建数据库记录成功，sliceId={}", sliceId);
                } catch (org.springframework.dao.DuplicateKeyException e) {
                    // 数据库唯一约束冲突，返回错误信息
                    logger.error("蚂蚁上传场景：sliceId在数据库中已存在，创建记录失败: sliceId={}", sliceId);
                    return Result.failure(ResultCode.BIZ_ERROR, String.format("切片ID已存在，请使用其他切片ID。当前sliceId: %s", sliceId));
                } catch (Exception e) {
                    logger.error("蚂蚁上传场景：创建数据库记录失败，sliceId={}, error={}", sliceId, e.getMessage(), e);
                    return Result.failure(ResultCode.SYSTEM_ERROR, "创建切片记录失败: " + e.getMessage());
                }
            }
            // 本地上传和切片池上传的数据库记录已在determineThreadingId中创建，无需重复创建
            
            // 初始化Redis任务信息
            if (scenario == UploadScenario.SLICE_POOL_UPLOAD) {
                chunkStatusManager.initTaskByIdentifier(redisKey, filename, totalSize, totalChunks);
            } else {
                chunkStatusManager.initTask(threadingId, filename, totalSize, totalChunks);
            }
            uploadTaskManager.createTask(threadingId, filename, totalSize, "anonymous");
        }
        
        // 创建临时目录并保存分片（使用合适的目录键）
        Path tempDir = tempDirConfig.getTempDir();
        String dirKey = (scenario == UploadScenario.SLICE_POOL_UPLOAD) ? redisKey : threadingId;
        Path taskDir = tempDir.resolve(dirKey);
        Path chunksDir = taskDir.resolve("chunks");
        Files.createDirectories(chunksDir);
        Path chunkPath = chunksDir.resolve(String.valueOf(chunkNumber));
        
        // 复制分片文件到临时目录
        Files.copy(file.getInputStream(), chunkPath, StandardCopyOption.REPLACE_EXISTING);
        logger.debug("分片文件已保存: scenario={}, dirKey={}, chunkNumber={}, path={}", scenario, dirKey, chunkNumber, chunkPath);
        
        // 更新分片状态
        if (scenario == UploadScenario.SLICE_POOL_UPLOAD) {
            chunkStatusManager.saveChunkByIdentifier(redisKey, chunkNumber, currentChunkSize, chunkPath.toString());
        } else {
            chunkStatusManager.saveChunk(threadingId, chunkNumber, currentChunkSize, chunkPath.toString());
        }
        
        // 检查是否所有分片都已上传
        boolean isComplete = (scenario == UploadScenario.SLICE_POOL_UPLOAD) ? 
            chunkStatusManager.isUploadCompleteByIdentifier(redisKey) : 
            chunkStatusManager.isUploadComplete(threadingId);
        if (isComplete) {
            // 根据场景输出不同的日志信息
            if (scenario == UploadScenario.LOCAL_UPLOAD) {
                logger.info("本地上传所有分片已完成，开始合并文件: 穿线标识(主键id)={}, 实际taskId={}", 
                           threadingId, taskId);
            } else if (scenario == UploadScenario.ANT_UPLOAD) {
                logger.info("蚂蚁上传所有分片已完成，开始合并文件: 穿线标识(sliceId)={}", threadingId);
            } else {
                logger.info("切片池上传所有分片已完成，开始合并文件: 穿线标识(主键id)={}", threadingId);
            }
            
            // 异步合并文件
            if (scenario == UploadScenario.SLICE_POOL_UPLOAD) {
                // 切片池上传传入identifier和sectionId
                String actualIdentifier = (identifier != null && !identifier.trim().isEmpty()) ? identifier.trim() : "default";
                chunkMergeService.mergeAndProcessWithScenario(threadingId, filename, totalSize, partnerCode, scenario, caseId, sectionId, actualIdentifier);
            } else {
                // 其他场景传入sectionId
                chunkMergeService.mergeAndProcessWithScenario(threadingId, filename, totalSize, partnerCode, scenario, caseId, sectionId);
            }
            
            // 更新数据库状态为处理中
            try {
                if (scenario == UploadScenario.ANT_UPLOAD) {
                    sliceService.updateSliceStatus(threadingId, "0");
                    logger.info("蚂蚁上传场景：更新数据库状态为处理中，sliceId={}", threadingId);
                } else {
                    // 本地上传和切片池上传使用主键id更新
                    SliceEntity slice = sliceService.getById(threadingId);
                    if (slice != null) {
                        slice.setQpState("0");
                        slice.setLastModifiedTime(LocalDateTime.now());
                        sliceService.updateById(slice);
                        String actualTaskId = slice.getTaskId(); // 获取实际的taskId
                        if (scenario == UploadScenario.LOCAL_UPLOAD) {
                            logger.info("本地上传场景：更新数据库状态为处理中，穿线标识(主键id)={}, 实际taskId={}", 
                                       threadingId, actualTaskId);
                        } else {
                            logger.info("切片池上传场景：更新数据库状态为处理中，穿线标识(主键id)={}", threadingId);
                        }
                    }
                }
            } catch (Exception e) {
                logger.warn("更新切片状态失败: 穿线标识={}, error={}", threadingId, e.getMessage());
            }
        }
        
        // 修改：使用简化的响应格式
        return SimplifiedUploadResponseDTO.success(chunkNumber);
    }
    
    /**
     * 校验蚂蚁上传的sliceId唯一性
     * 
     * @param sliceId 切片ID
     * @param chunkNumber 分片序号
     * @return 校验结果
     */
    private Result<?> validateAntUploadSliceIdUniqueness(String sliceId, int chunkNumber) {
        if (sliceId == null || sliceId.trim().isEmpty()) {
            logger.error("蚂蚁上传场景：sliceId不能为空，分片序号={}", chunkNumber);
            return Result.failure(ResultCode.BIZ_ERROR, "蚂蚁上传场景：sliceId不能为空");
        }
        
        sliceId = sliceId.trim();
        
        try {
            // 🔥 关键修改：对于第1个分片，进行严格的唯一性校验
            if (chunkNumber == 1) {
                // 检查数据库中是否已存在相同的sliceId
                SliceEntity existingSlice = sliceService.getBySliceId(sliceId);
                if (existingSlice != null) {
                    logger.error("蚂蚁上传场景：第1个分片校验失败，sliceId已存在于数据库中，sliceId={}, 现有记录ID={}", 
                               sliceId, existingSlice.getId());
                    
                    // 检查现有记录的状态，提供更详细的错误信息
                    String qpState = existingSlice.getQpState();
                    String statusMsg = getSliceStatusMessage(qpState);
                    String errorMsg = String.format("切片ID已存在，无法重复上传。sliceId: %s，当前状态: %s", sliceId, statusMsg);
                    
                    return Result.failure(ResultCode.BIZ_ERROR, errorMsg);
                }
                
                // 检查Redis中是否有正在进行的任务（避免并发上传）
                Map<String, Object> redisTaskInfo = chunkStatusManager.getTaskInfo(sliceId);
                if (!redisTaskInfo.isEmpty()) {
                    logger.error("蚂蚁上传场景：第1个分片校验失败，sliceId对应的上传任务正在Redis中进行，sliceId={}", sliceId);
                    return Result.failure(ResultCode.BIZ_ERROR, 
                        String.format("切片ID对应的上传任务正在进行中，请稍后重试。sliceId: %s", sliceId));
                }
                
                logger.info("蚂蚁上传场景：第1个分片sliceId唯一性校验通过，sliceId={}", sliceId);
            } else {
                // 🔥 对于第2-n个分片，检查是否属于当前正在进行的上传任务
                Map<String, Object> redisTaskInfo = chunkStatusManager.getTaskInfo(sliceId);
                if (redisTaskInfo.isEmpty()) {
                    logger.error("蚂蚁上传场景：第{}个分片校验失败，Redis中没有找到对应的上传任务，sliceId={}", chunkNumber, sliceId);
                    return Result.failure(ResultCode.BIZ_ERROR, 
                        String.format("上传任务不存在或已过期，请重新开始上传。sliceId: %s，分片序号: %d", sliceId, chunkNumber));
                }
                
                logger.debug("蚂蚁上传场景：第{}个分片校验通过，找到对应的Redis任务，sliceId={}", chunkNumber, sliceId);
            }
            
            return Result.success("sliceId校验通过");
            
        } catch (Exception e) {
            logger.error("蚂蚁上传场景：校验sliceId唯一性时发生异常，sliceId={}, 分片序号={}, error={}", 
                       sliceId, chunkNumber, e.getMessage(), e);
            return Result.failure(ResultCode.SYSTEM_ERROR, "校验切片ID唯一性时发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 根据切片状态获取状态描述信息
     * 
     * @param qpState 切片状态
     * @return 状态描述
     */
    private String getSliceStatusMessage(String qpState) {
        if (qpState == null) {
            return "未知状态";
        }
        
        switch (qpState) {
            case "0":
                return "处理中";
            case "1":
                return "解析成功";
            case "2":
                return "解析失败";
            default:
                return "未知状态(" + qpState + ")";
        }
    }

    /**
     * 获取场景名称
     */
    private String getScenarioName(UploadScenario scenario) {
        switch (scenario) {
            case LOCAL_UPLOAD:
                return "本地上传";
            case ANT_UPLOAD:
                return "蚂蚁上传";
            case SLICE_POOL_UPLOAD:
                return "切片池上传";
            default:
                return "未知场景";
        }
    }

    /**
     * 检查分片上传状态的调试端点
     * 
     * @param taskId 任务ID
     * @return 分片状态详情
     */
    @GetMapping("/debug/chunk-status/{taskId}")
    public Result<Map<String, Object>> debugChunkStatus(@PathVariable String taskId) {
        logger.info("调试分片状态: taskId={}", taskId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取Redis中的任务信息
            Map<String, Object> taskInfo = chunkStatusManager.getTaskInfo(taskId);
            Set<Integer> uploadedChunks = chunkStatusManager.getUploadedChunks(taskId);
            
            result.put("taskId", taskId);
            result.put("taskInfo", taskInfo);
            result.put("uploadedChunks", uploadedChunks);
            result.put("uploadedChunksCount", uploadedChunks.size());
            
            if (!taskInfo.isEmpty()) {
                Object totalChunksObj = taskInfo.get("totalChunks");
                if (totalChunksObj != null) {
                    int expectedTotalChunks = Integer.parseInt(totalChunksObj.toString());
                    int actualUploadedChunks = uploadedChunks.size();
                    
                    result.put("expectedTotalChunks", expectedTotalChunks);
                    result.put("actualUploadedChunks", actualUploadedChunks);
                    result.put("isComplete", actualUploadedChunks == expectedTotalChunks);
                    
                    // 分析缺失的分片
                    Set<Integer> missingChunks = new HashSet<>();
                    Set<Integer> expectedChunks = new HashSet<>();
                    for (int i = 1; i <= expectedTotalChunks; i++) {
                        expectedChunks.add(i);
                        if (!uploadedChunks.contains(i)) {
                            missingChunks.add(i);
                        }
                    }
                    
                    result.put("expectedChunks", expectedChunks);
                    result.put("missingChunks", missingChunks);
                    result.put("missingChunksCount", missingChunks.size());
                    
                    // 检查分片连续性
                    List<Integer> sortedChunks = new ArrayList<>(uploadedChunks);
                    Collections.sort(sortedChunks);
                    boolean isSequential = true;
                    List<String> sequentialErrors = new ArrayList<>();
                    
                    for (int i = 0; i < sortedChunks.size(); i++) {
                        int expectedChunk = i + 1;
                        int actualChunk = sortedChunks.get(i);
                        if (actualChunk != expectedChunk) {
                            isSequential = false;
                            sequentialErrors.add(String.format("位置%d: 期望%d, 实际%d", i, expectedChunk, actualChunk));
                        }
                    }
                    
                    result.put("sortedChunks", sortedChunks);
                    result.put("isSequential", isSequential);
                    result.put("sequentialErrors", sequentialErrors);
                    
                    // 检查Redis键状态
                    String taskKey = "upload:task:" + taskId;
                    String chunksKey = "upload:chunks:" + taskId;
                    boolean taskKeyExists = stringRedisTemplate.hasKey(taskKey);
                    boolean chunksKeyExists = stringRedisTemplate.hasKey(chunksKey);
                    Long taskKeyTtl = stringRedisTemplate.getExpire(taskKey);
                    Long chunksKeyTtl = stringRedisTemplate.getExpire(chunksKey);
                    
                    Map<String, Object> redisStatus = new HashMap<>();
                    redisStatus.put("taskKey", taskKey);
                    redisStatus.put("taskKeyExists", taskKeyExists);
                    redisStatus.put("taskKeyTtl", taskKeyTtl);
                    redisStatus.put("chunksKey", chunksKey);
                    redisStatus.put("chunksKeyExists", chunksKeyExists);
                    redisStatus.put("chunksKeyTtl", chunksKeyTtl);
                    
                    result.put("redisStatus", redisStatus);
                }
            }
            
            // 检查数据库记录
            try {
                SliceEntity slice = null;
                
                // 先尝试按sliceId查询（蚂蚁上传）
                try {
                    slice = sliceService.getBySliceId(taskId);
                    if (slice != null) {
                        result.put("databaseQueryMethod", "bySliceId");
                    }
                } catch (Exception e) {
                    logger.debug("按sliceId查询失败: {}", e.getMessage());
                }
                
                // 如果按sliceId查不到，再按主键id查询（本地上传和切片池上传）
                if (slice == null) {
                    try {
                        slice = sliceService.getById(taskId);
                        if (slice != null) {
                            result.put("databaseQueryMethod", "byId");
                        }
                    } catch (Exception e) {
                        logger.debug("按主键id查询失败: {}", e.getMessage());
                    }
                }
                
                if (slice != null) {
                    Map<String, Object> databaseInfo = new HashMap<>();
                    databaseInfo.put("id", slice.getId());
                    databaseInfo.put("sliceId", slice.getSliceId());
                    databaseInfo.put("taskId", slice.getTaskId());
                    databaseInfo.put("qpName", slice.getQpName());
                    databaseInfo.put("qpSize", slice.getQpSize());
                    databaseInfo.put("qpState", slice.getQpState());
                    databaseInfo.put("uploadTime", slice.getUploadTime());
                    databaseInfo.put("lastModifiedTime", slice.getLastModifiedTime());
                    
                    result.put("databaseRecord", databaseInfo);
                } else {
                    result.put("databaseRecord", null);
                    result.put("databaseError", "未找到对应的数据库记录");
                }
                
            } catch (Exception e) {
                result.put("databaseError", "查询数据库记录异常: " + e.getMessage());
                logger.error("查询数据库记录异常: taskId={}, error={}", taskId, e.getMessage(), e);
            }
            
            return Result.success(result);
            
        } catch (Exception e) {
            logger.error("调试分片状态失败: taskId={}, error={}", taskId, e.getMessage(), e);
            result.put("error", e.getMessage());
            return Result.failure(ResultCode.SYSTEM_ERROR, "调试失败: " + e.getMessage());
        }
    }

    /**
     * 删除切片文件
     * 接口路径: /api/upload/delete/{id}?cname=xxx
     * 删除顺序：先删文件，再清缓存，最后删数据库记录
     *
     * @param id 切片主键ID
     * @param cname 对应数据库的partner_code字段，用于权限验证
     * @return 删除结果
     */
    @DeleteMapping("/delete/{id}")
    public Result<String> deleteSlice(@PathVariable String id, @RequestParam String cname) {
        logger.info("接收删除切片请求: id={}, cname={}", id, cname);

        try {
            // 1. 验证阶段：根据ID+cname查询记录，确认存在性和权限
            SliceEntity sliceEntity = sliceService.getByIdentifierAndPartnerCode(id, cname);
            if (sliceEntity == null) {
                logger.warn("切片记录不存在: id={}, cname={}", id, cname);
                return Result.failure(ResultCode.RESOURCE_NOT_FOUND, "切片记录不存在");
            }

            logger.info("找到切片记录: id={}, cname={}, qpName={}, physicPath={}", 
                       id, cname, sliceEntity.getQpName(), sliceEntity.getPhysicPath());

            // 2. 文件删除阶段：删除所有相关的本地文件
            boolean filesDeleted = deleteRelatedFiles(sliceEntity);
            if (!filesDeleted) {
                logger.error("文件删除失败，停止删除数据库记录: id={}", id);
                return Result.failure(ResultCode.IO_ERROR, "文件删除失败，请检查文件权限或联系管理员");
            }

            // 3. 缓存清理阶段：清理相关缓存
            clearRelatedCache(id, sliceEntity);

            // 4. 数据清理阶段：删除数据库记录
            boolean recordDeleted = sliceService.removeById(id);
            if (!recordDeleted) {
                logger.error("数据库记录删除失败: id={}", id);
                return Result.failure(ResultCode.SYSTEM_ERROR, "数据库记录删除失败");
            }

            logger.info("切片删除完成: id={}, cname={}, qpName={}", id, cname, sliceEntity.getQpName());
            return Result.success("删除成功");

        } catch (Exception e) {
            logger.error("删除切片失败: id={}, cname={}, error={}", id, cname, e.getMessage(), e);
            return Result.failure(ResultCode.SYSTEM_ERROR, "删除失败: " + e.getMessage());
        }
    }

    /**
     * 删除切片相关的所有文件
     * 包括：主切片文件、缩略图文件、标签图文件、玻片图文件
     *
     * @param sliceEntity 切片实体
     * @return 删除是否成功
     */
    private boolean deleteRelatedFiles(SliceEntity sliceEntity) {
        try {
            String physicPath = sliceEntity.getPhysicPath();
            if (physicPath == null || physicPath.trim().isEmpty()) {
                logger.warn("切片physic_path为空，跳过文件删除: id={}", sliceEntity.getId());
                return true; // 认为成功，因为没有文件需要删除
            }

            // 构建完整的切片文件路径
            Path sliceFilePath = Paths.get(storageConfig.getLocalSliceDir(), physicPath);
            Path parentDir = sliceFilePath.getParent();

            logger.info("开始删除文件: 切片路径={}, 父目录={}", sliceFilePath, parentDir);

            if (parentDir == null || !Files.exists(parentDir)) {
                logger.warn("切片文件父目录不存在: {}", parentDir);
                return true; // 目录不存在，认为删除成功
            }

            // 获取切片文件名（不含扩展名）用于构建相关文件名
            String sliceFileName = sliceFilePath.getFileName().toString();
            String baseName = sliceFileName;
            int dotIdx = sliceFileName.lastIndexOf('.');
            if (dotIdx > 0) {
                baseName = sliceFileName.substring(0, dotIdx);
            }

            boolean allSuccess = true;
            List<String> deletedFiles = new ArrayList<>();
            List<String> failedFiles = new ArrayList<>();

            // 删除主切片文件
            if (Files.exists(sliceFilePath)) {
                try {
                    Files.delete(sliceFilePath);
                    deletedFiles.add(sliceFilePath.toString());
                    logger.info("已删除主切片文件: {}", sliceFilePath);
                } catch (Exception e) {
                    logger.error("删除主切片文件失败: {}, error={}", sliceFilePath, e.getMessage());
                    failedFiles.add(sliceFilePath.toString());
                    allSuccess = false;
                }
            }

            // 删除缩略图文件：thumb_{切片文件名}.jpeg
            Path thumbFile = parentDir.resolve("thumb_" + baseName + ".jpeg");
            if (Files.exists(thumbFile)) {
                try {
                    Files.delete(thumbFile);
                    deletedFiles.add(thumbFile.toString());
                    logger.info("已删除缩略图文件: {}", thumbFile);
                } catch (Exception e) {
                    logger.error("删除缩略图文件失败: {}, error={}", thumbFile, e.getMessage());
                    failedFiles.add(thumbFile.toString());
                    allSuccess = false;
                }
            }

            // 删除标签图文件：label_{切片文件名}.jpeg
            Path labelFile = parentDir.resolve("label_" + baseName + ".jpeg");
            if (Files.exists(labelFile)) {
                try {
                    Files.delete(labelFile);
                    deletedFiles.add(labelFile.toString());
                    logger.info("已删除标签图文件: {}", labelFile);
                } catch (Exception e) {
                    logger.error("删除标签图文件失败: {}, error={}", labelFile, e.getMessage());
                    failedFiles.add(labelFile.toString());
                    allSuccess = false;
                }
            }

            // 删除玻片图文件：slide_{切片文件名}.jpeg
            Path slideFile = parentDir.resolve("slide_" + baseName + ".jpeg");
            if (Files.exists(slideFile)) {
                try {
                    Files.delete(slideFile);
                    deletedFiles.add(slideFile.toString());
                    logger.info("已删除玻片图文件: {}", slideFile);
                } catch (Exception e) {
                    logger.error("删除玻片图文件失败: {}, error={}", slideFile, e.getMessage());
                    failedFiles.add(slideFile.toString());
                    allSuccess = false;
                }
            }

            logger.info("文件删除汇总 - 成功删除{}个文件，失败{}个文件", deletedFiles.size(), failedFiles.size());
            if (!deletedFiles.isEmpty()) {
                logger.info("成功删除的文件: {}", deletedFiles);
            }
            if (!failedFiles.isEmpty()) {
                logger.warn("删除失败的文件: {}", failedFiles);
            }

            return allSuccess;

        } catch (Exception e) {
            logger.error("删除相关文件过程中发生异常: id={}, error={}", sliceEntity.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 清理切片相关的缓存
     * 包括：DZI缓存、切片对象缓存、控制器层缓存、服务层内存缓存等
     *
     * @param identifier 切片标识符（主键id）
     * @param sliceEntity 切片实体
     */
    private void clearRelatedCache(String identifier, SliceEntity sliceEntity) {
        try {
            logger.info("开始清理多层缓存: identifier={}", identifier);

            // 1. 清理Spring @Cacheable缓存和Redis缓存
            List<String> cacheKeysToDelete = new ArrayList<>();
            
            // DZI描述文件缓存 - 根据DziServiceImpl中的@Cacheable注解
            cacheKeysToDelete.add("dziDescriptor::" + identifier);
            if (sliceEntity.getPartnerCode() != null) {
                cacheKeysToDelete.add("dziDescriptor::" + identifier + "_" + sliceEntity.getPartnerCode());
            }

            // 清理Redis缓存
            for (String cacheKey : cacheKeysToDelete) {
                try {
                    cacheService.delete(cacheKey);
                    logger.debug("已清理Spring缓存: {}", cacheKey);
                } catch (Exception e) {
                    logger.warn("清理Spring缓存失败: {}, error={}", cacheKey, e.getMessage());
                }
            }

            // 2. 清理DziController的静态响应缓存
            try {
                clearDziControllerStaticCache(identifier, sliceEntity.getPartnerCode());
                logger.debug("已清理DziController静态缓存");
            } catch (Exception e) {
                logger.warn("清理DziController静态缓存失败: {}", e.getMessage());
            }

            // 3. 清理DziServiceImpl的内存瓦片缓存
            try {
                clearDziServiceMemoryCache(identifier);
                logger.debug("已清理DziService内存瓦片缓存");
            } catch (Exception e) {
                logger.warn("清理DziService内存瓦片缓存失败: {}", e.getMessage());
            }

            // 4. 清理上传相关的临时缓存
            if (sliceEntity.getTaskId() != null) {
                String taskCacheKey = "upload:task:" + sliceEntity.getTaskId();
                String chunksCacheKey = "upload:chunks:" + sliceEntity.getTaskId();
                cacheService.delete(taskCacheKey);
                cacheService.delete(chunksCacheKey);
                logger.debug("已清理上传任务缓存: taskId={}", sliceEntity.getTaskId());
            }

            logger.info("多层缓存清理完成: identifier={}", identifier);

        } catch (Exception e) {
            logger.error("清理缓存过程中发生异常: identifier={}, error={}", identifier, e.getMessage(), e);
            // 缓存清理失败不影响删除流程，仅记录日志
        }
    }

    /**
     * 清理DziController的静态响应缓存
     * 使用反射清理RESPONSE_CACHE中的相关缓存项
     */
    private void clearDziControllerStaticCache(String identifier, String partnerCode) {
        try {
            // 通过反射获取DziController的静态缓存
            Class<?> dziControllerClass = Class.forName("cn.ccaa.slice.web.controller.DziController");
            java.lang.reflect.Field responseCacheField = dziControllerClass.getDeclaredField("RESPONSE_CACHE");
            responseCacheField.setAccessible(true);
            
            @SuppressWarnings("unchecked")
            Map<String, Object> responseCache = (Map<String, Object>) responseCacheField.get(null);
            
            // 构建可能的缓存键
            List<String> cacheKeysToRemove = new ArrayList<>();
            
            // DZI描述文件缓存键
            cacheKeysToRemove.add("dzi_" + identifier);
            if (partnerCode != null) {
                cacheKeysToRemove.add("dzi_" + identifier + "_" + partnerCode);
            }
            
            // 瓦片缓存键（0-2级，最常缓存的层级）
            for (int level = 0; level <= 2; level++) {
                // 常见的瓦片坐标范围
                for (int x = 0; x < 4; x++) {
                    for (int y = 0; y < 4; y++) {
                        cacheKeysToRemove.add("tile_" + identifier + "_" + level + "_" + x + "_" + y);
                        if (partnerCode != null) {
                            cacheKeysToRemove.add("tile_" + identifier + "_" + partnerCode + "_" + level + "_" + x + "_" + y);
                        }
                    }
                }
            }
            
            // 缩略图缓存键
            cacheKeysToRemove.add("thumb_" + identifier);
            
            // 移除匹配的缓存项
            int removedCount = 0;
            for (String key : cacheKeysToRemove) {
                if (responseCache.remove(key) != null) {
                    removedCount++;
                }
            }
            
            // 还要清理所有包含identifier的缓存项（防止遗漏）
            Set<String> keysToRemove = new HashSet<>();
            for (String key : responseCache.keySet()) {
                if (key.contains(identifier)) {
                    keysToRemove.add(key);
                }
            }
            
            for (String key : keysToRemove) {
                responseCache.remove(key);
                removedCount++;
            }
            
            logger.info("DziController静态缓存清理完成，移除{}个缓存项", removedCount);
            
        } catch (Exception e) {
            logger.warn("清理DziController静态缓存时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 清理DziServiceImpl的内存瓦片缓存
     * 使用反射清理tileCache中的相关缓存项
     */
    private void clearDziServiceMemoryCache(String identifier) {
        try {
            // 通过反射获取DziServiceImpl的瓦片缓存
            Class<?> dziServiceImplClass = Class.forName("cn.ccaa.slice.service.dzi.impl.DziServiceImpl");
            
            // 获取DziServiceImpl的Bean实例 - 简化版本，直接通过字段注入的方式
            // 由于当前类已经在Spring容器中，我们可以尝试通过反射找到dziService字段
            try {
                java.lang.reflect.Field dziServiceField = this.getClass().getDeclaredField("dziService");
                dziServiceField.setAccessible(true);
                Object dziServiceImpl = dziServiceField.get(this);
                
                if (dziServiceImpl != null && dziServiceImplClass.isInstance(dziServiceImpl)) {
                    java.lang.reflect.Field tileCacheField = dziServiceImplClass.getDeclaredField("tileCache");
                    tileCacheField.setAccessible(true);
                    
                    @SuppressWarnings("unchecked")
                    Map<String, byte[]> tileCache = (Map<String, byte[]>) tileCacheField.get(dziServiceImpl);
                    
                    // 移除所有包含identifier的缓存项
                    Set<String> keysToRemove = new HashSet<>();
                    for (String key : tileCache.keySet()) {
                        if (key.contains(identifier)) {
                            keysToRemove.add(key);
                        }
                    }
                    
                    for (String key : keysToRemove) {
                        tileCache.remove(key);
                    }
                    
                    logger.info("DziService内存瓦片缓存清理完成，移除{}个缓存项", keysToRemove.size());
                } else {
                    logger.warn("无法获取DziServiceImpl实例或类型不匹配");
                }
            } catch (NoSuchFieldException e) {
                logger.debug("当前类没有dziService字段，尝试其他方式清理缓存");
                // 如果没有直接的dziService字段，记录警告但不影响主流程
                logger.warn("无法直接访问DziService实例，建议手动清理内存缓存");
            }
            
        } catch (Exception e) {
            logger.warn("清理DziService内存瓦片缓存时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 强制清理所有DZI相关缓存 - 管理员接口
     * 当删除文件后DZI查看器仍能显示时，可以使用此接口强制清理所有缓存
     *
     * @param identifier 切片标识符（可选，如果不提供则清理所有缓存）
     * @return 清理结果
     */
    @PostMapping("/admin/clear-all-cache")
    public Result<Map<String, Object>> clearAllDziCache(@RequestParam(required = false) String identifier) {
        logger.info("管理员强制清理DZI缓存: identifier={}", identifier);

        Map<String, Object> result = new HashMap<>();
        int totalClearedItems = 0;

        try {
            // 1. 清理DziController静态响应缓存
            try {
                Class<?> dziControllerClass = Class.forName("cn.ccaa.slice.web.controller.DziController");
                java.lang.reflect.Field responseCacheField = dziControllerClass.getDeclaredField("RESPONSE_CACHE");
                responseCacheField.setAccessible(true);
                
                @SuppressWarnings("unchecked")
                Map<String, Object> responseCache = (Map<String, Object>) responseCacheField.get(null);
                
                if (identifier != null && !identifier.trim().isEmpty()) {
                    // 清理指定identifier的缓存
                    Set<String> keysToRemove = new HashSet<>();
                    for (String key : responseCache.keySet()) {
                        if (key.contains(identifier.trim())) {
                            keysToRemove.add(key);
                        }
                    }
                    
                    for (String key : keysToRemove) {
                        responseCache.remove(key);
                        totalClearedItems++;
                    }
                    result.put("controllerCacheCleared", keysToRemove.size());
                } else {
                    // 清理所有缓存
                    int size = responseCache.size();
                    responseCache.clear();
                    totalClearedItems += size;
                    result.put("controllerCacheCleared", size);
                }
                
                logger.info("DziController静态缓存已清理");
            } catch (Exception e) {
                logger.warn("清理DziController静态缓存失败: {}", e.getMessage());
                result.put("controllerCacheError", e.getMessage());
            }

            // 2. 清理DziServiceImpl内存瓦片缓存
            try {
                if (dziService != null) {
                    Class<?> dziServiceImplClass = dziService.getClass();
                    java.lang.reflect.Field tileCacheField = dziServiceImplClass.getDeclaredField("tileCache");
                    tileCacheField.setAccessible(true);
                    
                    @SuppressWarnings("unchecked")
                    Map<String, byte[]> tileCache = (Map<String, byte[]>) tileCacheField.get(dziService);
                    
                    if (identifier != null && !identifier.trim().isEmpty()) {
                        // 清理指定identifier的缓存
                        Set<String> keysToRemove = new HashSet<>();
                        for (String key : tileCache.keySet()) {
                            if (key.contains(identifier.trim())) {
                                keysToRemove.add(key);
                            }
                        }
                        
                        for (String key : keysToRemove) {
                            tileCache.remove(key);
                            totalClearedItems++;
                        }
                        result.put("serviceCacheCleared", keysToRemove.size());
                    } else {
                        // 清理所有缓存
                        int size = tileCache.size();
                        tileCache.clear();
                        totalClearedItems += size;
                        result.put("serviceCacheCleared", size);
                    }
                    
                    logger.info("DziService内存瓦片缓存已清理");
                }
            } catch (Exception e) {
                logger.warn("清理DziService内存瓦片缓存失败: {}", e.getMessage());
                result.put("serviceCacheError", e.getMessage());
            }

            // 3. 清理Redis缓存（Spring @Cacheable缓存）
            try {
                if (identifier != null && !identifier.trim().isEmpty()) {
                    // 清理指定identifier的缓存
                    List<String> cacheKeys = List.of(
                        "dziDescriptor::" + identifier.trim(),
                        "dziDescriptor::" + identifier.trim() + "_*"
                    );
                    
                    int redisCleared = 0;
                    for (String key : cacheKeys) {
                        try {
                            cacheService.delete(key);
                            redisCleared++;
                        } catch (Exception e) {
                            logger.debug("删除Redis缓存键失败: {}", key);
                        }
                    }
                    result.put("redisCacheCleared", redisCleared);
                } else {
                    // 如果有批量清理Redis缓存的能力，可以在这里添加
                    result.put("redisCacheCleared", "需要指定identifier才能清理Redis缓存");
                }
            } catch (Exception e) {
                logger.warn("清理Redis缓存失败: {}", e.getMessage());
                result.put("redisCacheError", e.getMessage());
            }

            // 4. 触发垃圾回收
            System.gc();
            
            result.put("success", true);
            result.put("totalClearedItems", totalClearedItems);
            result.put("message", "缓存清理完成");
            result.put("identifier", identifier);
            result.put("timestamp", System.currentTimeMillis());

            logger.info("管理员强制清理DZI缓存完成: identifier={}, 总清理项目数={}", identifier, totalClearedItems);
            return Result.success(result);

        } catch (Exception e) {
            logger.error("强制清理DZI缓存失败: identifier={}, error={}", identifier, e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return Result.failure(ResultCode.SYSTEM_ERROR, "缓存清理失败: " + e.getMessage());
        }
    }

    /**
     * 重新推送接口
     * 根据ID重新调用三方Info接口进行推送
     * 处理流程：
     * 1. 根据ID查询切片信息
     * 2. 检查缩略图、标签图、玻片图是否存在
     * 3. 如果图片不存在，重新解析切片文件生成图片
     * 4. 调用三方推送逻辑进行Info接口推送
     *
     * @param id 切片ID（主键）
     * @return 推送结果
     */
    @PostMapping("/repush/{id}")
    public Result<Map<String, Object>> repushSliceAnalysisInfo(@PathVariable String id) {
        logger.info("接收重新推送请求: id={}", id);
        
        if (id == null || id.trim().isEmpty()) {
            return Result.failure(ResultCode.PARAM_INVALID, "ID不能为空");
        }
        
        try {
            // 1. 根据ID查询切片信息
            SliceEntity sliceEntity = sliceService.getById(id);
            if (sliceEntity == null) {
                logger.warn("未找到切片记录: id={}", id);
                return Result.failure(ResultCode.RESOURCE_NOT_FOUND, "切片记录不存在");
            }
            
            logger.info("找到切片记录: id={}, qpName={}, physicPath={}, qpState={}", 
                       id, sliceEntity.getQpName(), sliceEntity.getPhysicPath(), sliceEntity.getQpState());
            
            // 2. 检查是否有partnerCode，没有则跳过推送
            String partnerCode = sliceEntity.getPartnerCode();
            if (partnerCode == null || partnerCode.trim().isEmpty()) {
                logger.info("该切片没有partnerCode，跳过推送: id={}", id);
                return Result.<Map<String, Object>>builder()
                    .success(true)
                    .code(0)
                    .message("操作成功")
                    .data(Map.of(
                        "id", id,
                        "status", "skipped", 
                        "message", "该切片没有partnerCode，无需推送"
                    ))
                    .build();
            }
            
            // 3. 检查切片文件和图片文件是否存在
            boolean needReprocess = false;
            String repushReason = "";
            
            if (sliceEntity.getPhysicPath() == null || sliceEntity.getPhysicPath().trim().isEmpty()) {
                repushReason = "切片文件物理路径为空，需要重新处理";
                needReprocess = true;
            } else {
                // 检查切片文件是否存在
                Path sliceFilePath = Paths.get(storageConfig.getLocalSliceDir(), sliceEntity.getPhysicPath());
                if (!Files.exists(sliceFilePath)) {
                    repushReason = "切片文件不存在: " + sliceFilePath;
                    needReprocess = true;
                } else {
                    // 检查图片文件是否存在
                    boolean hasImages = checkImageFilesExist(sliceEntity);
                    if (!hasImages) {
                        repushReason = "缩略图或其他图片文件不存在，需要重新生成";
                        needReprocess = true;
                    }
                }
            }
            
            // 4. 如果需要重新处理，先重新解析生成图片
            if (needReprocess) {
                logger.info("需要重新处理切片文件: id={}, reason={}", id, repushReason);
                
                // 检查切片文件是否确实存在
                if (sliceEntity.getPhysicPath() == null || sliceEntity.getPhysicPath().trim().isEmpty()) {
                    return Result.failure(ResultCode.SLIDE_ERROR, "切片文件物理路径为空，无法重新解析");
                }
                
                Path sliceFilePath = Paths.get(storageConfig.getLocalSliceDir(), sliceEntity.getPhysicPath());
                if (!Files.exists(sliceFilePath)) {
                    return Result.failure(ResultCode.SLIDE_ERROR, "切片文件不存在，无法重新解析: " + sliceFilePath);
                }
                
                // 调用切片解析服务重新生成图片
                boolean reprocessSuccess = reprocessSliceImages(sliceEntity);
                if (!reprocessSuccess) {
                    return Result.failure(ResultCode.SLIDE_ERROR, "重新解析切片文件失败");
                }
                
                logger.info("切片文件重新处理完成: id={}", id);
            }
            
            // 5. 调用三方推送逻辑
            boolean pushSuccess = sliceAnalysisCallbackService.pushSliceAnalysisInfo(id, partnerCode);
            
            Map<String, Object> result = new HashMap<>();
            result.put("id", id);
            result.put("partnerCode", partnerCode);
            result.put("needReprocess", needReprocess);
            if (needReprocess) {
                result.put("reprocessReason", repushReason);
            }
            result.put("pushSuccess", pushSuccess);
            result.put("status", pushSuccess ? "success" : "failed");
            result.put("message", pushSuccess ? "重新推送成功" : "重新推送失败");
            
            if (pushSuccess) {
                logger.info("重新推送成功: id={}, partnerCode={}, needReprocess={}", id, partnerCode, needReprocess);
                return Result.<Map<String, Object>>builder()
                    .success(true)
                    .code(0)
                    .message("操作成功")
                    .data(result)
                    .build();
            } else {
                logger.warn("重新推送失败: id={}, partnerCode={}, needReprocess={}", id, partnerCode, needReprocess);
                return Result.failure(ResultCode.UPLOAD_ERROR, "重新推送失败");
            }
            
        } catch (Exception e) {
            logger.error("重新推送异常: id={}, error={}", id, e.getMessage(), e);
            return Result.failure(ResultCode.SYSTEM_ERROR, "重新推送异常: " + e.getMessage());
        }
    }
    
    /**
     * 检查图片文件是否存在
     * 检查缩略图、标签图、玻片图是否存在
     */
    private boolean checkImageFilesExist(SliceEntity sliceEntity) {
        try {
            String physicPath = sliceEntity.getPhysicPath();
            if (physicPath == null || physicPath.trim().isEmpty()) {
                return false;
            }
            
            // 构建切片文件路径
            Path sliceFilePath = Paths.get(storageConfig.getLocalSliceDir(), physicPath);
            Path parentDir = sliceFilePath.getParent();
            
            if (parentDir == null || !Files.exists(parentDir)) {
                return false;
            }
            
            // 获取切片文件名（不含扩展名）
            String sliceFileName = sliceFilePath.getFileName().toString();
            String baseName = sliceFileName;
            int dotIdx = sliceFileName.lastIndexOf('.');
            if (dotIdx > 0) {
                baseName = sliceFileName.substring(0, dotIdx);
            }
            
            // 检查缩略图文件是否存在（必须存在）
            File thumbFile = parentDir.resolve("thumb_" + baseName + ".jpeg").toFile();
            if (!thumbFile.exists()) {
                logger.debug("缩略图文件不存在: {}", thumbFile.getAbsolutePath());
                return false;
            }
            
            logger.debug("图片文件检查通过: 缩略图存在");
            return true;
            
        } catch (Exception e) {
            logger.error("检查图片文件时发生异常: sliceId={}, error={}", sliceEntity.getSliceId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 重新处理切片图片
     * 调用切片解析服务重新生成缩略图、标签图等
     */
    private boolean reprocessSliceImages(SliceEntity sliceEntity) {
        try {
            logger.info("开始重新处理切片图片: id={}, physicPath={}", sliceEntity.getId(), sliceEntity.getPhysicPath());
            
            // 创建一个模拟的MultipartFile来调用现有的切片处理逻辑
            Path sliceFilePath = Paths.get(storageConfig.getLocalSliceDir(), sliceEntity.getPhysicPath());
            File sliceFile = sliceFilePath.toFile();
            
            if (!sliceFile.exists()) {
                logger.error("切片文件不存在，无法重新处理: {}", sliceFilePath);
                return false;
            }
            
            // 使用现有的切片解析服务重新处理
            // 注意：这里使用主键ID作为taskId，因为我们已经有了数据库记录
            Result<?> result = sliceUploadService.asyncUploadAndAnalyzeSlice(
                createMultipartFileFromPath(sliceFile, sliceEntity.getQpName()), 
                sliceEntity.getId(), 
                sliceEntity.getPartnerCode()
            );
            
            if (result.isSuccess()) {
                logger.info("切片图片重新处理成功: id={}", sliceEntity.getId());
                return true;
            } else {
                logger.error("切片图片重新处理失败: id={}, error={}", sliceEntity.getId(), result.getMessage());
                return false;
            }
            
        } catch (Exception e) {
            logger.error("重新处理切片图片时发生异常: id={}, error={}", sliceEntity.getId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 从文件路径创建MultipartFile对象
     * 用于复用现有的切片处理逻辑
     */
    private MultipartFile createMultipartFileFromPath(File file, String originalFilename) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return "file";
            }
            
            @Override
            public String getOriginalFilename() {
                return originalFilename != null ? originalFilename : file.getName();
            }
            
            @Override
            public String getContentType() {
                return "application/octet-stream";
            }
            
            @Override
            public boolean isEmpty() {
                return !file.exists() || file.length() == 0;
            }
            
            @Override
            public long getSize() {
                return file.length();
            }
            
            @Override
            public byte[] getBytes() throws IOException {
                return Files.readAllBytes(file.toPath());
            }
            
            @Override
            public InputStream getInputStream() throws IOException {
                return new FileInputStream(file);
            }
            
            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {
                Files.copy(file.toPath(), dest.toPath(), StandardCopyOption.REPLACE_EXISTING);
            }
        };
    }
}

