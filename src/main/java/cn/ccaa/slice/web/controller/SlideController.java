package cn.ccaa.slice.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.ccaa.slice.core.constant.ApiConstants;
import cn.ccaa.slice.core.constant.ResultCode;
import cn.ccaa.slice.core.exception.SlideException;
import cn.ccaa.slice.core.result.Result;
import cn.ccaa.slice.parsers.openslide.OpenSlideParserAdapter;
import cn.ccaa.slice.config.StorageConfig;
import cn.ccaa.slice.service.FilePathService;
import cn.ccaa.slice.service.UnifiedSlideService;
import cn.ccaa.slice.service.storage.StorageService;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Objects;
import java.util.function.Function;

/**
 * 统一切片控制器
 * 提供对不同类型切片文件的HTTP访问接口
 * <AUTHOR>
 */
@RestController
@RequestMapping(ApiConstants.Base.SLIDES)
@Slf4j
public class SlideController {

    private static final int MAX_REGION_SIZE = ApiConstants.Defaults.MAX_REGION_SIZE;

    @Autowired
    private UnifiedSlideService unifiedSlideService;

    @Autowired
    private StorageService storageService;

    @Autowired
    private FilePathService filePathService;

    @Autowired
    private StorageConfig storageConfig;

    /**
     * 检测文件格式
     * 简化版本，使用更清晰的错误处理和日志记录
     *
     * @param filePath 文件路径
     * @param deepCheck 是否进行深度检查，默认为false
     * @return 文件格式信息
     */
    @GetMapping(ApiConstants.SlideEndpoints.DETECT)
    public Result<Map<String, Object>> detectFormat(
            @RequestParam String filePath,
            @RequestParam(required = false, defaultValue = "false") boolean deepCheck) {
        log.info("接收到检测文件格式请求: {}, 深度检查: {}", filePath, deepCheck);

        try {
            // 准备文件路径
            String normalizedPath = Optional.ofNullable(filePath).map(String::trim).orElse("");
            FilePathInfo pathInfo = extractFilePathInfo(normalizedPath);
            String localPath = getLocalFilePath(normalizedPath);

            // 检测格式
            String format = unifiedSlideService.detectFormat(localPath);
            log.info("文件格式检测结果: {}", format);

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("format", format);
            
            boolean supported = format != null;
            response.put("supported", supported);

            // 深度检查（如果需要）
            if (supported && (deepCheck || "openslide".equals(format))) {
                response.put("fileValid", validateFileUsability(localPath, format));
            }

            // 添加路径信息
            Optional.ofNullable(pathInfo.getTaskId()).ifPresent(id -> response.put("taskId", id));
            Optional.ofNullable(pathInfo.getFileName()).ifPresent(name -> response.put("fileName", name));

            return Result.success(response);
        } catch (Exception e) {
            log.error("检测文件格式出错: {}", e.getMessage(), e);
            return Result.failure(ResultCode.FILE_FORMAT_ERROR, "检测文件格式失败，请确认文件有效性");
        }
    }

    /**
     * 获取本地文件路径
     *
     * @param filePath 原始文件路径
     * @return 本地文件路径
     */
    private String getLocalFilePath(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return null;
        }

        String normalizedPath = filePath.trim();

        // 检查是否已经是本地路径
        if (new File(normalizedPath).exists()) {
            return normalizedPath;
        }

        // 构建完整的Minio路径
        String uploadDir = storageConfig.getFile().getUploadDir();
        String minioPath = !normalizedPath.startsWith("/") && !normalizedPath.startsWith(uploadDir) 
            ? uploadDir + "/" + normalizedPath 
            : normalizedPath;
        
        // 直接使用FilePathService处理
        String localPath = filePathService.processFilePath(normalizedPath);
        
        // 如果处理结果不存在，尝试使用完整Minio路径
        if (localPath.equals(normalizedPath) && !new File(localPath).exists() && !minioPath.equals(normalizedPath)) {
            log.debug("尝试使用完整Minio路径: {}", minioPath);
            return minioPath;
        }

        return localPath;
    }

    /**
     * 验证文件可用性
     * 尝试实际打开文件并验证其可用性
     *
     * @param filePath 文件路径
     * @param format 文件格式
     * @return 如果文件可用返回true，否则返回false
     */
    private boolean validateFileUsability(String filePath, String format) {
        try {
            // 获取本地文件路径
            String localFilePath = getLocalFilePath(filePath);

            // 检查本地文件是否存在
            File localFile = new File(localFilePath);
            if (!localFile.exists() || !localFile.isFile()) {
                log.warn("本地文件不存在或不是有效文件: {}", localFilePath);
                return false;
            }

            // 针对不同格式的验证策略
            if ("openslide".equals(format)) {
                // 使用OpenSlide解析器进行深度检查
                return unifiedSlideService.getAllParsers().stream()
                        .filter(p -> "openslide".equals(p.getParserName()))
                        .findFirst()
                        .map(p -> {
                            if (p instanceof OpenSlideParserAdapter) {
                                return ((OpenSlideParserAdapter) p).deepCheckFile(localFilePath);
                            }
                            return false;
                        })
                        .orElse(false);
            } else if ("sqrayslide".equals(format)) {
                // SqraySlide只要能检测到格式就认为可用
                return true;
            }

            // 对于其他格式，尝试获取基本信息
            Map<String, Object> info = unifiedSlideService.getSlideInfo(localFilePath);
            return info != null && !info.isEmpty();
        } catch (Exception e) {
            log.warn("验证文件可用性失败: {}, 错误: {}", filePath, e.getMessage());
            return false;
        }
    }

    /**
     * 从文件路径中提取任务ID和文件名
     *
     * @param filePath 文件路径
     * @return 包含任务ID和文件名的对象
     */
    private FilePathInfo extractFilePathInfo(String filePath) {
        FilePathInfo info = new FilePathInfo();

        if (!filePath.contains("/")) {
            // 文件路径没有/，直接使用作为fileName
            info.setFileName(filePath);
            return info;
        }

        // 格式为 taskId/original_fileName
        int separatorIndex = filePath.indexOf("/");
        String taskId = filePath.substring(0, separatorIndex);
        String fileName = filePath.substring(separatorIndex + 1);

        // 如果fileName有original_前缀，去掉前缀
        if (fileName.startsWith("original_")) {
            fileName = fileName.substring("original_".length());
        }

        info.setTaskId(taskId);
        info.setFileName(fileName);

        return info;
    }

    /**
     * 获取切片基本信息
     *
     * @param filePath 文件路径
     * @return 切片信息
     */
    @GetMapping(ApiConstants.SlideEndpoints.INFO)
    public Result<Map<String, Object>> getSlideInfo(@RequestParam String filePath) {
        log.info("接收到获取切片信息请求: {}", filePath);
        return executeSlideOperation(filePath,
            unifiedSlideService::getSlideInfo,
            ResultCode.SLIDE_INFO_ERROR,
            "获取切片信息失败，系统内部错误");
    }

    /**
     * 获取切片层级信息
     *
     * @param filePath 文件路径
     * @param level 层级索引
     * @return 层级信息
     */
    @GetMapping({ApiConstants.SlideEndpoints.LEVEL, ApiConstants.SlideEndpoints.LEVEL + "/{level}"})
    public Result<Map<String, Object>> getLevelInfo(
            @RequestParam String filePath,
            @RequestParam(required = false, defaultValue = "0") @PathVariable(required = false) Integer level) {
        int effectiveLevel = level != null ? level : ApiConstants.Defaults.DEFAULT_LEVEL;
        return executeSlideOperation(filePath,
            path -> unifiedSlideService.getLevelInfo(path, effectiveLevel),
            ResultCode.LEVEL_INFO_ERROR,
            "获取层级信息失败，系统内部错误");
    }

    /**
     * 获取切片瓦片
     *
     * @param filePath 文件路径
     * @param x X坐标
     * @param y Y坐标
     * @param level 层级
     * @return 瓦片图像数据
     */
    @GetMapping(value = ApiConstants.SlideEndpoints.TILE, produces = MediaType.IMAGE_JPEG_VALUE)
    public ResponseEntity<byte[]> getTile(
            @RequestParam String filePath,
            @RequestParam int x,
            @RequestParam int y,
            @RequestParam(required = false, defaultValue = "0") int level) {
        try {
            FilePathResult pathResult = getValidFilePath(filePath);
            if (pathResult == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("文件不存在或不可访问".getBytes());
            }

            byte[] tileData = unifiedSlideService.getTileJpeg(pathResult.getPath(), x, y, level);
            return ResponseEntity.ok(tileData);
        } catch (SlideException e) {
            log.error("获取瓦片失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(e.getMessage().getBytes());
        } catch (Exception e) {
            log.error("获取瓦片失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage().getBytes());
        }
    }

    /**
     * 获取预处理图像（缩略图或标签图）
     *
     * @param filePath 文件路径
     * @param type 图像类型（缩略图或标签图）
     * @return 图像数据
     */
    private ResponseEntity<byte[]> getPreprocessedImage(String filePath, ImageType type) {
        try {
            if (Objects.isNull(filePath) || filePath.isEmpty()) {
                return ResponseEntity.badRequest().body(null);
            }

            filePath = filePath.trim();
            
            // 构建Minio中图像路径
            String minioImagePath = type == ImageType.THUMBNAIL
                ? "thumb_" + filePath + ".jpeg"
                : "label_" + filePath + ".jpeg";

            // 检查图像是否存在于Minio中
            if (storageService.fileExists(minioImagePath)) {
                return ResponseEntity.ok(storageService.downloadFile(minioImagePath));
            }

            // 如果Minio中不存在预解析的图像，则从源文件解析
            FilePathResult pathResult = getValidFilePath(filePath);
            if (pathResult == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
            }

            byte[] imageData = type == ImageType.THUMBNAIL
                ? unifiedSlideService.getThumbnailJpeg(pathResult.getPath())
                : unifiedSlideService.getLabelJpeg(pathResult.getPath());

            return ResponseEntity.ok(imageData);
        } catch (SlideException e) {
            log.error("获取{}失败: {}", type.getDescription(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        } catch (Exception e) {
            log.error("获取{}失败: {}", type.getDescription(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * 获取切片缩略图
     * 直接从Minio获取预解析好的缩略图，不再从源文件解析
     *
     * @param filePath 文件路径
     * @return 缩略图数据
     */
    @GetMapping(value = ApiConstants.SlideEndpoints.THUMBNAIL, produces = MediaType.IMAGE_JPEG_VALUE)
    public ResponseEntity<byte[]> getThumbnail(@RequestParam String filePath) {
        return getPreprocessedImage(filePath, ImageType.THUMBNAIL);
    }

    /**
     * 获取切片标签图
     * 直接从Minio获取预解析好的标签图，不再从源文件解析
     *
     * @param filePath 文件路径
     * @return 标签图数据
     */
    @GetMapping(value = ApiConstants.SlideEndpoints.LABEL, produces = MediaType.IMAGE_JPEG_VALUE)
    public ResponseEntity<byte[]> getLabel(@RequestParam String filePath) {
        return getPreprocessedImage(filePath, ImageType.LABEL);
    }

    /**
     * 获取切片元数据
     *
     * @param filePath 文件路径
     * @return 元数据信息
     */
    @GetMapping(ApiConstants.SlideEndpoints.METADATA)
    public Result<Map<String, Object>> getMetadata(@RequestParam String filePath) {
        return executeSlideOperation(filePath,
            unifiedSlideService::getMetadata,
            ResultCode.METADATA_ERROR,
            "获取元数据失败，系统内部错误");
    }

    /**
     * 获取区域图像
     *
     * @param filePath 文件路径
     * @param x 区域左上角X坐标
     * @param y 区域左上角Y坐标
     * @param width 区域宽度
     * @param height 区域高度
     * @param level 层级索引
     * @return 区域图像JPEG数据
     */
    @GetMapping(value = ApiConstants.SlideEndpoints.REGION, produces = MediaType.IMAGE_JPEG_VALUE)
    public ResponseEntity<byte[]> getRegion(
            @RequestParam String filePath,
            @RequestParam int x,
            @RequestParam int y,
            @RequestParam int width,
            @RequestParam int height,
            @RequestParam(defaultValue = "0") int level) {
        try {
            // 参数验证
            if (width <= 0 || height <= 0) {
                return ResponseEntity.badRequest().body("区域宽度和高度必须大于0".getBytes());
            }
            
            if (width > MAX_REGION_SIZE || height > MAX_REGION_SIZE) {
                return ResponseEntity.badRequest().body("区域尺寸过大，请减小尺寸或使用更高的层级".getBytes());
            }

            FilePathResult pathResult = getValidFilePath(filePath);
            if (pathResult == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("文件不存在或不可访问".getBytes());
            }

            byte[] regionData = unifiedSlideService.getRegionJpeg(pathResult.getPath(), x, y, width, height, level);
            return ResponseEntity.ok(regionData);
        } catch (SlideException e) {
            log.error("获取区域图像失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(e.getMessage().getBytes());
        } catch (Exception e) {
            log.error("获取区域图像失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage().getBytes());
        }
    }

    /**
     * 文件路径信息类
     */
    @lombok.Data
    private static class FilePathInfo {
        private String taskId;
        private String fileName;
    }

    /**
     * 图像类型枚举
     */
    private enum ImageType {
        THUMBNAIL("缩略图"),
        LABEL("标签图");

        private final String description;

        ImageType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 获取可用的文件路径
     * 尝试从缓存和存储服务中获取有效的文件路径
     *
     * @param filePath 原始文件路径
     * @return 文件路径和有效性的对象，如果文件不存在则返回null
     */
    private FilePathResult getValidFilePath(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return null;
        }

        // 获取本地文件路径
        String localPath = getLocalFilePath(filePath);
        
        // 检查本地文件是否存在
        File localFile = new File(localPath);
        if (localFile.exists() && localFile.isFile()) {
            return new FilePathResult(localPath, true);
        }

        // 构建Minio路径
        String uploadDir = storageConfig.getFile().getUploadDir();
        String minioPath = !filePath.startsWith("/") && !filePath.startsWith(uploadDir) 
            ? uploadDir + "/" + filePath 
            : filePath;
        
        // 如果包含路径分隔符，尝试在目录中查找匹配文件
        if (filePath.contains("/")) {
            String taskId = filePath.substring(0, filePath.indexOf("/"));
            String fileName = filePath.substring(filePath.indexOf("/") + 1);

            if (taskId != null && !taskId.isEmpty()) {
                String dirPath = uploadDir + "/" + taskId;
                try {
                    List<String> files = storageService.listFiles(dirPath);
                    
                    for (String file : files) {
                        String minioFileName = file.substring(file.lastIndexOf("/") + 1);
                        try {
                            String decodedFileName = java.net.URLDecoder.decode(minioFileName, "UTF-8");
                            
                            // 检查文件名匹配条件
                            boolean isMatch = fileName.equals(decodedFileName) || 
                                fileName.equals(minioFileName) ||
                                ("original_" + fileName).equals(decodedFileName) ||
                                ("original_" + fileName).equals(minioFileName) ||
                                (fileName.startsWith("original_") && fileName.substring("original_".length()).equals(decodedFileName));
                            
                            if (isMatch) {
                                // 下载文件
                                String downloadedPath = filePathService.processFilePath(file);
                                if (!downloadedPath.equals(file) && new File(downloadedPath).exists()) {
                                    return new FilePathResult(downloadedPath, true);
                                }
                                
                                return new FilePathResult(file, false);
                            }
                        } catch (Exception e) {
                            log.warn("处理文件名解码异常: {}", e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    log.warn("列出目录文件异常: {}", e.getMessage());
                }
            }
        }

        // 如果是相对路径，返回Minio路径
        if (!filePath.startsWith("/") && !new File(filePath).isAbsolute()) {
            return new FilePathResult(minioPath, false);
        }

        log.debug("本地文件不存在或不是有效文件: {}", localPath);
        return null;
    }

    /**
     * 文件路径结果类
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    private static class FilePathResult {
        private String path;
        private boolean exists;
    }

    /**
     * 通用的切片操作执行方法
     *
     * @param <T> 返回类型
     * @param filePath 文件路径
     * @param operation 操作函数
     * @param errorCode 错误码
     * @param systemErrorMsg 系统错误消息
     * @return 操作结果
     */
    private <T> Result<T> executeSlideOperation(String filePath, Function<String, T> operation, String errorCode, String systemErrorMsg) {
        try {
            if (filePath == null || filePath.trim().isEmpty()) {
                return Result.failure(ResultCode.PARAM_INVALID, "文件路径不能为空");
            }

            // 获取有效的文件路径
            FilePathResult pathResult = getValidFilePath(filePath);
            if (pathResult == null) {
                return Result.failure(errorCode, "文件不存在或不可访问: " + filePath);
            }

            // 执行操作
            T result = operation.apply(pathResult.getPath());
            return Result.success(result);
        } catch (SlideException e) {
            log.error("切片操作失败: {}", e.getMessage(), e);
            return Result.failure(errorCode, e.getMessage());
        } catch (Exception e) {
            log.error("系统异常: {}", e.getMessage(), e);
            return Result.failure(ResultCode.SYSTEM_ERROR, systemErrorMsg);
        }
    }
}
