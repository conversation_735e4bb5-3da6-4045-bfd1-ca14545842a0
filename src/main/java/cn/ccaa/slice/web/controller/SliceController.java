package cn.ccaa.slice.web.controller;

import java.util.Base64;
import java.util.Map;
import java.util.List;

import jakarta.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.ccaa.slice.core.constant.ApiConstants;
import cn.ccaa.slice.core.constant.ResultCode;
import cn.ccaa.slice.core.result.Result;
import cn.ccaa.slice.service.SliceService;
import cn.ccaa.slice.service.analysis.SliceAnalysisService;
import cn.ccaa.slice.web.dto.SliceCaptureRequestDTO;
import cn.ccaa.slice.web.dto.SliceCaptureResponseDTO;
import cn.ccaa.slice.web.dto.TCSliceCaptureResponseDTO;
import cn.ccaa.slice.web.dto.AICaptureBatchRequestDTO;
import cn.ccaa.slice.web.dto.AICaptureBatchResponseDTO;

/**
 * 切片控制器
 * 提供切片相关API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(ApiConstants.Base.SLICE)
public class SliceController {

    private static final Logger log = LoggerFactory.getLogger(SliceController.class);

    @Autowired
    private SliceAnalysisService sliceAnalysisService;

    @Autowired
    private SliceService sliceService;

    /**
     * 解析切片文件
     * 根据任务ID获取切片源文件并解析
     *
     * @param taskId 任务ID
     * @param crterId 创建者ID（可选）
     * @param crterName 创建者姓名（可选）
     * @return 解析结果
     */
    @PostMapping(ApiConstants.SliceEndpoints.ANALYZE)
    public Result<?> analyzeSlice(@RequestParam String taskId,
                                 @RequestParam(required = false) String crterId,
                                 @RequestParam(required = false) String crterName) {
        log.info("接收切片解析请求: taskId={}, crterId={}, crterName={}", 
                taskId, crterId, crterName);
                
        // 添加详细日志以跟踪参数
        log.info("切片解析请求参数详情 - taskId: [{}], crterId: [{}], crterName: [{}]", 
                taskId, crterId, crterName);

        try {
            log.info("执行切片解析: taskId={}, crterId={}, crterName={}", taskId, crterId, crterName);
            Result<?> result = sliceAnalysisService.asyncAnalyzeSliceByTaskId(taskId, crterId, crterName);
            
            log.info("切片解析请求处理完成: taskId={}, 结果状态={}", taskId, result.isSuccess() ? "成功" : "失败");
            return result;
        } catch (Exception e) {
            log.error("切片解析请求处理失败: {}", e.getMessage(), e);
            return Result.failure(ResultCode.SLIDE_ERROR, "切片解析失败，请检查文件格式或稍后重试");
        }
    }

    /**
     * 取消切片解析任务
     *
     * @param taskId 任务ID
     * @return 取消结果
     */
    @PostMapping(ApiConstants.SliceEndpoints.CANCEL)
    public Result<?> cancelAnalysis(@RequestParam String taskId) {
        log.info("接收取消切片解析请求: taskId={}", taskId);

        boolean canceled = sliceAnalysisService.cancelSliceAnalysis(taskId);

        if (canceled) {
            return Result.success(Map.of(
                "taskId", taskId,
                "canceled", true,
                "message", "切片解析任务已取消"
            ));
        } else {
            return Result.failure(ResultCode.TASK_NOT_FOUND, "无法取消切片解析任务，可能任务不存在或已完成");
        }
    }



    /**
     * 切片截图接口（JSON格式响应）
     * 根据传入的参数生成切片截图，返回JSON格式的响应，包含Base64编码的图片数据
     * 供其他Java服务调用解析
     *
     * @param request 截图请求参数
     * @return JSON格式的截图响应数据
     */
    @PostMapping(value = ApiConstants.SliceEndpoints.CAPTURE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<TCSliceCaptureResponseDTO> captureSliceJson(@RequestBody SliceCaptureRequestDTO request) {
        log.info("接收切片截图JSON请求: fileId={}, 功能选项={}", request.getFileId(), request.getFunctionOption());
        
        // 手动验证必要参数
        validateCaptureRequest(request);

        try {
            // 调用服务层处理截图逻辑
            SliceCaptureResponseDTO captureResult = sliceService.captureSlice(request);
            
            // 记录返回的数据大小
            log.info("服务层返回结果: 原图大小={} bytes, 缩略图大小={} bytes", 
                    captureResult.getCutImageOri() != null ? captureResult.getCutImageOri().length : 0,
                    captureResult.getCutImage() != null ? captureResult.getCutImage().length : 0);
            
            // 转换为JSON响应格式
            TCSliceCaptureResponseDTO jsonResponse = convertToJsonResponse(captureResult);
            
            log.info("切片截图JSON请求处理完成: fileId={}", request.getFileId());
            
            return Result.success(jsonResponse);
            
        } catch (Exception e) {
            log.error("切片截图JSON请求处理失败: fileId={}, error={}", request.getFileId(), e.getMessage(), e);
            return Result.failure(ResultCode.SLIDE_ERROR, "切片截图失败: " + e.getMessage());
        }
    }

    /**
     * 将截图响应结果转换为JSON格式
     * 
     * @param captureResult 截图结果
     * @return JSON响应格式
     */
    private TCSliceCaptureResponseDTO convertToJsonResponse(SliceCaptureResponseDTO captureResult) {
        TCSliceCaptureResponseDTO jsonResponse = new TCSliceCaptureResponseDTO();
        
        // 严格按照图2表格中的字段返回 - 只保留必要字段
        jsonResponse.setFileId(captureResult.getFileId());
        jsonResponse.setZoom(String.valueOf(captureResult.getZoom()));
        jsonResponse.setCsize(String.valueOf(captureResult.getCsize()));
        jsonResponse.setLevel(String.valueOf(captureResult.getLevel()));
        jsonResponse.setContent(captureResult.getContent());
        jsonResponse.setW(String.valueOf(captureResult.getW()));
        jsonResponse.setH(String.valueOf(captureResult.getH()));
        jsonResponse.setX(String.valueOf(captureResult.getX()));
        jsonResponse.setY(String.valueOf(captureResult.getY()));
        jsonResponse.setRotate(String.valueOf(captureResult.getRotate()));
        jsonResponse.setFunctionOption(captureResult.getFunctionOption());
        
        // 将图片数据转换为Base64编码
        if (captureResult.getCutImageOri() != null) {
            String base64Ori = Base64.getEncoder().encodeToString(captureResult.getCutImageOri());
            jsonResponse.setCutImageOri(base64Ori);
            log.info("原图Base64编码完成，原始大小: {} bytes, 编码后长度: {} 字符", 
                    captureResult.getCutImageOri().length, base64Ori.length());
        }
        
        if (captureResult.getCutImage() != null) {
            String base64Thumb = Base64.getEncoder().encodeToString(captureResult.getCutImage());
            jsonResponse.setCutImage(base64Thumb);
            log.info("缩略图Base64编码完成，原始大小: {} bytes, 编码后长度: {} 字符", 
                    captureResult.getCutImage().length, base64Thumb.length());
        }
        
        return jsonResponse;
    }



    /**
     * 手动验证截图请求参数
     * 
     * @param request 截图请求参数
     */
    private void validateCaptureRequest(SliceCaptureRequestDTO request) {
        if (request.getFileId() == null || request.getFileId().trim().isEmpty()) {
            throw new IllegalArgumentException("切片文件ID不能为空");
        }
        if (request.getZoom() == null) {
            throw new IllegalArgumentException("缩放倍率不能为空");
        }
        if (request.getCsize() == null) {
            throw new IllegalArgumentException("屏幕像素比不能为空");
        }
        if (request.getLevel() == null) {
            throw new IllegalArgumentException("层级不能为空");
        }
        if (request.getContent() == null || request.getContent().trim().isEmpty()) {
            throw new IllegalArgumentException("描述不能为空");
        }
        if (request.getW() == null) {
            throw new IllegalArgumentException("宽度不能为空");
        }
        if (request.getH() == null) {
            throw new IllegalArgumentException("高度不能为空");
        }
        if (request.getX() == null) {
            throw new IllegalArgumentException("X坐标不能为空");
        }
        if (request.getY() == null) {
            throw new IllegalArgumentException("Y坐标不能为空");
        }
        if (request.getRotate() == null) {
            throw new IllegalArgumentException("旋转角度不能为空");
        }
        if (request.getCoords() == null || request.getCoords().isEmpty()) {
            throw new IllegalArgumentException("四点坐标不能为空");
        }
        if (request.getFunctionOption() == null || request.getFunctionOption().trim().isEmpty()) {
            throw new IllegalArgumentException("功能选项不能为空");
        }
        
        // 验证坐标点
        for (SliceCaptureRequestDTO.Coordinate coord : request.getCoords()) {
            if (coord.getX() == null) {
                throw new IllegalArgumentException("坐标X不能为空");
            }
            if (coord.getY() == null) {
                throw new IllegalArgumentException("坐标Y不能为空");
            }
        }
    }

    /**
     * AI批量截图接口
     * 根据AI返回坐标生成图片
     *
     * @param requests 批量截图请求参数列表
     * @return 批量截图响应数据
     */
    @PostMapping(value = ApiConstants.SliceEndpoints.AI_CAPTURE_BATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public AICaptureBatchResponseDTO saveAIQpCaptureBatch(@Valid @RequestBody List<AICaptureBatchRequestDTO> requests) {
        log.info("接收AI批量截图请求，批次数量: {}", requests.size());
        
        // 参数验证
        if (requests == null || requests.isEmpty()) {
            return AICaptureBatchResponseDTO.builder()
                    .status("error")
                    .data(List.of())
                    .msg("请求参数为空")
                    .build();
        }
        
        // 限制批次大小，防止内存溢出
        if (requests.size() > 100) {
            return AICaptureBatchResponseDTO.builder()
                    .status("error")
                    .data(List.of())
                    .msg("批次数量超过限制，最大支持100个")
                    .build();
        }
        
        try {
            // 调用服务层处理批量截图
            AICaptureBatchResponseDTO response = sliceService.captureAIBatch(requests);
            
            log.info("AI批量截图请求处理完成: 状态={}, 结果数量={}", 
                    response.getStatus(), response.getData().size());
            
            return response;
            
        } catch (Exception e) {
            log.error("AI批量截图请求处理失败: error={}", e.getMessage(), e);
            return AICaptureBatchResponseDTO.builder()
                    .status("error")
                    .data(List.of())
                    .msg("系统内部错误: " + e.getMessage())
                    .build();
        }
    }


}
