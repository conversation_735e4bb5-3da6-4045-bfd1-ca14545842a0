package cn.ccaa.slice.web.controller.admin;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.ccaa.slice.core.result.Result;
import cn.ccaa.slice.task.FileCacheCleanupTask;
import cn.ccaa.slice.core.constant.ApiConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件缓存控制器
 * 提供文件缓存管理接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(ApiConstants.Base.ADMIN_FILE_CACHE)
@Slf4j
public class FileCacheController {

    @Autowired
    private FileCacheCleanupTask fileCacheCleanupTask;

    /**
     * 清理文件缓存
     *
     * @param type 清理类型：cache - 只清理缓存目录，temp - 只清理临时目录，all - 清理所有
     * @param hours 保留时间（小时），为0时使用配置的默认值
     * @return 清理结果
     */
    @PostMapping("/cleanup")
    public Result<String> cleanupFileCache(
            @RequestParam(defaultValue = "all") String type,
            @RequestParam(defaultValue = "0") int hours) {

        log.info("手动触发文件缓存清理: 类型={}, 保留时间={}小时", type, hours);

        try {
            if ("cache".equalsIgnoreCase(type) || "all".equalsIgnoreCase(type)) {
                fileCacheCleanupTask.cleanupCacheDirectory(hours);
            }

            if ("temp".equalsIgnoreCase(type) || "all".equalsIgnoreCase(type)) {
                fileCacheCleanupTask.cleanupTempDirectoryByConfig();
            }

            return Result.success("文件缓存清理成功");
        } catch (Exception e) {
            log.error("文件缓存清理失败: {}", e.getMessage(), e);
            return Result.failure("文件缓存清理失败: " + e.getMessage());
        }
    }
}
