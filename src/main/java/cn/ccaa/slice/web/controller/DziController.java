package cn.ccaa.slice.web.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.ccaa.slice.core.constant.ApiConstants;
import cn.ccaa.slice.core.exception.SlideException;
import cn.ccaa.slice.core.model.ColorCorrectionParams;
import cn.ccaa.slice.service.dzi.DziService;
import cn.ccaa.slice.service.dzi.impl.DziServiceImpl;
import lombok.extern.slf4j.Slf4j;

/**
 * DZI控制器
 * 提供OpenSeadragon所需的DZI(Deep Zoom Image)接口
 * <AUTHOR>
 */
@RestController
@RequestMapping(ApiConstants.Base.DZI)
@Slf4j
@CrossOrigin(origins = "*")
public class DziController {

    @Autowired
    private DziService dziService;
    
    @Autowired 
    private DziServiceImpl dziServiceImpl; // 用于访问统计信息
    
    /**
     * 响应缓存 - 用于高频请求的短期缓存
     * 在控制器层缓存DZI描述文件和最关键层级(0级)的瓦片响应
     */
    private static final Map<String, CachedResponse> RESPONSE_CACHE = new ConcurrentHashMap<>(1000);
    
    /**
     * 缓存的响应对象
     */
    private static class CachedResponse {
        private final byte[] data;
        private final String etag;
        private final long timestamp;
        private final String contentType;
        
        public CachedResponse(byte[] data, String etag, String contentType) {
            this.data = data;
            this.etag = etag;
            this.timestamp = System.currentTimeMillis();
            this.contentType = contentType;
        }
        
        public boolean isExpired(long ttlMillis) {
            return System.currentTimeMillis() - timestamp > ttlMillis;
        }
    }
    
    /**
     * 定期清理过期的缓存条目
     * 每10分钟执行一次，清理超过24小时的缓存
     */
    private void cleanupCache() {
        final long ttl = TimeUnit.HOURS.toMillis(24);
        
        for (Map.Entry<String, CachedResponse> entry : RESPONSE_CACHE.entrySet()) {
            if (entry.getValue().isExpired(ttl)) {
                RESPONSE_CACHE.remove(entry.getKey());
            }
        }
    }
    
    /**
     * 获取瓦片大小优化统计信息
     * 用于监控30KB优化目标的效果
     * 
     * @return 统计信息
     */
    @GetMapping("/stats/tile-size")
    public ResponseEntity<Map<String, Object>> getTileSizeStats() {
        try {
            String stats = dziServiceImpl.getTileSizeStats();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("stats", stats);
            response.put("targetSizeKB", 30);
            response.put("message", "瓦片大小优化统计");
            
            return ResponseEntity.ok()
                .cacheControl(CacheControl.noCache())
                .body(response);
        } catch (Exception e) {
            log.error("获取瓦片统计失败: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(errorResponse);
        }
    }

    /**
     * 获取系统内存使用情况
     * 用于监控内存状态，避免内存不足
     * 
     * @return 内存使用情况
     */
    @GetMapping("/stats/memory")
    public ResponseEntity<Map<String, Object>> getMemoryStats() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            Map<String, Object> memoryStats = new HashMap<>();
            memoryStats.put("maxMemoryMB", maxMemory / (1024 * 1024));
            memoryStats.put("totalMemoryMB", totalMemory / (1024 * 1024));
            memoryStats.put("usedMemoryMB", usedMemory / (1024 * 1024));
            memoryStats.put("freeMemoryMB", freeMemory / (1024 * 1024));
            memoryStats.put("availableMemoryMB", (maxMemory - usedMemory) / (1024 * 1024));
            memoryStats.put("memoryUsagePercent", Math.round(((double) usedMemory / maxMemory) * 100));
            
            // 添加缓存统计
            if (dziServiceImpl != null) {
                try {
                    // 通过反射获取缓存大小（因为tileCache字段是private）
                    java.lang.reflect.Field tileCacheField = dziServiceImpl.getClass().getDeclaredField("tileCache");
                    tileCacheField.setAccessible(true);
                    @SuppressWarnings("unchecked")
                    Map<String, byte[]> tileCache = (Map<String, byte[]>) tileCacheField.get(dziServiceImpl);
                    
                    memoryStats.put("tileCacheSize", tileCache.size());
                    
                    // 估算缓存占用内存
                    long cacheMemoryUsage = 0;
                    int sampleCount = 0;
                    for (Map.Entry<String, byte[]> entry : tileCache.entrySet()) {
                        if (entry.getValue() != null) {
                            cacheMemoryUsage += entry.getValue().length;
                            sampleCount++;
                        }
                        if (sampleCount > 100) break; // 只采样100个以避免性能影响
                    }
                    
                    if (sampleCount > 0) {
                        long estimatedTotalCacheMemory = (cacheMemoryUsage / sampleCount) * tileCache.size();
                        memoryStats.put("estimatedCacheMemoryMB", estimatedTotalCacheMemory / (1024 * 1024));
                    }
                    
                } catch (Exception e) {
                    log.debug("获取缓存统计失败: {}", e.getMessage());
                    memoryStats.put("tileCacheSize", "无法获取");
                }
            }
            
            memoryStats.put("success", true);
            memoryStats.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok()
                .cacheControl(CacheControl.noCache())
                .body(memoryStats);
                
        } catch (Exception e) {
            log.error("获取内存统计失败: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(errorResponse);
        }
    }

    /**
     * 获取DZI描述文件
     * OpenSeadragon需要的图像元数据信息
     * 
     * @param identifier 切片标识符，通常是taskId
     * @param cname 切片名称（必填参数，对应数据库 partner_code 字段）
     * @param brightness 亮度调整 (-1.0 到 1.0，可选)
     * @param contrast 对比度调整 (0.0 到 2.0，可选)
     * @param gamma 伽马校正 (0.1 到 3.0，可选)
     * @param saturation 饱和度调整 (0.0 到 2.0，可选)
     * @param hue 色调调整 (-180 到 180度，可选)
     * @param colorStyle 颜色风格 (0=默认，1=H&E标准化，可选)
     * @return DZI描述文件（XML格式）
     */
    @GetMapping(value = ApiConstants.DziEndpoints.DESCRIPTOR, produces = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> getDziDescriptor(
            @PathVariable String identifier,
            @RequestParam(value = "cname") String cname,
            @RequestParam(value = "brightness", defaultValue = "0.0") Float brightness,
            @RequestParam(value = "contrast", defaultValue = "1.0") Float contrast,
            @RequestParam(value = "gamma", defaultValue = "1.0") Float gamma,
            @RequestParam(value = "saturation", defaultValue = "1.0") Float saturation,
            @RequestParam(value = "hue", defaultValue = "0.0") Float hue,
            @RequestParam(value = "redGain", defaultValue = "1.0") Float redGain,
            @RequestParam(value = "greenGain", defaultValue = "1.0") Float greenGain,
            @RequestParam(value = "blueGain", defaultValue = "1.0") Float blueGain,
            @RequestParam(value = "sharpen", defaultValue = "0") Integer sharpen,
            @RequestParam(value = "colorStyle", defaultValue = "0") Integer colorStyle,
            @RequestHeader(value = "If-None-Match", required = false) String ifNoneMatch,
            @RequestHeader(value = "If-Modified-Since", required = false) String ifModifiedSince) {
        
        try {
            // 检查是否使用了颜色参数
            boolean hasColorParams = !brightness.equals(0.0f) || !contrast.equals(1.0f) || 
                                   !gamma.equals(1.0f) || !saturation.equals(1.0f) || 
                                   !hue.equals(0.0f) || !redGain.equals(1.0f) || 
                                   !greenGain.equals(1.0f) || !blueGain.equals(1.0f) || 
                                   !sharpen.equals(0) || !colorStyle.equals(0);
            
            // 构建缓存键，包含 cname 参数
            String cacheKey = hasColorParams ? 
                String.format("dzi_%s_%s_c_%.2f_%.2f_%.2f_%.2f_%.2f_%.2f_%.2f_%.2f_%d_%d", 
                    identifier, cname, brightness, contrast, gamma, saturation, hue, 
                    redGain, greenGain, blueGain, sharpen, colorStyle) :
                "dzi_" + identifier + "_" + cname;
            
            // 检查是否有缓存的响应
            CachedResponse cachedResponse = RESPONSE_CACHE.get(cacheKey);
            if (cachedResponse != null) {
                // 如果请求提供了ETag并匹配，则返回304 Not Modified
                if (ifNoneMatch != null && ifNoneMatch.equals(cachedResponse.etag)) {
                    return ResponseEntity.status(HttpStatus.NOT_MODIFIED)
                            .header("ETag", cachedResponse.etag)
                            .header("Cache-Control", "public, max-age=86400, immutable")
                            .header("Access-Control-Allow-Origin", "*")
                            .build();
                }
                
                // 返回缓存的XML数据
                String cachedXml = new String(cachedResponse.data);
                
                return ResponseEntity.ok()
                        .contentType(MediaType.APPLICATION_XML)
                        .header("Access-Control-Allow-Origin", "*")
                        .header("Access-Control-Allow-Methods", "GET, OPTIONS")
                        .header("Access-Control-Allow-Headers", "Content-Type, If-None-Match, If-Modified-Since")
                        .header("Cache-Control", "public, max-age=86400, immutable")
                        .header("ETag", cachedResponse.etag)
                        .header("Content-Length", String.valueOf(cachedResponse.data.length))
                        .body(cachedXml);
            }
            
            // 调用服务层获取DZI描述文件，传递 cname 参数
            String dziXml = dziService.getDziDescriptor(identifier, cname);
            if (dziXml == null || dziXml.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            // 添加调试日志
            log.debug("生成的DZI XML内容: {}", dziXml);
            
            // 计算更强的ETag，提高缓存命中率
            String etag = "\"" + identifier + "_" + cname + "_" + Integer.toHexString(dziXml.hashCode()) + "\"";
            
            // 将XML响应添加到缓存
            RESPONSE_CACHE.put(cacheKey, new CachedResponse(
                    dziXml.getBytes(), 
                    etag, 
                    MediaType.APPLICATION_XML_VALUE
            ));
            
            // 响应处理完成后异步清理过期缓存
            new Thread(this::cleanupCache).start();
            
            ResponseEntity<String> response = ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_XML)
                    .header("Access-Control-Allow-Origin", "*")
                    .header("Access-Control-Allow-Methods", "GET, OPTIONS")
                    .header("Access-Control-Allow-Headers", "Content-Type, If-None-Match, If-Modified-Since")
                    .header("Cache-Control", "public, max-age=86400, immutable") // 一周缓存且不重验证
                    .header("ETag", etag)
                    .header("Vary", "Accept-Encoding")
                    .header("Content-Length", String.valueOf(dziXml.getBytes().length))
                    .header("Expires", java.time.format.DateTimeFormatter.RFC_1123_DATE_TIME.format(
                            java.time.ZonedDateTime.now(java.time.ZoneId.of("GMT")).plusDays(7)))
                    .body(dziXml);
            
            return response;
        } catch (SlideException e) {
            log.error("获取DZI描述文件失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(e.getMessage());
        } catch (Exception e) {
            log.error("获取DZI描述文件失败，系统内部错误: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("系统内部错误");
        }
    }

    /**
     * 获取DZI瓦片
     * 处理OpenSeadragon的瓦片请求格式: /api/dzi/{identifier}_files/{level}/{x}_{y}.jpg
     */
    @GetMapping(value = ApiConstants.DziEndpoints.TILE, produces = MediaType.IMAGE_JPEG_VALUE)
    @ResponseBody
    public ResponseEntity<byte[]> getDziTile(
            @PathVariable String identifier,
            @PathVariable Integer level,
            @PathVariable Integer x,
            @PathVariable Integer y,
            @RequestParam(value = "brightness", defaultValue = "0.0") Float brightness,
            @RequestParam(value = "contrast", defaultValue = "1.0") Float contrast,
            @RequestParam(value = "gamma", defaultValue = "1.0") Float gamma,
            @RequestParam(value = "saturation", defaultValue = "1.0") Float saturation,
            @RequestParam(value = "hue", defaultValue = "0.0") Float hue,
            @RequestParam(value = "redGain", defaultValue = "1.0") Float redGain,
            @RequestParam(value = "greenGain", defaultValue = "1.0") Float greenGain,
            @RequestParam(value = "blueGain", defaultValue = "1.0") Float blueGain,
            @RequestParam(value = "sharpen", defaultValue = "0") Integer sharpen,
            @RequestParam(value = "colorStyle", defaultValue = "0") Integer colorStyle,
            @RequestHeader(value = "If-None-Match", required = false) String ifNoneMatch,
            @RequestHeader(value = "If-Modified-Since", required = false) String ifModifiedSince) {
        
        // 只对DEBUG级别记录详细日志，减少I/O开销
        if (log.isDebugEnabled()) {
            log.debug("接收到DZI瓦片请求: {}, 层级: {}, 坐标: ({}, {})", identifier, level, x, y);
        }
        
        try {
            // 检查是否使用了颜色参数（更严格的判断）
            boolean hasColorParams = !brightness.equals(0.0f) || !contrast.equals(1.0f) || 
                                   !gamma.equals(1.0f) || !saturation.equals(1.0f) || 
                                   !hue.equals(0.0f) || !redGain.equals(1.0f) || 
                                   !greenGain.equals(1.0f) || !blueGain.equals(1.0f) || 
                                   !sharpen.equals(0) || !colorStyle.equals(0);
            
            // 构建统一缓存键，确保无论色彩参数如何，坐标锁定都保持一致
            String cacheKey = String.format("tile_unified_%s_%d_%d_%d", identifier, level, x, y);
            
            // 检查是否有缓存的响应
            if (level <= 2) { // 只对最常用的低层级瓦片进行缓存
                CachedResponse cachedResponse = RESPONSE_CACHE.get(cacheKey);
                if (cachedResponse != null) {
                    // 如果请求提供了ETag并匹配，则返回304 Not Modified
                    if (ifNoneMatch != null && ifNoneMatch.equals(cachedResponse.etag)) {
                        return ResponseEntity.status(HttpStatus.NOT_MODIFIED)
                                .header("ETag", cachedResponse.etag)
                                .header("Cache-Control", "public, max-age=86400, immutable")
                                .header("Access-Control-Allow-Origin", "*")
                                .build();
                    }
                    
                    // 返回缓存的瓦片数据
                    return ResponseEntity.ok()
                            .contentType(MediaType.IMAGE_JPEG)
                            .header("Access-Control-Allow-Origin", "*")
                            .header("Access-Control-Allow-Methods", "GET, OPTIONS")
                            .header("Access-Control-Allow-Headers", "Content-Type, If-None-Match, If-Modified-Since")
                            .header("Cache-Control", "public, max-age=86400, immutable")
                            .header("ETag", cachedResponse.etag)
                            .header("Content-Length", String.valueOf(cachedResponse.data.length))
                            .body(cachedResponse.data);
                }
            }
            
            // 调用服务层获取瓦片
            byte[] tileData;
            if (hasColorParams) {
                // 创建颜色校正参数对象
                ColorCorrectionParams colorParams = new ColorCorrectionParams();
                colorParams.setBrightness(brightness);
                colorParams.setContrast(contrast);
                colorParams.setGamma(gamma);
                colorParams.setSaturation(saturation);
                colorParams.setHue(hue);
                colorParams.setRedGain(redGain);
                colorParams.setGreenGain(greenGain);
                colorParams.setBlueGain(blueGain);
                colorParams.setSharpen(sharpen);
                colorParams.setColorStyle(colorStyle);
                
                // 验证参数范围
                colorParams.validate();
                
                // 使用带颜色校正的方法获取瓦片
                tileData = dziService.getDziTileWithColor(identifier, level, x, y, colorParams);
                
                // 添加调试日志
                if (log.isDebugEnabled()) {
                    log.debug("使用颜色校正获取瓦片: 标识符={}, 层级={}, 坐标=({}, {}), 颜色参数=亮度:{}, 对比度:{}, 伽马:{}, 饱和度:{}, 色调:{}", 
                             identifier, level, x, y, brightness, contrast, gamma, saturation, hue);
                }
            } else {
                // 修复方案：当没有颜色参数时，直接调用普通瓦片获取，避免不必要的颜色处理
                tileData = dziService.getDziTile(identifier, level, x, y);
                
                if (log.isDebugEnabled()) {
                    log.debug("使用普通瓦片获取: 标识符={}, 层级={}, 坐标=({}, {})", 
                             identifier, level, x, y);
                }
            }
            
            if (tileData == null || tileData.length == 0) {
                if (log.isDebugEnabled()) {
                    log.debug("未获取到瓦片数据: {}, 层级: {}, 坐标: ({}, {})", 
                             identifier, level, x, y);
                }
                
                // 尝试获取父级瓦片作为回退（向上一级查找）
                if (level > 0) {
                    if (log.isDebugEnabled()) {
                        log.debug("尝试获取上一级瓦片作为替代: {}, 层级: {}, 坐标: ({}, {})", 
                                identifier, level-1, x/2, y/2);
                    }
                    byte[] parentTileData = dziService.getDziTile(identifier, level-1, x/2, y/2);
                    if (parentTileData != null && parentTileData.length > 0) {
                        // 将回退瓦片添加到缓存以提高后续访问速度
                        String fallbackEtag = "\"fb_" + identifier + "_" + level + "_" + x + "_" + y + "\"";
                        
                        // 添加到控制器层缓存
                        if (level <= 2) {
                            RESPONSE_CACHE.put(cacheKey, new CachedResponse(
                                    parentTileData,
                                    fallbackEtag,
                                    MediaType.IMAGE_JPEG_VALUE
                            ));
                        }
                        
                        // 添加替代响应标记
                        return ResponseEntity.ok()
                                .contentType(MediaType.IMAGE_JPEG)
                                .contentLength(parentTileData.length)
                                .header("Access-Control-Allow-Origin", "*")
                                .header("Access-Control-Allow-Methods", "GET, OPTIONS")
                                .header("Access-Control-Allow-Headers", "Content-Type, If-None-Match, If-Modified-Since")
                                .header("Cache-Control", "public, max-age=3600, must-revalidate") // 短期缓存，因为是替代内容
                                .header("X-Tile-Substitution", "parent-level")
                                .header("Content-Length", String.valueOf(parentTileData.length))
                                .header("ETag", fallbackEtag)
                                .body(parentTileData);
                    }
                }
                
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .header("Access-Control-Allow-Origin", "*")
                        .header("Cache-Control", "no-cache")
                        .build();
            }
            
            // 生成ETag
            String etagValue = "\"" + identifier + "_" + level + "_" + x + "_" + y + "_" + 
                    Integer.toHexString(java.util.Arrays.hashCode(tileData)) + "\"";
            
            // 将成功获取的低层级瓦片数据添加到控制器缓存
            if (level <= 2) {
                RESPONSE_CACHE.put(cacheKey, new CachedResponse(
                        tileData,
                        etagValue,
                        MediaType.IMAGE_JPEG_VALUE
                ));
            }
            
            // 大于70KB的瓦片设为一小时缓存（高分辨率瓦片），否则设为一天（低分辨率瓦片）
            String cacheControl = tileData.length > 70 * 1024 
                    ? "public, max-age=3600" 
                    : "public, max-age=86400, immutable";
            
            return ResponseEntity.ok()
                    .contentType(MediaType.IMAGE_JPEG)
                    .contentLength(tileData.length)
                    .header("Access-Control-Allow-Origin", "*")
                    .header("Access-Control-Allow-Methods", "GET, OPTIONS")
                    .header("Access-Control-Allow-Headers", "Content-Type, If-None-Match, If-Modified-Since")
                    .header("Cache-Control", cacheControl)
                    .header("Content-Length", String.valueOf(tileData.length))
                    .header("ETag", etagValue)
                    .body(tileData);
                    
        } catch (SlideException e) {
            // 只在DEBUG级别记录详细错误信息，减少日志噪音
            if (log.isDebugEnabled()) {
                log.debug("获取DZI瓦片失败: {}, 层级: {}, 坐标: ({}, {}), 错误: {}", 
                         identifier, level, x, y, e.getMessage());
            }
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .header("Access-Control-Allow-Origin", "*")
                    .header("Cache-Control", "no-store")
                    .build();
        } catch (Exception e) {
            // 系统错误仍然记录为ERROR级别
            log.error("获取DZI瓦片失败，系统内部错误: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header("Access-Control-Allow-Origin", "*")
                    .header("Cache-Control", "no-store")
                    .build();
        }
    }
    
    /**
     * 获取与DZI视图一致的缩略图
     * 这个方法确保缩略图与DZI查看器中的图像方向一致
     *
     * @param identifier 切片标识符
     * @return 缩略图数据
     */
    @GetMapping(value = "/thumbnail/{identifier}.jpg", produces = MediaType.IMAGE_JPEG_VALUE)
    @ResponseBody
    public ResponseEntity<byte[]> getDziThumbnail(
            @PathVariable String identifier,
            @RequestHeader(value = "If-None-Match", required = false) String ifNoneMatch) {
        
        try {
            // 构建缓存键
            String cacheKey = "thumb_" + identifier;
            
            // 检查是否有缓存的响应
            CachedResponse cachedResponse = RESPONSE_CACHE.get(cacheKey);
            if (cachedResponse != null) {
                // 如果请求提供了ETag并匹配，则返回304 Not Modified
                if (ifNoneMatch != null && ifNoneMatch.equals(cachedResponse.etag)) {
                    return ResponseEntity.status(HttpStatus.NOT_MODIFIED)
                            .header("ETag", cachedResponse.etag)
                            .header("Cache-Control", "public, max-age=86400, immutable")
                            .header("Access-Control-Allow-Origin", "*")
                            .build();
                }
                
                // 返回缓存的缩略图数据
                return ResponseEntity.ok()
                        .contentType(MediaType.IMAGE_JPEG)
                        .header("Access-Control-Allow-Origin", "*")
                        .header("Cache-Control", "public, max-age=86400, immutable")
                        .header("ETag", cachedResponse.etag)
                        .header("Content-Length", String.valueOf(cachedResponse.data.length))
                        .body(cachedResponse.data);
            }
            
            // 调用服务层获取与DZI一致的缩略图
            byte[] thumbnailData = dziService.getThumbnailForDzi(identifier);
            
            if (thumbnailData == null || thumbnailData.length == 0) {
                log.warn("未获取到DZI缩略图数据: {}", identifier);
                
                // 尝试获取0级瓦片作为缩略图，确保至少能显示一些内容
                byte[] fallbackTile = dziService.getDziTile(identifier, 0, 0, 0);
                if (fallbackTile != null && fallbackTile.length > 0) {
                    // 使用0级瓦片作为缩略图
                    String fallbackEtag = "\"thumbfb_" + identifier + "\"";
                    
                    // 添加到缓存
                    RESPONSE_CACHE.put(cacheKey, new CachedResponse(
                            fallbackTile,
                            fallbackEtag,
                            MediaType.IMAGE_JPEG_VALUE
                    ));
                    
                    return ResponseEntity.ok()
                            .contentType(MediaType.IMAGE_JPEG)
                            .header("Access-Control-Allow-Origin", "*")
                            .header("Cache-Control", "public, max-age=86400")
                            .header("ETag", fallbackEtag)
                            .header("X-Thumbnail-Substitution", "level0-tile")
                            .header("Content-Length", String.valueOf(fallbackTile.length))
                            .body(fallbackTile);
                }
                
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .header("Access-Control-Allow-Origin", "*")
                        .build();
            }
            
            // 计算ETag
            String etagValue = "\"thumb_" + identifier + "_" + 
                    Integer.toHexString(java.util.Arrays.hashCode(thumbnailData)) + "\"";
            
            // 添加到缓存
            RESPONSE_CACHE.put(cacheKey, new CachedResponse(
                    thumbnailData,
                    etagValue,
                    MediaType.IMAGE_JPEG_VALUE
            ));
            
            return ResponseEntity.ok()
                    .contentType(MediaType.IMAGE_JPEG)
                    .header("Access-Control-Allow-Origin", "*")
                    .header("Cache-Control", "public, max-age=86400, immutable")
                    .header("ETag", etagValue)
                    .header("Content-Length", String.valueOf(thumbnailData.length))
                    .header("Vary", "Accept-Encoding")
                    .body(thumbnailData);
            
        } catch (SlideException e) {
            log.error("获取DZI缩略图失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .header("Access-Control-Allow-Origin", "*")
                    .build();
        } catch (Exception e) {
            log.error("获取DZI缩略图失败，系统内部错误: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header("Access-Control-Allow-Origin", "*")
                    .build();
        }
    }
    
    /**
     * 停止指定标识符的异步任务 - 新增
     * 用于页面关闭时停止相关的预加载任务，减少不必要的资源消耗
     */
    @GetMapping("/stop-async/{identifier}")
    @ResponseBody
    public ResponseEntity<String> stopAsyncTasks(@PathVariable String identifier) {
        try {
            log.info("收到停止异步任务请求，标识符: {}", identifier);
            
            // 如果dziService是DziServiceImpl的实例，调用停止方法
            if (dziService instanceof DziServiceImpl) {
                ((DziServiceImpl) dziService).stopAsyncTasksForIdentifier(identifier);
                return ResponseEntity.ok()
                        .header("Access-Control-Allow-Origin", "*")
                        .body("已停止标识符 " + identifier + " 的异步任务");
            } else {
                log.warn("dziService 不是 DziServiceImpl 实例，无法停止异步任务");
                return ResponseEntity.ok()
                        .header("Access-Control-Allow-Origin", "*")
                        .body("无法停止异步任务：服务实例类型不匹配");
            }
        } catch (Exception e) {
            log.error("停止异步任务失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header("Access-Control-Allow-Origin", "*")
                    .body("停止异步任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 停止所有异步任务 - 新增
     * 用于系统维护或紧急情况下停止所有后台任务
     */
    @GetMapping("/stop-all-async")
    @ResponseBody
    public ResponseEntity<String> stopAllAsyncTasks() {
        try {
            log.info("收到停止所有异步任务请求");
            
            // 如果dziService是DziServiceImpl的实例，调用停止方法
            if (dziService instanceof DziServiceImpl) {
                ((DziServiceImpl) dziService).stopAllAsyncTasks();
                return ResponseEntity.ok()
                        .header("Access-Control-Allow-Origin", "*")
                        .body("已停止所有异步任务");
            } else {
                log.warn("dziService 不是 DziServiceImpl 实例，无法停止异步任务");
                return ResponseEntity.ok()
                        .header("Access-Control-Allow-Origin", "*")
                        .body("无法停止异步任务：服务实例类型不匹配");
            }
        } catch (Exception e) {
            log.error("停止所有异步任务失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header("Access-Control-Allow-Origin", "*")
                    .body("停止所有异步任务失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发垃圾回收和缓存清理
     * 紧急情况下释放内存
     * 
     * @return 清理结果
     */
    @PostMapping("/admin/cleanup")
    public ResponseEntity<Map<String, Object>> forceCleanup() {
        try {
            // 获取清理前的内存状态
            Runtime runtime = Runtime.getRuntime();
            long beforeUsed = runtime.totalMemory() - runtime.freeMemory();
            
            // 触发垃圾回收
            System.gc();
            Thread.sleep(100); // 等待GC完成
            
            // 获取清理后的内存状态
            long afterUsed = runtime.totalMemory() - runtime.freeMemory();
            long freedMemory = beforeUsed - afterUsed;
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("freedMemoryMB", freedMemory / (1024 * 1024));
            result.put("beforeUsedMB", beforeUsed / (1024 * 1024));
            result.put("afterUsedMB", afterUsed / (1024 * 1024));
            result.put("message", "内存清理完成");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("强制清理失败: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(errorResponse);
        }
    }

    /**
     * 导出切片图像
     * 根据切片ID导出整体的、完整的、清晰的切片图像，JPEG格式
     * 
     * @param identifier 切片标识符（切片ID）
     * @return 切片图像的JPEG格式二进制数据
     */
    @GetMapping(value = ApiConstants.DziEndpoints.EXPORT, produces = MediaType.IMAGE_JPEG_VALUE)
    @ResponseBody
    public ResponseEntity<byte[]> exportSliceImage(@PathVariable String identifier) {
        log.info("接收到切片导出请求: identifier={}", identifier);
        
        try {
            // 1. 根据ID获取切片文件路径
            String filePath = dziService.getFilePathByIdentifier(identifier);
            if (filePath == null || filePath.isEmpty()) {
                log.error("切片导出失败：无法获取文件路径，identifier={}", identifier);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("切片文件不存在".getBytes());
            }
            
            log.info("切片导出：获取到文件路径 identifier={}, filePath={}", identifier, filePath);
            
            // 2. 获取切片信息，选择合适的层级
            Map<String, Object> slideInfo = dziService.getSlideInfoFromPath(filePath);
            if (slideInfo == null) {
                log.error("切片导出失败：无法获取切片信息，identifier={}", identifier);
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("无法获取切片信息".getBytes());
            }
            
            // 3. 获取切片尺寸
            int width = ((Number) slideInfo.get("width")).intValue();
            int height = ((Number) slideInfo.get("height")).intValue();
            int levelCount = slideInfo.get("levelCount") != null ? 
                ((Number) slideInfo.get("levelCount")).intValue() : 1;
            
            log.info("切片导出：切片信息 identifier={}, 尺寸={}x{}, 层级数={}", 
                    identifier, width, height, levelCount);
            
            // 4. 选择合适的层级：平衡全貌展示和清晰度
            // 不是最高分辨率（level 0），而是能看到全貌又保证清晰度的中等层级
            int exportLevel = calculateOptimalExportLevel(width, height, levelCount);
            
            log.info("切片导出：选择导出层级 identifier={}, exportLevel={}", identifier, exportLevel);
            
            // 5. 获取该层级下的完整图像
            // 计算该层级的实际尺寸
            int levelWidth = width >> exportLevel;  // 除以2的exportLevel次方
            int levelHeight = height >> exportLevel;
            
            // 确保尺寸至少为1
            levelWidth = Math.max(1, levelWidth);
            levelHeight = Math.max(1, levelHeight);
            
            log.info("切片导出：层级{}的尺寸={}x{}", exportLevel, levelWidth, levelHeight);
            
            // 6. 读取整个切片在该层级下的完整图像
            byte[] imageData = dziService.getRegionJpegFromPath(filePath, 0, 0, 
                    levelWidth, levelHeight, exportLevel);
            
            if (imageData == null || imageData.length == 0) {
                log.error("切片导出失败：无法获取图像数据，identifier={}", identifier);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("无法获取切片图像数据".getBytes());
            }
            
            log.info("切片导出成功：identifier={}, 图像大小={}字节, 层级={}, 尺寸={}x{}", 
                    identifier, imageData.length, exportLevel, levelWidth, levelHeight);
            
            // 7. 返回JPEG格式的二进制数据
            return ResponseEntity.ok()
                    .contentType(MediaType.IMAGE_JPEG)
                    .contentLength(imageData.length)
                    .header("Access-Control-Allow-Origin", "*")
                    .header("Access-Control-Allow-Methods", "GET, OPTIONS")
                    .header("Content-Disposition", "attachment; filename=\"slice_" + identifier + ".jpg\"")
                    .body(imageData);
                    
        } catch (SlideException e) {
            log.error("切片导出失败：SlideException，identifier={}, error={}", identifier, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(("切片导出失败：" + e.getMessage()).getBytes());
        } catch (Exception e) {
            log.error("切片导出失败：系统异常，identifier={}, error={}", identifier, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("切片导出失败，系统内部错误".getBytes());
        }
    }
    
    /**
     * 计算最优的导出层级
     * 选择能平衡全貌展示和清晰度的层级，不是最高分辨率
     * 
     * @param width 切片宽度
     * @param height 切片高度
     * @param levelCount 总层级数
     * @return 最优导出层级
     */
    private int calculateOptimalExportLevel(int width, int height, int levelCount) {
        int maxDimension = Math.max(width, height);
        
        // 目标是导出尺寸在2048-8192像素之间的图像
        // 这样既能看到全貌，又保证足够的清晰度
        int targetSize = 4096; // 目标尺寸
        
        // 计算需要的缩放层级
        int optimalLevel = 0;
        int currentSize = maxDimension;
        
        while (currentSize > targetSize && optimalLevel < levelCount - 1) {
            currentSize = currentSize >> 1; // 除以2
            optimalLevel++;
        }
        
        // 确保不超过可用层级范围
        optimalLevel = Math.min(optimalLevel, levelCount - 1);
        optimalLevel = Math.max(optimalLevel, 0);
        
        // 特殊规则：对于非常大的图像，至少使用层级1或2，避免内存问题
        if (maxDimension > 50000) {
            optimalLevel = Math.max(optimalLevel, 2);
        } else if (maxDimension > 20000) {
            optimalLevel = Math.max(optimalLevel, 1);
        }
        
        log.debug("计算最优导出层级：原始尺寸={}x{}, 最大维度={}, 目标尺寸={}, 选择层级={}", 
                width, height, maxDimension, targetSize, optimalLevel);
        
        return optimalLevel;
    }

} 
