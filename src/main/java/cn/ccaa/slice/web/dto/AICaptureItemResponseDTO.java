package cn.ccaa.slice.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI截图响应数据项DTO
 * 单个截图结果
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AICaptureItemResponseDTO {

    /**
     * 截图唯一标识
     */
    private String cutimgid;
    
    /**
     * 截图的base64编码图像数据
     */
    private String image;
    
    /**
     * 结果消息（成功或失败原因）
     */
    private String msg;
} 