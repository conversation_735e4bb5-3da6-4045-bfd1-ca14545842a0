package cn.ccaa.slice.web.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class TCSliceMarkDTO {

    private String id;

    private String fileId;

    /**
     *
     */
    private String shape;

    /**
     *
     */
    private String coordsX;

    /**
     *
     */
    private String coordsY;

    /**
     * 标注倍率
     */
    private String rate;

    /**
     *
     */
    private String width;

    /**
     *
     */
    private String height;

    /**
     * 标注备注
     */
    private String content;

    /**
     *
     */
    private String color;

    /**
     *
     */
    private String title;

    /**
     *
     */
    private String filePath;

    /**
     * 标记（多边形标注的顶点坐标字符串）
     */
    private String flags;

    /**
     * 旋转角度
     */
    private String rotaing;

    /**
     *
     */
    private String border;

    /**
     *
     */
    private String opacity;

    /**
     * 实际X坐标（根据缩放比例计算的实际X轴坐标）
     */
    @JsonProperty("rX")
    private String rx;

    /**
     * 实际Y坐标（根据缩放比例计算的实际Y轴坐标）
     */
    @JsonProperty("rY")
    private String ry;

    /**
     * 实际宽度（根据缩放比例计算的实际宽度）
     */
    @JsonProperty("rWidth")
    private String rwidth;

    /**
     * 实际高度（根据缩放比例计算的实际高度）
     */
    @JsonProperty("rHeight")
    private String rheight;

    /**
     * 实际面积（根据缩放比例计算的实际面积）
     */
    @JsonProperty("rArea")
    private String rarea;

    /**
     * 病例ID（标注所属的病例标识）
     */
    private String caseId;

    /**
     * 有效标志（0.否，1.是）
     */
    private String valiFlag;

    /**
     *
     */
    private String crterId;

    /**
     *
     */
    private String crterName;

    /**
     *
     */
    private Date crteTime;

    /**
     *
     */
    private String lastModifierId;

    /**
     *
     */
    private String lastModifierName;

    /**
     *
     */
    private Date lastModifiedTime;

    private String cname;

}
