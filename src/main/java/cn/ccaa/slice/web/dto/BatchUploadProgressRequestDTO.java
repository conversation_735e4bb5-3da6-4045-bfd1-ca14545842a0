package cn.ccaa.slice.web.dto;

import java.io.Serializable;
import java.util.List;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量查询上传进度请求DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchUploadProgressRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 切片ID列表
     */
    @NotEmpty(message = "切片ID列表不能为空")
    @Size(max = 50, message = "批量查询最多支持50个切片ID")
    private List<String> sliceIds;
} 