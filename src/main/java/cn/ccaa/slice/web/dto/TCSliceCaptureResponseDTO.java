package cn.ccaa.slice.web.dto;

import lombok.Data;

/**
 * 切片截图JSON响应DTO
 * 用于其他Java服务调用时的JSON响应格式
 * 
 * <AUTHOR>
 */
@Data
public class TCSliceCaptureResponseDTO {

    private String fileId;

    private String zoom;

    private String csize;

    private String level;

    private String content;

    private String w;

    private String h;

    private String x;

    private String y;

    private String rotate;

    /**
     * 原图Base64编码数据
     */
    private String cutImageOri;

    /**
     * 缩略图Base64编码数据  
     */
    private String cutImage;

    /**
     * 1":细胞学采图功能 "0":截图功能 默认：截图功能
     */
    private String functionOption;

} 