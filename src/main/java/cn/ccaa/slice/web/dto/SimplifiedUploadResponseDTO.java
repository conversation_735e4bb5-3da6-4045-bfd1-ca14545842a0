package cn.ccaa.slice.web.dto;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 简化的上传响应DTO
 * 用于分片上传的简化响应格式：{"code":200,"data":{"chunkNumber":xx}}
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SimplifiedUploadResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应数据
     */
    private ChunkData data;

    /**
     * 分片数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChunkData {
        /**
         * 分片序号
         */
        private Integer chunkNumber;
    }

    /**
     * 创建成功响应
     * 
     * @param chunkNumber 分片序号
     * @return 简化响应对象
     */
    public static SimplifiedUploadResponseDTO success(Integer chunkNumber) {
        return SimplifiedUploadResponseDTO.builder()
                .code(200)
                .data(ChunkData.builder()
                        .chunkNumber(chunkNumber)
                        .build())
                .build();
    }
} 