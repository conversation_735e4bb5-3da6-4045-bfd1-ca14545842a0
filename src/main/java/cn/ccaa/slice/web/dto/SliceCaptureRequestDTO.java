package cn.ccaa.slice.web.dto;

import java.io.Serializable;
import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 切片截图请求DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SliceCaptureRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 切片文件ID
     */
    @NotBlank(message = "切片文件ID不能为空")
    private String fileId;

    /**
     * 缩放倍率
     */
    @NotNull(message = "缩放倍率不能为空")
    private Double zoom;

    /**
     * 屏幕像素比
     */
    @NotNull(message = "屏幕像素比不能为空")
    private Double csize;

    /**
     * 层级
     */
    @NotNull(message = "层级不能为空")
    private Integer level;

    /**
     * 描述
     */
    @NotBlank(message = "描述不能为空")
    private String content;

    /**
     * 宽度
     */
    @NotNull(message = "宽度不能为空")
    private Double w;

    /**
     * 高度
     */
    @NotNull(message = "高度不能为空")
    private Double h;

    /**
     * X坐标
     */
    @NotNull(message = "X坐标不能为空")
    private Double x;

    /**
     * Y坐标
     */
    @NotNull(message = "Y坐标不能为空")
    private Double y;

    /**
     * 旋转角度
     */
    @NotNull(message = "旋转角度不能为空")
    private Double rotate;

    /**
     * 四点坐标
     */
    @NotNull(message = "四点坐标不能为空")
    @Valid
    private List<Coordinate> coords;

    /**
     * 功能选项: "0" 截图功能，"1" 细胞学采图功能
     */
    @NotBlank(message = "功能选项不能为空")
    private String functionOption;

    /**
     * 病例ID（可选）
     */
    private String caseId;

    /**
     * 坐标点
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Coordinate implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * X坐标
         */
        @NotNull(message = "坐标X不能为空")
        private Double x;

        /**
         * Y坐标
         */
        @NotNull(message = "坐标Y不能为空")
        private Double y;
    }
} 