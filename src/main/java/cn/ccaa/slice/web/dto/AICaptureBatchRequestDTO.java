package cn.ccaa.slice.web.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI批量截图请求DTO
 * 用于根据AI返回坐标生成图片
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AICaptureBatchRequestDTO {

    /**
     * 文件ID（关联 si_slice_file.file_id）
     */
    @NotBlank(message = "文件ID不能为空")
    private String fileId;
    
    /**
     * 截图唯一标识（用于保存文件名）
     */
    @NotBlank(message = "截图标识不能为空")
    private String cutimgid;
    
    /**
     * 截图区域左上角X坐标
     */
    @NotNull(message = "X坐标不能为空")
    private Integer x;
    
    /**
     * 截图区域左上角Y坐标
     */
    @NotNull(message = "Y坐标不能为空")
    private Integer y;
    
    /**
     * 截图区域宽度
     */
    @NotNull(message = "宽度不能为空")
    private Integer w;
    
    /**
     * 截图区域高度
     */
    @NotNull(message = "高度不能为空")
    private Integer h;
} 