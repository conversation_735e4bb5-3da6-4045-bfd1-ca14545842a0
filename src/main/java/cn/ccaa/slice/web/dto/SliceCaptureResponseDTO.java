package cn.ccaa.slice.web.dto;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 切片截图响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SliceCaptureResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 切片文件ID
     */
    private String fileId;

    /**
     * 缩放倍率
     */
    private Double zoom;

    /**
     * 屏幕像素比
     */
    private Double csize;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 描述
     */
    private String content;

    /**
     * 宽度
     */
    private Double w;

    /**
     * 高度
     */
    private Double h;

    /**
     * X坐标
     */
    private Double x;

    /**
     * Y坐标
     */
    private Double y;

    /**
     * 旋转角度
     */
    private Double rotate;

    /**
     * 截图原图文件数据
     */
    private byte[] cutImageOri;

    /**
     * 截图缩略图文件数据
     */
    private byte[] cutImage;

    /**
     * 功能选项: "0" 截图功能，"1" 细胞学采图功能
     */
    private String functionOption;

    /**
     * 病例ID
     */
    private String caseId;

    /**
     * 四点坐标
     */
    private List<Coordinate> coords;

    /**
     * 坐标点
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Coordinate implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * X坐标
         */
        private Double x;

        /**
         * Y坐标
         */
        private Double y;
    }
} 