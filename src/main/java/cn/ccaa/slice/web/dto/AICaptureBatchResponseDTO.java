package cn.ccaa.slice.web.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI批量截图响应DTO
 * 根据AI返回坐标生成图片的响应格式
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AICaptureBatchResponseDTO {

    /**
     * 处理状态
     */
    private String status;
    
    /**
     * 截图结果数据数组
     */
    private List<AICaptureItemResponseDTO> data;
    
    /**
     * 整体结果描述
     */
    private String msg;
} 