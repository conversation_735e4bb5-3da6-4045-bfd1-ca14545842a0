package cn.ccaa.slice.web.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 上传进度DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadProgressDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 上传进度最大百分比
     * 上传完成后进度为90%，剩余10%用于后端处理（解析缩略图、标签图等）
     */
    private static final int MAX_UPLOAD_PROGRESS_PERCENTAGE = 90;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 上传状态
     */
    private String status;

    /**
     * 当前进度百分比
     */
    private Integer progressPercentage;

    /**
     * 已上传大小（字节）
     */
    private Long uploadedBytes;

    /**
     * 总大小（字节）
     */
    private Long totalBytes;

    /**
     * 上传速率（字节/秒）
     */
    private Long uploadRate;

    /**
     * 估计剩余时间（秒）
     */
    private Long estimatedTimeRemaining;

    /**
     * 消息时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 错误消息（如果有）
     */
    private String errorMessage;

    /**
     * 是否已完成
     */
    private Boolean completed;

    /**
     * 创建进度DTO
     *
     * @param taskId 任务ID
     * @param fileName 文件名
     * @param status 状态
     * @param uploadedBytes 已上传字节数
     * @param totalBytes 总字节数
     * @param uploadRate 上传速率
     * @return 进度DTO
     */
    public static UploadProgressDTO create(String taskId, String fileName, String status,
                                         Long uploadedBytes, Long totalBytes, Long uploadRate,
                                         Boolean completed) {
        // 计算进度百分比
        int originalPercentage = totalBytes > 0 ? (int) ((uploadedBytes * 100) / totalBytes) : 0;

        int progressPercentage;

        // 如果任务已完成，显示为100%
        if (completed != null && completed) {
            progressPercentage = 100;
        }
        // 如果任务状态是FAILED，保持进度在90%
        else if ("FAILED".equals(status)) {
            progressPercentage = MAX_UPLOAD_PROGRESS_PERCENTAGE;
        }
        // 否则，将0-100的进度映射到0-90的范围
        else {
            progressPercentage = (int) Math.min(originalPercentage * MAX_UPLOAD_PROGRESS_PERCENTAGE / 100.0, MAX_UPLOAD_PROGRESS_PERCENTAGE);
        }

        // 估计剩余时间
        long remaining = totalBytes - uploadedBytes;
        long estimatedTimeRemaining = uploadRate > 0 ? remaining / uploadRate : -1;

        return builder()
                .taskId(taskId)
                .fileName(fileName)
                .status(status)
                .uploadedBytes(uploadedBytes)
                .totalBytes(totalBytes)
                .uploadRate(uploadRate)
                .timestamp(LocalDateTime.now())
                .progressPercentage(progressPercentage)
                .estimatedTimeRemaining(estimatedTimeRemaining)
                .completed(completed)
                .build();
    }

    /**
     * 创建错误DTO
     *
     * @param taskId 任务ID
     * @param fileName 文件名
     * @param errorMessage 错误信息
     * @return 进度DTO
     */
    public static UploadProgressDTO error(String taskId, String fileName, String errorMessage) {
        return builder()
                .taskId(taskId)
                .fileName(fileName)
                .status("ERROR")
                .errorMessage(errorMessage)
                .timestamp(LocalDateTime.now())
                .build();
    }
}
