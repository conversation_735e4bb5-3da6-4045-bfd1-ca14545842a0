package cn.ccaa.slice.web.dto;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件上传响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UploadResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 原始文件名
     */
    private String originalFileName;
    
    /**
     * 缩略图文件名
     */
    private String thumbnailFileName;
    
    /**
     * 标签图文件名
     */
    private String labelFileName;
    
    /**
     * 基础目录
     */
    private String uploadDir;

    /**
     * 文件URL
     * 注意：对于切片文件（如svs、sdpc等），当上传完成后此字段为null，将不会在JSON响应中显示。
     * 因为切片文件会分解为三个文件（原始文件、缩略图、标签图）上传到不同路径。
     */
    private String fileUrl;

    /**
     * 上传状态
     */
    private String status;

    /**
     * 当前进度
     */
    private Integer progress;

    /**
     * 已上传大小
     */
    private Long uploadedSize;

    /**
     * 总大小
     */
    private Long totalSize;

    /**
     * 是否完成
     */
    private Boolean completed;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 创建成功响应
     * 
     * @param taskId 任务ID
     * @param fileName 文件名
     * @param fileUrl 文件URL（对于切片文件可以为null）
     * @param uploadedSize 已上传大小
     * @param totalSize 总大小
     * @param uploadDir 上传目录
     * @return 响应对象
     */
    public static UploadResponseDTO success(String taskId, String fileName, String fileUrl, 
                                          Long uploadedSize, Long totalSize, String uploadDir) {
        return builder()
                .taskId(taskId)
                .originalFileName(fileName)
                .fileUrl(fileUrl)
                .uploadDir(uploadDir)
                .status("COMPLETED")
                .progress(100)
                .uploadedSize(uploadedSize)
                .totalSize(totalSize)
                .completed(true)
                .build();
    }

    /**
     * 创建成功响应（无fileUrl版本）
     * 
     * @param taskId 任务ID
     * @param fileName 文件名
     * @param uploadedSize 已上传大小
     * @param totalSize 总大小
     * @param uploadDir 上传目录
     * @return 响应对象
     */
    public static UploadResponseDTO successWithoutUrl(String taskId, String fileName, 
                                                    Long uploadedSize, Long totalSize, String uploadDir) {
        return builder()
                .taskId(taskId)
                .originalFileName(fileName)
                .uploadDir(uploadDir)
                .status("COMPLETED")
                .progress(100)
                .uploadedSize(uploadedSize)
                .totalSize(totalSize)
                .completed(true)
                .build();
    }

    /**
     * 创建进行中响应
     * 
     * @param taskId 任务ID
     * @param fileName 文件名
     * @param uploadedSize 已上传大小
     * @param totalSize 总大小
     * @param uploadDir 上传目录
     * @return 响应对象
     */
    public static UploadResponseDTO progress(String taskId, String fileName, 
                                           Long uploadedSize, Long totalSize, String uploadDir) {
        int progressPercentage = totalSize > 0 ? (int) ((uploadedSize * 100) / totalSize) : 0;
        
        return builder()
                .taskId(taskId)
                .originalFileName(fileName)
                .uploadDir(uploadDir)
                .status("IN_PROGRESS")
                .progress(progressPercentage)
                .uploadedSize(uploadedSize)
                .totalSize(totalSize)
                .completed(false)
                .build();
    }

    /**
     * 创建错误响应
     * 
     * @param taskId 任务ID
     * @param fileName 文件名
     * @param errorMessage 错误消息
     * @param uploadDir 上传目录
     * @return 响应对象
     */
    public static UploadResponseDTO error(String taskId, String fileName, String errorMessage, String uploadDir) {
        return builder()
                .taskId(taskId)
                .originalFileName(fileName)
                .uploadDir(uploadDir)
                .status("ERROR")
                .completed(false)
                .errorMessage(errorMessage)
                .build();
    }
} 
