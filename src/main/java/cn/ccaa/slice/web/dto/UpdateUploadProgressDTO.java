package cn.ccaa.slice.web.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新上传进度请求DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateUploadProgressDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 已上传大小
     */
    private Long uploadedSize;
}
