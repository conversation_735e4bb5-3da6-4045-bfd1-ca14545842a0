package cn.ccaa.slice.web.dto;

import java.io.Serializable;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件上传请求DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件名
     */
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    /**
     * 文件大小（字节）
     */
    @NotNull(message = "文件大小不能为空")
    @Min(value = 1, message = "文件大小必须大于0")
    private Long fileSize;

    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 切片厂商代码（1.滨松，2.江丰，3.徕卡，4.生强）
     */
    @NotBlank(message = "切片厂商代码不能为空")
    private String sliceMfrCode;
    
    /**
     * 机构ID
     */
    @NotBlank(message = "机构ID不能为空")
    private String orgId;
    
    /**
     * 院区ID
     */
    @NotBlank(message = "院区ID不能为空")
    private String ahId;
    
    /**
     * 切片来源代码（1.本地上传，2.免上传，3.蚂蚁工具）
     */
    @NotBlank(message = "切片来源代码不能为空")
    private String sliceSourceCode;
    
    /**
     * 创建人ID
     */
    @NotBlank(message = "创建人ID不能为空")
    private String crterId;
    
    /**
     * 创建人姓名
     */
    @NotBlank(message = "创建人姓名不能为空")
    private String crterName;
    
    /**
     * 临时授权token
     */
    private String tempAuth;
} 
