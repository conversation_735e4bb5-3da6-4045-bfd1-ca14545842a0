package cn.ccaa.slice.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 缓存配置属性。
 * 从配置文件中加载缓存参数。
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "cache")
public class CacheProperties {

    /**
     * 是否启用缓存。
     */
    private boolean enabled = true;

    /**
     * 最大缓存条目数。
     */
    private int maximumSize = 5000;

    /**
     * 最大权重（MB）。
     */
    private int maximumWeightMB = 500;

    /**
     * 写入后过期时间（分钟）。
     */
    private long expireAfterWriteMinutes = 60;

    /**
     * 访问后过期时间（分钟）。
     */
    private long expireAfterAccessMinutes = 120;

    /**
     * 是否记录统计信息。
     */
    private boolean recordStats = true;

    /**
     * 将分钟转换为秒。
     *
     * @param minutes 分钟
     * @return 秒
     */
    public static long minutesToSeconds(long minutes) {
        return minutes * 60;
    }

    /**
     * 将MB转换为字节。
     *
     * @param megabytes MB
     * @return 字节
     */
    public static long mbToBytes(long megabytes) {
        return megabytes * 1024 * 1024;
    }

    /**
     * 获取写入后过期时间（秒）。
     *
     * @return 写入后过期时间（秒）
     */
    public long getExpireAfterWriteSeconds() {
        return minutesToSeconds(expireAfterWriteMinutes);
    }

    /**
     * 获取访问后过期时间（秒）。
     *
     * @return 访问后过期时间（秒）
     */
    public long getExpireAfterAccessSeconds() {
        return minutesToSeconds(expireAfterAccessMinutes);
    }

    /**
     * 获取最大权重（字节）。
     *
     * @return 最大权重（字节）
     */
    public long getMaximumWeight() {
        return mbToBytes(maximumWeightMB);
    }
}
