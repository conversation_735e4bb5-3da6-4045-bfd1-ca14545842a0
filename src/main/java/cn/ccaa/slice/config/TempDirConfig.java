package cn.ccaa.slice.config;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * 临时目录配置
 *
 * <AUTHOR>
 */
@Component
public class TempDirConfig {
    private static final Logger log = LoggerFactory.getLogger(TempDirConfig.class);

    /**
     * 临时目录路径
     */
    @Value("${storage.temp-dir:./temp}")
    private String tempDirPath;

    /**
     * 临时文件保留时间（小时）
     */
    @Value("${storage.temp-file-ttl-hours:24}")
    private int tempFileTtlHours;

    /**
     * 初始化临时目录
     */
    @PostConstruct
    public void init() {
        try {
            Path tempDir = Paths.get(tempDirPath);
            if (!Files.exists(tempDir)) {
                Files.createDirectories(tempDir);
                log.info("创建临时目录: {}", tempDir.toAbsolutePath());
            } else {
                log.info("临时目录已存在: {}", tempDir.toAbsolutePath());
            }
        } catch (IOException e) {
            log.error("初始化临时目录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取临时目录路径
     *
     * @return 临时目录路径
     */
    public Path getTempDir() {
        return Paths.get(tempDirPath);
    }

    /**
     * 获取临时文件保留时间（小时）
     *
     * @return 临时文件保留时间
     */
    public int getTempFileTtlHours() {
        return tempFileTtlHours;
    }

    /**
     * 清理过期临时文件
     *
     * @return 清理的文件数量
     */
    public int cleanupExpiredTempFiles() {
        try {
            Path tempDir = Paths.get(tempDirPath);
            if (!Files.exists(tempDir)) {
                return 0;
            }

            long cutoffTime = System.currentTimeMillis() - (tempFileTtlHours * 60 * 60 * 1000);
            final int[] count = {0};

            Files.walk(tempDir)
                .map(Path::toFile)
                .filter(file -> !file.isDirectory() && file.lastModified() < cutoffTime)
                .forEach(file -> {
                    if (file.delete()) {
                        count[0]++;
                        log.debug("删除过期临时文件: {}", file.getAbsolutePath());
                    } else {
                        log.warn("无法删除过期临时文件: {}", file.getAbsolutePath());
                    }
                });

            log.info("清理过期临时文件完成，共删除{}个文件", count[0]);
            return count[0];
        } catch (IOException e) {
            log.error("清理过期临时文件失败: {}", e.getMessage(), e);
            return 0;
        }
    }
} 
