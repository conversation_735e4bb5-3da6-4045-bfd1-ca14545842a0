package cn.ccaa.slice.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.http.CacheControl;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.util.UrlPathHelper;

import java.util.concurrent.TimeUnit;

/**
 * Web MVC配置
 * 用于配置Spring MVC相关设置
 *
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 配置路径匹配
     * 禁用URL路径中的分号内容移除，确保DZI URL格式能够正确处理
     * 
     * @param configurer 路径匹配配置器
     */
    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        UrlPathHelper urlPathHelper = new UrlPathHelper();
        // 设置不移除分号内容，解决特殊URL格式问题
        urlPathHelper.setRemoveSemicolonContent(false);
        // 设置为不解码URL，保留原始URL格式
        urlPathHelper.setUrlDecode(false);
        configurer.setUrlPathHelper(urlPathHelper);
    }
    
    /**
     * 配置跨域支持
     * 允许所有来源的跨域请求，解决前端访问API的CORS问题
     *
     * @param registry CORS注册表
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .maxAge(3600);
    }
    
    /**
     * 配置静态资源缓存
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 明确的静态资源路径配置
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .setCacheControl(CacheControl.maxAge(7, TimeUnit.DAYS).cachePublic());
        
        // OpenSeadragon JavaScript资源专用配置
        registry.addResourceHandler("/openseadragon/js/**")
                .addResourceLocations("classpath:/static/openseadragon/js/")
                .setCacheControl(CacheControl.maxAge(7, TimeUnit.DAYS).cachePublic());
        
        // OpenSeadragon图片资源专用配置
        registry.addResourceHandler("/openseadragon/images/**")
                .addResourceLocations("classpath:/static/openseadragon/images/")
                .setCacheControl(CacheControl.maxAge(7, TimeUnit.DAYS).cachePublic());
        
        // HTML文件直接访问配置
        registry.addResourceHandler("/*.html")
                .addResourceLocations("classpath:/static/")
                .setCacheControl(CacheControl.maxAge(1, TimeUnit.HOURS).cachePublic());
        
        // 其他静态文件类型配置（CSS、JS、图片等）
        registry.addResourceHandler("/static/**/*.jpg", "/static/**/*.jpeg", "/static/**/*.png", "/static/**/*.gif", "/static/**/*.js", "/static/**/*.css")
                .addResourceLocations("classpath:/static/")
                .setCacheControl(CacheControl.maxAge(1, TimeUnit.DAYS).cachePublic());
                
        // 注释掉DZI静态资源配置，DZI应该由DziController动态处理
        // registry.addResourceHandler("/api/dzi/**")
        //         .setCacheControl(CacheControl.maxAge(1, TimeUnit.DAYS).cachePublic());
    }
} 
