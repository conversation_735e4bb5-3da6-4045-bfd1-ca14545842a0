package cn.ccaa.slice.config;


import java.util.concurrent.*;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 优先级线程池任务执行器
 * 支持任务优先级
 *
 * <AUTHOR>
 */
public class PriorityThreadPoolTaskExecutor extends ThreadPoolTaskExecutor {

    private static final long serialVersionUID = 1L;

    /**
     * 创建优先级线程池
     */
    @Override
    protected ThreadPoolExecutor initializeExecutor(
            ThreadFactory threadFactory, RejectedExecutionHandler rejectedExecutionHandler) {
        // 使用优先级队列替代默认队列
        PriorityBlockingQueue<Runnable> priorityQueue = new PriorityBlockingQueue<>(getQueueCapacity());

        return new PriorityThreadPoolExecutor(
                getCorePoolSize(), getMaxPoolSize(), getKeepAliveSeconds(), TimeUnit.SECONDS,
                priorityQueue, threadFactory, rejectedExecutionHandler);
    }

    /**
     * 提交优先级任务
     *
     * @param task 任务
     * @param priority 优先级，值越小优先级越高
     * @return 任务Future
     */
    public Future<?> submit(Runnable task, int priority) {
        PriorityRunnable priorityTask = new PriorityRunnable(task, priority);
        return super.submit(priorityTask);
    }

    /**
     * 提交优先级任务
     *
     * @param task 任务
     * @param priority 优先级，值越小优先级越高
     * @param <T> 返回值类型
     * @return 任务Future
     */
    public <T> Future<T> submit(Callable<T> task, int priority) {
        PriorityCallable<T> priorityTask = new PriorityCallable<>(task, priority);
        return super.submit(priorityTask);
    }

    /**
     * 提交可监听的优先级任务
     *
     * @param task 任务
     * @param priority 优先级，值越小优先级越高
     * @return 可监听的任务Future
     */
    public CompletableFuture<?> submitListenable(Runnable task, int priority) {
        PriorityRunnable priorityTask = new PriorityRunnable(task, priority);
        CompletableFuture<?> future = new CompletableFuture<>();
        execute(() -> {
            try {
                priorityTask.run();
                future.complete(null);
            } catch (Exception e) {
                future.completeExceptionally(e);
            }
        });
        return future;
    }

    /**
     * 提交可监听的优先级任务
     *
     * @param task 任务
     * @param priority 优先级，值越小优先级越高
     * @param <T> 返回值类型
     * @return 可监听的任务Future
     */
    public <T> CompletableFuture<T> submitListenable(Callable<T> task, int priority) {
        PriorityCallable<T> priorityTask = new PriorityCallable<>(task, priority);
        CompletableFuture<T> future = new CompletableFuture<>();
        execute(() -> {
            try {
                T result = priorityTask.call();
                future.complete(result);
            } catch (Exception e) {
                future.completeExceptionally(e);
            }
        });
        return future;
    }

    /**
     * 优先级线程池执行器
     */
    private static class PriorityThreadPoolExecutor extends ThreadPoolExecutor {

        public PriorityThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                                        TimeUnit unit, PriorityBlockingQueue<Runnable> workQueue,
                                        ThreadFactory threadFactory, RejectedExecutionHandler handler) {
            super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
        }

        @Override
        protected <T> RunnableFuture<T> newTaskFor(Callable<T> callable) {
            if (callable instanceof PriorityCallable) {
                return new PriorityFutureTask<>((PriorityCallable<T>) callable);
            }
            return super.newTaskFor(callable);
        }

        @Override
        protected <T> RunnableFuture<T> newTaskFor(Runnable runnable, T value) {
            if (runnable instanceof PriorityRunnable) {
                return new PriorityFutureTask<>((PriorityRunnable) runnable, value);
            }
            return super.newTaskFor(runnable, value);
        }
    }

    /**
     * 优先级Runnable
     */
    public static class PriorityRunnable implements Runnable, Comparable<PriorityRunnable> {
        private final Runnable delegate;
        private final int priority;

        public PriorityRunnable(Runnable delegate, int priority) {
            this.delegate = delegate;
            this.priority = priority;
        }

        @Override
        public void run() {
            delegate.run();
        }

        @Override
        public int compareTo(PriorityRunnable other) {
            return Integer.compare(this.priority, other.priority);
        }

        public int getPriority() {
            return priority;
        }
    }

    /**
     * 优先级Callable
     */
    public static class PriorityCallable<T> implements Callable<T>, Comparable<PriorityCallable<?>> {
        private final Callable<T> delegate;
        private final int priority;

        public PriorityCallable(Callable<T> delegate, int priority) {
            this.delegate = delegate;
            this.priority = priority;
        }

        @Override
        public T call() throws Exception {
            return delegate.call();
        }

        @Override
        public int compareTo(PriorityCallable<?> other) {
            return Integer.compare(this.priority, other.priority);
        }

        public int getPriority() {
            return priority;
        }
    }

    /**
     * 优先级FutureTask
     */
    public static class PriorityFutureTask<T> extends FutureTask<T> implements Comparable<PriorityFutureTask<?>> {
        private final int priority;

        public PriorityFutureTask(PriorityCallable<T> callable) {
            super(callable);
            this.priority = callable.getPriority();
        }

        public PriorityFutureTask(PriorityRunnable runnable, T result) {
            super(runnable, result);
            this.priority = runnable.getPriority();
        }

        @Override
        public int compareTo(PriorityFutureTask<?> other) {
            return Integer.compare(this.priority, other.priority);
        }
    }
}
