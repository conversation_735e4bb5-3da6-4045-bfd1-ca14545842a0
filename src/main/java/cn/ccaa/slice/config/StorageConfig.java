package cn.ccaa.slice.config;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;

import jakarta.annotation.PostConstruct;
import lombok.Data;

/**
 * 存储配置
 * 定义文件存储相关的配置项
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "storage")
@Data
public class StorageConfig {

    private static final Logger log = LoggerFactory.getLogger(StorageConfig.class);

    /**
     * 本地文件存储配置
     */
    private FileStorage file = new FileStorage();

    /**
     * 临时文件配置
     */
    private TempStorage temp = new TempStorage();

    /**
     * 本地切片文件保存根目录
     */
    @Value("${storage.local-slice-dir:./local_slices}")
    private String localSliceDir;

    /**
     * 临时文件目录
     */
    @Value("${storage.temp-dir:./temp}")
    private String tempDir;

    /**
     * 临时文件保留时间（小时）
     */
    @Value("${storage.temp-file-ttl-hours:24}")
    private int tempFileTtlHours;

    /**
     * 初始化存储目录
     * 确保所需的目录存在
     */
    @PostConstruct
    public void initStorageDirectories() {
        try {
            // 获取当前工作目录
            String baseDir = System.getProperty("user.dir");
            log.info("当前工作目录: {}", baseDir);

            // 初始化本地切片目录
            if (localSliceDir.startsWith("./")) {
                // 相对路径，转换为绝对路径
                File sliceDir = new File(baseDir, localSliceDir.substring(2));
                if (!sliceDir.exists() && !sliceDir.mkdirs()) {
                    throw new IllegalStateException("无法创建本地切片目录: " + sliceDir.getAbsolutePath());
                }
                localSliceDir = sliceDir.getAbsolutePath();
            } else {
                // 绝对路径，确保目录存在
                File sliceDir = new File(localSliceDir);
                if (!sliceDir.exists() && !sliceDir.mkdirs()) {
                    throw new IllegalStateException("无法创建本地切片目录: " + sliceDir.getAbsolutePath());
                }
            }
            log.info("本地切片目录初始化为: {}", localSliceDir);

            // 初始化临时目录
            // 首先检查temp对象的baseDir
            if (temp.getBaseDir() == null || temp.getBaseDir().trim().isEmpty()) {
                temp.setBaseDir(tempDir);
            }

            if (temp.getBaseDir().startsWith("./")) {
                // 相对路径，转换为绝对路径
                File tmpDir = new File(baseDir, temp.getBaseDir().substring(2));
                if (!tmpDir.exists() && !tmpDir.mkdirs()) {
                    throw new IllegalStateException("无法创建临时目录: " + tmpDir.getAbsolutePath());
                }
                temp.setBaseDir(tmpDir.getAbsolutePath());
            } else {
                // 绝对路径，确保目录存在
                File tmpDir = new File(temp.getBaseDir());
                if (!tmpDir.exists() && !tmpDir.mkdirs()) {
                    throw new IllegalStateException("无法创建临时目录: " + tmpDir.getAbsolutePath());
                }
            }
            log.info("临时目录初始化为: {}", temp.getBaseDir());

            // 设置文件存储的临时目录
            file.setTempDir(temp.getBaseDir());
        } catch (Exception e) {
            String errorMsg = "初始化存储目录失败: " + e.getMessage();
            log.error(errorMsg, e);
            throw new IllegalStateException(errorMsg, e);
        }
    }

    public String getLocalSliceDir() {
        return localSliceDir;
    }

    /**
     * 文件存储配置（简化版，仅保留必要配置）
     */
    @Data
    public static class FileStorage {

        /**
         * 上传目录
         */
        private String uploadDir = "slides";

        /**
         * 临时目录
         */
        private String tempDir;

        /**
         * 临时文件保留时间（秒）
         */
        private int tempRetentionSeconds = 3600;
    }

    /**
     * 临时文件配置
     */
    @Data
    public static class TempStorage {

        /**
         * 临时文件基础目录
         */
        private String baseDir;

        /**
         * 临时文件保留时间（秒）
         */
        private int retentionSeconds = 3600;
    }
}
