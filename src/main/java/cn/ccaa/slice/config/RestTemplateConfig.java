package cn.ccaa.slice.config;

import java.time.Duration;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 *
 * <AUTHOR>
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 配置 RestTemplate Bean
     *
     * @param builder RestTemplateBuilder
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        // 配置连接和读取超时，使用默认值
        return builder
                .setConnectTimeout(Duration.ofMillis(10000))  // 10秒连接超时
                .setReadTimeout(Duration.ofMillis(30000))     // 30秒读取超时
                .build();
    }
} 
