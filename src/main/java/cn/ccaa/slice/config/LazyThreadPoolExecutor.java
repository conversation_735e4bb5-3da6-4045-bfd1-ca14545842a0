package cn.ccaa.slice.config;

import java.util.concurrent.Executor;
import java.util.function.Supplier;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import lombok.Getter;

/**
 * 延迟初始化的线程池执行器
 * 在第一次使用时才初始化真正的线程池，避免Spring生命周期中的初始化问题
 *
 * <AUTHOR>
 */
public class LazyThreadPoolExecutor implements Executor {
    private static final Logger log = LoggerFactory.getLogger(LazyThreadPoolExecutor.class);

    private final Supplier<Executor> executorSupplier;
    private volatile Executor delegate;
    private final Object lock = new Object();

    /**
     *  获取线程池名称
     */
    @Getter
    private final String name;

    /**
     * 构造函数
     *
     * @param name 线程池名称，用于日志记录
     * @param executorSupplier 线程池提供者，在第一次使用时调用
     */
    public LazyThreadPoolExecutor(String name, Supplier<Executor> executorSupplier) {
        this.name = name;
        this.executorSupplier = executorSupplier;
        log.info("创建延迟初始化的线程池执行器: {}", name);
    }

    @Override
    public void execute(Runnable command) {
        ensureInitialized();
        try {
            delegate.execute(command);
        } catch (Exception e) {
            log.error("执行任务失败: {}, 错误: {}", name, e.getMessage(), e);
            // 如果执行失败，尝试重新初始化线程池并再次执行
            reinitializeAndExecute(command, e);
        }
    }

    /**
     * 确保执行器已初始化
     */
    private void ensureInitialized() {
        if (delegate == null) {
            synchronized (lock) {
                if (delegate == null) {
                    log.info("初始化线程池执行器: {}", name);
                    try {
                        Executor executor = executorSupplier.get();
                        // 验证执行器是否可用
                        if (executor == null) {
                            throw new IllegalStateException("执行器提供者返回了null");
                        }

                        // 不在初始化时测试执行器，避免提前初始化问题
                        log.info("线程池执行器初始化成功: {}, 类型: {}", name, executor.getClass().getName());

                        delegate = executor;
                    } catch (Exception e) {
                        log.error("线程池执行器初始化失败: {}, 错误: {}", name, e.getMessage(), e);
                        // 如果初始化失败，使用一个简单的直接执行器作为后备方案
                        delegate = Runnable::run;
                        log.info("已使用直接执行器作为后备方案: {}", name);
                    }
                }
            }
        }
    }

    /**
     * 尝试重新初始化线程池并执行任务
     *
     * @param command 要执行的任务
     * @param originalException 原始异常
     */
    private void reinitializeAndExecute(Runnable command, Exception originalException) {
        try {
            log.info("尝试重新初始化线程池执行器: {}", name);
            synchronized (lock) {
                // 重新初始化
                try {
                    delegate = executorSupplier.get();
                    log.info("线程池执行器重新初始化成功: {}", name);
                } catch (Exception e) {
                    log.error("线程池执行器重新初始化失败: {}, 错误: {}", name, e.getMessage(), e);
                    delegate = Runnable::run;
                    log.info("已使用直接执行器作为后备方案: {}", name);
                }

                // 再次尝试执行任务
                try {
                    delegate.execute(command);
                    log.info("在重新初始化后成功执行任务: {}", name);
                } catch (Exception e) {
                    log.error("在重新初始化后执行任务仍然失败: {}, 错误: {}", name, e.getMessage(), e);
                    // 如果仍然失败，直接在当前线程执行任务
                    log.info("将在当前线程直接执行任务: {}", name);
                    command.run();
                }
            }
        } catch (Exception e) {
            log.error("重新初始化和执行过程中发生异常: {}, 错误: {}", name, e.getMessage(), e);
            // 最后的尝试：直接在当前线程执行任务
            try {
                command.run();
            } catch (Exception runException) {
                log.error("在当前线程直接执行任务失败: {}, 错误: {}", name, runException.getMessage(), runException);
                // 抛出组合异常
                RuntimeException combinedException = new RuntimeException(
                    "执行任务失败，原始错误: " + originalException.getMessage() +
                    ", 重试错误: " + e.getMessage() +
                    ", 直接执行错误: " + runException.getMessage(), runException);
                combinedException.addSuppressed(originalException);
                combinedException.addSuppressed(e);
                throw combinedException;
            }
        }
    }

    /**
     * 获取委托的执行器
     * 注意：这可能触发初始化
     *
     * @return 委托的执行器
     */
    public Executor getDelegate() {
        ensureInitialized();
        return delegate;
    }

    /**
     * 检查执行器是否已初始化
     *
     * @return 如果执行器已初始化，则返回true
     */
    public boolean isInitialized() {
        return delegate != null;
    }

}
