package cn.ccaa.slice.config;

import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

/**
 * 统一缓存配置类。
 * 使用Caffeine提供高性能的内存缓存。
 * 优化后的版本，移除了重复定义，统一使用配置属性。
 *
 * <AUTHOR>
 */
@Configuration
public class CacheConfig {

    private static final Logger log = LoggerFactory.getLogger(CacheConfig.class);

    @Autowired
    private CacheProperties cacheProperties;

    @Value("${slide.cache.format-max-size:1000}")
    private int formatCacheMaxSize;

    @Value("${slide.cache.thumbnail-max-size:500}")
    private int thumbnailCacheMaxSize;

    @Value("${slide.cache.tile-max-size:10000}")
    private int tileCacheMaxSize;

    @Value("${slide.cache.metadata-max-size:1000}")
    private int metadataCacheMaxSize;

    @Value("${slide.cache.region-max-size:500}")
    private int regionCacheMaxSize;

    @Value("${slide.cache.expire-after-minutes:60}")
    private int expireAfterMinutes;

    /**
     * 创建基础缓存构建器。
     *
     * @return Caffeine构建器。
     */
    private Caffeine<Object, Object> createBaseCacheBuilder() {
        Caffeine<Object, Object> builder = Caffeine.newBuilder();

        // 设置过期策略
        if (cacheProperties.getExpireAfterWriteMinutes() > 0) {
            builder.expireAfterWrite(cacheProperties.getExpireAfterWriteMinutes(), TimeUnit.MINUTES);
        }

        if (cacheProperties.getExpireAfterAccessMinutes() > 0) {
            builder.expireAfterAccess(cacheProperties.getExpireAfterAccessMinutes(), TimeUnit.MINUTES);
        }

        // 是否记录统计
        if (cacheProperties.isRecordStats()) {
            builder.recordStats();
        }

        return builder;
    }

    /**
     * 格式检测缓存。
     * 缓存文件格式检测结果。
     *
     * @return 格式检测缓存。
     */
    @Bean
    public Cache<String, String> formatCache() {
        log.info("初始化格式检测缓存，条目数上限: {}", formatCacheMaxSize);

        return createBaseCacheBuilder()
                .maximumSize(formatCacheMaxSize)
                .build();
    }

    /**
     * 缩略图缓存。
     * 缓存切片缩略图数据。
     *
     * @return 缩略图缓存。
     */
    @Bean
    public Cache<String, byte[]> thumbnailCache() {
        log.info("初始化缩略图缓存，权重上限: {}MB", cacheProperties.getMaximumWeightMB());

        return createBaseCacheBuilder()
                .weigher((key, value) -> ((byte[]) value).length)
                .maximumWeight(cacheProperties.getMaximumWeight())
                .build();
    }

    /**
     * 瓦片缓存。
     * 缓存切片瓦片数据。
     *
     * @return 瓦片缓存。
     */
    @Bean
    public Cache<String, byte[]> tileCache() {
        log.info("初始化瓦片缓存，条目数上限: {}", tileCacheMaxSize);

        return createBaseCacheBuilder()
                .maximumSize(tileCacheMaxSize)
                .build();
    }

    /**
     * 元数据缓存。
     * 缓存切片元数据信息。
     *
     * @return 元数据缓存。
     */
    @Bean
    public Cache<String, Object> metadataCache() {
        log.info("初始化元数据缓存，条目数上限: {}", metadataCacheMaxSize);

        return createBaseCacheBuilder()
                .maximumSize(metadataCacheMaxSize)
                .build();
    }

    /**
     * 区域缓存。
     * 缓存切片区域数据。
     *
     * @return 区域缓存。
     */
    @Bean
    public Cache<String, byte[]> regionCache() {
        log.info("初始化区域缓存，条目数上限: {}", regionCacheMaxSize);

        return createBaseCacheBuilder()
                .maximumSize(regionCacheMaxSize)
                .build();
    }

    // 为了保持向后兼容，保留原有的Bean名称

    @Bean(name = "slideFormatCache")
    public Cache<String, String> slideFormatCache() {
        return formatCache();
    }

    @Bean(name = "slideThumbnailCache")
    public Cache<String, byte[]> slideThumbnailCache() {
        return thumbnailCache();
    }

    @Bean(name = "slideTileCache")
    public Cache<String, byte[]> slideTileCache() {
        return tileCache();
    }

    @Bean(name = "slideMetadataCache")
    public Cache<String, Object> slideMetadataCache() {
        return metadataCache();
    }

    @Bean(name = "slideRegionCache")
    public Cache<String, byte[]> slideRegionCache() {
        return regionCache();
    }
}
