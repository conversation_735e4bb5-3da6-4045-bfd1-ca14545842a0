package cn.ccaa.slice.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.PostConstruct;

/**
 * 切片解析器配置类
 * 用于配置各种切片解析器的参数
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "slide")
@Data
@Slf4j
public class SlideParserConfig {
    
    /**
     * 是否启用OpenSlide解析器
     */
    private boolean enableOpenSlide = true;
    
    /**
     * 是否启用SqraySlide解析器
     */
    private boolean enableSqraySlide = true;
    
    /**
     * OpenSlide解析器配置
     */
    private OpenSlideConfig openSlide = new OpenSlideConfig();
    
    /**
     * SqraySlide解析器配置
     */
    private SqraySlideConfig sqraySlide = new SqraySlideConfig();
    
    @PostConstruct
    public void init() {
        log.info("切片解析器配置初始化完成");
        if (getOpenSlide() != null) {
            log.info("OpenSlide配置: enabled={}, libraryPath={}", getOpenSlide().isEnabled(), getOpenSlide().getLibraryPath());
        }
        if (getSqraySlide() != null) {
            log.info("SqraySlide配置: enabled={}, libraryPath={}", getSqraySlide().isEnabled(), getSqraySlide().getLibraryPath());
        }
    }
    
    /**
     * OpenSlide解析器配置类
     */
    @Data
    public static class OpenSlideConfig {
        
        /**
         * 是否启用
         */
        private boolean enabled = true;
        
        /**
         * OpenSlide库路径
         * 如果为空，则使用系统路径
         */
        private String libraryPath = "";
        
        /**
         * 是否使用自动检测格式
         * 如果为true，则使用OpenSlide的自动检测功能
         * 如果为false，则根据文件扩展名判断
         */
        private boolean useAutoDetect = true;
        
        /**
         * 支持的文件格式列表（扩展名）
         * 如果useAutoDetect为false，则使用此列表检查
         */
        private String[] supportedFormats = {
            ".svs", ".tif", ".tiff", ".ndpi", ".vms", ".vmu", ".scn", 
            ".mrxs", ".bif", ".svslide", ".isyntax"
        };
        
        /**
         * JPEG质量设置（1-100）
         * 值越高，图像质量越好，但文件大小也越大
         */
        private int jpegQuality = 90;
        
        /**
         * 是否使用背景色填充边界
         */
        private boolean fillBackground = true;
        
        /**
         * 背景色（RGB格式，例如：255,255,255为白色）
         */
        private String backgroundColor = "255,255,255";
    }
    
    /**
     * SqraySlide解析器配置类
     */
    @Data
    public static class SqraySlideConfig {
        
        /**
         * 是否启用
         */
        private boolean enabled = true;
        
        /**
         * SqraySlide库路径
         * 如果为空，则使用系统路径
         */
        private String libraryPath = "/usr/local/sqrayslide/lib/libsqrayslideservice.so";
        
        /**
         * JPEG质量设置（1-100）
         * 值越高，图像质量越好，但文件大小也越大
         */
        private int jpegQuality = 95;
        
        /**
         * 支持的文件格式列表（扩展名）
         */
        private String[] supportedFormats = {".sdpc"};
        
        /**
         * 是否使用透明背景而非白色背景
         * true: 不填充背景，保持透明
         * false: 使用白色背景填充
         */
        private boolean useTransparentBackground = false;
        
        /**
         * 背景色（RGB格式，例如：255,255,255为白色，0,0,0为黑色）
         * 仅在useTransparentBackground为false时生效
         */
        private String backgroundColor = "255,255,255";
        
        /**
         * 是否自动裁剪白色边框
         * true: 自动检测并移除周边的白色边框
         * false: 保持原始尺寸
         */
        private boolean autoTrimWhiteBorder = true;
        
        /**
         * 是否启用预加载功能
         * true: 启用异步预加载周围瓦片，提高浏览流畅度但增加内存使用
         * false: 禁用预加载，减少内存占用和后台任务，提高响应速度
         */
        private boolean preloadEnabled = true;
        
        /**
         * 最大缓存大小
         * 控制内存中缓存的瓦片数量，避免内存不足
         */
        private int maxCacheSize = 20000;
    }
}
