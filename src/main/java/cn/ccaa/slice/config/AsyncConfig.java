package cn.ccaa.slice.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.aop.interceptor.SimpleAsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 异步任务配置。
 *
 * <AUTHOR>
 */
@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(AsyncConfig.class);

    /**
     * 配置默认的异步任务执行器。
     *
     * 核心线程数：5
     * 最大线程数：10
     * 队列容量：25
     * 线程名前缀：async-task-
     * 拒绝策略：CallerRunsPolicy
     * 等待任务完成：true
     * 等待时间：60秒
     *
     * @return 异步任务执行器。
     */
    @Override
    @Bean(name = "taskExecutor")
    public Executor getAsyncExecutor() {
        logger.info("创建默认异步任务执行器");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(25);
        executor.setThreadNamePrefix("async-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();

        return executor;
    }

    /**
     * 配置异步任务异常处理器。
     *
     * @return 异步任务异常处理器。
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new SimpleAsyncUncaughtExceptionHandler();
    }

    /**
     * 配置文件上传专用执行器。
     *
     * 文件上传任务通常IO密集，可以设置更多的线程。
     *
     * 核心线程数：20
     * 最大线程数：50
     * 队列容量：100
     * 线程名前缀：file-upload-
     * 拒绝策略：CallerRunsPolicy
     * 等待任务完成：true
     * 等待时间：300秒
     *
     * @return 文件上传专用执行器。
     */
    @Bean(name = "uploadExecutor")
    public Executor uploadExecutor() {
        logger.info("创建文件上传专用执行器");

        // 创建延迟初始化的执行器
        LazyThreadPoolExecutor lazyExecutor = new LazyThreadPoolExecutor("uploadExecutor", () -> {
            logger.info("延迟初始化文件上传专用执行器");
            try {
                // 创建并配置优先级线程池
                PriorityThreadPoolTaskExecutor executor = new PriorityThreadPoolTaskExecutor();
                executor.setCorePoolSize(20);
                executor.setMaxPoolSize(50);
                executor.setQueueCapacity(100);
                executor.setThreadNamePrefix("file-upload-");
                executor.setKeepAliveSeconds(180);
                executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
                executor.setWaitForTasksToCompleteOnShutdown(true);
                executor.setAwaitTerminationSeconds(300);
                
                // 显式初始化线程池
                executor.initialize();

                // 验证线程池是否正确初始化
                ThreadPoolExecutor threadPoolExecutor = executor.getThreadPoolExecutor();
                logger.info("文件上传专用执行器初始化完成: 核心线程数={}, 最大线程数={}, 队列容量={}, 线程保活时间={}秒",
                        threadPoolExecutor.getCorePoolSize(),
                        threadPoolExecutor.getMaximumPoolSize(),
                        threadPoolExecutor.getQueue().remainingCapacity(),
                        threadPoolExecutor.getKeepAliveTime(java.util.concurrent.TimeUnit.SECONDS));

                return executor;
            } catch (Exception e) {
                logger.error("文件上传专用执行器初始化失败: {}", e.getMessage(), e);
                // 如果初始化失败，创建一个简单的标准线程池作为后备
                ThreadPoolTaskExecutor backupExecutor = new ThreadPoolTaskExecutor();
                backupExecutor.setCorePoolSize(10);
                backupExecutor.setMaxPoolSize(20);
                backupExecutor.setQueueCapacity(50);
                backupExecutor.setThreadNamePrefix("backup-upload-");
                backupExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
                backupExecutor.initialize();
                logger.info("使用后备文件上传执行器");
                return backupExecutor;
            }
        });

        // 不尝试提前初始化，真正实现延迟初始化
        logger.info("文件上传执行器将在第一次使用时自动初始化");
        // 注意：不要在这里调用getDelegate()或execute()方法，这会触发提前初始化

        return lazyExecutor;
    }

    /**
     * 配置切片解析专用执行器。
     *
     * 切片解析可能比较耗CPU，控制线程数。
     *
     * 核心线程数：4
     * 最大线程数：8
     * 队列容量：20
     * 线程名前缀：slide-parser-
     * 拒绝策略：CallerRunsPolicy
     * 等待任务完成：true
     * 等待时间：300秒
     *
     * @return 切片解析专用执行器。
     */
    @Bean(name = "slideParserExecutor")
    public Executor slideParserExecutor() {
        logger.info("创建切片解析专用执行器");

        // 创建延迟初始化的执行器
        LazyThreadPoolExecutor lazyExecutor = new LazyThreadPoolExecutor("slideParserExecutor", () -> {
            logger.info("延迟初始化切片解析专用执行器");
            try {
                // 创建并配置优先级线程池
                PriorityThreadPoolTaskExecutor executor = new PriorityThreadPoolTaskExecutor();
                executor.setCorePoolSize(4);
                executor.setMaxPoolSize(8);
                executor.setQueueCapacity(20);
                executor.setThreadNamePrefix("slide-parser-");
                executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
                executor.setWaitForTasksToCompleteOnShutdown(true);
                executor.setAwaitTerminationSeconds(300);

                // 显式初始化线程池
                executor.initialize();

                // 验证线程池是否正确初始化
                ThreadPoolExecutor threadPoolExecutor = executor.getThreadPoolExecutor();
                logger.info("切片解析专用执行器初始化完成: 核心线程数={}, 最大线程数={}, 队列容量={}",
                        threadPoolExecutor.getCorePoolSize(),
                        threadPoolExecutor.getMaximumPoolSize(),
                        threadPoolExecutor.getQueue().remainingCapacity());

                // 不在初始化时测试执行任务，避免提前初始化问题
                // executor.execute(() -> logger.debug("测试切片解析执行器"));

                return executor;
            } catch (Exception e) {
                logger.error("切片解析专用执行器初始化失败: {}", e.getMessage(), e);
                // 如果初始化失败，创建一个简单的标准线程池作为后备
                ThreadPoolTaskExecutor backupExecutor = new ThreadPoolTaskExecutor();
                backupExecutor.setCorePoolSize(2);
                backupExecutor.setMaxPoolSize(4);
                backupExecutor.setQueueCapacity(10);
                backupExecutor.setThreadNamePrefix("backup-parser-");
                backupExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
                backupExecutor.initialize();
                logger.info("使用后备切片解析执行器");
                return backupExecutor;
            }
        });

        return lazyExecutor;
    }

    /**
     * 配置切片分析专用执行器。
     *
     * 切片分析可能比较耗CPU，控制线程数。
     *
     * 核心线程数：4
     * 最大线程数：8
     * 队列容量：20
     * 线程名前缀：slice-analyzer-
     * 拒绝策略：CallerRunsPolicy
     * 等待任务完成：true
     * 等待时间：300秒
     *
     * @return 切片分析专用执行器。
     */
    @Bean(name = "sliceAnalyzerExecutor")
    public Executor sliceAnalyzerExecutor() {
        logger.info("创建切片分析专用执行器");

        // 创建延迟初始化的执行器
        LazyThreadPoolExecutor lazyExecutor = new LazyThreadPoolExecutor("sliceAnalyzerExecutor", () -> {
            logger.info("延迟初始化切片分析专用执行器");
            try {
                // 创建并配置优先级线程池
                PriorityThreadPoolTaskExecutor executor = new PriorityThreadPoolTaskExecutor();
                executor.setCorePoolSize(4);
                executor.setMaxPoolSize(8);
                executor.setQueueCapacity(20);
                executor.setThreadNamePrefix("slice-analyzer-");
                executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
                executor.setWaitForTasksToCompleteOnShutdown(true);
                executor.setAwaitTerminationSeconds(300);

                // 显式初始化线程池
                executor.initialize();

                // 验证线程池是否正确初始化
                ThreadPoolExecutor threadPoolExecutor = executor.getThreadPoolExecutor();
                logger.info("切片分析专用执行器初始化完成: 核心线程数={}, 最大线程数={}, 队列容量={}",
                        threadPoolExecutor.getCorePoolSize(),
                        threadPoolExecutor.getMaximumPoolSize(),
                        threadPoolExecutor.getQueue().remainingCapacity());

                // 不在初始化时测试执行任务，避免提前初始化问题
                // executor.execute(() -> logger.debug("测试切片分析执行器"));

                return executor;
            } catch (Exception e) {
                logger.error("切片分析专用执行器初始化失败: {}", e.getMessage(), e);
                // 如果初始化失败，创建一个简单的标准线程池作为后备
                ThreadPoolTaskExecutor backupExecutor = new ThreadPoolTaskExecutor();
                backupExecutor.setCorePoolSize(2);
                backupExecutor.setMaxPoolSize(4);
                backupExecutor.setQueueCapacity(10);
                backupExecutor.setThreadNamePrefix("backup-analyzer-");
                backupExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
                backupExecutor.initialize();
                logger.info("使用后备切片分析执行器");
                return backupExecutor;
            }
        });

        return lazyExecutor;
    }

    /**
     * 上传任务异步线程池
     * 专门用于处理上传到第三方系统的任务
     */
    @Bean("uploadTaskExecutor")
    public Executor uploadTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("upload-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
