package cn.ccaa.slice.core.constant;

/**
 * 结果码常量类
 * 定义API返回的结果码
 *
 * <AUTHOR>
 */
public final class ResultCode {

    private ResultCode() {
        // 私有构造函数防止实例化
    }

    /**
     * 成功码
     */
    public static final String SUCCESS = "200";

    /**
     * 通用错误码
     */
    public static final String ERROR = "ERROR";
    public static final String SYSTEM_ERROR = "SYSTEM_ERROR";
    public static final String BIZ_ERROR = "BIZ_ERROR";

    /**
     * 参数相关错误码
     */
    public static final String PARAM_INVALID = "PARAM_INVALID";
    public static final String PARAM_BIND_ERROR = "PARAM_BIND_ERROR";
    public static final String CONSTRAINT_VIOLATION = "CONSTRAINT_VIOLATION";

    /**
     * 文件相关错误码
     */
    public static final String FILE_NOT_FOUND = "FILE_NOT_FOUND";
    public static final String FILE_TOO_LARGE = "FILE_TOO_LARGE";
    public static final String FILE_FORMAT_ERROR = "FILE_FORMAT_ERROR";

    /**
     * 切片相关错误码
     */
    public static final String SLIDE_ERROR = "SLIDE_ERROR";
    public static final String SLIDE_INFO_ERROR = "SLIDE_INFO_ERROR";
    public static final String LEVEL_INFO_ERROR = "LEVEL_INFO_ERROR";
    public static final String METADATA_ERROR = "METADATA_ERROR";

    /**
     * 上传相关错误码
     */
    public static final String UPLOAD_ERROR = "UPLOAD_ERROR";
    public static final String TASK_NOT_FOUND = "TASK_NOT_FOUND";

    /**
     * 缓存相关错误码
     */
    public static final String CACHE_ERROR = "CACHE_ERROR";

    /**
     * IO相关错误码
     */
    public static final String IO_ERROR = "IO_ERROR";
    public static final String RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND";

    /**
     * 并发相关错误码
     */
    public static final String CONCURRENT_ERROR = "CONCURRENT_ERROR";
}
