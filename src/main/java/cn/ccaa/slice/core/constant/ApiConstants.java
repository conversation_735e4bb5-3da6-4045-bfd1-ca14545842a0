package cn.ccaa.slice.core.constant;

/**
 * API常量类
 * 定义API路径和其他常量
 *
 * <AUTHOR>
 */
public final class ApiConstants {

    private ApiConstants() {
        // 私有构造函数防止实例化
    }

    /**
     * API基础路径
     */
    public static final class Base {
        public static final String SLIDES = "/api/slides";
        public static final String SLICE = "/api/slice";
        public static final String UPLOAD = "/api/upload";
        public static final String ADMIN_CACHE = "/api/admin/cache";
        public static final String ADMIN_FILE_CACHE = "/api/admin/file-cache";
        public static final String DZI = "/api/dzi";
    }

    /**
     * 切片API端点
     */
    public static final class SlideEndpoints {
        public static final String DETECT = "/detect";
        public static final String INFO = "/info";
        public static final String LEVEL = "/level";
        public static final String TILE = "/tile";
        public static final String THUMBNAIL = "/thumbnail";
        public static final String LABEL = "/label";
        public static final String METADATA = "/metadata";
        public static final String REGION = "/region";
    }

    /**
     * 上传API端点
     */
    public static final class UploadEndpoints {
        public static final String TASK = "/task";
        public static final String FILE = "/file";
        public static final String PROGRESS = "/progress";
        public static final String PROGRESS_BATCH = "/progress/batch";
        public static final String CANCEL = "/cancel";
        public static final String TEMP = "/temp";
        public static final String UPLOADED_CHUNKS = "/chunks";
    }

    /**
     * 切片分析API端点
     */
    public static final class SliceEndpoints {
        public static final String ANALYZE = "/analyze";
        public static final String CANCEL = "/analyze/cancel";
        public static final String CAPTURE = "/capture";
        public static final String AI_CAPTURE_BATCH = "/SaveAIQpCaptureBatch";
    }

    /**
     * 缓存管理API端点
     */
    public static final class CacheEndpoints {
        public static final String CLEAN = "/clean";
        public static final String STATUS = "/status";
        public static final String STATS = "/stats";
    }

    /**
     * DZI API端点
     */
    public static final class DziEndpoints {
        public static final String DESCRIPTOR = "/{identifier}.dzi";
        public static final String TILE = "/{identifier}_files/{level}/{x}_{y}.jpg";
        public static final String EXPORT = "/export/{identifier}";
    }

    /**
     * 默认值常量
     */
    public static final class Defaults {
        public static final int DEFAULT_LEVEL = 0;
        public static final int DEFAULT_THUMBNAIL_SIZE = 300;
        public static final int MAX_REGION_SIZE = 1000; // 限制区域最大尺寸
        public static final int DEFAULT_JPEG_QUALITY = 90; // JPEG压缩质量
        public static final int DEFAULT_BATCH_SIZE = 50; // 批处理大小
    }

    /**
     * 内容类型常量
     */
    public static final class ContentType {
        public static final String MULTIPART_FORM_DATA = "multipart/form-data";
    }
}
