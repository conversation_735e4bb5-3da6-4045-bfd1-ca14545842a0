package cn.ccaa.slice.core.model;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 切片信息实体类
 * 用于存储切片文件相关信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SliceInfo {
    /**
     * ID
     */
    private String id;
    
    /**
     * 切片号
     */
    private String sliceNo;
    
    /**
     * 原始切片号
     */
    private String initSliceNo;
    
    /**
     * 扫描格式
     */
    private String scanFormat;
    
    /**
     * 扫描倍率
     */
    private Integer scanZoom;
    
    /**
     * 原始像素宽
     */
    private Integer origPixWidth;
    
    /**
     * 原始像素高
     */
    private Integer origPixHeight;
    
    /**
     * 切片文件名
     */
    private String sliceFileName;
    
    /**
     * 文件格式(1.ndpi,2.sdpc,3.svs,4.kfb,5.tron)
     */
    private Integer fmtCode;
    
    /**
     * 切片厂商(1.滨松,2.江丰,3.徕卡,4.生强)
     */
    private Integer sliceMfrCode;
    
    /**
     * 切片状态(1.未上传,2.上传中,3.未分析,4.分析中,5.分析完成,6.分析失败,7.解析失败)
     */
    private Integer sliceStas;
    
    /**
     * 切片来源代码(1.本地上传,2.免上传,3.蚂蚁工具)
     */
    private Integer sliceSourceCode;
    
    /**
     * 机构ID
     */
    private String orgId;
    
    /**
     * 院区ID
     */
    private String ahId;
    
    /**
     * 病理号
     */
    private String pathreqNo;
    
    /**
     * 是否关联任务(0.否,1.是)
     */
    private Integer linktaskFlag;
    
    /**
     * 是否关联病例(0.否,1.是)
     */
    private Integer linkcaseFlag;
    
    /**
     * 关联病例用户ID
     */
    private String linkcaseUserId;
    
    /**
     * 关联病例时间
     */
    private Date linkcaseTime;
    
    /**
     * 解除关联病例用户ID
     */
    private String unlinkcaseUserId;
    
    /**
     * 解除关联病例时间
     */
    private Date unlinkcaseTime;
    
    /**
     * 是否构检医嘱切片(0.否,1.是)
     */
    private Integer seosFlag;
    
    /**
     * 标签图文件路径
     */
    private String labelImagePath;
    
    /**
     * 缩略图文件路径
     */
    private String thumbImagePath;
    
    /**
     * 原始文件路径
     */
    private String originalFilePath;
    
    /**
     * 上传时间
     */
    private Date uploadTime;
    
    /**
     * 上传用户ID
     */
    private String uploadUserId;
    
    /**
     * 是否疑似良性(0.否,1.是)
     */
    private Integer ppiFlag;
    
    /**
     * 有效标志(0.否,1.是)
     */
    private Integer validFlag;
    
    /**
     * 创建人ID
     */
    private String crterId;
    
    /**
     * 创建人姓名
     */
    private String crterName;
    
    /**
     * 创建时间
     */
    private Date crteTime;
    
    /**
     * 最后修改人ID
     */
    private String lastModifierId;
    
    /**
     * 最后修改人姓名
     */
    private String lastModifierName;
    
    /**
     * 最后修改时间
     */
    private Date lastModifiedTime;
    
    /**
     * 玻片图文件路径
     */
    private String slideImagePath;
} 
