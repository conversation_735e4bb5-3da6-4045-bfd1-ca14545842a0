package cn.ccaa.slice.core.model;

import lombok.Data;

/**
 * 颜色校正参数
 * 用于调整切片图像颜色效果
 * <AUTHOR>
 */
@Data
public class ColorCorrectionParams {
    
    /**
     * 亮度调整参数，范围为-1.0到1.0
     * 0表示不变，正值增加亮度，负值降低亮度
     */
    private float brightness = 0.0f;
    
    /**
     * 对比度调整参数，范围为0.0到2.0
     * 1表示不变，大于1增加对比度，小于1降低对比度
     */
    private float contrast = 1.0f;
    
    /**
     * 伽马值调整，范围为0.1到3.0
     * 1表示不变，小于1增加亮部细节，大于1增加暗部细节
     */
    private float gamma = 1.0f;
    
    /**
     * 饱和度调整，范围为0.0到2.0
     * 1表示不变，大于1增加饱和度，小于1降低饱和度，0为灰度图
     */
    private float saturation = 1.0f;
    
    /**
     * 色调调整，范围为-180到180度
     * 0表示不变，正值顺时针旋转色调，负值逆时针旋转色调
     */
    private float hue = 0.0f;
    
    /**
     * 是否反转颜色
     * true表示反转，类似底片效果
     */
    private boolean invertColors = false;
    
    /**
     * 红色通道增益，范围为0.0到2.0
     * 1表示不变，大于1增强红色，小于1减弱红色
     */
    private float redGain = 1.0f;
    
    /**
     * 绿色通道增益，范围为0.0到2.0
     * 1表示不变，大于1增强绿色，小于1减弱绿色
     */
    private float greenGain = 1.0f;
    
    /**
     * 蓝色通道增益，范围为0.0到2.0
     * 1表示不变，大于1增强蓝色，小于1减弱蓝色
     */
    private float blueGain = 1.0f;
    
    /**
     * 锐化级别，范围为0到2
     * 0=无锐化，1=轻度锐化，2=强度锐化
     */
    private int sharpen = 0;
    
    /**
     * 颜色风格
     * 0=默认，1=H&E标准化
     */
    private int colorStyle = 0;
    
    /**
     * 验证参数是否在合法范围内，并自动调整到合法值
     */
    public void validate() {
        brightness = clamp(brightness, -1.0f, 1.0f);
        contrast = clamp(contrast, 0.0f, 2.0f);
        gamma = clamp(gamma, 0.1f, 3.0f);
        saturation = clamp(saturation, 0.0f, 2.0f);
        hue = clamp(hue, -180.0f, 180.0f);
        redGain = clamp(redGain, 0.0f, 2.0f);
        greenGain = clamp(greenGain, 0.0f, 2.0f);
        blueGain = clamp(blueGain, 0.0f, 2.0f);
        sharpen = clamp(sharpen, 0, 2);
        colorStyle = clamp(colorStyle, 0, 1);
    }
    
    /**
     * 限制值在指定范围内
     * 
     * @param value 原始值
     * @param min 最小值
     * @param max 最大值
     * @return 调整后的值
     */
    private float clamp(float value, float min, float max) {
        return Math.max(min, Math.min(max, value));
    }
    
    /**
     * 限制整数值在指定范围内
     * 
     * @param value 原始值
     * @param min 最小值
     * @param max 最大值
     * @return 调整后的值
     */
    private int clamp(int value, int min, int max) {
        return Math.max(min, Math.min(max, value));
    }
} 
