package cn.ccaa.slice.core.model;

import java.io.File;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 切片解析结果
 * 包含切片元数据、缩略图和标签图
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SliceAnalysisResult {
    
    /**
     * 切片元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 缩略图文件
     */
    private File thumbnailImage;
    
    /**
     * 标签图文件
     */
    private File labelImage;
    
    /**
     * 原始切片文件的类型代码
     * 1.ndpi, 2.sdpc, 3.svs, 4.kfb, 5.tron
     */
    private Integer fileTypeCode;
    
    /**
     * 切片厂商代码
     * 1.滨松, 2.江丰, 3.徕卡, 4.生强
     */
    private Integer manufacturerCode;
    
    /**
     * 玻片图文件 (macro 整体图)
     */
    private File slideImage;
} 
