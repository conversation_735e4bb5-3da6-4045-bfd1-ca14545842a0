package cn.ccaa.slice.core.model;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数字切片实体类（严格对应t_c_slice表结构）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_c_slice")
public class SliceEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键fileId */
    @TableId
    private String id;

    /** qpId，cs端上传时必填 */
    private String sliceId;

    /** 文件大小 */
    private Double qpSize;

    /** 任务ID - 对应数据库task_id字段，用于本地上传场景 */
    @TableField("task_id")
    private String taskId;

    /** token */
    private String tempAuth;

    /** 切片号 */
    private String sliceNo;

    /** 扫描格式 */
    private String scanFormat;

    /** 扫描倍率 */
    private String enlarge;

    /** 切片名称 */
    private String qpName;

    /** 上传时间 */
    private LocalDateTime uploadTime;

    /** 切片状态（0处理中，初始状态和合并中；1解析成功；2.解析失败） */
    private String qpState;

    /** 创建时间 */
    private LocalDateTime crteTime;

    /** 最后修改时间 */
    private LocalDateTime lastModifiedTime;
    
    /** 文件存储相对路径 */
    private String physicPath;
    
    /** 机构ID */
    @TableField("org_id")
    private String orgId;
    
    /** 院区ID */
    @TableField("ah_id")
    private String ahId;
    
    /** 项目ID */
    @TableField("proj_id")
    private String projId;
    
    /** 对接产品代码 */
    @TableField("partner_code")
    private String partnerCode;

    /** 原始像素宽 */
    @TableField("orig_pix_width")
    private Integer origPixWidth;

    /** 原始像素高 */
    @TableField("orig_pix_height")
    private Integer origPixHeight;

    /** 分辨率字符串，格式如 "0.243000*0.243000" */
    private String resolution;

        /** 切片层级数量 */
    @TableField("levels_cnt")
    private Integer levelsCnt;
} 
