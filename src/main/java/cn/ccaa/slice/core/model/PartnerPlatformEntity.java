package cn.ccaa.slice.core.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 对接平台URL及私钥配置表实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_partner_platform")
public class PartnerPlatformEntity {
    @TableId
    private String id;
    private String partnerCode;
    private String url;
    private String privateKey;
} 