package cn.ccaa.slice.core.upload;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * 上传任务模型
 * 用于管理上传操作
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public class UploadTask {
    /**
     * 任务ID
     */
    private final String taskId;

    /**
     * 原始文件名
     */
    private final String fileName;

    /**
     * 存储路径
     */
    private final String storagePath;

    /**
     * 文件总大小(字节)
     */
    private final long fileSize;

    /**
     * 上传者ID
     */
    private final String uploaderId;

    /**
     * 上传进度
     */
    private final UploadProgress progress;

    /**
     * 上传异步操作
     */
    @Setter
    private CompletableFuture<Void> uploadFuture;

    /**
     * 上传百分比
     */
    @Setter
    private Double uploadPercentage;

    /**
     * 分块信息
     */
    private final Map<String, String> chunks = new ConcurrentHashMap<>();

    /**
     * 分块信息
     */
    private final Map<Integer, ChunkInfo> chunkInfoMap = new ConcurrentHashMap<>();

    /**
     * 元数据
     */
    private final Map<String, Object> metadata = new ConcurrentHashMap<>();

    /**
     * 构造函数
     *
     * @param taskId 任务ID
     * @param fileName 原始文件名
     * @param storagePath 存储路径
     * @param fileSize 文件总大小
     * @param uploaderId 上传者ID
     */
    public UploadTask(String taskId, String fileName, String storagePath, long fileSize, String uploaderId) {
        this.taskId = taskId;
        this.fileName = fileName;
        this.storagePath = storagePath;
        this.fileSize = fileSize;
        this.uploaderId = uploaderId;
        this.progress = new UploadProgress(taskId, fileName, storagePath, fileSize, uploaderId);
    }

    /**
     * 获取上传者ID（别名方法，等同于getUploaderId）
     *
     * @return 上传者ID
     */
    public String getUserId() {
        return uploaderId;
    }

    /**
     * 获取上传状态
     *
     * @return 上传状态
     */
    public UploadStatus getStatus() {
        return progress.getStatus();
    }

    /**
     * 获取文件总大小（别名方法，等同于getFileSize）
     *
     * @return 文件总大小
     */
    public long getTotalSize() {
        return fileSize;
    }

    /**
     * 添加分块信息
     *
     * @param chunkIndex 分块索引
     * @param chunkSize 分块大小
     */
    public void addChunkInfo(int chunkIndex, long chunkSize) {
        chunkInfoMap.put(chunkIndex, new ChunkInfo(chunkIndex, chunkSize));
    }

    /**
     * 标记分块上传完成
     *
     * @param chunkIndex 分块索引
     */
    public void markChunkCompleted(int chunkIndex) {
        ChunkInfo chunkInfo = chunkInfoMap.get(chunkIndex);
        if (chunkInfo != null) {
            chunkInfo.setStatus(ChunkStatus.COMPLETED);
            chunkInfo.setEndTime(LocalDateTime.now());

            // 更新进度
            progress.addUploadedSize(chunkInfo.getSize());
        }
    }

    /**
     * 设置元数据
     *
     * @param key 键
     * @param value 值
     */
    public void setMetadata(String key, Object value) {
        metadata.put(key, value);
    }

    /**
     * 获取元数据
     *
     * @param key 键
     * @return 值
     */
    public Object getMetadata(String key) {
        return metadata.get(key);
    }

    /**
     * 获取所有元数据
     *
     * @return 元数据Map
     */
    public Map<String, Object> getAllMetadata() {
        return metadata;
    }

    /**
     * 获取文件URL
     *
     * @return 文件URL
     */
    public String getFileUrl() {
        return (String) metadata.get("fileUrl");
    }

    /**
     * 取消上传任务
     */
    public void cancel() {
        if (uploadFuture != null && !uploadFuture.isDone()) {
            uploadFuture.cancel(true);
        }
        progress.markAsCanceled();
    }

    /**
     * 获取已上传大小
     *
     * @return 已上传大小(字节)
     */
    public long getUploadedSize() {
        return progress.getUploadedSize();
    }

    /**
     * 判断任务是否已完成
     *
     * @return 如果任务已完成返回true，否则返回false
     */
    public boolean isCompleted() {
        return progress.isCompleted();
    }

    /**
     * 获取错误消息
     *
     * @return 错误消息
     */
    public String getErrorMessage() {
        return progress.getErrorMessage();
    }

    /**
     * 获取上传进度百分比
     *
     * @return 上传进度百分比(0-100)
     */
    public Double getUploadPercentage() {
        return (double) progress.getPercentage();
    }

    /**
     * 分块信息类
     */
    @Getter
    @RequiredArgsConstructor
    public static class ChunkInfo {
        /**
         * 分块索引
         */
        private final int index;

        /**
         * 分块大小
         */
        private final long size;

        /**
         * 分块状态
         */
        @Setter
        private ChunkStatus status = ChunkStatus.PENDING;

        /**
         * 开始时间
         */
        private final LocalDateTime startTime = LocalDateTime.now();

        /**
         * 结束时间
         */
        @Setter
        private LocalDateTime endTime;
    }

    /**
     * 分块状态枚举
     */
    public enum ChunkStatus {
        /**
         * 等待上传
         */
        PENDING,

        /**
         * 上传中
         */
        UPLOADING,

        /**
         * 已完成
         */
        COMPLETED,

        /**
         * 失败
         */
        FAILED
    }
}
