package cn.ccaa.slice.core.upload;

/**
 * 上传状态枚举
 * 定义上传任务的各种状态
 * <AUTHOR>
 */
public enum UploadStatus {
    /**
     * 初始化中
     */
    INITIALIZING("初始化中"),
    
    /**
     * 等待上传
     */
    PENDING("等待上传"),
    
    /**
     * 上传中
     */
    UPLOADING("上传中"),
    
    /**
     * 暂停中
     */
    PAUSED("暂停中"),
    
    /**
     * 验证中
     */
    VALIDATING("验证中"),
    
    /**
     * 处理中
     */
    PROCESSING("处理中"),
    
    /**
     * 合并中
     */
    MERGING("合并中"),
    
    /**
     * 已完成
     */
    COMPLETED("已完成"),
    
    /**
     * 失败
     */
    FAILED("失败"),
    
    /**
     * 已取消
     */
    CANCELED("已取消");
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 构造函数
     * @param description 状态描述
     */
    UploadStatus(String description) {
        this.description = description;
    }
    
    /**
     * 获取状态描述
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 检查是否为终态（完成、失败、取消）
     * @return 如果是终态返回true，否则返回false
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED || this == CANCELED;
    }
    
    /**
     * 检查是否为进行中状态
     * @return 如果是进行中状态返回true，否则返回false
     */
    public boolean isInProgress() {
        return this == INITIALIZING || this == UPLOADING || 
               this == VALIDATING || this == PROCESSING || this == MERGING;
    }
} 
