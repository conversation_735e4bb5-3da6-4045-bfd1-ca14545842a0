package cn.ccaa.slice.core.upload;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * 上传进度模型
 * 用于跟踪上传任务的进度
 * <AUTHOR>
 */
@Getter
public class UploadProgress {
    /**
     * 任务ID
     */
    private final String taskId;
    
    /**
     * 原始文件名
     */
    private final String fileName;
    
    /**
     * 存储路径
     */
    private final String storagePath;
    
    /**
     * 文件总大小(字节)
     */
    private final long totalSize;
    
    /**
     * 已上传大小(字节)
     */
    private long uploadedSize;
    
    /**
     * 上传状态
     */
    private UploadStatus status;
    
    /**
     * 错误消息
     */
    @Setter
    private String errorMessage;
    
    /**
     * 开始时间
     */
    private final LocalDateTime startTime;
    
    /**
     * 完成时间
     */
    @Setter
    private LocalDateTime endTime;
    
    /**
     * 上传者ID
     */
    private final String uploaderId;
    
    /**
     * 最后更新时间
     */
    @Setter
    private LocalDateTime lastUpdateTime;
    
    /**
     * 构造函数
     * 
     * @param taskId 任务ID
     * @param fileName 原始文件名
     * @param storagePath 存储路径
     * @param totalSize 文件总大小
     * @param uploaderId 上传者ID
     */
    public UploadProgress(String taskId, String fileName, String storagePath, long totalSize, String uploaderId) {
        this.taskId = taskId;
        this.fileName = fileName;
        this.storagePath = storagePath;
        this.totalSize = totalSize;
        this.uploaderId = uploaderId;
        this.uploadedSize = 0;
        this.status = UploadStatus.INITIALIZING;
        this.startTime = LocalDateTime.now();
        this.lastUpdateTime = this.startTime;
    }
    
    /**
     * 设置已上传大小
     * 
     * @param uploadedSize 已上传大小(字节)
     */
    public void setUploadedSize(long uploadedSize) {
        this.uploadedSize = uploadedSize;
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 设置上传状态
     * 
     * @param status 上传状态
     */
    public void setStatus(UploadStatus status) {
        this.status = status;
        this.lastUpdateTime = LocalDateTime.now();
        
        // 如果是终态，设置结束时间
        if (status.isTerminal()) {
            this.endTime = this.lastUpdateTime;
        }
    }
    
    /**
     * 计算上传进度百分比
     * 
     * @return 上传进度百分比(0-100)
     */
    public int getPercentage() {
        if (totalSize <= 0) {
            return 0;
        }
        return (int) (uploadedSize * 100 / totalSize);
    }
    
    /**
     * 增加已上传大小
     * 
     * @param bytes 新增的字节数
     */
    public void addUploadedSize(long bytes) {
        this.uploadedSize += bytes;
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 检查上传是否完成
     * 
     * @return 如果上传完成返回true，否则返回false
     */
    public boolean isCompleted() {
        return status == UploadStatus.COMPLETED;
    }
    
    /**
     * 检查上传是否失败
     * 
     * @return 如果上传失败返回true，否则返回false
     */
    public boolean isFailed() {
        return status == UploadStatus.FAILED;
    }
    
    /**
     * 检查上传是否被取消
     * 
     * @return 如果上传被取消返回true，否则返回false
     */
    public boolean isCanceled() {
        return status == UploadStatus.CANCELED;
    }
    
    /**
     * 标记为上传中
     */
    public void markAsUploading() {
        setStatus(UploadStatus.UPLOADING);
    }
    
    /**
     * 标记为已完成
     */
    public void markAsCompleted() {
        setUploadedSize(totalSize);
        setStatus(UploadStatus.COMPLETED);
    }
    
    /**
     * 标记为失败
     * 
     * @param errorMessage 错误消息
     */
    public void markAsFailed(String errorMessage) {
        setStatus(UploadStatus.FAILED);
        setErrorMessage(errorMessage);
    }
    
    /**
     * 标记为取消
     */
    public void markAsCanceled() {
        setStatus(UploadStatus.CANCELED);
    }
} 
