package cn.ccaa.slice.core.event;

import java.io.Serial;

import org.springframework.context.ApplicationEvent;

import lombok.Getter;

/**
 * 文件上传完成事件。
 * 当文件上传完成后触发。
 *
 * <AUTHOR>
 */
@Getter
public class FileUploadedEvent extends ApplicationEvent {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件路径
     */
    private final String filePath;

    /**
     * 上传者ID
     */
    private final String uploaderId;

    /**
     * 构造函数。
     *
     * @param source     事件源
     * @param filePath   文件路径
     * @param uploaderId 上传者ID
     */
    public FileUploadedEvent(Object source, String filePath, String uploaderId) {
        super(source);
        this.filePath = filePath;
        this.uploaderId = uploaderId;
    }
}
