package cn.ccaa.slice.core.event;

import org.springframework.context.ApplicationEvent;

import lombok.Getter;

import java.io.Serial;

/**
 * 文件删除事件
 * 当文件被删除后触发
 * 
 * <AUTHOR>
 */
@Getter
public class FileDeletedEvent extends ApplicationEvent {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    /**
     * 文件路径
     *

     */
    private final String filePath;
    
    /**
     * 操作者ID
     */
    private final String operatorId;
    
    /**
     * 构造函数
     * 
     * @param source 事件源
     * @param filePath 文件路径
     * @param operatorId 操作者ID
     */
    public FileDeletedEvent(Object source, String filePath, String operatorId) {
        super(source);
        this.filePath = filePath;
        this.operatorId = operatorId;
    }

} 
