package cn.ccaa.slice.core.parser;

import java.util.Map;

import cn.ccaa.slice.core.model.ColorCorrectionParams;

/**
 * 切片解析器接口
 * 定义所有切片解析器共同行为
 * <AUTHOR>
 */
public interface SlideParser {
    
    /**
     * 检查解析器是否支持给定文件格式
     * 
     * @param filePath 文件路径
     * @return 如果支持该格式返回true，否则返回false
     */
    boolean supportsFormat(String filePath);
    
    /**
     * 打开切片文件
     * 
     * @param filePath 文件路径
     * @return 切片对象（不透明对象，由实现类定义具体类型）
     */
    Object openSlide(String filePath);
    
    /**
     * 关闭切片
     * 
     * @param slide 切片对象
     */
    void closeSlide(Object slide);
    
    /**
     * 获取切片基本信息
     * 
     * @param slide 切片对象
     * @return 包含切片信息的Map
     */
    Map<String, Object> getSlideInfo(Object slide);
    
    /**
     * 获取切片指定层级的信息
     * 
     * @param slide 切片对象
     * @param level 层级索引
     * @return 包含层级信息的Map
     */
    Map<String, Object> getLevelInfo(Object slide, int level);
    
    /**
     * 获取指定位置和层级的图块的JPEG数据
     * 
     * @param slide 切片对象
     * @param x 图块X坐标
     * @param y 图块Y坐标
     * @param level 层级索引
     * @return JPEG格式的图块数据
     */
    byte[] getTileJpeg(Object slide, int x, int y, int level);
    
    /**
     * 获取切片的缩略图
     * 
     * @param slide 切片对象
     * @return JPEG格式的缩略图数据
     */
    byte[] getThumbnailJpeg(Object slide);
    
    /**
     * 获取切片标签图
     * 
     * @param slide 切片对象
     * @return JPEG格式的标签图数据
     */
    byte[] getLabelJpeg(Object slide);
    
    /**
     * 获取指定区域的图像数据
     * 
     * @param slide 切片对象
     * @param x 区域左上角X坐标
     * @param y 区域左上角Y坐标
     * @param width 区域宽度
     * @param height 区域高度
     * @param level 层级索引
     * @return JPEG格式的区域图像数据
     */
    byte[] getRegionJpeg(Object slide, int x, int y, int width, int height, int level);
    
    /**
     * 获取解析器名称
     * 
     * @return 解析器名称
     */
    String getParserName();
    
    /**
     * 获取条形码信息
     * 
     * @param slide 切片对象
     * @return 条形码信息，如果不可用则返回null
     */
    default String getBarcode(Object slide) {
        return null;
    }
    
    /**
     * 获取病例描述信息
     * 
     * @param slide 切片对象
     * @return 描述信息，如果不可用则返回null
     */
    default String getDescription(Object slide) {
        return null;
    }
    
    /**
     * 获取扫描仪信息
     * 
     * @param slide 切片对象
     * @return 扫描仪信息Map
     */
    default Map<String, Object> getScannerInfo(Object slide) {
        return Map.of();
    }
    
    /**
     * 获取染色信息
     * 
     * @param slide 切片对象
     * @return 染色信息，如果不可用则返回null
     */
    default String getStaining(Object slide) {
        return null;
    }
    
    /**
     * 获取组织信息
     * 
     * @param slide 切片对象
     * @return 组织信息，如果不可用则返回null
     */
    default String getTissueInfo(Object slide) {
        return null;
    }
    
    /**
     * 获取患者信息
     * 
     * @param slide 切片对象
     * @return 患者信息Map
     */
    default Map<String, Object> getPatientInfo(Object slide) {
        return Map.of();
    }
    
    /**
     * 获取时间戳信息
     * 
     * @param slide 切片对象
     * @return 时间戳信息Map
     */
    default Map<String, Object> getTimestamps(Object slide) {
        return Map.of();
    }
    
    /**
     * 获取指定区域的图像数据（支持颜色校正）
     * 
     * @param slide 切片对象
     * @param x 区域左上角X坐标
     * @param y 区域左上角Y坐标
     * @param width 区域宽度
     * @param height 区域高度
     * @param level 层级索引
     * @param colorParams 颜色校正参数
     * @return JPEG格式的调色区域图像数据
     */
    default byte[] getRegionJpegWithColor(Object slide, int x, int y, int width, int height, int level, ColorCorrectionParams colorParams) {
        // 默认实现：不支持颜色校正的解析器返回原始图像
        return getRegionJpeg(slide, x, y, width, height, level);
    }
    
    /**
     * 获取指定位置和层级的图块的JPEG数据（支持颜色校正）
     * 
     * @param slide 切片对象
     * @param x 图块X坐标
     * @param y 图块Y坐标
     * @param level 层级索引
     * @param colorParams 颜色校正参数
     * @return JPEG格式的调色图块数据
     */
    default byte[] getTileJpegWithColor(Object slide, int x, int y, int level, ColorCorrectionParams colorParams) {
        // 默认实现：不支持颜色校正的解析器返回原始图像
        return getTileJpeg(slide, x, y, level);
    }
    
    /**
     * 检查解析器是否支持颜色校正
     * 
     * @return 如果支持颜色校正返回true，否则返回false
     */
    default boolean supportsColorCorrection() {
        return false;
    }
    
    /**
     * 获取切片玻片图（macro图 / overall preview）
     * 默认返回null，由具体解析器实现；调用方需自行做兜底处理。
     *
     * @param slide 切片对象
     * @return JPEG格式的玻片图数据，如果解析器不支持则返回null
     */
    default byte[] getSlideJpeg(Object slide) {
        return null; // 默认实现返回null，统一服务会进行fallback处理
    }
} 
