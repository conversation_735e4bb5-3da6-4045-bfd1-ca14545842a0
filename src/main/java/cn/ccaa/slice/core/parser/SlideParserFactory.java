package cn.ccaa.slice.core.parser;

import java.io.File;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.ccaa.slice.core.exception.SlideException;

/**
 * 切片解析器工厂
 * 负责选择合适的解析器处理不同格式的切片
 * <AUTHOR>
 */
@Component
public class SlideParserFactory {

    private final List<SlideParser> parsers;
    private static final Logger log = LoggerFactory.getLogger(SlideParserFactory.class);

    /**
     * 构造函数，注入所有可用的解析器
     * Spring会自动注入所有实现了SlideParser接口的Bean
     *
     * @param parsers 所有可用的解析器列表
     */
    public SlideParserFactory(List<SlideParser> parsers) {
        this.parsers = parsers;
    }

    /**
     * 根据文件路径选择合适的解析器
     *
     * @param filePath 文件路径
     * @return 支持该格式的解析器
     * @throws SlideException 如果没有找到支持该格式的解析器
     */
    public SlideParser getParser(String filePath) {
        // 首先根据文件扩展名强制选择解析器，避免解析器检测的歧义
        String fileName = new File(filePath).getName().toLowerCase();
        
        log.info("SlideParserFactory.getParser 开始为文件选择解析器: {}, 文件名: {}", filePath, fileName);
        
        // 优先级1: SDPC格式强制使用SqraySlide解析器
        if (fileName.endsWith(".sdpc")) {
            Optional<SlideParser> sqraySlideParser = parsers.stream()
                    .filter(p -> "sqrayslide".equals(p.getParserName()))
                    .findFirst();

            if (sqraySlideParser.isPresent()) {
                log.info("基于文件扩展名选择SqraySlide解析器处理: {}", filePath);
                
                // 检查SqraySlide解析器是否可用
                try {
                    SlideParser parser = sqraySlideParser.get();
                    if (parser.supportsFormat(filePath)) {
                        log.info("SqraySlide解析器确认支持该文件: {}", filePath);
                        return parser;
                    } else {
                        log.warn("SqraySlide解析器不支持该文件，尽管扩展名是.sdpc: {}", filePath);
                        // 继续尝试其他解析器
                    }
                } catch (Exception e) {
                    log.error("检查SqraySlide解析器支持性时发生异常: {}", e.getMessage(), e);
                    // 继续尝试其他解析器
                }
            } else {
                log.warn("未找到SqraySlide解析器实例，但文件扩展名为.sdpc: {}", filePath);
            }
        }

        // 优先级2: TRON格式强制使用TronSDK解析器
        if (fileName.endsWith(".tron")) {
            Optional<SlideParser> tronSdkParser = parsers.stream()
                    .filter(p -> "tronsdk".equals(p.getParserName()))
                    .findFirst();

            if (tronSdkParser.isPresent()) {
                log.info("基于文件扩展名选择TronSDK解析器处理: {}", filePath);
                
                // 检查TronSDK解析器是否可用
                try {
                    SlideParser parser = tronSdkParser.get();
                    if (parser.supportsFormat(filePath)) {
                        log.info("TronSDK解析器确认支持该文件: {}", filePath);
                        return parser;
                    } else {
                        log.warn("TronSDK解析器不支持该文件，尽管扩展名是.tron: {}", filePath);
                        // 继续尝试其他解析器
                    }
                } catch (Exception e) {
                    log.error("检查TronSDK解析器支持性时发生异常: {}", e.getMessage(), e);
                    // 继续尝试其他解析器
                }
            } else {
                log.warn("未找到TronSDK解析器实例，但文件扩展名为.tron: {}", filePath);
            }
        }

        // 优先级3: OpenSlide支持的格式强制使用OpenSlide解析器
        if (fileName.endsWith(".svs") || fileName.endsWith(".ndpi") ||
            fileName.endsWith(".tif") || fileName.endsWith(".tiff") ||
            fileName.endsWith(".mrxs") || fileName.endsWith(".vms") ||
            fileName.endsWith(".vmu") || fileName.endsWith(".scn") ||
            fileName.endsWith(".bif") || fileName.endsWith(".svslide") ||
            fileName.endsWith(".isyntax")) {

            Optional<SlideParser> openSlideParser = parsers.stream()
                    .filter(p -> "openslide".equals(p.getParserName()))
                    .findFirst();

            if (openSlideParser.isPresent()) {
                log.info("基于文件扩展名选择OpenSlide解析器处理: {}", filePath);
                return openSlideParser.get();
            } else {
                log.warn("文件 {} 应该使用OpenSlide解析器，但未找到OpenSlide解析器实例", filePath);
            }
        }

        // 如果基于扩展名没有找到合适的解析器，才尝试使用解析器的supportsFormat方法检测
        log.info("基于文件扩展名未找到合适解析器，尝试动态检测: {}", filePath);
        
        // 记录所有可用的解析器
        log.debug("当前注册的解析器列表:");
        for (SlideParser parser : parsers) {
            log.debug("- 解析器: {}", parser.getParserName());
        }
        
        // 尝试所有解析器
        for (SlideParser parser : parsers) {
            try {
                log.debug("测试解析器 {} 是否支持文件: {}", parser.getParserName(), filePath);
                if (parser.supportsFormat(filePath)) {
                    log.info("通过动态检测选择解析器: {} 处理文件: {}", parser.getParserName(), filePath);
                    return parser;
                } else {
                    log.debug("解析器 {} 不支持文件: {}", parser.getParserName(), filePath);
                }
            } catch (Exception e) {
                log.warn("解析器 {} 检测文件 {} 时发生异常: {}", parser.getParserName(), filePath, e.getMessage());
            }
        }

        // 如果仍然没有找到支持的解析器，抛出异常
        log.error("没有找到支持的解析器处理文件: {}", filePath);
        throw new SlideException("不支持的切片格式: " + filePath);
    }

    /**
     * 根据解析器名称获取解析器
     *
     * @param parserName 解析器名称
     * @return 指定名称的解析器
     * @throws SlideException 如果没有找到指定名称的解析器
     */
    public SlideParser getParserByName(String parserName) {
        Optional<SlideParser> parser = parsers.stream()
                .filter(p -> p.getParserName().equalsIgnoreCase(parserName))
                .findFirst();

        return parser.orElseThrow(() ->
                new SlideException("未找到指定的解析器: " + parserName));
    }

    /**
     * 获取所有注册的解析器
     *
     * @return 解析器列表
     */
    public List<SlideParser> getAllParsers() {
        return parsers;
    }
}
