package cn.ccaa.slice.core.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用结果包装类
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误码
     */
    private Integer code;
    
    /**
     * 错误信息
     */
    private String message;
    
    /**
     * 返回数据
     */
    private T data;
    
    /**
     * 成功结果，无数据
     *
     * @param <T> 泛型
     * @return 结果
     */
    public static <T> Result<T> success() {
        return success(null);
    }
    
    /**
     * 成功结果，带数据
     *
     * @param data 数据
     * @param <T> 泛型
     * @return 结果
     */
    public static <T> Result<T> success(T data) {
        return Result.<T>builder()
                .success(true)
                .code(0)
                .message("操作成功")
                .data(data)
                .build();
    }
    
    /**
     * 失败结果，带错误信息
     *
     * @param message 错误信息
     * @param <T> 泛型
     * @return 结果
     */
    public static <T> Result<T> failure(String message) {
        return failure(500, message);
    }
    
    /**
     * 失败结果，带错误码和错误信息
     *
     * @param code 错误码
     * @param message 错误信息
     * @param <T> 泛型
     * @return 结果
     */
    public static <T> Result<T> failure(Integer code, String message) {
        return Result.<T>builder()
                .success(false)
                .code(code)
                .message(message)
                .build();
    }
    
    /**
     * 失败结果，带错误码和错误信息（兼容String类型的错误码）
     *
     * @param code 错误码
     * @param message 错误信息
     * @param <T> 泛型
     * @return 结果
     */
    public static <T> Result<T> failure(String code, String message) {
        // 尝试将字符串错误码转换为数字，如果失败则使用500
        Integer numericCode;
        try {
            numericCode = Integer.parseInt(code);
        } catch (NumberFormatException e) {
            numericCode = 500; // 默认服务器错误码
        }
        return failure(numericCode, message);
    }
    
    /**
     * 失败结果，带错误信息（别名方法）
     *
     * @param message 错误信息
     * @param <T> 泛型
     * @return 结果
     */
    public static <T> Result<T> fail(String message) {
        return failure(message);
    }
    
    /**
     * 失败结果，带错误码和错误信息（别名方法）
     *
     * @param code 错误码
     * @param message 错误信息
     * @param <T> 泛型
     * @return 结果
     */
    public static <T> Result<T> fail(String code, String message) {
        return failure(code, message);
    }
    
    /**
     * 失败结果，带错误码和错误信息（别名方法，数字版本）
     *
     * @param code 错误码
     * @param message 错误信息
     * @param <T> 泛型
     * @return 结果
     */
    public static <T> Result<T> fail(Integer code, String message) {
        return failure(code, message);
    }
} 
