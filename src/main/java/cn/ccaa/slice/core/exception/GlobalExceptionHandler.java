package cn.ccaa.slice.core.exception;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import cn.ccaa.slice.core.constant.ResultCode;
import cn.ccaa.slice.core.result.Result;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理切片异常
     *
     * @param e 切片异常
     * @return 错误响应
     */
    @ExceptionHandler(SlideException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleSlideException(SlideException e) {
        log.error("切片异常: {}", e.getMessage(), e);
        return Result.failure(ResultCode.SLIDE_ERROR, e.getMessage());
    }

    /**
     * 处理存储异常
     *
     * @param e 存储异常
     * @return 错误响应
     */
    @ExceptionHandler(StorageException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleStorageException(StorageException e) {
        log.error("存储异常: {}", e.getMessage(), e);
        return Result.failure(ResultCode.ERROR, e.getMessage());
    }

    /**
     * 处理业务异常
     *
     * @param e 业务异常
     * @return 响应结果
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.warn("业务异常：{}", e.getMessage());
        return Result.failure(e.getCode(), e.getMessage());
    }

    /**
     * 处理参数校验异常（@RequestBody）
     *
     * @param e 参数校验异常
     * @return 响应结果
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        String errorMessage = fieldErrors.stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.joining(", "));
        log.warn("参数校验异常：{}", errorMessage);
        return Result.failure(ResultCode.PARAM_INVALID, errorMessage);
    }

    /**
     * 处理参数绑定异常（@ModelAttribute）
     *
     * @param e 参数绑定异常
     * @return 响应结果
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleBindException(BindException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        String errorMessage = fieldErrors.stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.joining(", "));
        log.warn("参数绑定异常：{}", errorMessage);
        return Result.failure(ResultCode.PARAM_BIND_ERROR, errorMessage);
    }

    /**
     * 处理约束违反异常
     *
     * @param e 约束违反异常
     * @return 响应结果
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleConstraintViolationException(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessage = violations.stream()
                .map(violation -> violation.getPropertyPath() + ": " + violation.getMessage())
                .collect(Collectors.joining(", "));
        log.warn("约束违反异常：{}", errorMessage);
        return Result.failure(ResultCode.CONSTRAINT_VIOLATION, errorMessage);
    }

    /**
     * 处理文件上传大小超限异常
     *
     * @param e 文件上传大小超限异常
     * @return 响应结果
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.warn("文件上传大小超限：{}", e.getMessage());
        return Result.failure(ResultCode.FILE_TOO_LARGE, "上传文件过大，请检查文件大小");
    }

    /**
     * 处理IO异常
     *
     * @param e IO异常
     * @return 错误响应
     */
    @ExceptionHandler(java.io.IOException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleIOException(java.io.IOException e) {
        log.error("文件IO异常: {}, 异常类型: {}", e.getMessage(), e.getClass().getName(), e);
        return Result.failure(ResultCode.IO_ERROR, "文件读写错误: " + e.getMessage());
    }

    /**
     * 处理并发相关异常
     *
     * @param e 并发异常
     * @return 错误响应
     */
    @ExceptionHandler({java.util.concurrent.TimeoutException.class,
                       java.util.concurrent.ExecutionException.class,
                       java.util.concurrent.CancellationException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleConcurrencyException(Exception e) {
        log.error("并发处理异常: {}, 异常类型: {}", e.getMessage(), e.getClass().getName(), e);
        return Result.failure(ResultCode.CONCURRENT_ERROR, "并发处理错误: " + e.getMessage());
    }

    /**
     * 处理资源不存在异常
     *
     * @param e 资源不存在异常
     * @return 错误响应
     */
    @ExceptionHandler(java.io.FileNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Result<Void> handleFileNotFoundException(java.io.FileNotFoundException e) {
        log.error("文件不存在异常: {}", e.getMessage(), e);
        return Result.failure(ResultCode.RESOURCE_NOT_FOUND, "请求的文件不存在: " + e.getMessage());
    }

    /**
     * 处理通用异常
     *
     * @param e 异常
     * @return 错误响应
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleException(Exception e) {
        // 记录更详细的异常信息，包括异常类型
        log.error("系统异常: {}, 异常类型: {}", e.getMessage(), e.getClass().getName(), e);
        return Result.failure(ResultCode.SYSTEM_ERROR, "系统内部错误，请联系管理员");
    }
}
