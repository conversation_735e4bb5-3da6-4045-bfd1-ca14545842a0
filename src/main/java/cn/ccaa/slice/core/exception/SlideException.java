package cn.ccaa.slice.core.exception;

import java.io.Serial;

/**
 * 切片处理基础异常类
 * <AUTHOR>
 */
public class SlideException extends RuntimeException {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    /**
     * 使用错误消息构造异常
     * 
     * @param message 错误消息
     */
    public SlideException(String message) {
        super(message);
    }
    
    /**
     * 使用错误消息和原因构造异常
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public SlideException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 使用原因构造异常
     * 
     * @param cause 原因异常
     */
    public SlideException(Throwable cause) {
        super(cause);
    }
} 
