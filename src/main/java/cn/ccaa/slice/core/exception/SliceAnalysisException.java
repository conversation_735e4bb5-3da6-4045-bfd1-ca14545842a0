package cn.ccaa.slice.core.exception;

import lombok.Getter;

import java.io.Serial;

/**
 * 切片解析异常
 * 用于表示切片文件解析过程中的异常
 * 
 * <AUTHOR>
 */
@Getter
public class SliceAnalysisException extends RuntimeException {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private final String code;
    
    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public SliceAnalysisException(String message) {
        super(message);
        this.code = "SLICE_ANALYSIS_ERROR";
    }
    
    /**
     * 构造函数
     *
     * @param code 错误码
     * @param message 错误信息
     */
    public SliceAnalysisException(String code, String message) {
        super(message);
        this.code = code;
    }
    
    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause 异常原因
     */
    public SliceAnalysisException(String message, Throwable cause) {
        super(message, cause);
        this.code = "SLICE_ANALYSIS_ERROR";
    }
    
    /**
     * 构造函数
     *
     * @param code 错误码
     * @param message 错误信息
     * @param cause 异常原因
     */
    public SliceAnalysisException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

} 
