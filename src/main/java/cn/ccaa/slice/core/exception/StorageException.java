package cn.ccaa.slice.core.exception;

/**
 * 存储相关异常类
 * <AUTHOR>
 */
public class StorageException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 使用错误消息构造异常
     * 
     * @param message 错误消息
     */
    public StorageException(String message) {
        super(message);
    }
    
    /**
     * 使用错误消息和原因构造异常
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public StorageException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 使用原因构造异常
     * 
     * @param cause 原因异常
     */
    public StorageException(Throwable cause) {
        super(cause);
    }
} 
