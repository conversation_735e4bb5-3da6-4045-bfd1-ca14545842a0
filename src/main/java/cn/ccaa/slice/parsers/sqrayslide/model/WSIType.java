package cn.ccaa.slice.parsers.sqrayslide.model;

/**
 * 切片类型枚举
 * 对应C++中的WSI_TYPE枚举
 * <AUTHOR>
 */
public enum WSIType {
    /**
     * 明场切片
     */
    BRIGHTFIELD(0),
    
    /**
     * 荧光切片
     */
    FLUORESCENCE(1),
    
    /**
     * 未知类型
     */
    UNKNOWN(-1);
    
    private final int code;
    
    WSIType(int code) {
        this.code = code;
    }
    
    public int getCode() {
        return code;
    }
    
    /**
     * 从整数值获取枚举
     * 
     * @param code 整数值
     * @return 对应的枚举值
     */
    public static WSIType fromCode(int code) {
        for (WSIType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return UNKNOWN;
    }
} 
