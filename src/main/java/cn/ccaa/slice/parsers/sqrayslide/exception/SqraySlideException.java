package cn.ccaa.slice.parsers.sqrayslide.exception;

import cn.ccaa.slice.parsers.sqrayslide.model.SqError;
import lombok.Getter;

import java.io.Serial;

/**
 * SqraySlide异常类
 * <AUTHOR>
 */
@Getter
public class SqraySlideException extends RuntimeException {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private final int errorCode;
    
    /**
     * 错误类型
     */
    private final SqError errorType;
    
    /**
     * 根据错误消息构造异常
     * 
     * @param message 错误消息
     */
    public SqraySlideException(String message) {
        this(message, SqError.UNKNOWN_ERROR);
    }
    
    /**
     * 根据错误消息和原因构造异常
     * 
     * @param message 错误消息
     * @param cause 原因
     */
    public SqraySlideException(String message, Throwable cause) {
        this(message, cause, SqError.UNKNOWN_ERROR);
    }
    
    /**
     * 根据错误消息和错误代码构造异常
     * 
     * @param message 错误消息
     * @param errorCode 错误代码
     */
    public SqraySlideException(String message, int errorCode) {
        super(message);
        this.errorCode = errorCode;
        this.errorType = SqError.fromCode(errorCode);
    }
    
    /**
     * 根据错误消息、原因和错误代码构造异常
     * 
     * @param message 错误消息
     * @param cause 原因
     * @param errorCode 错误代码
     */
    public SqraySlideException(String message, Throwable cause, int errorCode) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorType = SqError.fromCode(errorCode);
    }
    
    /**
     * 根据错误消息和错误类型构造异常
     * 
     * @param message 错误消息
     * @param errorType 错误类型
     */
    public SqraySlideException(String message, SqError errorType) {
        super(message);
        this.errorCode = errorType.getCode();
        this.errorType = errorType;
    }
    
    /**
     * 根据错误消息、原因和错误类型构造异常
     * 
     * @param message 错误消息
     * @param cause 原因
     * @param errorType 错误类型
     */
    public SqraySlideException(String message, Throwable cause, SqError errorType) {
        super(message, cause);
        this.errorCode = errorType.getCode();
        this.errorType = errorType;
    }

    /**
     * 获取完整错误信息
     * 
     * @return 完整错误信息
     */
    @Override
    public String getMessage() {
        return String.format("[%d] %s: %s", 
                errorCode, 
                errorType.getDescription(), 
                super.getMessage());
    }
    
    @Override
    public String toString() {
        return String.format("%s: [%d] %s", 
                this.getClass().getSimpleName(), 
                errorCode, 
                super.getMessage());
    }
} 
