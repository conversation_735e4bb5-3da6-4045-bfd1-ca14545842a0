package cn.ccaa.slice.parsers.sqrayslide.service;

import java.awt.Rectangle;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import javax.imageio.ImageIO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.sun.jna.Pointer;
import com.sun.jna.ptr.DoubleByReference;
import com.sun.jna.ptr.FloatByReference;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.PointerByReference;

import cn.ccaa.slice.config.SlideParserConfig;
import cn.ccaa.slice.core.model.ColorCorrectionParams;
import cn.ccaa.slice.parsers.sqrayslide.exception.SqraySlideException;
import cn.ccaa.slice.parsers.sqrayslide.jna.SlideImage;
import cn.ccaa.slice.parsers.sqrayslide.jna.SqraySlideLibrary;
import cn.ccaa.slice.parsers.sqrayslide.model.SqError;
import cn.ccaa.slice.parsers.sqrayslide.model.WSIType;
import jakarta.annotation.PostConstruct;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.Rectangle;
import java.awt.image.BufferedImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.imageio.plugins.jpeg.JPEGImageWriteParam;
import java.awt.RenderingHints;

/**
 * SqraySlide服务类
 * 封装对SqraySlide的调用，提供高级API
 * <AUTHOR>
 */
@Slf4j
@Service
public class SqraySlideService {

    /**
     * 常量定义
     */
    private static final int DEFAULT_JPEG_QUALITY = 95;
    private static final int REGION_JPEG_QUALITY = 95;
    private static final String VENDOR_NAME = "SqraySlide";
    private static final String HE_STAIN = "H&E";
    private static final String FLUORESCENCE_STAIN = "Fluorescence";

    // 添加线程安全相关字段
    /**
     * 文件级别的读写锁映射，防止多线程并发访问同一文件
     */
    private static final ConcurrentHashMap<String, ReentrantReadWriteLock> FILE_LOCKS = new ConcurrentHashMap<>();
    
    /**
     * Slide对象包装类，包含引用计数
     */
    private static class SlideHolder {
        final SlideImage slide;
        final java.util.concurrent.atomic.AtomicInteger refCount = new java.util.concurrent.atomic.AtomicInteger(1);
        SlideHolder(SlideImage slide) { this.slide = slide; }
    }

    /**
     * 已打开的切片缓存，避免重复打开同一文件（value改为SlideHolder）
     */
    private static final ConcurrentHashMap<String, SlideHolder> OPENED_SLIDES = new ConcurrentHashMap<>();
    
    /**
     * 全局JNA调用锁，用于保护所有native方法调用
     * 使用单一锁确保native库调用的线程安全
     */
    private static final Object JNA_CALL_LOCK = new Object();

    @Autowired
    private SlideParserConfig parserConfig;

    /**
     * 从配置文件直接读取库路径，支持从两个位置读取
     */
    @Value("${slide.sqrayslide.library-path:${sqrayslide.library-path:}}")
    private String configLibraryPath;

    /**
     * 从配置文件直接读取JPEG质量，支持从两个位置读取
     */
    @Value("${slide.sqrayslide.jpeg-quality:${sqrayslide.jpeg-quality:90}}")
    private int jpegQuality;

    /**
     * 瓦片一致性验证缓存 - 新增：验证瓦片内容一致性
     */
    private final Map<String, String> tileVerificationCache = new ConcurrentHashMap<>();
    
    /**
     * SDPC瓦片边界缓存 - 新增：确保相邻瓦片边界对齐
     */
    private final Map<String, int[]> tileBoundaryCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        try {
            String libraryPath = getLibraryPath();

            if (libraryPath != null && !libraryPath.isEmpty()) {
                validateLibraryFile(libraryPath);

                log.info("初始化SqraySlide库: {}", libraryPath);
                SqraySlideLibrary.Holder.setLibraryPath(libraryPath);

                testLibraryLoading();
            } else {
                log.warn("SqraySlide库路径未配置，将尝试从系统路径加载");
            }
        } catch (Exception e) {
            log.error("初始化SqraySlide库失败", e);
        }
    }

    /**
     * 获取库路径
     */
    private String getLibraryPath() {
        // 优先使用直接配置的library-path
        String libraryPath = configLibraryPath;

        // 如果直接配置为空，则尝试从parserConfig获取
        if ((libraryPath == null || libraryPath.isEmpty()) && parserConfig != null) {
            libraryPath = parserConfig.getSqraySlide().getLibraryPath();
        }

        return libraryPath;
    }

    /**
     * 验证库文件
     */
    private void validateLibraryFile(String libraryPath) {
        File libFile = new File(libraryPath);
        if (!libFile.exists()) {
            log.error("SqraySlide库文件不存在: {}", libraryPath);
            return;
        }

        if (libFile.isDirectory()) {
            log.error("SqraySlide库路径是一个目录而不是文件: {}", libraryPath);
            log.error("请指定具体的库文件路径，例如 /usr/local/sqrayslide/lib/libsqrayslideservice.so");
        }
    }

    /**
     * 测试库加载
     */
    private void testLibraryLoading() {
        try {
            // 首先检查JNA Native类是否可用
            try {
                Class.forName("com.sun.jna.Native");
                log.debug("JNA Native类加载成功");
            } catch (ClassNotFoundException | NoClassDefFoundError e) {
                log.error("JNA Native类不可用: {}", e.getMessage());
                throw new RuntimeException("JNA库不可用，无法加载SqraySlide库", e);
            }
            
            SqraySlideLibrary instance = SqraySlideLibrary.Holder.getInstance();
            if (instance == null) {
                log.error("SqraySlide库实例为null，库加载失败");
                throw new RuntimeException("SqraySlide库加载失败");
            }
            
            boolean result = instance.sqrayslide_always_true();
            log.info("SqraySlide库加载测试: {}", result ? "成功" : "失败");
        } catch (Exception e) {
            log.error("SqraySlide库测试失败: {}", e.getMessage(), e);
            // 不再重新抛出异常，让应用程序继续启动
            // throw new RuntimeException("SqraySlide库测试失败", e);
        }
    }

    /**
     * 打开切片（线程安全版本）
     *
     * @param filePath 切片文件路径
     * @return 切片对象
     * @throws SqraySlideException 如果打开失败
     */
    public SlideImage openSlide(@NonNull String filePath) {
        log.info("开始打开切片文件: {}", filePath);

        try {
            validateFilePath(filePath);
            log.info("文件路径验证通过: {}", filePath);

            // 使用文件绝对路径作为锁的键，确保同一文件不会被多线程并发打开
            String absolutePath = new File(filePath).getAbsolutePath();
            
            // 检查缓存
            SlideHolder holder = OPENED_SLIDES.get(absolutePath);
            if (holder != null && holder.slide.getPointer() != Pointer.NULL) {
                int cnt = holder.refCount.incrementAndGet();
                log.debug("文件已打开，复用现有切片对象: {}，当前引用计数:{}", absolutePath, cnt);
                return holder.slide;
            }

            // 获取文件级别的锁
            ReentrantReadWriteLock fileLock = FILE_LOCKS.computeIfAbsent(absolutePath, 
                k -> new ReentrantReadWriteLock());
            
            // 使用写锁保护文件打开操作
            fileLock.writeLock().lock();
            try {
                // 双重检查：在获取锁后再次检查是否已经打开
                holder = OPENED_SLIDES.get(absolutePath);
                if (holder != null && holder.slide.getPointer() != Pointer.NULL) {
                    int cnt = holder.refCount.incrementAndGet();
                    log.debug("在锁保护下发现文件已打开，复用现有切片对象: {}，当前引用计数:{}", absolutePath, cnt);
                    return holder.slide;
                }

                // 首次加载SDPC文件可能需要更长时间，添加预处理和重试机制
                SlideImage slide = null;
                int maxRetries = 3;
                long startTime = System.currentTimeMillis();
                
                for (int attempt = 1; attempt <= maxRetries; attempt++) {
                    log.info("尝试第{}次打开切片: {}", attempt, filePath);
                    
                    try {
                        // 调用JNA方法打开切片
                        IntByReference status = new IntByReference(0);
                        
                        // 检查SqraySlideLibrary实例是否可用
                        SqraySlideLibrary instance = SqraySlideLibrary.Holder.getInstance();
                        if (instance == null) {
                            log.error("SqraySlideLibrary实例为null，无法打开切片");
                            throw new SqraySlideException("SqraySlideLibrary实例为null", SqError.UNKNOWN_ERROR);
                        }
                        
                        // 同步调用native方法打开切片，防止并发访问
                        synchronized (instance) {
                            slide = instance.sqrayslide_open(filePath, status);
                        }
                        
                        // 检查slide对象和status状态
                        if (status.getValue() != 0) {
                            SqError error = SqError.fromCode(status.getValue());
                            log.error("打开切片失败，错误码: {}, 错误描述: {}", error.getCode(), error.getDescription());
                            throw new SqraySlideException("打开切片失败: " + filePath, error);
                        }
                        
                        if (slide == null || slide.getPointer() == Pointer.NULL) {
                            log.error("打开切片返回无效指针，文件: {}", filePath);
                            throw new SqraySlideException("打开切片返回无效指针", SqError.UNKNOWN_ERROR);
                        }
                        
                        long elapsed = System.currentTimeMillis() - startTime;
                        log.info("成功打开切片: {}, 切片对象指针: {}, 耗时: {}ms", 
                                filePath, slide.getPointer(), elapsed);
                        
                        // 验证切片是否真正可用：尝试获取基本信息
                        try {
                            int levelCount = instance.sqrayslide_get_level_count(slide);
                            if (levelCount > 0) {
                                log.info("切片验证成功，层级数量: {}", levelCount);
                                // 缓存成功打开的切片对象
                                SlideHolder newHolder = new SlideHolder(slide);
                                OPENED_SLIDES.put(absolutePath, newHolder);
                                log.info("新打开切片，初始引用计数:1");
                                return slide;
                            } else {
                                log.warn("切片验证失败，层级数量无效: {}", levelCount);
                                // 关闭当前切片，准备重试
                                if (slide != null) {
                                    try {
                                        instance.sqrayslide_close(slide);
                                    } catch (Exception closeEx) {
                                        log.warn("关闭无效切片时发生异常: {}", closeEx.getMessage());
                                    }
                                }
                                slide = null;
                                throw new SqraySlideException("切片验证失败，层级数量无效: " + levelCount, SqError.UNKNOWN_ERROR);
                            }
                        } catch (Exception validationEx) {
                            log.warn("切片验证时发生异常: {}", validationEx.getMessage());
                            // 关闭当前切片，准备重试
                            if (slide != null) {
                                try {
                                    instance.sqrayslide_close(slide);
                                } catch (Exception closeEx) {
                                    log.warn("关闭异常切片时发生异常: {}", closeEx.getMessage());
                                }
                            }
                            slide = null;
                            throw new SqraySlideException("切片验证时发生异常: " + validationEx.getMessage(), validationEx, SqError.UNKNOWN_ERROR);
                        }
                    } catch (SqraySlideException e) {
                        if (attempt == maxRetries) {
                            throw e;
                        }
                        log.warn("第{}次尝试打开切片时发生SqraySlide异常: {}", attempt, e.getMessage());
                    } catch (UnsatisfiedLinkError e) {
                        if (attempt == maxRetries) {
                            throw new SqraySlideException("SqraySlide库链接错误: " + e.getMessage(), e, SqError.UNKNOWN_ERROR);
                        }
                        log.warn("第{}次尝试打开切片时发生库链接错误: {}", attempt, e.getMessage());
                    } catch (Exception e) {
                        if (attempt == maxRetries) {
                            throw new SqraySlideException("打开切片时发生未知错误: " + e.getMessage(), e, SqError.UNKNOWN_ERROR);
                        }
                        log.warn("第{}次尝试打开切片时发生异常: {}", attempt, e.getMessage());
                    }
                    
                    // 如果不是最后一次尝试，等待一段时间后重试
                    if (attempt < maxRetries) {
                        try {
                            long waitTime = attempt * 500; // 递增等待时间：500ms, 1000ms
                            log.info("等待{}ms后进行第{}次重试", waitTime, attempt + 1);
                            Thread.sleep(waitTime);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new SqraySlideException("打开切片被中断: " + filePath, SqError.UNKNOWN_ERROR);
                        }
                    }
                }
                
                // 如果所有尝试都失败了
                throw new SqraySlideException("经过" + maxRetries + "次尝试仍无法打开切片: " + filePath, SqError.OPEN_FILE_ERROR);
                
            } finally {
                // 释放写锁
                fileLock.writeLock().unlock();
            }
            
        } catch (SqraySlideException e) {
            log.error("打开切片时发生SqraySlide异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("打开切片时发生未知错误: {}", e.getMessage(), e);
            throw new SqraySlideException("打开切片时发生未知错误: " + e.getMessage(), e, SqError.UNKNOWN_ERROR);
        }
    }

    /**
     * 验证文件路径
     */
    private void validateFilePath(String filePath) {
        // 详细检查文件路径
        if (filePath == null || filePath.trim().isEmpty()) {
            log.error("文件路径为空");
            throw new SqraySlideException("文件路径不能为空", SqError.FILE_NOT_EXIST);
        }

        // 检查文件是否存在
        File file = new File(filePath);
        log.debug("文件路径: {}, 绝对路径: {}", filePath, file.getAbsolutePath());
        log.debug("文件是否存在: {}, 是否为文件: {}", file.exists(), file.isFile());

        if (!file.exists()) {
            log.error("文件不存在: {}", filePath);
            throw new SqraySlideException("文件不存在: " + filePath,
                    SqError.FILE_NOT_EXIST);
        }

        if (!file.isFile()) {
            log.error("不是有效文件: {}", filePath);
            throw new SqraySlideException("不是有效文件: " + filePath,
                    SqError.FILE_NOT_EXIST);
        }

        // 检查文件可访问性
        log.debug("文件是否可读: {}", file.canRead());
        if (!file.canRead()) {
            log.error("文件无法读取: {}", filePath);
            throw new SqraySlideException("文件无法读取: " + filePath,
                    SqError.OPEN_FILE_ERROR);
        }
    }

    /**
     * 关闭切片（线程安全版本）
     * 
     * @param slide 切片对象
     * @param filePath 原始文件路径，用于从缓存中移除
     */
    public void closeSlide(SlideImage slide, String filePath) {
        if (slide != null && slide.getPointer() != Pointer.NULL) {
            String absolutePath = null;
            if (filePath != null) {
                try {
                    absolutePath = new File(filePath).getAbsolutePath();
                } catch (Exception e) {
                    log.warn("获取文件绝对路径失败: {}", e.getMessage());
                }
            }
            
            try {
                // 查找缓存，减少引用计数
                if (absolutePath != null) {
                    SlideHolder holder = OPENED_SLIDES.get(absolutePath);
                    if (holder != null && holder.slide == slide) {
                        int cnt = holder.refCount.decrementAndGet();
                        log.info("closeSlide: {}，引用计数减1，当前:{}", absolutePath, cnt);
                        if (cnt <= 0) {
                            // 真正释放
                            synchronized (slide) {
                                SqraySlideLibrary.Holder.getInstance().sqrayslide_close(slide);
                            }
                            OPENED_SLIDES.remove(absolutePath);
                            log.info("引用计数为0，已释放并移除缓存: {}", absolutePath);
                        }
                        return;
                    }
                }
                // fallback: 未找到缓存，直接释放
                synchronized (slide) {
                    SqraySlideLibrary.Holder.getInstance().sqrayslide_close(slide);
                }
                log.info("未找到缓存，直接释放slide");
            } catch (Exception e) {
                log.error("关闭切片失败", e);
                if (absolutePath != null) {
                    OPENED_SLIDES.remove(absolutePath);
                }
            }
        }
    }

    /**
     * 关闭切片（兼容方法）
     *
     * @param slide 切片对象
     */
    public void closeSlide(SlideImage slide) {
        closeSlide(slide, null);
    }

    /**
     * 获取切片类型
     *
     * @param slide 切片对象
     * @return 切片类型（明场或荧光）
     */
    public WSIType getSlideType(SlideImage slide) {
        validateSlide(slide);

        int typeCode = SqraySlideLibrary.Holder.getInstance().sqrayslide_get_type(slide);
        return WSIType.fromCode(typeCode);
    }

    /**
     * 验证切片对象
     */
    private void validateSlide(SlideImage slide) {
        if (slide == null) {
            throw new SqraySlideException("切片对象为null", SqError.UNKNOWN_ERROR);
        }
        
        if (slide.getPointer() == null || slide.getPointer() == Pointer.NULL) {
            throw new SqraySlideException("无效的切片对象指针", SqError.UNKNOWN_ERROR);
        }
        
        try {
            // 尝试获取层级数量来验证对象是否可用
            SqraySlideLibrary instance = SqraySlideLibrary.Holder.getInstance();
            if (instance == null) {
                throw new SqraySlideException("SqraySlideLibrary实例不可用", SqError.UNKNOWN_ERROR);
            }
            
            int levelCount = instance.sqrayslide_get_level_count(slide);
            if (levelCount <= 0) {
                throw new SqraySlideException("切片层级数量无效: " + levelCount, SqError.UNKNOWN_ERROR);
            }
        } catch (SqraySlideException e) {
            throw e;
        } catch (Exception e) {
            throw new SqraySlideException("验证切片对象时发生异常: " + e.getMessage(), e, SqError.UNKNOWN_ERROR);
        }
    }

    /**
     * 获取切片标签图
     *
     * @param slide 切片对象
     * @return 标签图的JPEG字节数组
     * @throws SqraySlideException 如果获取失败
     */
    public byte[] getLabelImage(SlideImage slide) {
        validateSlide(slide);
        log.debug("开始获取标签图...");
        try {
            // 添加更多详细日志
            log.debug("获取标签图类型代码: {}", SqraySlideLibrary.IMAGE_TYPE_LABEL);
            
            // 直接使用底层API以便更好地排查问题
            IntByReference width = new IntByReference();
            IntByReference height = new IntByReference();
            PointerByReference data = new PointerByReference();
            IntByReference dataSize = new IntByReference();
            
            boolean success = SqraySlideLibrary.Holder.getInstance().sqrayslide_read_label_jpeg(
                    slide, SqraySlideLibrary.IMAGE_TYPE_LABEL, width, height, data, dataSize);
            
            log.debug("调用sqrayslide_read_label_jpeg(IMAGE_TYPE_LABEL)结果: {}", success);
            log.debug("标签图尺寸: {}x{}, 数据大小: {}", 
                    width.getValue(), height.getValue(), dataSize != null ? dataSize.getValue() : "null");
            
            // 如果没有成功获取标签图，尝试使用宏观(macro)图像类型
            if (!success || data.getValue() == null || dataSize.getValue() <= 10) {
                log.warn("尝试使用IMAGE_TYPE_MACRO获取图像作为标签图替代");
                
                // 重置引用对象
                width = new IntByReference();
                height = new IntByReference();
                data = new PointerByReference();
                dataSize = new IntByReference();
                
                success = SqraySlideLibrary.Holder.getInstance().sqrayslide_read_label_jpeg(
                        slide, SqraySlideLibrary.IMAGE_TYPE_MACRO, width, height, data, dataSize);
                
                log.debug("调用sqrayslide_read_label_jpeg(IMAGE_TYPE_MACRO)结果: {}", success);
                log.debug("宏观图尺寸: {}x{}, 数据大小: {}", 
                        width.getValue(), height.getValue(), dataSize != null ? dataSize.getValue() : "null");
                
                if (!success || data.getValue() == null) {
                    log.error("获取宏观图失败: 调用返回失败或数据为空");
                    
                    // 最后尝试获取缩略图作为备用方案
                    log.warn("尝试使用缩略图作为标签图的备选方案");
                    return getThumbnailImage(slide);
                }
            }
            
            Pointer dataPointer = data.getValue();
            try {
                log.debug("准备从指针读取字节数组，大小: {}", dataSize.getValue());
                byte[] result = dataPointer.getByteArray(0, dataSize.getValue());
                log.debug("成功获取图像数据，大小: {}", result.length);
                
                // 检查数据是否有效
                if (result.length <= 10) {
                    log.warn("图像数据异常小: {} 字节", result.length);
                    return getThumbnailImage(slide);
                }
                
                return result;
            } catch (Exception e) {
                log.error("处理图像数据时发生异常: {}", e.getMessage(), e);
                return getThumbnailImage(slide);
            } finally {
                // 确保在所有情况下都释放内存
                if (dataPointer != null && dataPointer != Pointer.NULL) {
                    SqraySlideLibrary.Holder.getInstance().sqrayslide_free_memory(dataPointer);
                }
            }
        } catch (Exception e) {
            log.error("获取标签图过程中发生异常: {}", e.getMessage(), e);
            // 出现异常时尝试使用缩略图
            return getThumbnailImage(slide);
        }
    }

    /**
     * 获取切片缩略图
     *
     * @param slide 切片对象
     * @return 缩略图的JPEG字节数组
     * @throws SqraySlideException 如果获取失败
     */
    public byte[] getThumbnailImage(SlideImage slide) {
        validateSlide(slide);
        return getImage(slide, SqraySlideLibrary.IMAGE_TYPE_THUMBNAIL, "缩略图");
    }

    /**
     * 获取图像通用方法
     */
    private byte[] getImage(SlideImage slide, int imageType, String imageTypeName) {
        IntByReference width = new IntByReference();
        IntByReference height = new IntByReference();
        PointerByReference data = new PointerByReference();
        IntByReference dataSize = new IntByReference();

        boolean success = SqraySlideLibrary.Holder.getInstance().sqrayslide_read_label_jpeg(
                slide, imageType, width, height, data, dataSize);

        if (!success || data.getValue() == null) {
            throw new SqraySlideException("获取" + imageTypeName + "失败", SqError.UNKNOWN_ERROR);
        }

        Pointer dataPointer = data.getValue();
        try {
            byte[] result = dataPointer.getByteArray(0, dataSize.getValue());
            return result;
        } catch (Exception e) {
            throw new SqraySlideException("处理" + imageTypeName + "数据失败: " + e.getMessage(), e, SqError.UNKNOWN_ERROR);
        } finally {
            // 确保在所有情况下都释放内存
            if (dataPointer != null && dataPointer != Pointer.NULL) {
                SqraySlideLibrary.Holder.getInstance().sqrayslide_free_memory(dataPointer);
            }
        }
    }

    /**
     * 获取切片信息
     *
     * @param slide 切片对象
     * @return 包含切片信息的Map
     */
    public Map<String, Object> getSlideInfo(SlideImage slide) {
        validateSlide(slide);
        log.debug("开始获取切片信息...");

        Map<String, Object> info = new HashMap<>();
        
        // 获取SqraySlideLibrary实例
        SqraySlideLibrary instance = SqraySlideLibrary.Holder.getInstance();
        if (instance == null) {
            throw new SqraySlideException("SqraySlideLibrary实例不可用", SqError.UNKNOWN_ERROR);
        }

        // 使用全局锁保护所有native方法调用，防止多线程并发访问
        synchronized (JNA_CALL_LOCK) {
            try {
                // 获取切片类型
                log.debug("正在获取切片类型...");
                int typeCode = instance.sqrayslide_get_type(slide);
                WSIType slideType = WSIType.fromCode(typeCode);
                info.put("type", slideType.name());
                log.debug("切片类型: ...");

                // 获取层级数量
                log.debug("正在获取层级数量...");
                int levelCount = instance.sqrayslide_get_level_count(slide);
                info.put("levelCount", levelCount);
                log.debug("层级数量: ...");

                // 获取最大分辨率尺寸（层级0）
                log.debug("正在获取最大分辨率尺寸...");
                IntByReference widthRef = new IntByReference();
                IntByReference heightRef = new IntByReference();
                instance.sqrayslide_get_level_size(slide, 0, widthRef, heightRef);
                int width = widthRef.getValue();
                int height = heightRef.getValue();
                info.put("width", width);
                info.put("height", height);
                log.debug("切片尺寸: ...");

                // 获取MPP (Microns Per Pixel)
                log.debug("正在获取MPP信息...");
                DoubleByReference xMpp = new DoubleByReference();
                DoubleByReference yMpp = new DoubleByReference();
                try {
                    instance.sqrayslide_get_mpp(slide, xMpp, yMpp);
                    double xMppValue = xMpp.getValue();
                    double yMppValue = yMpp.getValue();
                    double mpp = (xMppValue + yMppValue) / 2.0; // 使用平均MPP
                    info.put("mpp", mpp);
                    log.debug("MPP信息: ...");
                } catch (Exception e) {
                    log.error("获取MPP信息失败: {}", e.getMessage(), e);
                    info.put("mpp", 0.0);
                }

                // 获取扫描倍率
                log.debug("正在获取扫描倍率...");
                FloatByReference magRef = new FloatByReference();
                try {
                    instance.sqrayslide_get_magnification(slide, magRef);
                    float magnification = magRef.getValue();
                    int objective = Math.round(magnification); // 四舍五入到整数
                    info.put("objective", objective);
                    log.debug("扫描倍率: ...");
                    
                    // 如果倍率为0或异常值，尝试其他方法获取
                    if (magnification <= 0 || magnification > 1000) {
                        log.warn("扫描倍率异常: {}，尝试其他方法获取", magnification);
                        // 可以尝试从MPP计算倍率
                        if (info.containsKey("mpp") && (Double) info.get("mpp") > 0) {
                            double mppValue = (Double) info.get("mpp");
                            // 常见的倍率和MPP对应关系：40x约0.25μm/pixel，20x约0.5μm/pixel，10x约1.0μm/pixel
                            int calculatedObjective = (int) Math.round(10.0 / mppValue);
                            info.put("objective", calculatedObjective);
                            log.info("从MPP计算得到的倍率: {}", calculatedObjective);
                        }
                    }
                } catch (Exception e) {
                    log.error("获取扫描倍率失败: {}", e.getMessage(), e);
                    info.put("objective", 0);
                }

                // 获取瓦片尺寸
                log.debug("正在获取瓦片尺寸...");
                IntByReference tileWidthRef = new IntByReference();
                IntByReference tileHeightRef = new IntByReference();
                try {
                    instance.sqrayslide_get_tile_size(slide, tileWidthRef, tileHeightRef);
                    int tileWidth = tileWidthRef.getValue();
                    int tileHeight = tileHeightRef.getValue();
                    info.put("tileWidth", tileWidth);
                    info.put("tileHeight", tileHeight);
                    log.debug("瓦片尺寸: ...");
                } catch (Exception e) {
                    log.error("获取瓦片尺寸失败: {}", e.getMessage(), e);
                    info.put("tileWidth", 256);
                    info.put("tileHeight", 256);
                }

                // 新增：SDPC内容区域检测和缓存，避免瓦片获取时重复计算导致抖动
                try {
                    log.debug("正在检测SDPC内容区域...");
                    
                    // 获取缩略图用于内容区域检测
                    byte[] thumb = getThumbnailImage(slide);
                    if (thumb != null && thumb.length > 0) {
                        BufferedImage thumbImg = ImageIO.read(new ByteArrayInputStream(thumb));
                        if (thumbImg != null) {
                            // 检测内容区域
                            Rectangle contentBounds = detectContentBounds(thumbImg);
                            if (contentBounds != null) {
                                // 计算缩放比例
                                double scaleX = (double) width / thumbImg.getWidth();
                                double scaleY = (double) height / thumbImg.getHeight();
                                
                                // 计算内容区域在原图中的实际尺寸
                                int contentWidth = (int) Math.round(contentBounds.width * scaleX);
                                int contentHeight = (int) Math.round(contentBounds.height * scaleY);
                                
                                // 添加到切片信息中
                                info.put("contentWidth", contentWidth);
                                info.put("contentHeight", contentHeight);
                                
                                // 同时记录内容区域边界，供瓦片获取使用
                                int[] contentRegion = new int[]{
                                    contentBounds.x,
                                    contentBounds.y,
                                    contentBounds.x + contentBounds.width - 1,
                                    contentBounds.y + contentBounds.height - 1
                                };
                                info.put("contentRegion", contentRegion);
                                
                                log.debug("SDPC内容区域检测完成: 原图{}x{} -> 内容{}x{}, 区域[{},{},{},{}]", 
                                         width, height, contentWidth, contentHeight,
                                         contentRegion[0], contentRegion[1], contentRegion[2], contentRegion[3]);
                            } else {
                                log.debug("未检测到SDPC内容区域，使用原图尺寸");
                            }
                        } else {
                            log.warn("无法解析SDPC缩略图");
                        }
                    } else {
                        log.warn("无法获取SDPC缩略图");
                    }
                } catch (Exception e) {
                    log.warn("SDPC内容区域检测失败: {}", e.getMessage());
                }

                log.debug("切片信息获取完成: ...");
                return info;
            } catch (SqraySlideException e) {
                log.error("获取切片信息时发生SqraySlide异常: {}", e.getMessage(), e);
                throw e;
            } catch (Exception e) {
                log.error("获取切片信息时发生未知异常: {}", e.getMessage(), e);
                throw new SqraySlideException("获取切片信息失败: " + e.getMessage(), e, SqError.UNKNOWN_ERROR);
            }
        }
    }

    /**
     * 获取层级信息
     *
     * @param slide 切片对象
     * @param level 层级
     * @return 包含层级信息的Map
     */
    public Map<String, Object> getLevelInfo(SlideImage slide, int level) {
        validateSlide(slide);
        validateLevel(slide, level);

        Map<String, Object> info = new HashMap<>();
        
        // 获取SqraySlideLibrary实例
        SqraySlideLibrary instance = SqraySlideLibrary.Holder.getInstance();
        if (instance == null) {
            throw new SqraySlideException("SqraySlideLibrary实例不可用", SqError.UNKNOWN_ERROR);
        }

        // 使用全局锁保护所有native方法调用，防止多线程并发访问
        synchronized (JNA_CALL_LOCK) {
            try {
                // 获取层级索引
                info.put("index", level);

                // 获取层级分辨率
                IntByReference widthRef = new IntByReference();
                IntByReference heightRef = new IntByReference();
                instance.sqrayslide_get_level_size(slide, level, widthRef, heightRef);
                info.put("width", widthRef.getValue());
                info.put("height", heightRef.getValue());

                // 获取缩放因子
                double downsample = instance.sqrayslide_get_level_downsample(slide, level);
                info.put("downsample", downsample);

                // 获取瓦片尺寸
                IntByReference tileWidthRef = new IntByReference();
                IntByReference tileHeightRef = new IntByReference();
                instance.sqrayslide_get_tile_size(slide, tileWidthRef, tileHeightRef);
                info.put("tileWidth", tileWidthRef.getValue());
                info.put("tileHeight", tileHeightRef.getValue());

                // 获取瓦片数量
                IntByReference xCountRef = new IntByReference();
                IntByReference yCountRef = new IntByReference();
                instance.sqrayslide_get_level_tile_count(slide, level, xCountRef, yCountRef);
                info.put("tileCountX", xCountRef.getValue());
                info.put("tileCountY", yCountRef.getValue());

                return info;
            } catch (SqraySlideException e) {
                throw e;
            } catch (Exception e) {
                throw new SqraySlideException("获取层级信息失败: " + e.getMessage(), e, SqError.UNKNOWN_ERROR);
            }
        }
    }

    /**
     * 验证层级是否有效
     */
    private void validateLevel(SlideImage slide, int level) {
        // 获取SqraySlideLibrary实例
        SqraySlideLibrary instance = SqraySlideLibrary.Holder.getInstance();
        if (instance == null) {
            throw new SqraySlideException("SqraySlideLibrary实例不可用", SqError.UNKNOWN_ERROR);
        }
        
        // 使用全局锁保护native方法调用
        synchronized (JNA_CALL_LOCK) {
            int levelCount = instance.sqrayslide_get_level_count(slide);
            if (level < 0 || level >= levelCount) {
                throw new SqraySlideException("无效的层级索引: " + level + ", 有效范围: 0-" + (levelCount - 1),
                        SqError.UNKNOWN_ERROR);
            }
        }
    }

    /**
     * 获取瓦片JPEG
     *
     * @param slide 切片对象
     * @param x 瓦片x坐标
     * @param y 瓦片y坐标
     * @param level 层级
     * @return 瓦片JPEG字节数组
     * @throws SqraySlideException 如果获取失败
     */
    public byte[] getTileJpeg(SlideImage slide, int x, int y, int level) {
        validateSlide(slide);

        // 获取SqraySlideLibrary实例
        SqraySlideLibrary instance = SqraySlideLibrary.Holder.getInstance();
        if (instance == null) {
            throw new SqraySlideException("SqraySlideLibrary实例不可用", SqError.UNKNOWN_ERROR);
        }

        // 测试：移除全局锁，依赖缓存机制保证线程安全
        // 如果出现问题，可以重新添加同步保护
        
        // 新增：SDPC瓦片边界一致性检查
        String boundaryKey = slide.getPointer() + "_" + level + "_" + x + "_" + y;
        int[] cachedBoundary = tileBoundaryCache.get(boundaryKey);
        
        // 添加瓦片边界检查
        try {
            IntByReference xCountRef = new IntByReference();
            IntByReference yCountRef = new IntByReference();
            instance.sqrayslide_get_level_tile_count(slide, level, xCountRef, yCountRef);
            int tileCountX = xCountRef.getValue();
            int tileCountY = yCountRef.getValue();
            
            if (x < 0 || x >= tileCountX || y < 0 || y >= tileCountY) {
                log.warn("瓦片位置超出范围: ({}, {}), 有效范围: (0-{}, 0-{}), 层级: {}", 
                        x, y, tileCountX - 1, tileCountY - 1, level);
                throw new SqraySlideException("瓦片位置超出范围, 位置: (" + x + ", " + y + "), 层级: " + level,
                        SqError.UNKNOWN_ERROR);
            }
            
            // 计算并缓存瓦片边界信息
            if (cachedBoundary == null) {
                // 获取瓦片尺寸
                IntByReference tileWidthRef = new IntByReference();
                IntByReference tileHeightRef = new IntByReference();
                instance.sqrayslide_get_tile_size(slide, tileWidthRef, tileHeightRef);
                int tileWidth = tileWidthRef.getValue();
                int tileHeight = tileHeightRef.getValue();
                
                // 计算瓦片在图像中的精确边界（像素级对齐）
                int tilePx = (x * tileWidth / 4) * 4; // 4像素对齐
                int tilePy = (y * tileHeight / 4) * 4;
                int tileW = Math.min(tileWidth, tileCountX * tileWidth - tilePx);
                int tileH = Math.min(tileHeight, tileCountY * tileHeight - tilePy);
                
                cachedBoundary = new int[]{tilePx, tilePy, tileW, tileH};
                tileBoundaryCache.put(boundaryKey, cachedBoundary);
                
                log.debug("SDPC瓦片边界缓存: 瓦片({},{}) -> 边界({},{},{}x{})", 
                         x, y, tilePx, tilePy, tileW, tileH);
            } else {
                log.debug("使用缓存的SDPC瓦片边界: 瓦片({},{}) -> 边界({},{},{}x{})", 
                         x, y, cachedBoundary[0], cachedBoundary[1], cachedBoundary[2], cachedBoundary[3]);
            }
        } catch (Exception e) {
            log.warn("获取瓦片数量失败，跳过边界检查: {}", e.getMessage());
        }

        // 设置JPEG质量
        int quality = getJpegQuality();
        instance.sqrayslide_set_jpeg_quality(slide, quality);

        PointerByReference dataRef = new PointerByReference();

        int dataSize = instance.sqrayslide_read_tile_jpeg(slide, dataRef, x, y, level);

        if (dataSize <= 0 || dataRef.getValue() == null) {
            throw new SqraySlideException("获取瓦片失败, 位置: (" + x + ", " + y + "), 层级: " + level,
                    SqError.UNKNOWN_ERROR);
        }

        Pointer dataPointer = dataRef.getValue();
        try {
            byte[] result = dataPointer.getByteArray(0, dataSize);
            return result;
        } catch (Exception e) {
            throw new SqraySlideException("处理瓦片数据失败: " + e.getMessage(), e, SqError.UNKNOWN_ERROR);
        } finally {
            // 确保在所有情况下都释放内存
            if (dataPointer != null && dataPointer != Pointer.NULL) {
                instance.sqrayslide_free_memory(dataPointer);
            }
        }
    }

    /**
     * 获取配置的JPEG质量
     */
    private int getJpegQuality() {
        // 优先使用直接配置的质量值
        int quality = jpegQuality;

        // 如果未直接配置且parserConfig存在，则从parserConfig获取
        if (quality <= 0 && parserConfig != null) {
            quality = parserConfig.getSqraySlide().getJpegQuality();
        }

        // 确保质量值有效
        if (quality <= 0) {
            quality = DEFAULT_JPEG_QUALITY;
        }

        return quality;
    }

    /**
     * 获取区域图像JPEG
     *
     * @param slide 切片对象
     * @param x 区域x坐标
     * @param y 区域y坐标
     * @param width 区域宽度
     * @param height 区域高度
     * @param level 层级
     * @return 区域JPEG字节数组
     * @throws SqraySlideException 如果获取失败
     */
    public byte[] getRegionJpeg(SlideImage slide, int x, int y, int width, int height, int level) {
        validateSlide(slide);

        // 添加详细的参数日志，便于排查问题
        log.debug("SqraySlide.getRegionJpeg - 请求参数: x={}, y={}, width={}, height={}, level={}", 
                x, y, width, height, level);

        // 获取SqraySlideLibrary实例
        SqraySlideLibrary instance = SqraySlideLibrary.Holder.getInstance();
        if (instance == null) {
            throw new SqraySlideException("SqraySlideLibrary实例不可用", SqError.UNKNOWN_ERROR);
        }

        // 使用全局锁保护所有native方法调用，防止多线程并发访问
        synchronized (JNA_CALL_LOCK) {
            // 获取切片的基本信息进行参数验证
            try {
                // 获取层级数量，验证层级是否有效
                int levelCount = instance.sqrayslide_get_level_count(slide);
                if (level < 0 || level >= levelCount) {
                    log.error("无效的层级参数: level={}, 有效范围: 0-{}", level, levelCount - 1);
                    throw new SqraySlideException("无效的层级参数: " + level, SqError.UNKNOWN_ERROR);
                }

                // 获取层级0（原图）的尺寸信息用于边界检查
                IntByReference level0WidthRef = new IntByReference();
                IntByReference level0HeightRef = new IntByReference();
                instance.sqrayslide_get_level_size(slide, 0, level0WidthRef, level0HeightRef);
                int level0Width = level0WidthRef.getValue();
                int level0Height = level0HeightRef.getValue();
                
                // 获取目标层级的尺寸信息用于验证
                IntByReference levelWidthRef = new IntByReference();
                IntByReference levelHeightRef = new IntByReference();
                instance.sqrayslide_get_level_size(slide, level, levelWidthRef, levelHeightRef);
                int levelWidth = levelWidthRef.getValue();
                int levelHeight = levelHeightRef.getValue();
                
                log.debug("SqraySlide.getRegionJpeg - 层级0尺寸: {}x{}, 层级{}尺寸: {}x{}", 
                        level0Width, level0Height, level, levelWidth, levelHeight);

                // 修复的参数边界检查：
                // SDPC格式关键修复：直接使用传入的坐标和尺寸，不进行额外转换
                // 问题根源：坐标转换导致图像显示不完整
                
                // 计算下采样比例 - 修复：确保获得正确的下采样比例
                double downsample = instance.sqrayslide_get_level_downsample(slide, level);
            
            // 修复下采样比例计算：如果返回的值异常，使用层级尺寸比例计算
            if (downsample <= 0 || Double.isNaN(downsample) || Double.isInfinite(downsample)) {
                downsample = (double) level0Width / levelWidth;
                log.info("SqraySlide下采样比例异常，使用计算值: {}", downsample);
            } else if (downsample < 1.0) {
                // 使用层级0和目标层级的尺寸比例来计算实际下采样比例
                double actualDownsample = (double) level0Width / levelWidth;
                log.debug("SqraySlide返回的下采样比例({})小于1，使用计算的实际比例: {}", downsample, actualDownsample);
                downsample = actualDownsample;
            }
            
            // 关键修复：直接使用传入的坐标和尺寸，不进行坐标系统转换
            // DZI服务已经处理了坐标映射，这里直接使用即可
            int adjX = Math.max(0, Math.min(x, level0Width - 1));
            int adjY = Math.max(0, Math.min(y, level0Height - 1));
            int adjWidth = Math.min(width, level0Width - adjX);
            int adjHeight = Math.min(height, level0Height - adjY);
            
            // 确保宽度和高度为正数
            if (adjWidth <= 0 || adjHeight <= 0) {
                log.warn("调整后的区域尺寸无效: adjWidth={}, adjHeight={}, 原始请求: x={}, y={}, width={}, height={}", 
                        adjWidth, adjHeight, x, y, width, height);
                throw new SqraySlideException("请求的区域超出图像边界", SqError.UNKNOWN_ERROR);
            }

            // 如果坐标或尺寸被调整了，记录日志
            if (adjX != x || adjY != y || adjWidth != width || adjHeight != height) {
                log.debug("SqraySlide.getRegionJpeg - 参数已调整: 原始({},{},{}x{}) -> 调整后({},{},{}x{})", 
                         x, y, width, height, adjX, adjY, adjWidth, adjHeight);
            }

            // 分配BGRA缓冲区 - 使用调整后的尺寸
            byte[] bgra = new byte[adjWidth * adjHeight * 4];
            
            try {
                // 关键修复：使用调整后的坐标和尺寸进行区域读取
                log.debug("SqraySlide.getRegionJpeg - 调用sqrayslide_read_region_bgra: 坐标({}, {}), 尺寸: {}x{}, 层级: {}", 
                         adjX, adjY, adjWidth, adjHeight, level);
                
                boolean success = instance.sqrayslide_read_region_bgra(
                        slide, bgra, adjX, adjY, adjWidth, adjHeight, level);

                if (!success) {
                    log.warn("sqrayslide_read_region_bgra调用失败，尝试使用瓦片读取方式...");
                    return getRegionJpegUsingTiles(slide, x, y, width, height, level);
                }

                log.debug("SqraySlide.getRegionJpeg - sqrayslide_read_region_bgra 调用成功");

                // 将BGRA数据转换为BufferedImage
                BufferedImage resultImage = new BufferedImage(adjWidth, adjHeight, BufferedImage.TYPE_INT_RGB);
                
                // 优化的BGRA到RGB转换
                int[] rgbArray = new int[adjWidth * adjHeight];
                for (int i = 0; i < rgbArray.length; i++) {
                    int idx = i * 4;
                    int b = bgra[idx] & 0xFF;
                    int g = bgra[idx + 1] & 0xFF;
                    int r = bgra[idx + 2] & 0xFF;
                    rgbArray[i] = (r << 16) | (g << 8) | b;
                }
                resultImage.setRGB(0, 0, adjWidth, adjHeight, rgbArray, 0, adjWidth);
                
                // 简化SDPC格式的坐标映射逻辑
                int useLevel = 0;
                for (int l = 1; l < levelCount; l++) {
                    int levelScale = 1 << l;
                    if (levelScale * 1.5 > downsample) break;
                    useLevel = l;
                }

                // 直接映射到原图坐标
                int origPx = (int) (x * downsample);
                int origPy = (int) (y * downsample);
                int origW = Math.min((int) (width * downsample), level0Width - (int) (x * downsample));
                int origH = Math.min((int) (height * downsample), level0Height - (int) (y * downsample));

                // 确保不会超出原图边界
                if (origPx + origW > level0Width) {
                    origW = level0Width - origPx;
                }
                if (origPy + origH > level0Height) {
                    origH = level0Height - origPy;
                }

                // 启用自动裁剪功能
                BufferedImage finalImage = cropToContentBoundsConservative(resultImage);
                if (finalImage == null) {
                    finalImage = resultImage;
                    log.debug("SDPC图像处理：裁剪失败，使用原始图像尺寸 {}x{}", resultImage.getWidth(), resultImage.getHeight());
                } else {
                    log.debug("SDPC图像处理：使用裁剪后图像尺寸 {}x{}", finalImage.getWidth(), finalImage.getHeight());
                }
                
                // 添加自动裁剪效果验证日志
                if (finalImage != resultImage) {
                    log.debug("SDPC保守裁剪已应用: 原始尺寸 {}x{} -> 裁剪后尺寸 {}x{}", 
                            resultImage.getWidth(), resultImage.getHeight(), 
                            finalImage.getWidth(), finalImage.getHeight());
                }
                
                // 转换为JPEG - 使用高质量设置
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                
                // 使用ImageWriter进行高质量JPEG压缩
                javax.imageio.ImageWriter writer = javax.imageio.ImageIO.getImageWritersByFormatName("JPEG").next();
                javax.imageio.ImageWriteParam param = writer.getDefaultWriteParam();
                param.setCompressionMode(javax.imageio.ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.95f); // 高质量
                
                javax.imageio.stream.ImageOutputStream ios = javax.imageio.ImageIO.createImageOutputStream(baos);
                writer.setOutput(ios);
                writer.write(null, new javax.imageio.IIOImage(finalImage, null, null), param);
                writer.dispose();
                ios.close();
                
                byte[] result = baos.toByteArray();
                
                log.debug("SqraySlide.getRegionJpeg - 成功生成JPEG数据，大小: {} bytes", result.length);
                return result;
                
            } catch (Exception e) {
                log.error("SqraySlide.getRegionJpeg - 处理过程中发生异常: {}", e.getMessage(), e);
                // 尝试使用瓦片读取的方式作为备选方案
                log.info("由于异常，尝试使用瓦片读取方式作为备选方案...");
                return getRegionJpegUsingTiles(slide, x, y, width, height, level);
            }
            } catch (SqraySlideException e) {
                throw e;
            } catch (Exception e) {
                log.error("SqraySlide.getRegionJpeg - 获取区域图像失败: {}", e.getMessage(), e);
                throw new SqraySlideException("获取区域图像失败: " + e.getMessage(), e, SqError.UNKNOWN_ERROR);
            }
        }
    }

    /**
     * 保守的内容边界裁剪，避免过度裁剪
     */
    private BufferedImage cropToContentBoundsConservative(BufferedImage image) {
        if (image == null) {
            return null;
        }
        
        try {
            int width = image.getWidth();
            int height = image.getHeight();
            
            // 使用更保守的边界检测，只移除明显的黑色边框
            int minX = 0, minY = 0, maxX = width - 1, maxY = height - 1;
            boolean foundContent = false;
            
            // 针对SDPC文件优化：扩大边缘检查范围，更好地识别圆形内容
            int borderWidth = Math.min(50, width / 10); // 增加检查范围，最多检查50像素或图像宽度的10%
            int borderHeight = Math.min(50, height / 10);
            
            // 从左边检查
            for (int x = 0; x < borderWidth && x < width; x++) {
                boolean hasContent = false;
                for (int y = 0; y < height; y += 2) { // 跳跃式检查，提高性能
                    if (!isBackgroundPixelConservative(image.getRGB(x, y))) {
                        hasContent = true;
                        break;
                    }
                }
                if (hasContent) {
                    minX = Math.max(0, x - 1); // 保留1像素边距
                    foundContent = true;
                    break;
                }
            }
            
            // 从右边检查
            for (int x = width - 1; x >= width - borderWidth && x >= 0; x--) {
                boolean hasContent = false;
                for (int y = 0; y < height; y += 2) {
                    if (!isBackgroundPixelConservative(image.getRGB(x, y))) {
                        hasContent = true;
                        break;
                    }
                }
                if (hasContent) {
                    maxX = Math.min(width - 1, x + 1); // 保留1像素边距
                    break;
                }
            }
            
            // 从上边检查
            for (int y = 0; y < borderHeight && y < height; y++) {
                boolean hasContent = false;
                for (int x = minX; x <= maxX; x += 2) {
                    if (!isBackgroundPixelConservative(image.getRGB(x, y))) {
                        hasContent = true;
                        break;
                    }
                }
                if (hasContent) {
                    minY = Math.max(0, y - 1);
                    break;
                }
            }
            
            // 从下边检查
            for (int y = height - 1; y >= height - borderHeight && y >= 0; y--) {
                boolean hasContent = false;
                for (int x = minX; x <= maxX; x += 2) {
                    if (!isBackgroundPixelConservative(image.getRGB(x, y))) {
                        hasContent = true;
                        break;
                    }
                }
                if (hasContent) {
                    maxY = Math.min(height - 1, y + 1);
                    break;
                }
            }
            
            if (!foundContent || minX >= maxX || minY >= maxY) {
                log.debug("保守裁剪：未检测到需要裁剪的边框");
                return image;
            }
            
            int cropWidth = maxX - minX + 1;
            int cropHeight = maxY - minY + 1;
            
            // 针对SDPC文件优化：降低裁剪阈值，确保能去除边框显示圆形内容
            double widthRatio = (double) cropWidth / width;
            double heightRatio = (double) cropHeight / height;
            
            if (widthRatio >= 0.85 && heightRatio >= 0.85) {
                log.debug("保守裁剪：裁剪幅度太小，保持原图");
                return image;
            }
            
            log.debug("保守裁剪：{}x{} -> {}x{}, 位置: ({}, {})", 
                    width, height, cropWidth, cropHeight, minX, minY);
            
            return image.getSubimage(minX, minY, cropWidth, cropHeight);
            
        } catch (Exception e) {
            log.warn("保守裁剪失败，返回原图: {}", e.getMessage());
            return image;
        }
    }
    
    /**
     * 保守的背景像素检测，针对SDPC文件优化，能更好地识别圆形内容边界
     */
    private boolean isBackgroundPixelConservative(int rgb) {
        int red = (rgb >> 16) & 0xFF;
        int green = (rgb >> 8) & 0xFF;
        int blue = rgb & 0xFF;
        
        // 针对SDPC文件：检测明显的黑色背景（放宽阈值以更好地检测边框）
        if (red < 50 && green < 50 && blue < 50) {
            return true;
        }
        
        // 检测明显的白色背景（放宽阈值）
        if (red > 240 && green > 240 && blue > 240) {
            return true;
        }
        
        // 检测灰色背景（SDPC文件可能有灰色边框）
        int maxChannel = Math.max(Math.max(red, green), blue);
        int minChannel = Math.min(Math.min(red, green), blue);
        if (maxChannel - minChannel < 30 && maxChannel < 120) {
            return true;
        }
        
        // 检测均匀的低亮度像素（可能是压缩导致的边框像素）
        int avgValue = (red + green + blue) / 3;
        if (avgValue < 40) {
            return true;
        }
        
        return false;
    }

    /**
     * 使用瓦片读取方式获取区域图像（备选方案）- 优化版本
     */
    private byte[] getRegionJpegUsingTiles(SlideImage slide, int x, int y, int width, int height, int level) {
        try {
            log.debug("使用瓦片读取方式获取区域图像: x={}, y={}, width={}, height={}, level={}", 
                     x, y, width, height, level);
            
            // 获取瓦片大小
            IntByReference tileWidthRef = new IntByReference();
            IntByReference tileHeightRef = new IntByReference();
            SqraySlideLibrary.Holder.getInstance().sqrayslide_get_tile_size(slide, tileWidthRef, tileHeightRef);
            int tileWidth = tileWidthRef.getValue();
            int tileHeight = tileHeightRef.getValue();
            
            if (tileWidth <= 0 || tileHeight <= 0) {
                log.warn("无效的瓦片尺寸: {}x{}", tileWidth, tileHeight);
                throw new SqraySlideException("无效的瓦片尺寸", SqError.UNKNOWN_ERROR);
            }
            
            // 计算需要的瓦片范围 - 使用目标层级坐标
            int startTileX = x / tileWidth;
            int startTileY = y / tileHeight;
            int endTileX = (x + width - 1) / tileWidth;
            int endTileY = (y + height - 1) / tileHeight;
            
            // 计算结果图像的实际尺寸
            int resultWidth = width;
            int resultHeight = height;
            
            // 创建结果图像
            BufferedImage resultImage = new BufferedImage(resultWidth, resultHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = resultImage.createGraphics();
            
            // 设置高质量渲染
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            
            // 设置白色背景
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, resultWidth, resultHeight);
            
            try {
                // 逐个读取瓦片并拼接
                for (int tileY_idx = startTileY; tileY_idx <= endTileY; tileY_idx++) {
                    for (int tileX_idx = startTileX; tileX_idx <= endTileX; tileX_idx++) {
                        try {
                            byte[] tileData = getTileJpeg(slide, tileX_idx, tileY_idx, level);
                            if (tileData != null && tileData.length > 0) {
                                BufferedImage tileImage = ImageIO.read(new ByteArrayInputStream(tileData));
                                if (tileImage != null) {
                                    // 计算瓦片在结果图像中的位置
                                    int destX = tileX_idx * tileWidth - x;
                                    int destY = tileY_idx * tileHeight - y;
                                    
                                    // 计算需要绘制的区域
                                    int srcX = Math.max(0, x - tileX_idx * tileWidth);
                                    int srcY = Math.max(0, y - tileY_idx * tileHeight);
                                    int srcW = Math.min(tileImage.getWidth() - srcX, resultWidth - Math.max(0, destX));
                                    int srcH = Math.min(tileImage.getHeight() - srcY, resultHeight - Math.max(0, destY));
                                    
                                    if (srcW > 0 && srcH > 0) {
                                        int finalDestX = Math.max(0, destX);
                                        int finalDestY = Math.max(0, destY);
                                        
                                        g2d.drawImage(tileImage, 
                                                     finalDestX, finalDestY, finalDestX + srcW, finalDestY + srcH,
                                                     srcX, srcY, srcX + srcW, srcY + srcH, 
                                                     null);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.debug("读取瓦片({},{})失败: {}", tileX_idx, tileY_idx, e.getMessage());
                            // 继续处理其他瓦片
                        }
                    }
                }
            } finally {
                g2d.dispose();
            }
            
            // 转换为JPEG
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            
            // 使用高质量JPEG压缩
            javax.imageio.ImageWriter writer = javax.imageio.ImageIO.getImageWritersByFormatName("JPEG").next();
            javax.imageio.ImageWriteParam param = writer.getDefaultWriteParam();
            param.setCompressionMode(javax.imageio.ImageWriteParam.MODE_EXPLICIT);
            param.setCompressionQuality(0.92f); // 稍低于主方法的质量，但仍然很高
            
            javax.imageio.stream.ImageOutputStream ios = javax.imageio.ImageIO.createImageOutputStream(baos);
            writer.setOutput(ios);
            writer.write(null, new javax.imageio.IIOImage(resultImage, null, null), param);
            writer.dispose();
            ios.close();
            
            byte[] result = baos.toByteArray();
            
            log.debug("SqraySlide.getRegionJpegUsingTiles - 成功生成合成图像，大小: {} bytes", result.length);
            return result;
            
        } catch (Exception e) {
            log.error("SqraySlide.getRegionJpegUsingTiles - 瓦片读取方式也失败: {}", e.getMessage(), e);
            throw new SqraySlideException("所有读取方式都失败: " + e.getMessage(), e, SqError.UNKNOWN_ERROR);
        }
    }

    /**
     * 获取条形码信息
     *
     * @param slide 切片对象
     * @return 条形码信息，如果不可用则返回null
     */
    public String getBarcode(SlideImage slide) {
        if (slide == null || slide.getPointer() == Pointer.NULL) {
            throw new SqraySlideException("无效的切片对象", SqError.UNKNOWN_ERROR);
        }

        try {
            // 使用原生方法获取条形码（如果支持）
            String barcode = SqraySlideLibrary.Holder.getInstance().sqrayslide_get_barcode(slide);
            if (barcode != null && !barcode.isEmpty()) {
                log.debug("获取到条形码信息: {}", barcode);
                return barcode;
            }

            // 我们知道sqrayslide_get_property在当前库中不可用
            // 因此不再尝试使用该方法获取条形码
            log.debug("sqrayslide_get_barcode方法返回了空值，且sqrayslide_get_property方法不可用，无法获取条形码信息");
            return null;
        } catch (Exception e) {
            log.error("获取条形码信息出现异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取描述信息
     *
     * @param slide 切片对象
     * @return 描述信息，如果不可用则返回null
     */
    public String getDescription(SlideImage slide) {
        validateSlide(slide);

        // 当前库中没有直接获取描述信息的方法
        // sqrayslide_get_property方法也不可用
        log.debug("当前SqraySlide库不支持获取描述信息功能");
        return null;
    }

    /**
     * 获取扫描仪信息
     *
     * @param slide 切片对象
     * @return 扫描仪信息Map
     */
    public Map<String, Object> getScannerInfo(SlideImage slide) {
        validateSlide(slide);

        Map<String, Object> scannerInfo = new HashMap<>();

        // 标记为SqraySlide扫描仪
        scannerInfo.put("vendor", VENDOR_NAME);

        // 当前库中没有可用的sqrayslide_get_property方法获取模型信息
        log.debug("获取到扫描仪基本信息: {}", scannerInfo);
        return scannerInfo;
    }

    /**
     * 获取染色信息
     *
     * @param slide 切片对象
     * @return 染色信息，如果不可用则返回null
     */
    public String getStaining(SlideImage slide) {
        validateSlide(slide);

        // 当前库中没有直接获取染色信息的方法
        // sqrayslide_get_property方法也不可用
        // 使用WSI类型作为备选
        try {
            WSIType type = getSlideType(slide);
            if (type == WSIType.BRIGHTFIELD) {
                log.debug("根据切片类型推断染色信息: {}", HE_STAIN);
                return HE_STAIN;
            } else if (type == WSIType.FLUORESCENCE) {
                log.debug("根据切片类型推断染色信息: {}", FLUORESCENCE_STAIN);
                return FLUORESCENCE_STAIN;
            }

            log.debug("无法获取染色信息");
            return null;
        } catch (Exception e) {
            log.error("获取染色信息出现异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 解析背景色字符串为Color对象
     * 
     * @param backgroundColorStr RGB格式的背景色字符串，如"255,255,255"
     * @return Color对象，默认为白色
     */
    private Color parseBackgroundColor(String backgroundColorStr) {
        try {
            if (backgroundColorStr != null && !backgroundColorStr.trim().isEmpty()) {
                String[] rgb = backgroundColorStr.split(",");
                if (rgb.length == 3) {
                    int r = Integer.parseInt(rgb[0].trim());
                    int g = Integer.parseInt(rgb[1].trim());
                    int b = Integer.parseInt(rgb[2].trim());
                    return new Color(r, g, b);
                }
            }
        } catch (Exception e) {
            log.warn("解析背景色失败，使用默认白色: {}", e.getMessage());
        }
        return Color.WHITE;
    }
    
    /**
     * 判断像素是否为背景色
     * 
     * @param red 红色分量
     * @param green 绿色分量  
     * @param blue 蓝色分量
     * @return 是否为背景像素
     */
    private boolean isBackgroundPixel(int red, int green, int blue) {
        // 白色背景检测（高亮度）
        if (red > 240 && green > 240 && blue > 240) {
            return true;
        }
        
        // 黑色背景检测（低亮度） - 针对SDPC黑色细线问题，放宽阈值
        if (red < 35 && green < 35 && blue < 35) {
            return true;
        }
        
        // 灰色背景检测（各颜色分量相近且亮度较低）
        int maxChannel = Math.max(Math.max(red, green), blue);
        int minChannel = Math.min(Math.min(red, green), blue);
        if (maxChannel - minChannel < 25 && maxChannel < 85) {  // 放宽颜色偏差和亮度阈值
            return true;
        }
        
        // 针对SDPC格式的特殊处理：检测近黑色边框（RGB值都较小但可能略有差异）
        // 扩大检测范围，更有效地识别黑色细线
        if (red < 60 && green < 60 && blue < 60) {
            return true;
        }
        
        // 额外检测：非常暗的像素（可能是压缩导致的近黑色像素）
        int avgValue = (red + green + blue) / 3;
        if (avgValue < 25) {  // 平均值很低的像素也视为背景
            return true;
        }
        
        // 检测深灰色边框（可能出现在某些SDPC文件中）
        if (maxChannel < 100 && (maxChannel - minChannel) < 15) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检测图像中的内容边界
     * 
     * @param image 待检测的图像
     * @return 内容区域的Rectangle，如果未检测到内容则返回null
     */
    private Rectangle detectContentBounds(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        
        int minX = width, minY = height, maxX = -1, maxY = -1;
        boolean foundContent = false;
        
        // 针对SDPC格式，使用更精确的边界检测算法
        log.debug("开始检测图像内容边界，图像尺寸: {}x{}", width, height);
        
        // 从边缘向内扫描，寻找内容边界
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int red = (rgb >> 16) & 0xFF;
                int green = (rgb >> 8) & 0xFF;
                int blue = rgb & 0xFF;
                
                // 检查是否为内容像素
                if (!isBackgroundPixel(red, green, blue)) {
                    foundContent = true;
                    minX = Math.min(minX, x);
                    minY = Math.min(minY, y);
                    maxX = Math.max(maxX, x);
                    maxY = Math.max(maxY, y);
                }
            }
        }
        
        if (!foundContent || minX >= maxX || minY >= maxY) {
            log.debug("未检测到有效内容区域");
            return null;
        }
        
        // 针对SDPC格式的特殊处理：额外检查右侧和下方是否有细线
        // 检查右边界附近是否存在细线
        int rightBorderCheck = Math.min(width, maxX + 10);
        for (int x = maxX + 1; x < rightBorderCheck; x++) {
            boolean hasContentInColumn = false;
            for (int y = minY; y <= maxY; y++) {
                int rgb = image.getRGB(x, y);
                int red = (rgb >> 16) & 0xFF;
                int green = (rgb >> 8) & 0xFF;
                int blue = rgb & 0xFF;
                
                // 使用更严格的标准检查是否为细线
                if (!isBackgroundPixel(red, green, blue)) {
                    // 检查是否为孤立的细线（周围主要是背景像素）
                    int backgroundCount = 0;
                    int totalChecked = 0;
                    
                    // 检查周围3x3区域
                    for (int dy = -1; dy <= 1; dy++) {
                        for (int dx = -1; dx <= 1; dx++) {
                            int checkX = x + dx;
                            int checkY = y + dy;
                            if (checkX >= 0 && checkX < width && checkY >= 0 && checkY < height) {
                                int checkRgb = image.getRGB(checkX, checkY);
                                int checkRed = (checkRgb >> 16) & 0xFF;
                                int checkGreen = (checkRgb >> 8) & 0xFF;
                                int checkBlue = checkRgb & 0xFF;
                                if (isBackgroundPixel(checkRed, checkGreen, checkBlue)) {
                                    backgroundCount++;
                                }
                                totalChecked++;
                            }
                        }
                    }
                    
                    // 如果周围大部分是背景像素，可能是细线，跳过
                    if (backgroundCount < totalChecked * 0.6) {
                        hasContentInColumn = true;
                        break;
                    }
                }
            }
            
            if (hasContentInColumn) {
                maxX = x;
            }
        }
        
        // 检查下边界附近是否存在细线
        int bottomBorderCheck = Math.min(height, maxY + 10);
        for (int y = maxY + 1; y < bottomBorderCheck; y++) {
            boolean hasContentInRow = false;
            for (int x = minX; x <= maxX; x++) {
                int rgb = image.getRGB(x, y);
                int red = (rgb >> 16) & 0xFF;
                int green = (rgb >> 8) & 0xFF;
                int blue = rgb & 0xFF;
                
                if (!isBackgroundPixel(red, green, blue)) {
                    // 检查是否为孤立的细线
                    int backgroundCount = 0;
                    int totalChecked = 0;
                    
                    // 检查周围3x3区域
                    for (int dy = -1; dy <= 1; dy++) {
                        for (int dx = -1; dx <= 1; dx++) {
                            int checkX = x + dx;
                            int checkY = y + dy;
                            if (checkX >= 0 && checkX < width && checkY >= 0 && checkY < height) {
                                int checkRgb = image.getRGB(checkX, checkY);
                                int checkRed = (checkRgb >> 16) & 0xFF;
                                int checkGreen = (checkRgb >> 8) & 0xFF;
                                int checkBlue = checkRgb & 0xFF;
                                if (isBackgroundPixel(checkRed, checkGreen, checkBlue)) {
                                    backgroundCount++;
                                }
                                totalChecked++;
                            }
                        }
                    }
                    
                    // 如果周围大部分是背景像素，可能是细线，跳过
                    if (backgroundCount < totalChecked * 0.6) {
                        hasContentInRow = true;
                        break;
                    }
                }
            }
            
            if (hasContentInRow) {
                maxY = y;
            }
        }
        
        log.debug("检测到内容边界: ({},{}) 到 ({},{}), 尺寸: {}x{}", 
                minX, minY, maxX, maxY, maxX - minX + 1, maxY - minY + 1);
        
        return new Rectangle(minX, minY, maxX - minX + 1, maxY - minY + 1);
    }
    
    /**
     * 自动检测图像的有效区域，裁剪掉边框
     * 
     * @param image 原始图像
     * @return 裁剪后的图像，如果无法检测到有效区域则返回原图
     */
    private BufferedImage cropToContentBounds(BufferedImage image) {
        if (image == null) {
            return null;
        }
        
        try {
            int width = image.getWidth();
            int height = image.getHeight();
            
            // 如果图像太小，不进行裁剪
            if (width < 50 || height < 50) {
                log.debug("图像尺寸太小，跳过裁剪: {}x{}", width, height);
                return image;
            }
            
            // 使用智能边界检测算法
            Rectangle contentBounds = detectContentBounds(image);
            
            if (contentBounds == null) {
                log.debug("未检测到有效内容区域，保持原图尺寸");
                return image;
            }
            
            int minX = contentBounds.x;
            int minY = contentBounds.y;
            int maxX = contentBounds.x + contentBounds.width - 1;
            int maxY = contentBounds.y + contentBounds.height - 1;
            
            // 针对SDPC格式：更保守的边距设置，避免裁剪过度
            int margin = Math.min(5, Math.min(minX, minY)); // 减小边距，但确保不会裁剪过度
            minX = Math.max(0, minX - margin);
            minY = Math.max(0, minY - margin);
            maxX = Math.min(width - 1, maxX + margin);
            maxY = Math.min(height - 1, maxY + margin);
            
            int cropWidth = maxX - minX + 1;
            int cropHeight = maxY - minY + 1;
            
            // 如果裁剪区域与原图相差不大，保持原图
            double widthRatio = (double) cropWidth / width;
            double heightRatio = (double) cropHeight / height;
            
            if (widthRatio >= 0.98 && heightRatio >= 0.98) {
                log.debug("有效区域与原图相差很小（{}x{} -> {}x{}），保持原图尺寸", 
                        width, height, cropWidth, cropHeight);
                return image;
            }
            
            // 记录裁剪信息
            double cropRatio = (double)(cropWidth * cropHeight) / (width * height);
            log.info("SDPC图像自动裁剪: {}x{} -> {}x{}, 位置: ({}, {}), 保留比例: {:.2f}%", 
                    width, height, cropWidth, cropHeight, minX, minY, cropRatio * 100);
            
            // 裁剪图像
            BufferedImage croppedImage = image.getSubimage(minX, minY, cropWidth, cropHeight);
            
            // 验证裁剪结果
            if (croppedImage.getWidth() <= 0 || croppedImage.getHeight() <= 0) {
                log.warn("裁剪后图像尺寸无效，返回原图");
                return image;
            }
            
            return croppedImage;
            
        } catch (Exception e) {
            log.warn("自动裁剪图像失败，返回原图: {}", e.getMessage());
            return image;
        }
    }

    /**
     * 获取瓦片JPEG（支持颜色校正）
     *
     * @param slide 切片对象
     * @param x 瓦片x坐标
     * @param y 瓦片y坐标
     * @param level 层级
     * @param colorParams 颜色校正参数
     * @return 调色后的瓦片JPEG字节数组
     * @throws SqraySlideException 如果获取失败
     */
    public byte[] getTileJpegWithColor(SlideImage slide, int x, int y, int level, ColorCorrectionParams colorParams) {
        validateSlide(slide);

        try {
            log.debug("SqraySlide颜色校正瓦片: 坐标=({}, {}), 层级={}, 亮度={}, 对比度={}, 伽马={}", 
                     x, y, level, colorParams.getBrightness(), colorParams.getContrast(), colorParams.getGamma());

            // 修复：改用软件颜色校正实现，避免SqraySlide内置颜色校正的问题
            // 1. 先获取原始瓦片数据
            byte[] originalTileData = getTileJpeg(slide, x, y, level);
            if (originalTileData == null || originalTileData.length == 0) {
                log.warn("获取原始瓦片数据失败");
                return originalTileData;
            }
            
            // 2. 检查是否需要颜色校正
            if (isDefaultColorParams(colorParams)) {
                log.debug("使用默认颜色参数，直接返回原始瓦片");
                return originalTileData;
            }
            
            // 3. 应用软件颜色校正
            byte[] correctedTileData = applySoftwareColorCorrection(originalTileData, colorParams);
            
            log.debug("颜色校正瓦片成功获取，原始大小: {} bytes, 校正后大小: {} bytes", 
                     originalTileData.length, correctedTileData.length);
            return correctedTileData;
            
        } catch (SqraySlideException e) {
            throw e;
        } catch (Exception e) {
            log.error("颜色校正瓦片获取失败，回退到普通瓦片: {}", e.getMessage());
            return getTileJpeg(slide, x, y, level);
        }
    }

    /**
     * 获取区域图像JPEG（支持颜色校正）
     *
     * @param slide 切片对象
     * @param x 区域x坐标
     * @param y 区域y坐标
     * @param width 区域宽度
     * @param height 区域高度
     * @param level 层级
     * @param colorParams 颜色校正参数
     * @return 调色后的区域JPEG字节数组
     * @throws SqraySlideException 如果获取失败
     */
    public byte[] getRegionJpegWithColor(SlideImage slide, int x, int y, int width, int height, int level, ColorCorrectionParams colorParams) {
        validateSlide(slide);

        log.info("SqraySlide颜色校正区域: x={}, y={}, width={}, height={}, level={}, 亮度={}, 对比度={}, 伽马={}", 
                x, y, width, height, level, colorParams.getBrightness(), colorParams.getContrast(), colorParams.getGamma());

        try {
            // 修复：改用软件颜色校正实现，避免SqraySlide内置颜色校正的问题
            // 1. 先获取原始区域数据
            byte[] originalRegionData = getRegionJpeg(slide, x, y, width, height, level);
            if (originalRegionData == null || originalRegionData.length == 0) {
                log.warn("获取原始区域数据失败");
                return originalRegionData;
            }
            
            // 2. 检查是否需要颜色校正
            if (isDefaultColorParams(colorParams)) {
                log.debug("使用默认颜色参数，直接返回原始区域");
                return originalRegionData;
            }
            
            // 3. 应用软件颜色校正
            byte[] correctedRegionData = applySoftwareColorCorrection(originalRegionData, colorParams);
            
            log.info("颜色校正区域JPEG生成成功，原始大小: {} bytes, 校正后大小: {} bytes", 
                    originalRegionData.length, correctedRegionData.length);
            return correctedRegionData;
            
        } catch (SqraySlideException e) {
            throw e;
        } catch (Exception e) {
            log.error("颜色校正区域获取失败: {}", e.getMessage(), e);
            throw new SqraySlideException("获取颜色校正区域图像失败: " + e.getMessage(), e, SqError.UNKNOWN_ERROR);
        }
    }
    
    /**
     * 检查是否为默认颜色参数
     */
    private boolean isDefaultColorParams(ColorCorrectionParams params) {
        return params.getBrightness() == 0.0f &&
               params.getContrast() == 1.0f &&
               params.getGamma() == 1.0f &&
               params.getSaturation() == 1.0f &&
               params.getHue() == 0.0f &&
               params.getRedGain() == 1.0f &&
               params.getGreenGain() == 1.0f &&
               params.getBlueGain() == 1.0f &&
               params.getSharpen() == 0 &&
               !params.isInvertColors();
    }
    
    /**
     * 应用软件颜色校正
     */
    private byte[] applySoftwareColorCorrection(byte[] originalJpegData, ColorCorrectionParams params) {
        try {
            // 将JPEG转换为BufferedImage
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(originalJpegData));
            if (image == null) {
                log.warn("无法解析JPEG图像数据，返回原始数据");
                return originalJpegData;
            }
            
            // 应用颜色校正
            BufferedImage correctedImage = applyColorCorrection(image, params);
            
            // 转换回JPEG
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(correctedImage, "JPEG", baos);
            return baos.toByteArray();
            
        } catch (Exception e) {
            log.error("软件颜色校正失败: {}", e.getMessage(), e);
            return originalJpegData; // 回退到原始数据
        }
    }
    
    /**
     * 应用颜色校正到BufferedImage
     */
    private BufferedImage applyColorCorrection(BufferedImage image, ColorCorrectionParams params) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        // 预计算色调调整的三角函数值（如果需要色调调整）
        float hueRadians = (float) Math.toRadians(params.getHue());
        float cosHue = (float) Math.cos(hueRadians);
        float sinHue = (float) Math.sin(hueRadians);
        boolean needHueAdjust = params.getHue() != 0.0f;
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int r = (rgb >> 16) & 0xFF;
                int g = (rgb >> 8) & 0xFF;
                int b = rgb & 0xFF;
                
                // 应用通道增益
                r = clamp((int)(r * params.getRedGain()), 0, 255);
                g = clamp((int)(g * params.getGreenGain()), 0, 255);
                b = clamp((int)(b * params.getBlueGain()), 0, 255);
                
                // 应用亮度调整
                r = clamp((int)(r + params.getBrightness() * 255), 0, 255);
                g = clamp((int)(g + params.getBrightness() * 255), 0, 255);
                b = clamp((int)(b + params.getBrightness() * 255), 0, 255);
                
                // 应用对比度调整
                r = clamp((int)((r - 128) * params.getContrast() + 128), 0, 255);
                g = clamp((int)((g - 128) * params.getContrast() + 128), 0, 255);
                b = clamp((int)((b - 128) * params.getContrast() + 128), 0, 255);
                
                // 应用伽马校正
                if (params.getGamma() != 1.0f) {
                    r = clamp((int)(255 * Math.pow(r / 255.0, 1.0 / params.getGamma())), 0, 255);
                    g = clamp((int)(255 * Math.pow(g / 255.0, 1.0 / params.getGamma())), 0, 255);
                    b = clamp((int)(255 * Math.pow(b / 255.0, 1.0 / params.getGamma())), 0, 255);
                }
                
                // 应用饱和度和色调调整（在HSV色彩空间中进行）
                if (params.getSaturation() != 1.0f || needHueAdjust) {
                    float[] hsv = new float[3];
                    Color.RGBtoHSB(r, g, b, hsv);
                    
                    // 调整饱和度
                    hsv[1] = clamp(hsv[1] * params.getSaturation(), 0.0f, 1.0f);
                    
                    // 调整色调
                    if (needHueAdjust) {
                        hsv[0] = (hsv[0] + params.getHue() / 360.0f) % 1.0f;
                        if (hsv[0] < 0) hsv[0] += 1.0f;
                    }
                    
                    int newRgb = Color.HSBtoRGB(hsv[0], hsv[1], hsv[2]);
                    r = (newRgb >> 16) & 0xFF;
                    g = (newRgb >> 8) & 0xFF;
                    b = newRgb & 0xFF;
                }
                
                // 应用颜色反转
                if (params.isInvertColors()) {
                    r = 255 - r;
                    g = 255 - g;
                    b = 255 - b;
                }
                
                // 设置最终颜色
                int finalRgb = (r << 16) | (g << 8) | b;
                result.setRGB(x, y, finalRgb);
            }
        }
        
        return result;
    }
    
    /**
     * 限制值在指定范围内
     */
    private int clamp(int value, int min, int max) {
        return Math.max(min, Math.min(max, value));
    }
    
    /**
     * 限制浮点值在指定范围内
     */
    private float clamp(float value, float min, float max) {
        return Math.max(min, Math.min(max, value));
    }

    /**
     * 获取切片宏观(玻片)图
     * @param slide 切片对象
     * @return 宏观图JPEG字节数组
     */
    public byte[] getMacroImage(SlideImage slide) {
        validateSlide(slide);
        return getImage(slide, SqraySlideLibrary.IMAGE_TYPE_MACRO, "宏观图");
    }
}
