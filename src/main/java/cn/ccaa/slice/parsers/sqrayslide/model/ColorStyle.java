package cn.ccaa.slice.parsers.sqrayslide.model;

/**
 * 颜色样式枚举
 * 对应C++中的ColorStyle枚举
 * <AUTHOR>
 */
public enum ColorStyle {
    /**
     * 真实颜色
     */
    Real(0),
    
    /**
     * HE颜色
     */
    HE(1),
    
    /**
     * 未知样式
     */
    Unknown(-1);
    
    private final int code;
    
    ColorStyle(int code) {
        this.code = code;
    }
    
    public int getCode() {
        return code;
    }
    
    /**
     * 从整数值获取枚举
     * 
     * @param code 整数值
     * @return 对应的枚举值
     */
    public static ColorStyle fromCode(int code) {
        for (ColorStyle style : values()) {
            if (style.code == code) {
                return style;
            }
        }
        return Unknown;
    }
} 
