package cn.ccaa.slice.parsers.sqrayslide.model;

import lombok.Getter;

/**
 * SqraySlide错误代码枚举
 * <AUTHOR>
 */
@Getter
public enum SqError {
    /**
     * 无错误
     */
    NONE(0),
    
    /**
     * 文件不存在
     */
    FILE_NOT_EXIST(1),
    
    /**
     * 不支持的文件格式
     */
    UNSUPPORTED_FORMAT(2),
    
    /**
     * 打开文件错误
     */
    OPEN_FILE_ERROR(3),
    
    /**
     * 内存分配失败
     */
    MEMORY_ALLOCATION_FAILED(4),
    
    /**
     * 未知错误
     */
    UNKNOWN_ERROR(100);
    
    private final int code;
    
    SqError(int code) {
        this.code = code;
    }

    /**
     * 根据错误码获取对应的错误枚举
     * 
     * @param code 错误码
     * @return 对应的错误枚举，未知错误码返回UNKNOWN_ERROR
     */
    public static SqError fromCode(int code) {
        for (SqError error : values()) {
            if (error.code == code) {
                return error;
            }
        }
        return UNKNOWN_ERROR;
    }
    
    /**
     * 获取错误描述
     * 
     * @return 错误描述字符串
     */
    public String getDescription() {
        switch (this) {
            case NONE:
                return "无错误";
            case FILE_NOT_EXIST:
                return "文件不存在";
            case UNSUPPORTED_FORMAT:
                return "不支持的文件格式";
            case OPEN_FILE_ERROR:
                return "打开文件错误";
            case MEMORY_ALLOCATION_FAILED:
                return "内存分配失败";
            case UNKNOWN_ERROR:
                return "未知错误";
            default:
                return "未定义错误";
        }
    }
} 
