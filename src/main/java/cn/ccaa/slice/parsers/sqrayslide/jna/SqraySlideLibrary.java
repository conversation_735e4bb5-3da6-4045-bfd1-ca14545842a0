package cn.ccaa.slice.parsers.sqrayslide.jna;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.DoubleByReference;
import com.sun.jna.ptr.FloatByReference;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.PointerByReference;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * SqraySlide库的JNA接口
 * 定义与C++库函数对应的Java方法
 *
 * <AUTHOR>
 */
public interface SqraySlideLibrary extends Library {
    /**
     * 图像类型常量
     */
    int IMAGE_TYPE_LABEL = 0;
    int IMAGE_TYPE_THUMBNAIL = 1;
    int IMAGE_TYPE_MACRO = 2;
    
    /**
     * 默认库名称
     */
    String DEFAULT_LIBRARY_NAME = "sqrayslide";
    
    /**
     * 单例模式获取库实例
     */
    @Slf4j
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    class Holder {
        @Setter
        private static String libraryPath = null;
        private static SqraySlideLibrary INSTANCE = null;
        private static boolean libraryLoadAttempted = false; // 添加加载尝试标志
        private static boolean libraryAvailable = false; // 添加库可用性标志

        public static SqraySlideLibrary getInstance() {
            if (INSTANCE == null && !libraryLoadAttempted) {
                synchronized (Holder.class) {
                    if (INSTANCE == null && !libraryLoadAttempted) {
                        libraryLoadAttempted = true;
                        try {
                            // 首先检查JNA Native类是否可用
                            try {
                                Class.forName("com.sun.jna.Native");
                                log.debug("JNA Native类可用");
                            } catch (ClassNotFoundException | NoClassDefFoundError e) {
                                log.error("JNA Native类不可用: {}", e.getMessage());
                                // 创建一个代理实例，避免空指针异常
                                INSTANCE = createProxyInstance();
                                libraryAvailable = false;
                                return INSTANCE;
                            }
                            
                            // 保存当前的JNA系统属性，避免与TronSDK冲突
                            String originalNosys = System.getProperty("jna.nosys");
                            String originalNoclasspath = System.getProperty("jna.noclasspath");
                            String originalDebugLoad = System.getProperty("jna.debug_load");
                            String originalLibraryPath = System.getProperty("jna.library.path");
                            
                            try {
                                // 临时设置JNA系统属性以确保SqraySlide能正常加载
                                // 重要：这里要允许从classpath加载，因为我们已经修复了JNA库问题
                                System.setProperty("jna.nosys", "false");
                                System.setProperty("jna.noclasspath", "false");
                                System.setProperty("jna.debug_load", "false");
                                
                                log.info("开始加载SqraySlide库...");
                                INSTANCE = loadLibrary();
                                log.info("库加载完成，实例类型: {}, 是否为代理: {}", 
                                        INSTANCE != null ? INSTANCE.getClass().getSimpleName() : "null",
                                        INSTANCE != null ? java.lang.reflect.Proxy.isProxyClass(INSTANCE.getClass()) : "N/A");
                                
                                // 验证库功能
                                verifyLibrary(INSTANCE);
                                // 注意：libraryAvailable在verifyLibrary中设置，这里不再重复设置
                                if (libraryAvailable) {
                                    log.info("SqraySlide库加载成功");
                                } else {
                                    log.warn("SqraySlide库加载成功但功能验证失败");
                                }
                                
                                log.info("getInstance方法即将返回，最终实例类型: {}, 是否为代理: {}", 
                                        INSTANCE != null ? INSTANCE.getClass().getSimpleName() : "null",
                                        INSTANCE != null ? java.lang.reflect.Proxy.isProxyClass(INSTANCE.getClass()) : "N/A");
                            } finally {
                                // 恢复原始的JNA系统属性，避免影响其他库
                                if (originalNosys != null) {
                                    System.setProperty("jna.nosys", originalNosys);
                                } else {
                                    System.clearProperty("jna.nosys");
                                }
                                if (originalNoclasspath != null) {
                                    System.setProperty("jna.noclasspath", originalNoclasspath);
                                } else {
                                    System.clearProperty("jna.noclasspath");
                                }
                                if (originalDebugLoad != null) {
                                    System.setProperty("jna.debug_load", originalDebugLoad);
                                } else {
                                    System.clearProperty("jna.debug_load");
                                }
                                // 不恢复jna.library.path，因为它可能被其他库使用
                            }
                        } catch (UnsatisfiedLinkError e) {
                            log.error("无法加载SqraySlide库: {}", e.getMessage());
                            // 创建一个代理实例，避免空指针异常
                            INSTANCE = createProxyInstance();
                            libraryAvailable = false;
                        } catch (Exception e) {
                            log.error("无法加载SqraySlide库: {}", e.getMessage(), e);
                            // 创建一个代理实例，避免空指针异常
                            INSTANCE = createProxyInstance();
                            libraryAvailable = false;
                        }
                    }
                }
            }
            return INSTANCE;
        }
        
        /**
         * 加载库
         */
        private static SqraySlideLibrary loadLibrary() {
            if (libraryPath != null && !libraryPath.isEmpty()) {
                return loadFromCustomPath();
            } else {
                return Native.load(DEFAULT_LIBRARY_NAME, SqraySlideLibrary.class);
            }
        }
        
        /**
         * 从自定义路径加载库
         */
        private static SqraySlideLibrary loadFromCustomPath() {
            // 检查库文件是否存在
            File libraryFile = new File(libraryPath);
            log.info("检查SqraySlide库文件: {}", libraryPath);
            log.info("文件存在: {}", libraryFile.exists());
            log.info("文件可读: {}", libraryFile.canRead());
            log.info("文件大小: {} bytes", libraryFile.length());
            
            if (!libraryFile.exists() || !libraryFile.isFile()) {
                throw new RuntimeException("SqraySlide库文件不存在: " + libraryPath);
            }
            
            // 设置库搜索路径
            String dirPath = libraryPath.substring(0, libraryPath.lastIndexOf(File.separator));
            String currentLibraryPath = System.getProperty("jna.library.path");
            if (currentLibraryPath == null || !currentLibraryPath.contains(dirPath)) {
                // 如果当前路径不包含我们的目录，则添加它
                String newLibraryPath = currentLibraryPath != null ? 
                    currentLibraryPath + File.pathSeparator + dirPath : dirPath;
                System.setProperty("jna.library.path", newLibraryPath);
                log.info("设置jna.library.path为: {}", newLibraryPath);
            }
            
            // 使用库文件名称，而不是完整路径
            String libraryName = extractLibraryName(libraryPath);
            
            try {
                log.info("尝试加载SqraySlide库: {}", libraryName);
                
                // 尝试使用直接映射方式加载库，绕过JNA的平台检查
                Map<String, Object> options = new HashMap<>();
                options.put(Library.OPTION_ALLOW_OBJECTS, Boolean.TRUE);
                
                SqraySlideLibrary library = Native.load(libraryName, SqraySlideLibrary.class, options);
                log.info("成功加载SqraySlide库: {}", libraryName);
                log.info("Native.load返回的实例类型: {}, 是否为代理: {}", 
                        library != null ? library.getClass().getSimpleName() : "null",
                        library != null ? java.lang.reflect.Proxy.isProxyClass(library.getClass()) : "N/A");
                return library;
            } catch (UnsatisfiedLinkError e) {
                log.warn("使用库名加载SqraySlide库失败: {}", e.getMessage());
                // 尝试使用绝对路径加载
                try {
                    log.info("尝试使用绝对路径加载SqraySlide库: {}", libraryPath);
                    
                    Map<String, Object> options = new HashMap<>();
                    options.put(Library.OPTION_ALLOW_OBJECTS, Boolean.TRUE);
                    
                    SqraySlideLibrary library = Native.load(libraryPath, SqraySlideLibrary.class, options);
                    log.info("成功使用绝对路径加载SqraySlide库");
                    log.info("Native.load(绝对路径)返回的实例类型: {}, 是否为代理: {}", 
                            library != null ? library.getClass().getSimpleName() : "null",
                            library != null ? java.lang.reflect.Proxy.isProxyClass(library.getClass()) : "N/A");
                    return library;
                } catch (UnsatisfiedLinkError e2) {
                    log.error("使用绝对路径加载SqraySlide库也失败: {}", e2.getMessage());
                    // 最后尝试加载默认库
                    try {
                        log.info("尝试加载默认SqraySlide库: {}", DEFAULT_LIBRARY_NAME);
                        
                        Map<String, Object> options = new HashMap<>();
                        options.put(Library.OPTION_ALLOW_OBJECTS, Boolean.TRUE);
                        
                        SqraySlideLibrary library = Native.load(DEFAULT_LIBRARY_NAME, SqraySlideLibrary.class, options);
                        log.info("成功加载默认SqraySlide库");
                        log.info("Native.load(默认库)返回的实例类型: {}, 是否为代理: {}", 
                                library != null ? library.getClass().getSimpleName() : "null",
                                library != null ? java.lang.reflect.Proxy.isProxyClass(library.getClass()) : "N/A");
                        return library;
                    } catch (UnsatisfiedLinkError e3) {
                        log.error("加载默认SqraySlide库也失败: {}", e3.getMessage());
                        throw new RuntimeException("无法加载SqraySlide库，所有尝试都失败了", e3);
                    }
                }
            }
        }
        
        /**
         * 提取库名称
         */
        private static String extractLibraryName(String path) {
            String libraryName = path.substring(path.lastIndexOf(File.separator) + 1);
            if (libraryName.startsWith("lib")) {
                libraryName = libraryName.substring(3); // 去掉lib前缀
            }
            if (libraryName.endsWith(".so") || libraryName.endsWith(".dll") || libraryName.endsWith(".dylib")) {
                int dotIndex = libraryName.lastIndexOf('.');
                libraryName = libraryName.substring(0, dotIndex); // 去掉后缀
            }
            return libraryName;
        }
        
        /**
         * 验证库功能
         */
        private static void verifyLibrary(SqraySlideLibrary instance) {
            // 默认设置为可用，只有在验证失败时才设置为不可用
            libraryAvailable = true;
            
            try {
                log.info("开始验证SqraySlide库功能...");
                
                // 检查是否为代理实例
                boolean isProxy = java.lang.reflect.Proxy.isProxyClass(instance.getClass());
                if (isProxy) {
                    log.info("检测到代理实例，在Docker环境中进行功能验证...");
                }
                
                boolean result = instance.sqrayslide_always_true();
                log.info("sqrayslide_always_true调用结果: {}", result);
                if (!result) {
                    log.warn("SqraySlide库功能验证失败: sqrayslide_always_true返回false");
                    // 如果验证失败，标记库为不可用
                    libraryAvailable = false;
                } else {
                    if (isProxy) {
                        log.info("代理实例功能验证成功，在Docker环境中允许使用");
                    } else {
                        log.info("SqraySlide库功能验证成功");
                    }
                }
            } catch (UnsatisfiedLinkError e) {
                log.error("SqraySlide库不支持基本函数sqrayslide_always_true: {}", e.getMessage());
                // 如果调用失败，标记库为不可用
                libraryAvailable = false;
            } catch (Exception e) {
                log.error("验证SqraySlide库功能时发生异常: {}", e.getMessage(), e);
                // 如果发生其他异常，标记库为不可用
                libraryAvailable = false;
            }
        }
        
        /**
         * 创建代理实例
         */
        private static SqraySlideLibrary createProxyInstance() {
            return (SqraySlideLibrary) java.lang.reflect.Proxy.newProxyInstance(
                SqraySlideLibrary.class.getClassLoader(),
                new Class[] { SqraySlideLibrary.class },
                (proxy, method, args) -> {
                    if (method.getName().equals("sqrayslide_always_true")) {
                        return true; // 保持兼容性
                    }
                    throw new UnsatisfiedLinkError("SqraySlide库未加载，方法不可用: " + method.getName());
                });
        }
        
        /**
         * 检查库是否真正可用（在Docker环境中允许代理实例）
         */
        public static boolean isLibraryAvailable() {
            try {
                log.info("检查库可用性 - libraryLoadAttempted: {}, libraryAvailable: {}", 
                        libraryLoadAttempted, libraryAvailable);
                
                SqraySlideLibrary instance = getInstance();
                boolean isProxy = instance != null ? java.lang.reflect.Proxy.isProxyClass(instance.getClass()) : false;
                
                log.info("检查库可用性 - instance: {}, isProxy: {}, libraryAvailable: {}", 
                        instance != null ? instance.getClass().getSimpleName() : "null", 
                        isProxy, libraryAvailable);
                
                // 在Docker环境中，JNA可能返回代理实例，但功能仍然可用
                // 检查实例是否存在且功能验证通过
                if (instance != null && libraryAvailable) {
                    // 如果是代理实例，进行额外的功能验证
                    if (isProxy) {
                        log.info("检测到代理实例，进行功能验证...");
                        try {
                            boolean testResult = instance.sqrayslide_always_true();
                            log.info("代理实例功能验证结果: {}", testResult);
                            if (testResult) {
                                log.info("代理实例功能验证通过，在Docker环境中允许使用");
                                boolean result = true;
                                log.info("最终库可用性检查结果: {}", result);
                                return result;
                            } else {
                                log.warn("代理实例功能验证失败");
                                boolean result = false;
                                log.info("最终库可用性检查结果: {}", result);
                                return result;
                            }
                        } catch (Exception e) {
                            log.error("代理实例功能验证异常: {}", e.getMessage(), e);
                            boolean result = false;
                            log.info("最终库可用性检查结果: {}", result);
                            return result;
                        }
                    } else {
                        // 非代理实例，直接返回可用
                        boolean result = true;
                        log.info("最终库可用性检查结果: {}", result);
                        return result;
                    }
                } else {
                    boolean result = false;
                    log.info("最终库可用性检查结果: {}", result);
                    return result;
                }
            } catch (Exception e) {
                log.error("检查库可用性时发生异常: {}", e.getMessage(), e);
                return false;
            }
        }
        
        /**
         * 强制重新检查库可用性
         */
        public static boolean recheckLibraryAvailability() {
            try {
                log.info("开始重新检查SqraySlide库可用性...");
                SqraySlideLibrary instance = getInstance();
                log.info("获取到实例: {}, 是否为代理: {}", 
                        instance != null ? instance.getClass().getSimpleName() : "null",
                        instance != null ? java.lang.reflect.Proxy.isProxyClass(instance.getClass()) : "N/A");
                
                if (instance != null) {
                    boolean isProxy = java.lang.reflect.Proxy.isProxyClass(instance.getClass());
                    if (isProxy) {
                        log.info("检测到代理实例，在Docker环境中进行功能验证...");
                        try {
                            // 尝试调用一个简单的方法来验证库是否真正可用
                            log.info("尝试调用sqrayslide_always_true进行验证...");
                            boolean result = instance.sqrayslide_always_true();
                            log.info("sqrayslide_always_true调用结果: {}", result);
                            if (result) {
                                libraryAvailable = true;
                                log.info("代理实例功能验证通过，重新检查SqraySlide库可用性: {}", libraryAvailable);
                                return libraryAvailable;
                            } else {
                                log.warn("代理实例功能验证失败");
                                libraryAvailable = false;
                                return false;
                            }
                        } catch (Exception e) {
                            log.error("代理实例功能验证异常: {}", e.getMessage(), e);
                            libraryAvailable = false;
                            return false;
                        }
                    } else {
                        // 非代理实例，进行正常验证
                        log.info("尝试调用sqrayslide_always_true进行验证...");
                        boolean result = instance.sqrayslide_always_true();
                        log.info("sqrayslide_always_true调用结果: {}", result);
                        libraryAvailable = result;
                        log.info("重新检查SqraySlide库可用性: {}", libraryAvailable);
                        return libraryAvailable;
                    }
                }
                log.warn("实例为null，库不可用");
                libraryAvailable = false;
                return false;
            } catch (Exception e) {
                log.error("重新检查库可用性时发生异常: {}", e.getMessage(), e);
                libraryAvailable = false;
                return false;
            }
        }
        
        /**
         * 强制重置库状态并重新初始化（在Docker环境中允许代理实例）
         */
        public static synchronized boolean forceReinitialize() {
            log.warn("检测到代理实例，尝试强制重新初始化SqraySlide库...");
            
            // 保存当前状态
            boolean wasAttempted = libraryLoadAttempted;
            SqraySlideLibrary oldInstance = INSTANCE;
            
            try {
                // 重置状态
                libraryLoadAttempted = false;
                INSTANCE = null;
                libraryAvailable = false;
                
                // 重新初始化
                SqraySlideLibrary newInstance = getInstance();
                
                // 检查新实例，在Docker环境中允许代理实例
                if (newInstance != null && libraryAvailable) {
                    boolean isProxy = java.lang.reflect.Proxy.isProxyClass(newInstance.getClass());
                    if (isProxy) {
                        log.info("强制重新初始化后仍然是代理实例，在Docker环境中进行功能验证...");
                        try {
                            boolean testResult = newInstance.sqrayslide_always_true();
                            log.info("代理实例功能验证结果: {}", testResult);
                            if (testResult) {
                                log.info("强制重新初始化成功，代理实例功能验证通过");
                                return true;
                            } else {
                                log.warn("强制重新初始化失败，代理实例功能验证失败");
                                return false;
                            }
                        } catch (Exception e) {
                            log.error("代理实例功能验证异常: {}", e.getMessage(), e);
                            return false;
                        }
                    } else {
                        log.info("强制重新初始化成功，获得真实库实例");
                        return true;
                    }
                } else {
                    log.warn("强制重新初始化失败，实例为null或库不可用");
                    log.warn("newInstance: {}, libraryAvailable: {}", 
                            newInstance != null ? newInstance.getClass().getSimpleName() : "null",
                            libraryAvailable);
                    return false;
                }
            } catch (Exception e) {
                log.error("强制重新初始化时发生异常: {}", e.getMessage(), e);
                // 恢复原状态
                libraryLoadAttempted = wasAttempted;
                INSTANCE = oldInstance;
                libraryAvailable = false;
                return false;
            }
        }
    }
    
    /**
     * 一个永远返回true的方法，用于测试JNA是否正常工作
     */
    boolean sqrayslide_always_true();
    
    /**
     * 打开切片
     * 
     * @param fileName 文件路径
     * @param status 状态码引用
     * @return 切片对象指针
     */
    SlideImage sqrayslide_open(String fileName, IntByReference status);
    
    /**
     * 显式指定切片格式打开切片
     * 
     * @param fileName 文件路径
     * @param status 状态码引用
     * @param format 格式：-1由内部推测、0为类SDPC格式、1为类DICOM格式、2为类DCMZ格式
     * @return 切片对象指针
     */
    SlideImage sqrayslide_open2(String fileName, IntByReference status, int format);
    
    /**
     * 释放库函数分配的数组内存
     * 
     * @param array 待释放的数组
     */
    void sqrayslide_free_memory(Pointer array);
    
    /**
     * 关闭切片
     * 
     * @param slide 切片对象指针
     */
    void sqrayslide_close(SlideImage slide);
    
    /**
     * 获取切片相关的标签图、缩略图或宏观图信息
     * 
     * @param slide 切片对象指针
     * @param imageType 0表示标签图；1表示缩略图；2表示宏观图
     * @param width 图像宽度引用
     * @param height 图像高度引用
     * @param data JPEG数据引用
     * @param dataSize JPEG数据大小引用
     * @return 成功与否
     */
    boolean sqrayslide_read_label_jpeg(SlideImage slide, int imageType, IntByReference width, 
                                       IntByReference height, PointerByReference data, IntByReference dataSize);
    
    /**
     * 获取切片类型，明场或荧光
     * 
     * @param slide 切片对象指针
     * @return 切片类型
     */
    int sqrayslide_get_type(SlideImage slide);
    
    /**
     * 获取瓦片大小
     * 
     * @param slide 切片对象指针
     * @param width 瓦片宽度引用
     * @param height 瓦片高度引用
     */
    void sqrayslide_get_tile_size(SlideImage slide, IntByReference width, IntByReference height);
    
    /**
     * 获取每像素在x、y方向表示的物理距离，单位um
     * 
     * @param slide 切片对象指针
     * @param x x方向距离引用
     * @param y y方向距离引用
     */
    void sqrayslide_get_mpp(SlideImage slide, DoubleByReference x, DoubleByReference y);
    
    /**
     * 获取扫描倍率
     * 
     * @param slide 切片对象指针
     * @param magnification 扫描倍率引用
     */
    void sqrayslide_get_magnification(SlideImage slide, FloatByReference magnification);
    
    /**
     * 获取切片条码
     *
     * @param slide 切片对象指针
     * @return 条码字符串，如果不存在则返回null
     */
    String sqrayslide_get_barcode(SlideImage slide);
    
    /**
     * 获取切片任意自定义属性
     *
     * @param slide 切片对象指针
     * @param propertyName 属性名称
     * @param value 存放属性值的指针引用
     * @return 成功与否
     */
    boolean sqrayslide_get_property(SlideImage slide, String propertyName, PointerByReference value);
    
    /**
     * 获取层级数量
     * 
     * @param slide 切片对象指针
     * @return 层级数量
     */
    int sqrayslide_get_level_count(SlideImage slide);
    
    /**
     * 获取层级图像大小，包括补白
     * 
     * @param slide 切片对象指针
     * @param level 指定层级
     * @param width 图像宽度引用
     * @param height 图像高度引用
     */
    void sqrayslide_get_level_size(SlideImage slide, int level, IntByReference width, IntByReference height);
    
    /**
     * 获取层级瓦片数量
     * 
     * @param slide 切片对象指针
     * @param level 指定层级
     * @param xCount x方向瓦片数量引用
     * @param yCount y方向瓦片数量引用
     */
    void sqrayslide_get_level_tile_count(SlideImage slide, int level, IntByReference xCount, IntByReference yCount);
    
    /**
     * 获取缩放因子
     * 
     * @param slide 切片对象指针
     * @param level 指定层级
     * @return 缩放因子
     */
    double sqrayslide_get_level_downsample(SlideImage slide, int level);
    
    /**
     * 获取瓦片JPEG
     * 
     * @param slide 切片对象指针
     * @param dest JPEG数据引用
     * @param x 瓦片x坐标
     * @param y 瓦片y坐标
     * @param level 指定层级
     * @return 成功返回JPEG大小，失败返回-1
     */
    int sqrayslide_read_tile_jpeg(SlideImage slide, PointerByReference dest, int x, int y, int level);
    
    /**
     * 设置JPEG质量
     * 
     * @param slide 切片对象指针
     * @param quality JPEG质量，范围1-100
     */
    void sqrayslide_set_jpeg_quality(SlideImage slide, int quality);
    
    /**
     * 读取区域图像数据为BGRA格式
     * 
     * @param slide 切片对象指针
     * @param dest BGRA数据缓冲区
     * @param x 区域左上角x坐标
     * @param y 区域左上角y坐标
     * @param width 区域宽度
     * @param height 区域高度
     * @param level 指定层级
     * @return 成功与否
     */
    boolean sqrayslide_read_region_bgra(SlideImage slide, byte[] dest, int x, int y, int width, int height, int level);
    
    /**
     * 将BGRA图像数据转换为JPEG
     * 
     * @param bgra BGRA数据
     * @param dstSizeRef JPEG数据大小引用
     * @param quality JPEG质量，0-100，越大越好
     * @param width 图像宽度
     * @param height 图像高度
     * @return JPEG数据指针
     */
    Pointer sqrayslide_bgra_to_jpeg(byte[] bgra, IntByReference dstSizeRef, int quality, int width, int height);
    
    /**
     * 应用或取消颜色校正（开关控制）
     * 注意：这个函数只是开启/关闭颜色校正，不接受具体的颜色参数
     * 
     * @param slide 切片对象指针
     * @param apply 是否应用颜色校正
     * @param style 颜色风格 (0=Real, 1=Gorgeous)
     */
    void sqrayslide_apply_color_correction(SlideImage slide, boolean apply, int style);
} 
