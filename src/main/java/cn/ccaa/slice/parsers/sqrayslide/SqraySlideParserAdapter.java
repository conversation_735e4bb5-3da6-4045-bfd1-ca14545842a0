package cn.ccaa.slice.parsers.sqrayslide;

import java.io.File;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Qualifier;

import com.sun.jna.Pointer;

import cn.ccaa.slice.config.SlideParserConfig;
import cn.ccaa.slice.core.exception.SlideException;
import cn.ccaa.slice.core.model.ColorCorrectionParams;
import cn.ccaa.slice.core.parser.SlideParser;
import cn.ccaa.slice.parsers.sqrayslide.exception.SqraySlideException;
import cn.ccaa.slice.parsers.sqrayslide.jna.SlideImage;
import cn.ccaa.slice.parsers.sqrayslide.jna.SqraySlideLibrary;
import cn.ccaa.slice.parsers.sqrayslide.service.SqraySlideService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * SqraySlide解析器适配器
 * 将SqraySlideService适配为统一的SlideParser接口
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class SqraySlideParserAdapter implements SlideParser {

    private static final String PARSER_NAME = "sqrayslide";
    private static final String SDPC_EXTENSION = ".sdpc";
    private static final String INVALID_SLIDE_ERROR = "无效的切片对象类型: ";
    
    // 直接注入 SqraySlideService，避免动态获取导致的 null 问题
    @Autowired
    private SqraySlideService sqraySlideService;
    
    @Autowired
    private SlideParserConfig parserConfig;
    
    private boolean sqraySlideAvailable = true;
    
    // 用于跟踪slide对象和对应的文件路径，便于线程安全关闭
    private final ConcurrentHashMap<SlideImage, String> slideToPathMap = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        try {
            log.info("开始初始化SqraySlideParserAdapter...");
            
            // 检查 SqraySlideService 是否正确注入
            if (sqraySlideService == null) {
                log.error("SqraySlideService未正确注入，SqraySlide解析器初始化失败");
                sqraySlideAvailable = false;
                return;
            }
            log.info("SqraySlideService注入成功");
            
            // 添加空检查防止NPE
            if (parserConfig == null) {
                log.error("SlideParserConfig未注入，SqraySlide解析器初始化失败");
                sqraySlideAvailable = false;
                return;
            }
            log.info("SlideParserConfig注入成功");
            
            // 添加空检查防止NPE
            if (parserConfig.getSqraySlide() == null) {
                log.error("SqraySlide配置为null，SqraySlide解析器初始化失败");
                sqraySlideAvailable = false;
                return;
            }
            log.info("SqraySlide配置获取成功: enabled={}, libraryPath={}", 
                    parserConfig.getSqraySlide().isEnabled(), 
                    parserConfig.getSqraySlide().getLibraryPath());
            
            // 检查是否启用SqraySlide
            if (!parserConfig.getSqraySlide().isEnabled()) {
                log.info("SqraySlide解析器已禁用");
                sqraySlideAvailable = false;
                return;
            }
            
            // 首先检查库是否真正可用
            sqraySlideAvailable = SqraySlideLibrary.Holder.isLibraryAvailable();
            log.info("初始库可用性检查结果: {}", sqraySlideAvailable);
            
            // 如果初始检查失败，尝试重新检查（可能是JNA系统属性冲突导致的）
            if (!sqraySlideAvailable) {
                log.warn("初始库可用性检查失败，尝试重新检查...");
                sqraySlideAvailable = SqraySlideLibrary.Holder.recheckLibraryAvailability();
                log.info("重新检查库可用性结果: {}", sqraySlideAvailable);
                
                // 如果重新检查仍然失败，尝试强制重新初始化
                if (!sqraySlideAvailable) {
                    log.warn("重新检查仍然失败，尝试强制重新初始化...");
                    sqraySlideAvailable = SqraySlideLibrary.Holder.forceReinitialize();
                    log.info("强制重新初始化结果: {}", sqraySlideAvailable);
                }
            }
            
            if (sqraySlideAvailable) {
                log.info("SqraySlide解析器适配器初始化成功，库可用");
            } else {
                log.warn("SqraySlide库不可用，解析器将被禁用。可能原因：JNA库不可用、SqraySlide库文件不存在或与其他库存在冲突");
            }
        } catch (Exception e) {
            log.error("SqraySlideParserAdapter初始化失败: {}", e.getMessage(), e);
            sqraySlideAvailable = false;
        }
    }
    
    @Override
    public boolean supportsFormat(String filePath) {
        log.debug("SqraySlideParserAdapter.supportsFormat 开始检查文件: {}", filePath);
        
        // 首先检查基本条件
        if (filePath == null || filePath.trim().isEmpty()) {
            log.debug("文件路径为空，不支持");
            return false;
        }
        
        // 严格检查文件扩展名 - 只支持.sdpc格式
        String fileName = new File(filePath).getName().toLowerCase();
        log.debug("SqraySlide解析器检查文件名: {}", fileName);
        
        // 明确拒绝OpenSlide支持的格式
        if (fileName.endsWith(".svs") || fileName.endsWith(".ndpi") ||
            fileName.endsWith(".tif") || fileName.endsWith(".tiff") ||
            fileName.endsWith(".mrxs") || fileName.endsWith(".vms") ||
            fileName.endsWith(".vmu") || fileName.endsWith(".scn") ||
            fileName.endsWith(".bif") || fileName.endsWith(".svslide") ||
            fileName.endsWith(".isyntax")) {
            log.debug("文件 {} 是OpenSlide支持的格式，SqraySlide解析器拒绝处理", filePath);
            return false;
        }
        
        // 明确拒绝TronSDK支持的格式
        if (fileName.endsWith(".tron")) {
            log.debug("文件 {} 是TronSDK支持的格式，SqraySlide解析器拒绝处理", filePath);
            return false;
        }
        
        // 只有.sdpc格式由SqraySlide处理
        if (!fileName.endsWith(SDPC_EXTENSION)) {
            log.debug("文件扩展名不是{}格式，SqraySlide解析器不支持: {}", SDPC_EXTENSION, filePath);
            return false;
        }
        
        // 检查SqraySlide是否可用，如果不可用则尝试重新检查
        if (!sqraySlideAvailable) {
            log.debug("SqraySlide标记为不可用，尝试重新检查...");
            sqraySlideAvailable = SqraySlideLibrary.Holder.recheckLibraryAvailability();
            if (!sqraySlideAvailable) {
                log.debug("重新检查后SqraySlide仍不可用，不支持文件: {}", filePath);
                return false;
            } else {
                log.info("重新检查后SqraySlide现在可用了");
            }
        }
        
        // 检查 SqraySlideService 是否可用
        if (sqraySlideService == null) {
            log.error("SqraySlideService为null，无法检查文件格式: {}", filePath);
            return false;
        }
        
        // 检查配置
        if (parserConfig == null || parserConfig.getSqraySlide() == null) {
            log.warn("SlideParserConfig未注入或SqraySlide配置为null，基于扩展名确认支持.sdpc格式");
            return true; // 基于扩展名确认支持
        }
        
        // 检查是否启用SqraySlide
        try {
            if (!parserConfig.getSqraySlide().isEnabled()) {
                log.debug("SqraySlide未启用，不支持文件: {}", filePath);
                return false;
            }
        } catch (Exception e) {
            log.warn("检查SqraySlide是否启用时出错: {}，基于扩展名确认支持.sdpc格式", e.getMessage());
            return true; // 基于扩展名确认支持
        }

        try {
            log.debug("SqraySlideParserAdapter.supportsFormat文件格式验证: {}", filePath);
            
            File file = new File(filePath);
            if (!file.exists() || !file.isFile()) {
                log.debug("文件不存在或不是有效文件: {}", filePath);
                return false;
            }
            
            log.info("SqraySlide解析器确认支持文件: {} (基于扩展名和库可用性)", filePath);
            
            // 对于.sdpc格式文件，如果库可用且配置正确，直接返回true
            // 避免在此处进行文件打开测试，因为这可能很耗时
            return true;
        } catch (SqraySlideException e) {
            log.error("SqraySlide打开文件异常: {}, 错误: {}", filePath, e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("判断文件格式异常: {}, 错误: {}", filePath, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Object openSlide(String filePath) {
        log.info("SqraySlideParserAdapter.openSlide 开始打开切片: {}", filePath);
        
        // 检查SqraySlide是否可用，如果不可用则尝试重新检查
        if (!sqraySlideAvailable) {
            log.warn("SqraySlide标记为不可用，尝试重新检查...");
            sqraySlideAvailable = SqraySlideLibrary.Holder.recheckLibraryAvailability();
            if (!sqraySlideAvailable) {
                log.warn("重新检查后SqraySlide仍不可用，无法打开切片: {}", filePath);
                throw new SlideException("SqraySlide库不可用，无法打开切片: " + filePath);
            } else {
                log.info("重新检查后SqraySlide现在可用了");
            }
        }
        
        // 检查 SqraySlideService 是否可用
        if (sqraySlideService == null) {
            log.error("SqraySlideService为null，无法打开切片: {}", filePath);
            throw new SlideException("SqraySlideService为null，无法打开切片: " + filePath);
        }
        
        try {
            log.info("使用SqraySlide打开切片: {}", filePath);
            Object slide = sqraySlideService.openSlide(filePath);
            
            // 检查返回的slide对象是否为null
            if (slide == null) {
                log.error("SqraySlideService.openSlide返回null，无法打开切片: {}", filePath);
                throw new SlideException("无法打开切片文件: " + filePath + "，可能是文件格式不兼容或文件已损坏");
            }
            
            // 将slide对象和文件路径的映射关系保存，用于线程安全关闭
            if (slide instanceof SlideImage) {
                slideToPathMap.put((SlideImage) slide, filePath);
                log.debug("保存slide对象到文件路径的映射关系");
            }
            
            log.info("SqraySlideParserAdapter.openSlide 成功打开切片: {}", filePath);
            return slide;
        } catch (SlideException se) {
            // 直接抛出SlideException
            throw se;
        } catch (Exception e) {
            log.error("打开切片失败: {}", e.getMessage(), e);
            throw new SlideException("打开切片失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void closeSlide(Object slide) {
        if (!sqraySlideAvailable) {
            return;
        }
        if (slide instanceof SlideImage) {
            SlideImage slideImage = (SlideImage) slide;
            try {
                if (sqraySlideService != null) {
                    // 获取对应的文件路径，用于线程安全关闭
                    String filePath = slideToPathMap.remove(slideImage);
                    if (filePath != null) {
                        // 使用线程安全方式关闭切片（传递filePath，支持引用计数）
                        sqraySlideService.closeSlide(slideImage, filePath);
                        log.debug("使用线程安全方式关闭切片: {}", filePath);
                    } else {
                        // 兼容旧方式（如果没有找到文件路径映射）
                        sqraySlideService.closeSlide(slideImage, null);
                        log.debug("使用兼容方式关闭切片");
                    }
                }
            } catch (Exception e) {
                log.error("关闭切片失败: {}", e.getMessage(), e);
                // 即使关闭失败也要从映射表中移除，避免内存泄漏
                slideToPathMap.remove(slideImage);
            }
        } else {
            throw new SlideException(INVALID_SLIDE_ERROR + (slide != null ? slide.getClass().getName() : "null"));
        }
    }

    @Override
    public Map<String, Object> getSlideInfo(Object slide) {
        log.info("SqraySlideParserAdapter.getSlideInfo 开始获取切片信息");
        
        if (!sqraySlideAvailable) {
            log.warn("SqraySlide库不可用，无法获取切片信息");
            throw new SlideException("SqraySlide库不可用，无法获取切片信息");
        }
        
        if (sqraySlideService == null) {
            log.error("SqraySlideService为null，无法获取切片信息");
            throw new SlideException("SqraySlideService为null，无法获取切片信息");
        }
        
        if (slide == null) {
            log.error("切片对象为null，无法获取切片信息");
            throw new SlideException("切片对象为null，无法获取切片信息");
        }
        
        if (!(slide instanceof SlideImage)) {
            log.error("无效的切片对象类型: {}", slide.getClass().getName());
            throw new SlideException(INVALID_SLIDE_ERROR + slide.getClass().getName());
        }
        
        try {
            log.info("调用SqraySlideService.getSlideInfo获取切片信息");
            SlideImage slideImage = (SlideImage) slide;
            
            // 验证SlideImage对象是否有效
            if (slideImage.getPointer() == null || slideImage.getPointer() == Pointer.NULL) {
                log.error("SlideImage对象指针无效");
                throw new SlideException("SlideImage对象指针无效");
            }
            
            Map<String, Object> slideInfo = sqraySlideService.getSlideInfo(slideImage);
            
            if (slideInfo == null) {
                log.error("获取切片信息返回null");
                throw new SlideException("获取切片信息返回null");
            }
            
            log.info("SqraySlideParserAdapter.getSlideInfo 获取切片信息成功: {}", slideInfo);
            return slideInfo;
        } catch (SlideException se) {
            log.error("获取切片信息失败: {}", se.getMessage(), se);
            throw se;
        } catch (Exception e) {
            log.error("获取切片信息失败: {}", e.getMessage(), e);
            throw new SlideException("获取切片信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> getLevelInfo(Object slide, int level) {
        if (!sqraySlideAvailable) {
            log.warn("SqraySlide库不可用，无法获取层级信息");
            return Map.of();
        }
        
        if (slide instanceof SlideImage) {
            try {
                if (sqraySlideService == null) {
                    log.error("SqraySlideService为null，无法获取层级信息");
                    return Map.of();
                }
                Map<String, Object> levelInfo = sqraySlideService.getLevelInfo((SlideImage) slide, level);
                if (levelInfo.isEmpty()) {
                    log.warn("层级{}不存在", level);
                    throw new SlideException("无效的层级: " + level);
                }
                return levelInfo;
            } catch (Exception e) {
                log.error("获取层级信息失败: {}", e.getMessage(), e);
                throw new SlideException("无法获取层级信息: " + e.getMessage(), e);
            }
        }
        throw new SlideException(INVALID_SLIDE_ERROR + (slide != null ? slide.getClass().getName() : "null"));
    }

    @Override
    public byte[] getTileJpeg(Object slide, int x, int y, int level) {
        if (!sqraySlideAvailable) {
            log.warn("SqraySlide库不可用，无法获取瓦片图像");
            return null;
        }
        
        if (slide instanceof SlideImage) {
            try {
                if (sqraySlideService == null) {
                    log.error("SqraySlideService为null，无法获取瓦片图像");
                    return null;
                }
                return sqraySlideService.getTileJpeg((SlideImage) slide, x, y, level);
            } catch (Exception e) {
                log.error("获取瓦片图像失败: {}", e.getMessage(), e);
                return null;
            }
        }
        throw new SlideException(INVALID_SLIDE_ERROR + (slide != null ? slide.getClass().getName() : "null"));
    }

    @Override
    public byte[] getThumbnailJpeg(Object slide) {
        if (!sqraySlideAvailable) {
            log.warn("SqraySlide库不可用，无法获取缩略图");
            return null;
        }
        
        if (slide instanceof SlideImage) {
            try {
                if (sqraySlideService == null) {
                    log.error("SqraySlideService为null，无法获取缩略图");
                    return null;
                }
                return sqraySlideService.getThumbnailImage((SlideImage) slide);
            } catch (Exception e) {
                log.error("获取缩略图失败: {}", e.getMessage(), e);
                return null;
            }
        }
        throw new SlideException(INVALID_SLIDE_ERROR + (slide != null ? slide.getClass().getName() : "null"));
    }

    @Override
    public byte[] getLabelJpeg(Object slide) {
        if (!sqraySlideAvailable) {
            log.warn("SqraySlide库不可用，无法获取标签图像");
            return null;
        }
        
        if (slide instanceof SlideImage) {
            try {
                if (sqraySlideService == null) {
                    log.error("SqraySlideService为null，无法获取标签图像");
                    return null;
                }
                return sqraySlideService.getLabelImage((SlideImage) slide);
            } catch (Exception e) {
                log.error("获取标签图像失败: {}", e.getMessage(), e);
                return null;
            }
        }
        throw new SlideException(INVALID_SLIDE_ERROR + (slide != null ? slide.getClass().getName() : "null"));
    }

    @Override
    public byte[] getRegionJpeg(Object slide, int x, int y, int width, int height, int level) {
        if (!sqraySlideAvailable) {
            log.warn("SqraySlide库不可用，无法获取区域图像");
            return null;
        }
        
        if (slide instanceof SlideImage) {
            try {
                if (sqraySlideService == null) {
                    log.error("SqraySlideService为null，无法获取区域图像");
                    return null;
                }
                return sqraySlideService.getRegionJpeg((SlideImage) slide, x, y, width, height, level);
            } catch (Exception e) {
                log.error("获取区域图像失败: {}", e.getMessage(), e);
                return null;
            }
        }
        throw new SlideException(INVALID_SLIDE_ERROR + (slide != null ? slide.getClass().getName() : "null"));
    }

    @Override
    public String getParserName() {
        return PARSER_NAME;
    }
    
    @Override
    public String getBarcode(Object slide) {
        if (!sqraySlideAvailable) {
            log.warn("SqraySlide库不可用，无法获取条形码");
            return null;
        }
        
        if (slide instanceof SlideImage) {
            try {
                if (sqraySlideService == null) {
                    log.error("SqraySlideService为null，无法获取条形码");
                    return null;
                }
                SlideImage slideImage = (SlideImage) slide;
                return sqraySlideService.getBarcode(slideImage);
            } catch (Exception e) {
                log.error("获取条形码信息失败: {}", e.getMessage(), e);
            }
        }
        return null;
    }
    
    @Override
    public String getDescription(Object slide) {
        if (!sqraySlideAvailable) {
            log.warn("SqraySlide库不可用，无法获取描述信息");
            return null;
        }
        
        if (slide instanceof SlideImage) {
            try {
                if (sqraySlideService == null) {
                    log.error("SqraySlideService为null，无法获取描述信息");
                    return null;
                }
                SlideImage slideImage = (SlideImage) slide;
                return sqraySlideService.getDescription(slideImage);
            } catch (Exception e) {
                log.error("获取描述信息失败: {}", e.getMessage(), e);
            }
        }
        return null;
    }
    
    @Override
    public Map<String, Object> getScannerInfo(Object slide) {
        if (!sqraySlideAvailable) {
            log.warn("SqraySlide库不可用，无法获取扫描仪信息");
            return Map.of();
        }
        
        if (slide instanceof SlideImage) {
            try {
                if (sqraySlideService == null) {
                    log.error("SqraySlideService为null，无法获取扫描仪信息");
                    return Map.of();
                }
                SlideImage slideImage = (SlideImage) slide;
                return sqraySlideService.getScannerInfo(slideImage);
            } catch (Exception e) {
                log.error("获取扫描仪信息失败: {}", e.getMessage(), e);
                return Map.of();
            }
        }
        throw new SlideException(INVALID_SLIDE_ERROR + (slide != null ? slide.getClass().getName() : "null"));
    }
    
    @Override
    public String getStaining(Object slide) {
        if (!sqraySlideAvailable) {
            log.warn("SqraySlide库不可用，无法获取染色信息");
            return null;
        }
        
        if (slide instanceof SlideImage) {
            try {
                if (sqraySlideService == null) {
                    log.error("SqraySlideService为null，无法获取染色信息");
                    return null;
                }
                SlideImage slideImage = (SlideImage) slide;
                return sqraySlideService.getStaining(slideImage);
            } catch (Exception e) {
                log.error("获取染色信息失败: {}", e.getMessage(), e);
            }
        }
        return null;
    }

    @Override
    public boolean supportsColorCorrection() {
        return sqraySlideAvailable && sqraySlideService != null;
    }
    
    @Override
    public byte[] getRegionJpegWithColor(Object slide, int x, int y, int width, int height, int level, ColorCorrectionParams colorParams) {
        if (!sqraySlideAvailable || sqraySlideService == null) {
            log.warn("SqraySlide不可用，使用默认颜色校正实现");
            return getRegionJpeg(slide, x, y, width, height, level);
        }
        
        if (!(slide instanceof SlideImage)) {
            throw new SlideException(INVALID_SLIDE_ERROR + (slide != null ? slide.getClass().getName() : "null"));
        }
        
        try {
            log.debug("SqraySlide颜色校正：获取区域图像 坐标=({}, {}), 尺寸={}x{}, 层级={}, 参数=亮度:{}, 对比度:{}, 伽马:{}", 
                     x, y, width, height, level, colorParams.getBrightness(), colorParams.getContrast(), colorParams.getGamma());
                     
            return sqraySlideService.getRegionJpegWithColor((SlideImage) slide, x, y, width, height, level, colorParams);
        } catch (Exception e) {
            log.error("SqraySlide颜色校正获取区域图像失败: {}", e.getMessage(), e);
            // 回退到默认实现
            return getRegionJpeg(slide, x, y, width, height, level);
        }
    }
    
    @Override
    public byte[] getTileJpegWithColor(Object slide, int x, int y, int level, ColorCorrectionParams colorParams) {
        if (!sqraySlideAvailable || sqraySlideService == null) {
            log.warn("SqraySlide不可用，使用默认瓦片实现");
            return getTileJpeg(slide, x, y, level);
        }
        
        if (!(slide instanceof SlideImage)) {
            throw new SlideException(INVALID_SLIDE_ERROR + (slide != null ? slide.getClass().getName() : "null"));
        }
        
        try {
            log.debug("SqraySlide颜色校正：获取瓦片 坐标=({}, {}), 层级={}, 参数=亮度:{}, 对比度:{}, 伽马:{}", 
                     x, y, level, colorParams.getBrightness(), colorParams.getContrast(), colorParams.getGamma());
                     
            return sqraySlideService.getTileJpegWithColor((SlideImage) slide, x, y, level, colorParams);
        } catch (Exception e) {
            log.error("SqraySlide颜色校正获取瓦片失败: {}", e.getMessage(), e);
            // 回退到默认实现
            return getTileJpeg(slide, x, y, level);
        }
    }

    @Override
    public byte[] getSlideJpeg(Object slide) {
        if (!(slide instanceof cn.ccaa.slice.parsers.sqrayslide.jna.SlideImage)) {
            return new byte[0];
        }
        try {
            byte[] macro = sqraySlideService.getMacroImage((cn.ccaa.slice.parsers.sqrayslide.jna.SlideImage) slide);
            return (macro != null && macro.length > 0) ? macro : new byte[0];
        } catch (Exception e) {
            log.error("获取宏观图失败，返回空数组: {}", e.getMessage());
            return new byte[0];
        }
    }
} 
