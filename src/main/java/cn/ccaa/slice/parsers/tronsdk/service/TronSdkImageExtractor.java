package cn.ccaa.slice.parsers.tronsdk.service;

import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.plugins.jpeg.JPEGImageWriteParam;
import javax.imageio.stream.ImageOutputStream;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * TronSDK图像提取器
 * 直接调用本地的图像提取器程序来获取图像数据，避免JNA的不稳定性
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class TronSdkImageExtractor {
    
    private static final String EXTRACTOR_BINARY = "/usr/local/bin/tron_image_extractor";
    private static final int TIMEOUT_SECONDS = 30;
    
    /**
     * 提取缩略图
     */
    public byte[] extractThumbnail(String tronFilePath) {
        return extractImage(tronFilePath, "thumbnail");
    }
    
    /**
     * 提取标签图
     */
    public byte[] extractLabel(String tronFilePath) {
        // 尝试多个可能的标签图名称
        String[] labelNames = {"label", "macro"};
        
        for (String labelName : labelNames) {
            byte[] imageData = extractImage(tronFilePath, labelName);
            if (imageData != null && imageData.length > 0) {
                log.info("成功提取{}标签图: {} bytes", labelName, imageData.length);
                return imageData;
            }
        }
        
        log.warn("未找到任何可用的标签图");
        return null;
    }
    
    /**
     * 提取指定名称的图像
     */
    private byte[] extractImage(String tronFilePath, String imageName) {
        try {
            log.debug("开始提取图像: {} from {}", imageName, tronFilePath);
            
            // 检查TRON文件是否存在
            File tronFile = new File(tronFilePath);
            if (!tronFile.exists()) {
                log.warn("TRON文件不存在: {}", tronFilePath);
                return null;
            }
            
            // 创建临时输出文件
            Path tempOutputFile = Files.createTempFile("tron_image_", ".dat");
            
            try {
                // 构建本地命令
                ProcessBuilder pb = new ProcessBuilder(
                    EXTRACTOR_BINARY,
                    tronFilePath,
                    imageName,
                    tempOutputFile.toString()
                );
                
                pb.redirectErrorStream(true);
                
                log.debug("执行本地命令: {}", String.join(" ", pb.command()));
                
                // 启动进程
                Process process = pb.start();
                
                // 读取输出
                StringBuilder output = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        output.append(line).append("\n");
                        log.debug("提取器输出: {}", line);
                    }
                }
                
                // 等待进程完成
                boolean finished = process.waitFor(TIMEOUT_SECONDS, TimeUnit.SECONDS);
                
                if (!finished) {
                    log.error("图像提取超时: {} seconds", TIMEOUT_SECONDS);
                    process.destroyForcibly();
                    return null;
                }
                
                int exitCode = process.exitValue();
                if (exitCode != 0) {
                    log.error("图像提取失败，退出码: {}, 输出: {}", exitCode, output.toString());
                    return null;
                }
                
                // C程序可能会自动将扩展名改为不同格式，需要检查多种可能的输出文件
                Path actualOutputFile = tempOutputFile;
                
                // 检查可能的输出文件格式（按优先级排序）
                String[] possibleExtensions = {".dat", ".ppm", ".dot", ".jpg", ".jpeg", ".png"};
                String tempPath = tempOutputFile.toString();
                String basePath = tempPath.substring(0, tempPath.lastIndexOf('.'));
                
                // 首先检查原始的.dat文件
                if (!Files.exists(tempOutputFile) || Files.size(tempOutputFile) == 0) {
                    log.debug("原始.dat文件不存在或为空，检查其他可能的格式");
                    
                    // 检查所有可能的扩展名
                    for (String ext : possibleExtensions) {
                        if (ext.equals(".dat")) continue; // 已经检查过了
                        
                        Path candidateFile = Paths.get(basePath + ext);
                        if (Files.exists(candidateFile) && Files.size(candidateFile) > 0) {
                            actualOutputFile = candidateFile;
                            log.debug("找到{}格式输出文件: {}", ext, candidateFile);
                            break;
                        }
                    }
                    
                    // 如果还是没找到，检查是否有任何以basePath开头的文件
                    if (!Files.exists(actualOutputFile) || Files.size(actualOutputFile) == 0) {
                        try {
                            Path parentDir = tempOutputFile.getParent();
                            String baseFileName = tempOutputFile.getFileName().toString();
                            String baseNameWithoutExt = baseFileName.substring(0, baseFileName.lastIndexOf('.'));
                            
                            Files.list(parentDir)
                                .filter(p -> p.getFileName().toString().startsWith(baseNameWithoutExt))
                                .filter(p -> {
                                    try {
                                        return Files.size(p) > 0;
                                    } catch (IOException e) {
                                        return false;
                                    }
                                })
                                .findFirst()
                                .ifPresent(p -> {
                                    log.debug("找到匹配的输出文件: {}", p);
                                    // 这里不能直接赋值，因为actualOutputFile是final的
                                });
                        } catch (Exception e) {
                            log.debug("搜索匹配文件时出错: {}", e.getMessage());
                        }
                    }
                    
                    if (!Files.exists(actualOutputFile) || Files.size(actualOutputFile) == 0) {
                        log.warn("未找到任何有效的输出文件，检查的路径: {}", basePath);
                        return null;
                    }
                }
                
                // 读取输出文件
                if (Files.exists(actualOutputFile) && Files.size(actualOutputFile) > 0) {
                    byte[] imageData = Files.readAllBytes(actualOutputFile);
                    String fileExtension = getFileExtension(actualOutputFile.toString());
                    log.info("成功提取图像: {}, 大小: {} bytes, 文件: {}, 格式: {}", 
                            imageName, imageData.length, actualOutputFile, fileExtension);
                    
                    // 根据文件格式进行相应处理
                    return processImageByFormat(imageData, fileExtension, imageName);
                    
                } else {
                    log.warn("输出文件不存在或为空: {}", actualOutputFile);
                    return null;
                }
                
            } finally {
                // 清理临时文件
                try {
                    Files.deleteIfExists(tempOutputFile);
                    // 也清理可能生成的其他格式文件
                    String tempPath = tempOutputFile.toString();
                    String basePath = tempPath.substring(0, tempPath.lastIndexOf('.'));
                    String[] possibleExtensions = {".ppm", ".dot", ".jpg", ".jpeg", ".png"};
                    
                    for (String ext : possibleExtensions) {
                        Files.deleteIfExists(Paths.get(basePath + ext));
                    }
                } catch (IOException e) {
                    log.warn("清理临时文件失败: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("提取图像{}失败: {}", imageName, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filePath) {
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex).toLowerCase();
        }
        return "";
    }
    
    /**
     * 根据文件格式处理图像数据
     */
    private byte[] processImageByFormat(byte[] imageData, String fileExtension, String imageName) {
        try {
            switch (fileExtension) {
                case ".ppm":
                    log.debug("处理PPM格式图像: {}", imageName);
                    return convertPpmToJpeg(imageData);
                    
                case ".dot":
                    log.debug("处理DOT格式图像: {}", imageName);
                    return convertDotToJpeg(imageData);
                    
                case ".jpg":
                case ".jpeg":
                case ".png":
                    log.debug("处理标准图像格式{}: {}", fileExtension, imageName);
                    return imageData; // 直接返回标准格式
                    
                case ".dat":
                default:
                    log.debug("处理未知格式图像，尝试自动检测: {}", imageName);
                    return autoDetectAndConvert(imageData, imageName);
            }
        } catch (Exception e) {
            log.error("处理{}格式图像失败: {}", fileExtension, e.getMessage(), e);
            return imageData; // 返回原始数据
        }
    }
    
    /**
     * 转换DOT格式为JPEG
     * DOT格式可能是某种自定义的图像格式，需要根据实际情况处理
     */
    private byte[] convertDotToJpeg(byte[] dotData) {
        try {
            log.debug("开始转换DOT格式，数据大小: {} bytes", dotData.length);
            
            // 首先尝试作为标准图像格式解析
            try {
                ByteArrayInputStream bais = new ByteArrayInputStream(dotData);
                BufferedImage image = javax.imageio.ImageIO.read(bais);
                if (image != null) {
                    log.debug("DOT数据可以作为标准图像格式解析");
                    return convertBufferedImageToJpeg(image);
                }
            } catch (Exception e) {
                log.debug("DOT数据不是标准图像格式: {}", e.getMessage());
            }
            
            // 如果不是标准格式，尝试作为原始像素数据处理
            // 这里需要根据实际的DOT格式规范来实现
            // 暂时返回原始数据，等待进一步分析
            log.warn("DOT格式转换尚未完全实现，返回原始数据");
            return dotData;
            
        } catch (Exception e) {
            log.error("DOT转JPEG失败: {}", e.getMessage(), e);
            return dotData;
        }
    }
    
    /**
     * 自动检测图像格式并转换
     */
    private byte[] autoDetectAndConvert(byte[] imageData, String imageName) {
        try {
            // 首先检查是否是标准图像格式
            if (isStandardImageFormat(imageData)) {
                log.debug("检测到标准图像格式: {}", imageName);
                return imageData;
            }
            
            // 检查是否是PPM格式
            if (isPpmFormat(imageData)) {
                log.debug("检测到PPM格式: {}", imageName);
                return convertPpmToJpeg(imageData);
            }
            
            // 检查是否可能是BGR24原始数据
            // 这需要根据数据大小和已知的图像尺寸来判断
            log.debug("尝试作为BGR24原始数据处理: {}", imageName);
            return tryConvertBgr24ToJpeg(imageData);
            
        } catch (Exception e) {
            log.error("自动检测和转换失败: {}", e.getMessage(), e);
            return imageData;
        }
    }
    
    /**
     * 检查是否是标准图像格式
     */
    private boolean isStandardImageFormat(byte[] data) {
        if (data == null || data.length < 4) {
            return false;
        }
        
        // 检查常见图像格式的魔数
        // JPEG: FF D8 FF
        if (data.length >= 3 && (data[0] & 0xFF) == 0xFF && (data[1] & 0xFF) == 0xD8 && (data[2] & 0xFF) == 0xFF) {
            return true;
        }
        
        // PNG: 89 50 4E 47
        if (data.length >= 4 && (data[0] & 0xFF) == 0x89 && (data[1] & 0xFF) == 0x50 && 
            (data[2] & 0xFF) == 0x4E && (data[3] & 0xFF) == 0x47) {
            return true;
        }
        
        // BMP: 42 4D
        if (data.length >= 2 && (data[0] & 0xFF) == 0x42 && (data[1] & 0xFF) == 0x4D) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 尝试将BGR24原始数据转换为JPEG
     */
    private byte[] tryConvertBgr24ToJpeg(byte[] rawData) {
        try {
            // 这里需要知道图像的宽度和高度
            // 由于我们不知道确切的尺寸，可以尝试一些常见的尺寸
            int[] commonSizes = {256, 512, 1024, 2048};
            
            for (int size : commonSizes) {
                int expectedLength = size * size * 3; // BGR24格式
                if (rawData.length == expectedLength) {
                    log.debug("尝试将{}x{}的BGR24数据转换为JPEG", size, size);
                    return convertBgr24ToJpeg(rawData, size, size);
                }
            }
            
            // 如果没有匹配的正方形尺寸，尝试一些常见的矩形尺寸
            int[][] commonRectSizes = {{800, 600}, {1024, 768}, {1280, 960}, {1600, 1200}};
            
            for (int[] rectSize : commonRectSizes) {
                int expectedLength = rectSize[0] * rectSize[1] * 3;
                if (rawData.length == expectedLength) {
                    log.debug("尝试将{}x{}的BGR24数据转换为JPEG", rectSize[0], rectSize[1]);
                    return convertBgr24ToJpeg(rawData, rectSize[0], rectSize[1]);
                }
            }
            
            log.warn("无法确定BGR24数据的尺寸，数据长度: {}", rawData.length);
            return rawData;
            
        } catch (Exception e) {
            log.error("BGR24转换失败: {}", e.getMessage(), e);
            return rawData;
        }
    }
    
    /**
     * 将BGR24数据转换为JPEG
     */
    private byte[] convertBgr24ToJpeg(byte[] bgrData, int width, int height) {
        // 使用智能转换替代原有的简单转换
        return smartConvertBgr24ToJpeg(bgrData, width, height);
    }
    
    /**
     * 将BufferedImage转换为JPEG字节数组
     */
    private byte[] convertBufferedImageToJpeg(BufferedImage image) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            
            // 获取JPEG写入器
            ImageWriter jpegWriter = ImageIO.getImageWritersByFormatName("jpeg").next();
            ImageOutputStream ios = ImageIO.createImageOutputStream(baos);
            jpegWriter.setOutput(ios);
            
            // 设置高质量JPEG参数
            JPEGImageWriteParam jpegParams = (JPEGImageWriteParam) jpegWriter.getDefaultWriteParam();
            jpegParams.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            jpegParams.setCompressionQuality(0.95f); // 高质量
            
            // 确保使用正确的颜色空间，避免颜色偏移
            // 强制使用RGB颜色空间
            if (image.getType() != BufferedImage.TYPE_INT_RGB) {
                BufferedImage rgbImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
                Graphics2D g2d = rgbImage.createGraphics();
                g2d.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g2d.drawImage(image, 0, 0, null);
                g2d.dispose();
                image = rgbImage;
            }
            
            // 写入图像
            IIOImage iioImage = new IIOImage(image, null, null);
            jpegWriter.write(null, iioImage, jpegParams);
            
            // 清理资源
            jpegWriter.dispose();
            ios.close();
            
            byte[] result = baos.toByteArray();
            log.debug("JPEG转换成功，输出大小: {} bytes, 质量: 0.95", result.length);
            return result;
            
        } catch (Exception e) {
            log.error("BufferedImage转JPEG失败: {}", e.getMessage(), e);
            
            // 如果高质量转换失败，回退到简单方式
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                boolean success = javax.imageio.ImageIO.write(image, "JPEG", baos);
                
                if (success) {
                    log.warn("使用简单JPEG转换作为回退方案");
                    return baos.toByteArray();
                } else {
                    log.warn("JPEG写入失败");
                    return null;
                }
                
            } catch (Exception fallbackException) {
                log.error("回退JPEG转换也失败: {}", fallbackException.getMessage(), fallbackException);
                return null;
            }
        }
    }
    
    /**
     * 检查图像提取器是否可用
     */
    public boolean isAvailable() {
        try {
            // 检查提取器文件是否存在且可执行
            Path extractorPath = Paths.get(EXTRACTOR_BINARY);
            if (!Files.exists(extractorPath)) {
                log.warn("图像提取器不存在: {}", EXTRACTOR_BINARY);
                return false;
            }
            
            if (!Files.isExecutable(extractorPath)) {
                log.warn("图像提取器不可执行: {}", EXTRACTOR_BINARY);
                return false;
            }
            
            // 尝试运行提取器检查版本（无参数调用会显示用法信息）
            ProcessBuilder pb = new ProcessBuilder(EXTRACTOR_BINARY);
            Process process = pb.start();
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            
            if (!finished) {
                log.warn("图像提取器响应超时");
                process.destroyForcibly();
                return false;
            }
            
            // 退出码为1是正常的（因为没有提供参数）
            int exitCode = process.exitValue();
            if (exitCode == 1) {
                log.info("TronSDK图像提取器可用: {}", EXTRACTOR_BINARY);
                return true;
            } else {
                log.warn("图像提取器异常退出，退出码: {}", exitCode);
                return false;
            }
            
        } catch (Exception e) {
            log.warn("检查图像提取器可用性失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 提取瓦片图像
     * 通过获取完整图像然后裁剪的方式来获取瓦片，避免JNA的不稳定性
     * 
     * @param tronFilePath TRON文件路径
     * @param tileX 瓦片X坐标
     * @param tileY 瓦片Y坐标
     * @param level DZI层级
     * @return 瓦片图像数据（JPEG格式）
     */
    public byte[] extractTile(String tronFilePath, int tileX, int tileY, int level) {
        try {
            log.debug("开始提取瓦片: ({},{}) 层级 {} from {}", tileX, tileY, level, tronFilePath);
            
            // 首先获取完整的缩略图
            byte[] fullImageData = extractThumbnail(tronFilePath);
            if (fullImageData == null || fullImageData.length == 0) {
                log.warn("无法获取完整图像用于瓦片裁剪，尝试获取标签图");
                
                // 尝试获取标签图作为替代
                try {
                    fullImageData = extractLabel(tronFilePath);
                    if (fullImageData != null && fullImageData.length > 0) {
                        log.debug("成功获取标签图用于瓦片裁剪: {} bytes", fullImageData.length);
                    }
                } catch (Exception e) {
                    log.debug("获取标签图失败: {}", e.getMessage());
                }
                
                if (fullImageData == null || fullImageData.length == 0) {
                    log.warn("无法获取任何图像用于瓦片裁剪");
                    return createEmptyTile(256);
                }
            }
            
            // 从完整图像中裁剪瓦片
            return extractTileFromFullImage(fullImageData, tileX, tileY, level);
            
        } catch (Exception e) {
            log.error("提取瓦片失败: {}", e.getMessage(), e);
            return createEmptyTile(256);
        }
    }
    
    /**
     * 从完整图像中提取指定的瓦片
     * 
     * @param fullImageData 完整图像数据
     * @param tileX 瓦片X坐标
     * @param tileY 瓦片Y坐标
     * @param level DZI层级
     * @return 瓦片图像数据
     */
    private byte[] extractTileFromFullImage(byte[] fullImageData, int tileX, int tileY, int level) {
        try {
            // 解码图像
            BufferedImage fullImage = javax.imageio.ImageIO.read(new java.io.ByteArrayInputStream(fullImageData));
            if (fullImage == null) {
                log.warn("无法解码完整图像数据");
                return createEmptyTile(256);
            }
            
            int fullWidth = fullImage.getWidth();
            int fullHeight = fullImage.getHeight();
            
            // DZI瓦片参数
            int tileSize = 256;
            
            // 计算当前层级的图像尺寸
            // DZI层级0是最低分辨率，层级越高分辨率越高
            // 假设原图是最高层级，我们需要根据层级计算缩放比例
            int maxLevel = (int) Math.floor(Math.log(Math.max(fullWidth, fullHeight)) / Math.log(2));
            double scale = Math.pow(2, level - maxLevel);
            
            // 如果scale小于等于0，使用最小缩放
            if (scale <= 0) {
                scale = 1.0 / Math.pow(2, maxLevel - level);
            }
            
            int levelWidth = (int) Math.round(fullWidth * scale);
            int levelHeight = (int) Math.round(fullHeight * scale);
            
            // 确保尺寸至少为1
            levelWidth = Math.max(1, levelWidth);
            levelHeight = Math.max(1, levelHeight);
            
            log.debug("瓦片裁剪参数: 原图{}x{}, 层级{}, 缩放{}, 层级尺寸{}x{}", 
                     fullWidth, fullHeight, level, scale, levelWidth, levelHeight);
            
            // 计算瓦片在层级图像中的位置
            int startX = tileX * tileSize;
            int startY = tileY * tileSize;
            
            // 检查边界
            if (startX >= levelWidth || startY >= levelHeight) {
                log.debug("瓦片({},{})超出层级边界: 起始位置({},{}), 层级尺寸{}x{}", 
                         tileX, tileY, startX, startY, levelWidth, levelHeight);
                return createEmptyTile(tileSize);
            }
            
            // 计算实际瓦片尺寸
            int actualTileWidth = Math.min(tileSize, levelWidth - startX);
            int actualTileHeight = Math.min(tileSize, levelHeight - startY);
            
            // 将层级坐标映射回原图坐标
            int srcX = (int) Math.round(startX / scale);
            int srcY = (int) Math.round(startY / scale);
            int srcWidth = (int) Math.round(actualTileWidth / scale);
            int srcHeight = (int) Math.round(actualTileHeight / scale);
            
            // 确保不超出原图边界
            srcX = Math.max(0, Math.min(srcX, fullWidth - 1));
            srcY = Math.max(0, Math.min(srcY, fullHeight - 1));
            srcWidth = Math.min(srcWidth, fullWidth - srcX);
            srcHeight = Math.min(srcHeight, fullHeight - srcY);
            
            if (srcWidth <= 0 || srcHeight <= 0) {
                log.debug("计算的源区域无效: ({},{}) {}x{}", srcX, srcY, srcWidth, srcHeight);
                return createEmptyTile(tileSize);
            }
            
            log.debug("瓦片({},{})映射: 层级区域({},{}) {}x{} -> 源区域({},{}) {}x{}", 
                     tileX, tileY, startX, startY, actualTileWidth, actualTileHeight,
                     srcX, srcY, srcWidth, srcHeight);
            
            // 从原图裁剪区域
            BufferedImage regionImage = fullImage.getSubimage(srcX, srcY, srcWidth, srcHeight);
            
            // 缩放到瓦片尺寸
            BufferedImage tileImage = new BufferedImage(tileSize, tileSize, BufferedImage.TYPE_INT_RGB);
            java.awt.Graphics2D g2d = tileImage.createGraphics();
            
            // 设置高质量渲染
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_INTERPOLATION, java.awt.RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_RENDERING, java.awt.RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING, java.awt.RenderingHints.VALUE_ANTIALIAS_ON);
            
            // 填充白色背景
            g2d.setColor(java.awt.Color.WHITE);
            g2d.fillRect(0, 0, tileSize, tileSize);
            
            // 绘制缩放后的区域图像
            g2d.drawImage(regionImage, 0, 0, actualTileWidth, actualTileHeight, null);
            g2d.dispose();
            
            // 转换为JPEG
            java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
            javax.imageio.ImageIO.write(tileImage, "JPEG", baos);
            
            byte[] tileData = baos.toByteArray();
            log.debug("瓦片({},{})生成成功: {} bytes", tileX, tileY, tileData.length);
            
            return tileData;
            
        } catch (Exception e) {
            log.error("从完整图像提取瓦片失败: {}", e.getMessage(), e);
            return createEmptyTile(256);
        }
    }
    
    /**
     * 创建空白瓦片
     */
    private byte[] createEmptyTile(int size) {
        try {
            BufferedImage emptyTile = new BufferedImage(size, size, BufferedImage.TYPE_INT_RGB);
            java.awt.Graphics2D g2d = emptyTile.createGraphics();
            g2d.setColor(java.awt.Color.WHITE);
            g2d.fillRect(0, 0, size, size);
            g2d.dispose();
            
            java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
            javax.imageio.ImageIO.write(emptyTile, "JPEG", baos);
            return baos.toByteArray();
        } catch (Exception e) {
            log.error("创建空白瓦片失败: {}", e.getMessage());
            return new byte[0];
        }
    }
    
    /**
     * 将PPM格式转换为JPEG格式
     */
    private byte[] convertPpmToJpeg(byte[] ppmData) {
        try {
            log.debug("开始手动解析PPM格式，数据大小: {} bytes", ppmData.length);
            
            // 手动解析PPM头部
            int headerEnd = findPpmHeaderEnd(ppmData);
            if (headerEnd == -1) {
                log.warn("无法找到PPM头部结束位置");
                return null;
            }
            
            // 解析头部信息
            String headerStr = new String(ppmData, 0, headerEnd, "ASCII");
            String[] lines = headerStr.split("\\n");
            
            if (lines.length < 3) {
                log.warn("PPM头部格式不正确，行数: {}", lines.length);
                return null;
            }
            
            // 检查魔数
            if (!"P6".equals(lines[0].trim())) {
                log.warn("PPM魔数不正确: {}", lines[0]);
                return null;
            }
            
            // 解析尺寸
            String[] dimensions = lines[1].trim().split("\\s+");
            if (dimensions.length != 2) {
                log.warn("PPM尺寸格式不正确: {}", lines[1]);
                return null;
            }
            
            int width = Integer.parseInt(dimensions[0]);
            int height = Integer.parseInt(dimensions[1]);
            int maxVal = Integer.parseInt(lines[2].trim());
            
            log.debug("PPM图像信息: {}x{}, 最大值: {}, 头部长度: {}", width, height, maxVal, headerEnd);
            
            // 验证数据长度
            int expectedPixelDataLength = width * height * 3;
            int actualPixelDataLength = ppmData.length - headerEnd;
            
            if (actualPixelDataLength < expectedPixelDataLength) {
                log.warn("PPM像素数据长度不足，期望: {}, 实际: {}", expectedPixelDataLength, actualPixelDataLength);
                return null;
            }
            
            // 手动创建BufferedImage，使用正确的颜色空间
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            
            // 逐像素读取RGB数据
            int pixelIndex = headerEnd;
            int nonZeroPixels = 0;
            
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    if (pixelIndex + 2 < ppmData.length) {
                        // PPM格式是RGB顺序，直接使用
                        int r = ppmData[pixelIndex] & 0xFF;
                        int g = ppmData[pixelIndex + 1] & 0xFF;
                        int b = ppmData[pixelIndex + 2] & 0xFF;
                        
                        // 统计非零像素
                        if (r != 0 || g != 0 || b != 0) {
                            nonZeroPixels++;
                        }
                        
                        // 记录前几个像素用于调试
                        if (y * width + x < 5) {
                            log.debug("PPM原始像素({},{}): RGB({},{},{})", x, y, r, g, b);
                        }
                        
                        // 确保颜色值在正确范围内，使用标准RGB顺序
                        int rgb = ((r & 0xFF) << 16) | ((g & 0xFF) << 8) | (b & 0xFF);
                        image.setRGB(x, y, rgb);
                        pixelIndex += 3;
                    } else {
                        log.warn("PPM像素数据不足，位置: ({},{}), 索引: {}", x, y, pixelIndex);
                        break;
                    }
                }
            }
            
            log.info("PPM解析完成: {}x{}, 非零像素: {}/{} ({:.2f}%)", 
                    width, height, nonZeroPixels, width * height, 
                    (double)nonZeroPixels / (width * height) * 100);
            
            // 如果所有像素都是0，生成一个测试图案
            if (nonZeroPixels == 0) {
                log.warn("PPM图像所有像素都为0，生成测试图案");
                generateTestPattern(image, width, height);
            }
            
            // 转换为JPEG
            return convertBufferedImageToJpeg(image);
            
        } catch (Exception e) {
            log.error("手动解析PPM失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 查找PPM头部结束位置
     */
    private int findPpmHeaderEnd(byte[] ppmData) {
        int lineCount = 0;
        boolean inComment = false;
        
        for (int i = 0; i < ppmData.length; i++) {
            byte b = ppmData[i];
            
            // 处理注释
            if (b == '#') {
                inComment = true;
                continue;
            }
            
            if (b == '\n') {
                if (!inComment) {
                    lineCount++;
                    if (lineCount == 3) {
                        return i + 1; // 返回像素数据开始位置
                    }
                }
                inComment = false;
            }
        }
        
        return -1; // 未找到完整头部
    }
    
    /**
     * 生成测试图案
     */
    private void generateTestPattern(BufferedImage image, int width, int height) {
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb;
                // 生成彩色条纹图案
                if (y < height / 3) {
                    rgb = 0xFF0000; // 红色
                } else if (y < 2 * height / 3) {
                    rgb = 0x00FF00; // 绿色
                } else {
                    rgb = 0x0000FF; // 蓝色
                }
                
                // 添加边框
                if (x < 5 || x >= width - 5 || y < 5 || y >= height - 5) {
                    rgb = 0x000000; // 黑色边框
                }
                
                image.setRGB(x, y, rgb);
            }
        }
        log.info("已生成{}x{}的彩色条纹测试图案", width, height);
    }
    
    /**
     * 检查是否为PPM格式
     */
    private boolean isPpmFormat(byte[] data) {
        if (data == null || data.length < 3) {
            return false;
        }
        
        // PPM格式以"P6\n"开头
        return data[0] == 'P' && data[1] == '6' && (data[2] == '\n' || data[2] == ' ');
    }
    
    /**
     * 提取指定区域图像
     * @param tronFilePath TRON文件路径
     * @param x 区域左上角X
     * @param y 区域左上角Y
     * @param width 区域宽度
     * @param height 区域高度
     * @param level DZI层级
     * @return 区域图像数据（JPEG格式）
     */
    public byte[] extractRegion(String tronFilePath, int x, int y, int width, int height, int level) {
        try {
            log.debug("命令行提取Tron区域: {} region {} {} {} {} {}", tronFilePath, level, 0, x, y, width, height);
            // Tron C工具 region 参数顺序: <tron文件> region <lod_level> <layer> <x> <y> <width> <height> <输出文件>
            // 这里默认 layer=0
            Path tempOutputFile = Files.createTempFile("tron_region_", ".dat");
            try {
                ProcessBuilder pb = new ProcessBuilder(
                    EXTRACTOR_BINARY,
                    tronFilePath,
                    "region",
                    String.valueOf(level),
                    "0",
                    String.valueOf(x),
                    String.valueOf(y),
                    String.valueOf(width),
                    String.valueOf(height),
                    tempOutputFile.toString()
                );
                pb.redirectErrorStream(true);
                log.debug("执行命令: {}", String.join(" ", pb.command()));
                Process process = pb.start();
                StringBuilder output = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        output.append(line).append("\n");
                        log.debug("提取器输出: {}", line);
                    }
                }
                boolean finished = process.waitFor(TIMEOUT_SECONDS, TimeUnit.SECONDS);
                if (!finished) {
                    log.error("Tron区域提取超时: {} seconds", TIMEOUT_SECONDS);
                    process.destroyForcibly();
                    return null;
                }
                int exitCode = process.exitValue();
                if (exitCode != 0) {
                    log.error("Tron区域提取失败，退出码: {}, 输出: {}", exitCode, output.toString());
                    return null;
                }
                // 检查输出文件
                if (Files.exists(tempOutputFile) && Files.size(tempOutputFile) > 0) {
                    byte[] imageData = Files.readAllBytes(tempOutputFile);
                    String fileExtension = getFileExtension(tempOutputFile.toString());
                    log.info("成功提取Tron区域: {} bytes, 文件: {}, 格式: {}", imageData.length, tempOutputFile, fileExtension);
                    return processImageByFormat(imageData, fileExtension, "region");
                } else {
                    log.warn("Tron区域输出文件不存在或为空: {}", tempOutputFile);
                    return null;
                }
            } finally {
                try { Files.deleteIfExists(tempOutputFile); } catch (IOException e) { log.warn("清理临时文件失败: {}", e.getMessage()); }
            }
        } catch (Exception e) {
            log.error("Tron区域提取失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取TRON切片的基本信息（层数、LOD范围、瓦片大小、瓦片数量、分辨率、内容区域、整图宽高）
     * @param tronFilePath TRON文件路径
     * @return 关键信息Map
     */
    public Map<String, Object> getInfo(String tronFilePath) {
        Map<String, Object> info = new HashMap<>();
        try {
            ProcessBuilder pb = new ProcessBuilder(EXTRACTOR_BINARY, tronFilePath, "info");
            pb.redirectErrorStream(true);
            Process process = pb.start();
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    parseInfoLine(line, info);
                }
            }
            boolean finished = process.waitFor(TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (!finished) {
                log.error("Tron info 获取超时: {} seconds", TIMEOUT_SECONDS);
                process.destroyForcibly();
                info.put("error", "timeout");
                return info;
            }
            int exitCode = process.exitValue();
            if (exitCode != 0) {
                log.error("Tron info 获取失败，退出码: {}, 输出: {}", exitCode, output.toString());
                info.put("error", "exitCode=" + exitCode);
                return info;
            }
        } catch (Exception e) {
            log.error("Tron info 获取异常: {}", e.getMessage(), e);
            info.put("error", e.getMessage());
        }
        return info;
    }

    /**
     * 解析info输出的每一行，填充到Map
     */
    private void parseInfoLine(String line, Map<String, Object> info) {
        if (line == null || line.trim().isEmpty()) return;
        line = line.trim();
        if (line.startsWith("层数:")) {
            info.put("layerCount", Integer.parseInt(line.replace("层数:", "").trim()));
        } else if (line.startsWith("LOD层级范围:")) {
            String[] parts = line.replace("LOD层级范围:", "").trim().split(",");
            Integer lodMin = null, lodMax = null;
            for (String part : parts) {
                if (part.contains("min=")) {
                    lodMin = Integer.parseInt(part.replace("min=", "").trim());
                    info.put("lodMin", lodMin);
                }
                if (part.contains("max=")) {
                    lodMax = Integer.parseInt(part.replace("max=", "").trim());
                    info.put("lodMax", lodMax);
                }
            }
            // 新增：计算并保存层级数量，确保tron格式文件的metadata包含levelCount
            if (lodMin != null && lodMax != null) {
                int levelCount = lodMax - lodMin + 1;
                info.put("levelCount", levelCount);
                log.debug("TronSDK计算层级数量: min={}, max={}, levelCount={}", lodMin, lodMax, levelCount);
            }
        } else if (line.startsWith("瓦片大小:")) {
            String[] wh = line.replace("瓦片大小:", "").trim().split("x");
            if (wh.length == 2) {
                info.put("tileWidth", Integer.parseInt(wh[0].trim()));
                info.put("tileHeight", Integer.parseInt(wh[1].trim()));
            }
        } else if (line.startsWith("瓦片数量:")) {
            String[] parts = line.replace("瓦片数量:", "").trim().split(",");
            for (String part : parts) {
                if (part.contains("水平=")) info.put("tileCountH", Integer.parseInt(part.replace("水平=", "").trim()));
                if (part.contains("垂直=")) info.put("tileCountV", Integer.parseInt(part.replace("垂直=", "").trim()));
            }
        } else if (line.startsWith("分辨率:")) {
            String[] parts = line.replace("分辨率:", "").trim().split(",");
            Double resH = null, resV = null;
            for (String part : parts) {
                if (part.contains("水平=")) {
                    resH = Double.parseDouble(part.replace("水平=", "").replace("um/px", "").trim());
                    info.put("resolutionH", resH);
                }
                if (part.contains("垂直=")) {
                    resV = Double.parseDouble(part.replace("垂直=", "").replace("um/px", "").trim());
                    info.put("resolutionV", resV);
                }
            }
            // 同步写入mppX/mppY/mpp，供后续分析使用
            if (resH != null) {
                info.put("mppX", resH);
            }
            if (resV != null) {
                info.put("mppY", resV);
            }
            if (resH != null && resV != null) {
                info.put("mpp", (resH + resV) / 2.0);
            }
        } else if (line.startsWith("内容区域:")) {
            String[] parts = line.replace("内容区域:", "").trim().split(",");
            for (String part : parts) {
                if (part.contains("left=")) info.put("regionLeft", Integer.parseInt(part.replace("left=", "").trim()));
                if (part.contains("top=")) info.put("regionTop", Integer.parseInt(part.replace("top=", "").trim()));
                if (part.contains("width=")) info.put("width", Integer.parseInt(part.replace("width=", "").trim()));
                if (part.contains("height=")) info.put("height", Integer.parseInt(part.replace("height=", "").trim()));
            }
        } else if (line.startsWith("最大缩放倍率:")) {
            info.put("maximumZoomLevel", Double.parseDouble(line.replace("最大缩放倍率:", "").trim()));
        }
    }

    /**
     * 智能检测最佳通道顺序
     * 
     * @param imageData BGR24原始数据
     * @param width 图像宽度
     * @param height 图像高度
     * @return 最佳通道顺序索引 (0=BGR, 1=RGB, 2=GBR, 3=GRB, 4=RBG, 5=BRG)
     */
    private int detectBestChannelOrder(byte[] imageData, int width, int height) {
        int pixelCount = width * height;
        if (pixelCount == 0 || imageData.length < pixelCount * 3) {
            return 0; // 默认BGR
        }
        
        // 测试样本（前1000个像素或全部像素）
        int sampleSize = Math.min(1000, pixelCount);
        
        double[] scores = new double[6]; // BGR, RGB, GBR, GRB, RBG, BRG
        int[][] orders = {
            {2, 1, 0}, // BGR -> RGB
            {0, 1, 2}, // RGB -> RGB  
            {1, 2, 0}, // GBR -> RGB
            {1, 0, 2}, // GRB -> RGB
            {2, 0, 1}, // RBG -> RGB
            {0, 2, 1}  // BRG -> RGB
        };
        String[] orderNames = {"BGR", "RGB", "GBR", "GRB", "RBG", "BRG"};
        
        log.debug("检测最佳通道顺序...");
        
        for (int order = 0; order < 6; order++) {
            double rSum = 0, gSum = 0, bSum = 0;
            
            for (int i = 0; i < sampleSize; i++) {
                int baseIndex = i * 3;
                int r = (imageData[baseIndex + orders[order][0]] & 0xFF);
                int g = (imageData[baseIndex + orders[order][1]] & 0xFF);
                int b = (imageData[baseIndex + orders[order][2]] & 0xFF);
                
                rSum += r; gSum += g; bSum += b;
            }
            
            double rAvg = rSum / sampleSize;
            double gAvg = gSum / sampleSize;
            double bAvg = bSum / sampleSize;
            
            // 评分标准：理想情况下，病理切片应该有较平衡的色彩分布
            double yellowBias = (rAvg + gAvg) / 2.0 - bAvg;
            double colorBalance = 255.0 - (Math.abs(rAvg - gAvg) + Math.abs(gAvg - bAvg) + Math.abs(rAvg - bAvg)) / 3.0;
            
            // 综合评分：色彩平衡占70%，黄色偏移占30%
            scores[order] = colorBalance * 0.7 - Math.abs(yellowBias) * 0.3;
            
            log.debug("  {}: R={}, G={}, B={}, 黄色偏移={}, 评分={}", 
                     orderNames[order], 
                     String.format("%.1f", rAvg), 
                     String.format("%.1f", gAvg), 
                     String.format("%.1f", bAvg), 
                     String.format("%.1f", yellowBias), 
                     String.format("%.2f", scores[order]));
        }
        
        // 找到最高分的顺序
        int bestOrder = 0;
        for (int i = 1; i < 6; i++) {
            if (scores[i] > scores[bestOrder]) {
                bestOrder = i;
            }
        }
        
        log.info("选择最佳通道顺序: {} (评分: {})", orderNames[bestOrder], String.format("%.2f", scores[bestOrder]));
        return bestOrder;
    }
    
    /**
     * 自适应颜色校正
     * 
     * @param r 红色分量
     * @param g 绿色分量
     * @param b 蓝色分量
     * @param yellowBiasLevel 整体黄色偏移水平
     * @return 校正后的RGB数组
     */
    private int[] applyAdaptiveColorCorrection(int r, int g, int b, double yellowBiasLevel) {
        // 根据整体黄色偏移水平调整校正强度
        float baseCorrection = 0.0f;
        if (yellowBiasLevel > 40) {
            baseCorrection = 0.25f; // 强校正
        } else if (yellowBiasLevel > 20) {
            baseCorrection = 0.15f; // 中等校正
        } else if (yellowBiasLevel > 10) {
            baseCorrection = 0.08f; // 轻微校正
        } else {
            return new int[]{r, g, b}; // 无需校正
        }
        
        // 计算当前像素的黄色偏移
        float pixelYellow = (r + g) / 2.0f - b;
        
        if (pixelYellow > 15) { // 像素级黄色偏移阈值
            // 动态调整校正强度
            float pixelCorrection = baseCorrection * (pixelYellow / 100.0f);
            pixelCorrection = Math.min(pixelCorrection, 0.3f); // 最大30%
            
            // 应用校正
            r = Math.max(0, Math.min(255, (int)(r * (1.0f - pixelCorrection * 0.4f)))); // 降红
            g = Math.max(0, Math.min(255, (int)(g * (1.0f - pixelCorrection * 0.6f)))); // 降绿（更多）
            b = Math.max(0, Math.min(255, (int)(b * (1.0f + pixelCorrection * 0.5f)))); // 增蓝
        }
        
        return new int[]{r, g, b};
    }
    
    /**
     * 智能BGR24到JPEG转换（带颜色校正）
     */
    private byte[] smartConvertBgr24ToJpeg(byte[] bgrData, int width, int height) {
        log.debug("开始智能BGR24到JPEG转换，尺寸: {}x{}", width, height);
        
        // 1. 检测最佳通道顺序
        int bestOrder = detectBestChannelOrder(bgrData, width, height);
        
        int[][] orders = {
            {2, 1, 0}, // BGR -> RGB
            {0, 1, 2}, // RGB -> RGB  
            {1, 2, 0}, // GBR -> RGB
            {1, 0, 2}, // GRB -> RGB
            {2, 0, 1}, // RBG -> RGB
            {0, 2, 1}  // BRG -> RGB
        };
        
        // 2. 计算整体黄色偏移水平
        long rTotal = 0, gTotal = 0, bTotal = 0;
        int pixelCount = width * height;
        
        for (int i = 0; i < pixelCount; i++) {
            int baseIndex = i * 3;
            rTotal += (bgrData[baseIndex + orders[bestOrder][0]] & 0xFF);
            gTotal += (bgrData[baseIndex + orders[bestOrder][1]] & 0xFF);
            bTotal += (bgrData[baseIndex + orders[bestOrder][2]] & 0xFF);
        }
        
        double rAvg = (double)rTotal / pixelCount;
        double gAvg = (double)gTotal / pixelCount;
        double bAvg = (double)bTotal / pixelCount;
        double yellowBiasLevel = (rAvg + gAvg) / 2.0 - bAvg;
        
        log.info("整体色彩: R={}, G={}, B={}", 
                String.format("%.1f", rAvg), 
                String.format("%.1f", gAvg), 
                String.format("%.1f", bAvg));
        log.info("黄色偏移水平: {}", String.format("%.1f", yellowBiasLevel));
        
        // 3. 创建BufferedImage并应用智能转换
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int index = (y * width + x) * 3;
                
                // 按检测到的最佳顺序提取颜色
                int r = (bgrData[index + orders[bestOrder][0]] & 0xFF);
                int g = (bgrData[index + orders[bestOrder][1]] & 0xFF);
                int b = (bgrData[index + orders[bestOrder][2]] & 0xFF);
                
                // 应用自适应颜色校正
                int[] corrected = applyAdaptiveColorCorrection(r, g, b, yellowBiasLevel);
                r = corrected[0];
                g = corrected[1];
                b = corrected[2];
                
                // 设置像素
                int rgb = (r << 16) | (g << 8) | b;
                image.setRGB(x, y, rgb);
                
                // 输出前几个像素的详细信息
                if (x < 3 && y == 0) {
                    log.debug("像素({},{}): 原始({},{},{}) -> 校正RGB({},{},{})", 
                             x, y, 
                             (bgrData[index] & 0xFF), (bgrData[index+1] & 0xFF), (bgrData[index+2] & 0xFF),
                             r, g, b);
                }
            }
        }
        
        log.info("智能转换完成");
        return convertBufferedImageToJpeg(image);
    }

    /**
     * 验证BGR通道顺序是否正确（调试用）
     * 
     * @param r 红色分量
     * @param g 绿色分量  
     * @param b 蓝色分量
     * @return 调试信息
     */
    private String debugColorChannels(int r, int g, int b) {
        // 检测可能的通道错误
        if (g > r && g > b && (g - Math.max(r, b)) > 30) {
            return "可能绿色通道过强";
        }
        if ((r + g) > b * 2 && (r + g - b * 2) > 40) {
            return "明显偏黄(R+G >> B)";
        }
        if (r > g && r > b && (r - Math.max(g, b)) > 30) {
            return "可能红色通道过强";
        }
        return "正常";
    }

    /**
     * 尝试不同的BGR通道映射方式（调试用）
     */
    private int[] tryAlternativeChannelMapping(int bgrIndex0, int bgrIndex1, int bgrIndex2) {
        // 原始BGR -> RGB
        int[] mapping1 = {bgrIndex2, bgrIndex1, bgrIndex0}; // B,G,R -> R,G,B
        
        // 尝试其他可能的映射
        int[] mapping2 = {bgrIndex0, bgrIndex1, bgrIndex2}; // B,G,R -> B,G,R (不变)
        int[] mapping3 = {bgrIndex1, bgrIndex2, bgrIndex0}; // B,G,R -> G,R,B
        int[] mapping4 = {bgrIndex1, bgrIndex0, bgrIndex2}; // B,G,R -> G,B,R
        int[] mapping5 = {bgrIndex2, bgrIndex0, bgrIndex1}; // B,G,R -> R,B,G
        int[] mapping6 = {bgrIndex0, bgrIndex2, bgrIndex1}; // B,G,R -> B,R,G
        
        // 返回原始映射（可以在调试时切换）
        return mapping1;
    }

    /**
     * 向左旋转图像90度
     * 
     * @param originalImage 原始图像
     * @return 旋转后的图像
     */
    private BufferedImage rotateImageLeft90(BufferedImage originalImage) {
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();
        
        // 旋转90度后，宽高互换
        BufferedImage rotatedImage = new BufferedImage(originalHeight, originalWidth, BufferedImage.TYPE_INT_RGB);
        
        log.info("开始向左旋转90度: 原始尺寸{}x{} -> 旋转后尺寸{}x{}", 
                originalWidth, originalHeight, originalHeight, originalWidth);
        
        // 向左旋转90度的像素坐标转换：
        // 原坐标(x, y) -> 新坐标(y, originalWidth - 1 - x)
        for (int x = 0; x < originalWidth; x++) {
            for (int y = 0; y < originalHeight; y++) {
                int rgb = originalImage.getRGB(x, y);
                // 向左旋转90度的坐标变换
                int newX = y;
                int newY = originalWidth - 1 - x;
                rotatedImage.setRGB(newX, newY, rgb);
            }
        }
        
        log.info("图像旋转完成");
        return rotatedImage;
    }

    /**
     * 提取宏观(玻片)图，只尝试macro，并向左旋转90度
     */
    public byte[] extractMacro(String tronFilePath) {
        byte[] macroData = extractImage(tronFilePath, "macro");
        
        if (macroData != null && macroData.length > 0) {
            // 宏观图需要向左旋转90度
            return rotateMacroImage(macroData);
        }
        
        return macroData;
    }
    
    /**
     * 旋转宏观图像
     */
    private byte[] rotateMacroImage(byte[] imageData) {
        try {
            log.info("开始对宏观图应用向左旋转90度");
            
            // 将图像数据转换为BufferedImage
            ByteArrayInputStream bais = new ByteArrayInputStream(imageData);
            BufferedImage originalImage = ImageIO.read(bais);
            
            if (originalImage != null) {
                // 向左旋转90度
                BufferedImage rotatedImage = rotateImageLeft90(originalImage);
                
                // 转换回JPEG字节数组
                return convertBufferedImageToJpeg(rotatedImage);
            } else {
                log.warn("无法解析宏观图像数据，返回原始数据");
                return imageData;
            }
            
        } catch (Exception e) {
            log.error("旋转宏观图失败: {}", e.getMessage(), e);
            return imageData; // 失败时返回原始数据
        }
    }
} 