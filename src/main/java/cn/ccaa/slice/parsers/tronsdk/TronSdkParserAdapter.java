package cn.ccaa.slice.parsers.tronsdk;

import java.io.File;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.ccaa.slice.core.exception.SlideException;
import cn.ccaa.slice.core.model.ColorCorrectionParams;
import cn.ccaa.slice.core.parser.SlideParser;
import cn.ccaa.slice.parsers.tronsdk.service.TronSdkImageExtractor;

/**
 * TronSDK解析器适配器
 * 通过命令行工具处理Tron格式文件，支持DZI查看器功能
 * 
 * <AUTHOR>
 */
@Component
public class TronSdkParserAdapter implements SlideParser {

    private static final Logger log = LoggerFactory.getLogger(TronSdkParserAdapter.class);
    
    @Autowired
    private TronSdkImageExtractor tronSdkImageExtractor;

    @Override
    public String getParserName() {
        return "tronsdk";
    }

    @Override
    public boolean supportsFormat(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }

        try {
            File file = new File(filePath);
            if (!file.exists() || !file.isFile()) {
                log.debug("文件不存在或不是有效文件: {}", filePath);
                return false;
            }

            // 检查文件扩展名
            String fileName = file.getName().toLowerCase();
            if (!fileName.endsWith(".tron")) {
                log.debug("文件 {} 不是.tron格式", filePath);
                return false;
            }

            // 检查TronSDK图像提取器是否可用
            if (!tronSdkImageExtractor.isAvailable()) {
                log.warn("TronSDK图像提取器不可用: {}", filePath);
                return false;
            }

            log.debug("TronSDK解析器支持文件: {}", filePath);
            return true;

        } catch (Exception e) {
            log.error("检查TronSDK文件支持性时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Object openSlide(String filePath) {
        if (!supportsFormat(filePath)) {
            throw new SlideException("TronSDK不支持该文件格式: " + filePath);
        }

        try {
            log.info("使用TronSDK打开切片文件: {}", filePath);
            
            // 对于Tron格式，我们返回文件路径作为"slide"对象
            // 因为Tron是通过命令行工具处理的，不需要保持连接
            return filePath;
            
        } catch (Exception e) {
            log.error("TronSDK打开文件失败: {}", e.getMessage(), e);
            throw new SlideException("TronSDK无法打开文件: " + filePath, e);
        }
    }

    @Override
    public void closeSlide(Object slide) {
        // Tron格式通过命令行工具处理，不需要显式关闭
        log.debug("TronSDK关闭切片文件");
    }

    @Override
    public Map<String, Object> getSlideInfo(Object slide) {
        String filePath = (String) slide;
        
        try {
            log.debug("获取TronSDK切片信息: {}", filePath);
            
            Map<String, Object> info = tronSdkImageExtractor.getInfo(filePath);
            if (info != null && !info.containsKey("error")) {
                log.debug("成功获取TronSDK切片信息");
                return info;
            } else {
                log.warn("TronSDK getInfo返回错误: {}", info);
                throw new SlideException("获取Tron文件信息失败");
            }
            
        } catch (Exception e) {
            log.error("获取TronSDK切片信息失败: {}", e.getMessage(), e);
            throw new SlideException("获取Tron切片信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> getLevelInfo(Object slide, int level) {
        String filePath = (String) slide;
        
        try {
            Map<String, Object> slideInfo = getSlideInfo(slide);
            Map<String, Object> levelInfo = new java.util.HashMap<>();
            
            // 计算当前层级的尺寸
            long width = getLevelWidth(slide, level);
            long height = getLevelHeight(slide, level);
            
            levelInfo.put("level", level);
            levelInfo.put("width", width);
            levelInfo.put("height", height);
            
            // 从切片信息中复制瓦片相关信息
            if (slideInfo.containsKey("tileWidth")) {
                levelInfo.put("tileWidth", slideInfo.get("tileWidth"));
            }
            if (slideInfo.containsKey("tileHeight")) {
                levelInfo.put("tileHeight", slideInfo.get("tileHeight"));
            }
            
            log.debug("TronSDK层级{}信息: {}x{}", level, width, height);
            return levelInfo;
            
        } catch (Exception e) {
            log.error("获取TronSDK层级信息失败: {}", e.getMessage(), e);
            throw new SlideException("获取Tron层级信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public byte[] getThumbnailJpeg(Object slide) {
        String filePath = (String) slide;
        
        try {
            log.debug("获取TronSDK缩略图");
            
            byte[] thumbnailData = tronSdkImageExtractor.extractThumbnail(filePath);
            
            if (thumbnailData != null && thumbnailData.length > 0) {
                log.debug("成功获取TronSDK缩略图: {} bytes", thumbnailData.length);
                return thumbnailData;
            } else {
                log.warn("TronSDK缩略图数据为空");
                return createEmptyTile();
            }
            
        } catch (Exception e) {
            log.error("获取TronSDK缩略图失败: {}", e.getMessage(), e);
            return createEmptyTile();
        }
    }

    @Override
    public byte[] getLabelJpeg(Object slide) {
        String filePath = (String) slide;
        
        try {
            log.debug("获取TronSDK标签图");
            
            byte[] labelData = tronSdkImageExtractor.extractLabel(filePath);
            
            if (labelData != null && labelData.length > 0) {
                log.debug("成功获取TronSDK标签图: {} bytes", labelData.length);
                return labelData;
            } else {
                log.warn("TronSDK标签图数据为空");
                return createEmptyTile();
            }
            
        } catch (Exception e) {
            log.error("获取TronSDK标签图失败: {}", e.getMessage(), e);
            return createEmptyTile();
        }
    }

    public int getLevelCount(Object slide) {
        Map<String, Object> properties = getSlideInfo(slide);
        
        // 从属性中获取LOD层级信息
        Object lodMin = properties.get("lodMin");
        Object lodMax = properties.get("lodMax");
        
        if (lodMin != null && lodMax != null) {
            try {
                int min = Integer.parseInt(lodMin.toString());
                int max = Integer.parseInt(lodMax.toString());
                int levelCount = max - min + 1;
                log.debug("TronSDK层级数量: {}", levelCount);
                return levelCount;
            } catch (NumberFormatException e) {
                log.warn("解析LOD层级失败: min={}, max={}", lodMin, lodMax);
            }
        }
        
        // 默认返回1层
        log.warn("无法确定TronSDK层级数量，返回默认值1");
        return 1;
    }

    public long getLevelWidth(Object slide, int level) {
        Map<String, Object> properties = getSlideInfo(slide);
        
        Object width = properties.get("width");
        if (width != null) {
            try {
                long fullWidth = Long.parseLong(width.toString());
                // 根据层级计算缩放后的宽度
                long levelWidth = fullWidth >> level;
                log.debug("TronSDK层级{}宽度: {}", level, levelWidth);
                return Math.max(1, levelWidth);
            } catch (NumberFormatException e) {
                log.warn("解析宽度失败: {}", width);
            }
        }
        
        log.warn("无法获取TronSDK宽度，返回默认值1024");
        return 1024;
    }

    public long getLevelHeight(Object slide, int level) {
        Map<String, Object> properties = getSlideInfo(slide);
        
        Object height = properties.get("height");
        if (height != null) {
            try {
                long fullHeight = Long.parseLong(height.toString());
                // 根据层级计算缩放后的高度
                long levelHeight = fullHeight >> level;
                log.debug("TronSDK层级{}高度: {}", level, levelHeight);
                return Math.max(1, levelHeight);
            } catch (NumberFormatException e) {
                log.warn("解析高度失败: {}", height);
            }
        }
        
        log.warn("无法获取TronSDK高度，返回默认值1024");
        return 1024;
    }

    @Override
    public byte[] getTileJpeg(Object slide, int x, int y, int level) {
        String filePath = (String) slide;
        
        try {
            log.debug("获取TronSDK瓦片: ({}, {}), 层级: {}", x, y, level);
            
            // 使用TronSdkImageExtractor提取瓦片
            byte[] tileData = tronSdkImageExtractor.extractTile(filePath, x, y, level);
            
            if (tileData != null && tileData.length > 0) {
                log.debug("成功获取TronSDK瓦片: {} bytes", tileData.length);
                return tileData;
            } else {
                log.warn("TronSDK瓦片数据为空");
                return createEmptyTile();
            }
            
        } catch (Exception e) {
            log.error("获取TronSDK瓦片失败: {}", e.getMessage(), e);
            return createEmptyTile();
        }
    }

    @Override
    public byte[] getRegionJpeg(Object slide, int x, int y, int width, int height, int level) {
        String filePath = (String) slide;
        
        try {
            log.debug("获取TronSDK区域: ({}, {}), 尺寸: {}x{}, 层级: {}", x, y, width, height, level);
            
            // 使用TronSdkImageExtractor提取区域
            byte[] regionData = tronSdkImageExtractor.extractRegion(filePath, x, y, width, height, level);
            
            if (regionData != null && regionData.length > 0) {
                log.debug("成功获取TronSDK区域: {} bytes", regionData.length);
                return regionData;
            } else {
                log.warn("TronSDK区域数据为空");
                return createEmptyTile();
            }
            
        } catch (Exception e) {
            log.error("获取TronSDK区域失败: {}", e.getMessage(), e);
            return createEmptyTile();
        }
    }

    public String getBackgroundColor(Object slide) {
        // Tron格式默认白色背景
        return "#FFFFFF";
    }

    public byte[] getAssociatedImage(Object slide, String imageName) {
        String filePath = (String) slide;
        
        try {
            log.debug("获取TronSDK关联图像: {}", imageName);
            
            byte[] imageData = null;
            
            // 根据图像名称选择提取方法
            switch (imageName.toLowerCase()) {
                case "thumbnail":
                    imageData = tronSdkImageExtractor.extractThumbnail(filePath);
                    break;
                case "label":
                case "macro":
                    imageData = tronSdkImageExtractor.extractLabel(filePath);
                    break;
                default:
                    log.warn("不支持的关联图像类型: {}", imageName);
                    return null;
            }
            
            if (imageData != null && imageData.length > 0) {
                log.debug("成功获取TronSDK关联图像: {} bytes", imageData.length);
                return imageData;
            } else {
                log.warn("TronSDK关联图像数据为空: {}", imageName);
                return null;
            }
            
        } catch (Exception e) {
            log.error("获取TronSDK关联图像失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean supportsColorCorrection() {
        // TronSDK不支持原生颜色校正，但已经内置了颜色优化
        return false;
    }

    @Override
    public byte[] getRegionJpegWithColor(Object slide, int x, int y, int width, int height, int level, ColorCorrectionParams colorParams) {
        // TronSDK不支持原生颜色校正，返回原始区域图像（已应用内置颜色优化）
        log.debug("TronSDK不支持原生颜色校正，返回原始区域图像");
        return getRegionJpeg(slide, x, y, width, height, level);
    }

    @Override
    public byte[] getTileJpegWithColor(Object slide, int x, int y, int level, ColorCorrectionParams colorParams) {
        // TronSDK不支持原生颜色校正，返回原始瓦片（已应用内置颜色优化）
        log.debug("TronSDK不支持原生颜色校正，返回原始瓦片");
        return getTileJpeg(slide, x, y, level);
    }

    @Override
    public byte[] getSlideJpeg(Object slide) {
        String filePath = (String) slide;
        try {
            byte[] macro = tronSdkImageExtractor.extractMacro(filePath);
            return (macro != null && macro.length > 0) ? macro : new byte[0];
        } catch (Exception e) {
            log.error("获取TronSDK宏观图失败: {}", e.getMessage());
            return new byte[0];
        }
    }

    /**
     * 创建空白瓦片
     */
    private byte[] createEmptyTile() {
        try {
            // 创建256x256的白色图像
            java.awt.image.BufferedImage emptyTile = new java.awt.image.BufferedImage(256, 256, java.awt.image.BufferedImage.TYPE_INT_RGB);
            java.awt.Graphics2D g2d = emptyTile.createGraphics();
            g2d.setColor(java.awt.Color.WHITE);
            g2d.fillRect(0, 0, 256, 256);
            g2d.dispose();
            
            java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
            javax.imageio.ImageIO.write(emptyTile, "JPEG", baos);
            return baos.toByteArray();
        } catch (Exception e) {
            log.error("创建空白瓦片失败: {}", e.getMessage());
            return new byte[0];
        }
    }
} 