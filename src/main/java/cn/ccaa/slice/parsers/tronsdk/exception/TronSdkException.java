package cn.ccaa.slice.parsers.tronsdk.exception;

import cn.ccaa.slice.parsers.tronsdk.model.TronError;

/**
 * TronSDK操作相关的自定义异常
 * 
 * <AUTHOR>
 */
public class TronSdkException extends RuntimeException {
    
    private final TronError error;

    public TronSdkException(String message) {
        super(message);
        this.error = TronError.UNKNOWN_ERROR;
    }

    public TronSdkException(String message, Throwable cause) {
        super(message, cause);
        this.error = TronError.UNKNOWN_ERROR;
    }

    public TronSdkException(String message, TronError error) {
        super(message);
        this.error = error;
    }

    public TronSdkException(String message, Throwable cause, TronError error) {
        super(message, cause);
        this.error = error;
    }

    public TronError getError() {
        return error;
    }

    public int getErrorCode() {
        return error.getCode();
    }

    @Override
    public String toString() {
        return String.format("TronSdkException{error=%s, message='%s'}", 
                           error, getMessage());
    }
} 