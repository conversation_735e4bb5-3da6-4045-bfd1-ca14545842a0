package cn.ccaa.slice.parsers.tronsdk.model;

/**
 * TronSDK错误码枚举
 * 对应tronc.h中定义的错误常量
 * 
 * <AUTHOR>
 */
public enum TronError {
    
    SUCCESS(0, "操作成功"),
    UNKNOWN_ERROR(-1, "未知错误"),
    INVALID_PATH(1, "无效的文件路径"),
    IO_ERROR(2, "IO错误"),
    INVALID_ARCHIVE(3, "无效的归档文件"),
    INVALID_HANDLER(10, "无效的处理器"),
    INVALID_LOD_LEVEL(20, "无效的LOD级别"),
    INSUFFICIENT_LENGTH(30, "缓冲区长度不足"),
    INVALID_IMAGE_NAME(40, "无效的图像名称"),
    CLIP_ARCHIVE_ERROR(100, "切片归档错误"),
    CLIP_INVALID_ARGUMENT(101, "切片参数无效");

    private final int code;
    private final String description;

    TronError(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据错误码获取对应的错误枚举
     * 
     * @param code 错误码
     * @return 对应的错误枚举，如果未找到则返回UNKNOWN_ERROR
     */
    public static TronError fromCode(int code) {
        for (TronError error : values()) {
            if (error.code == code) {
                return error;
            }
        }
        return UNKNOWN_ERROR;
    }

    @Override
    public String toString() {
        return String.format("TronError{code=%d, description='%s'}", code, description);
    }
} 