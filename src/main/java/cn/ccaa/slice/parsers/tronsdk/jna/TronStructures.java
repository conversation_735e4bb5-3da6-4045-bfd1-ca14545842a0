package cn.ccaa.slice.parsers.tronsdk.jna;

import com.sun.jna.Structure;
import java.util.Arrays;
import java.util.List;

/**
 * TronSDK结构体的JNA映射
 * 对应tronc.h中定义的所有结构体
 * 
 * <AUTHOR>
 */
public class TronStructures {

    /**
     * 背景颜色结构体
     */
    public static class TronBackgroundColor extends Structure {
        public byte red;
        public byte green;
        public byte blue;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("red", "green", "blue");
        }
    }

    /**
     * 内容区域结构体
     */
    public static class TronContentRegion extends Structure {
        public int left;
        public int top;
        public int width;
        public int height;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("left", "top", "width", "height");
        }
    }

    /**
     * LOD级别范围结构体
     */
    public static class TronLodLevelRange extends Structure {
        public int minimum;
        public int maximum;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("minimum", "maximum");
        }
    }

    /**
     * 图像信息结构体
     */
    public static class TronImageInfo extends Structure {
        public boolean existed;
        public long width;
        public long height;
        public long length;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("existed", "width", "height", "length");
        }
    }

    /**
     * 分辨率结构体
     */
    public static class TronResolution extends Structure {
        public float horizontal;
        public float vertical;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("horizontal", "vertical");
        }
    }

    /**
     * 瓦片数量结构体
     */
    public static class TronTileCount extends Structure {
        public int horizontal;
        public int vertical;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("horizontal", "vertical");
        }
    }

    /**
     * 瓦片大小结构体
     */
    public static class TronTileSize extends Structure {
        public int width;
        public int height;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("width", "height");
        }
    }

    /**
     * 版本信息结构体
     */
    public static class TronVersion extends Structure {
        public int major;
        public int minor;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("major", "minor");
        }
    }
} 