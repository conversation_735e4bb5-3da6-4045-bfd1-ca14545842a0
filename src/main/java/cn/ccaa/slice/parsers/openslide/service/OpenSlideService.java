package cn.ccaa.slice.parsers.openslide.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import cn.ccaa.slice.config.SlideParserConfig;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * OpenSlide服务类
 * 初始化OpenSlide库路径配置
 * <AUTHOR>
 */
@Slf4j
@Service
public class OpenSlideService {

    @Autowired
    private SlideParserConfig parserConfig;

    /**
     * 从配置文件直接读取库路径，支持从两个位置读取
     */
    @Value("${slide.openslide.library-path:${openslide.library-path:}}")
    private String configLibraryPath;

    /**
     * 初始化OpenSlide库路径
     */
    @PostConstruct
    public void init() {
        try {
            // 优先使用直接配置的library-path
            String libraryPath = configLibraryPath;
            
            // 如果直接配置为空，则尝试从parserConfig获取
            if ((libraryPath == null || libraryPath.isEmpty()) && parserConfig != null) {
                libraryPath = parserConfig.getOpenSlide().getLibraryPath();
            }
            
            if (libraryPath != null && !libraryPath.isEmpty()) {
                log.info("设置OpenSlide库路径: {}", libraryPath);
                
                // OpenSlide使用系统属性设置库路径
                System.setProperty("openslide.path", libraryPath);
                
                // 对于JNA方式加载，也设置jna.library.path
                System.setProperty("jna.library.path", libraryPath);
                
                log.info("OpenSlide库路径设置成功");
            } else {
                log.warn("OpenSlide库路径未配置，将尝试从系统路径加载");
            }
        } catch (Exception e) {
            log.error("初始化OpenSlide库失败", e);
        }
    }
} 
