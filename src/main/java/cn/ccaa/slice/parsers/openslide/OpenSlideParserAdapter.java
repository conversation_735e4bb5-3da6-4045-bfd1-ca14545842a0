package cn.ccaa.slice.parsers.openslide;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

import javax.imageio.ImageIO;

import org.openslide.OpenSlide;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import cn.ccaa.slice.core.exception.SlideException;
import cn.ccaa.slice.core.model.ColorCorrectionParams;
import cn.ccaa.slice.core.parser.SlideParser;
import lombok.NonNull;

/**
 * OpenSlide解析器适配器
 * 将OpenSlide库适配为统一的SlideParser接口
 *
 * <AUTHOR>
 */
@Component
public class OpenSlideParserAdapter implements SlideParser {

    private static final Logger log = LoggerFactory.getLogger(OpenSlideParserAdapter.class);

    private static final String PARSER_NAME = "openslide";
    private static final int TILE_SIZE = 256;
    private static final int MAX_THUMBNAIL_SIZE = 600; // 与DZI缩略图一致

    private static final String[] BARCODE_KEYS = {
        "openslide.barcode",
        "aperio.Barcode",
        "hamamatsu.Barcode",
        "leica.barcode",
        "mirax.BARCODE",
        "philips.barcode",
        "ventana.BarcodeID"
    };

    private static final String[] DESCRIPTION_KEYS = {
        "openslide.comment",
        "aperio.Description",
        "aperio.Comment",
        "hamamatsu.Description",
        "hamamatsu.Comment",
        "leica.description",
        "mirax.DESCRIPTION",
        "philips.description",
        "ventana.Description"
    };

    private static final String[] SCANNER_MODEL_KEYS = {
        "aperio.Filename",
        "hamamatsu.ScannerSerialNumber",
        "leica.scanner",
        "mirax.SCANNER_MODEL",
        "philips.scanner.model",
        "ventana.ScannerModel"
    };

    private static final String[] SCANNER_SERIAL_KEYS = {
        "aperio.ScannerSerialNumber",
        "hamamatsu.ScannerSerialNumber",
        "leica.serial",
        "mirax.SCANNER_SERIAL",
        "philips.scanner.serial_number",
        "ventana.ScannerSerialNumber"
    };

    private static final String[] STAINING_KEYS = {
        "aperio.Stain",
        "hamamatsu.StainName",
        "leica.stain",
        "mirax.STAINING_TYPE",
        "philips.stain",
        "ventana.StainName"
    };

    /**
     * 深度检查文件是否可用
     * 尝试实际打开文件并验证其可用性
     *
     * @param filePath 文件路径
     * @return 如果文件可用返回true，否则返回false
     */
    public boolean deepCheckFile(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            log.warn("深度检查: 文件路径为空");
            return false;
        }

        File file = new File(filePath);

        if (!file.exists() || !file.isFile() || !file.canRead()) {
            log.warn("深度检查: 文件不存在或不是有效文件: {}", filePath);
            return false;
        }

        try {
            // 尝试检测文件供应商
            String vendor = OpenSlide.detectVendor(file);
        } catch (UnsatisfiedLinkError e) {
            log.warn("深度检查: 检测文件供应商失败(库加载异常): {}, 错误: {}", filePath, e.getMessage());
        } catch (IllegalArgumentException e) {
            log.warn("深度检查: 检测文件供应商失败(参数异常): {}, 错误: {}", filePath, e.getMessage());
        }

        // 尝试使用命令行工具检查
        try {
            CommandResult result = executeCommand("openslide-show-properties", filePath);
            if (result.getExitCode() == 0) {
                return true;
            } else {
                log.warn("深度检查: 命令行工具检查失败: {}, 退出码: {}, 输出: {}",
                        filePath, result.getExitCode(), result.getOutput());
            }
        } catch (Exception e) {
            // 命令行工具检查失败，继续尝试其他方法
        }

        // 尝试直接打开文件进行验证
        try (OpenSlide slide = new OpenSlide(file)) {
            long width = slide.getLevel0Width();
            long height = slide.getLevel0Height();
            int levelCount = slide.getLevelCount();

            if (width <= 0 || height <= 0 || levelCount <= 0) {
                log.warn("深度检查: 文件尺寸不合理: 宽度={}, 高度={}, 层级数={}, 文件: {}",
                        width, height, levelCount, filePath);
                return false;
            }

            // 尝试获取一些基本属性
            Map<String, String> properties = slide.getProperties();
            if (properties == null) {
                properties = new HashMap<>();
            }

            return true;

        } catch (IOException e) {
            log.warn("深度检查: 文件IO异常: {}, 错误: {}", filePath, e.getMessage(), e);
        } catch (UnsatisfiedLinkError e) {
            log.warn("深度检查: OpenSlide库加载异常: {}, 错误: {}", filePath, e.getMessage(), e);
        } catch (IllegalArgumentException e) {
            log.warn("深度检查: 参数异常: {}, 错误: {}", filePath, e.getMessage(), e);
        } catch (Exception e) {
            log.warn("深度检查: 文件不可用: {}, 错误: {}", filePath, e.getMessage(), e);
        }

        return false;
    }

    @Override
    public boolean supportsFormat(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }

        try {
            File file = new File(filePath);
            if (!file.exists() || !file.isFile()) {
                log.debug("文件不存在或不是有效文件: {}", filePath);
                return false;
            }

            // 检查文件大小
            long fileSize = file.length();
            if (fileSize == 0) {
                log.debug("文件大小为0: {}", filePath);
                return false;
            }

            // 首先检查文件扩展名 - 优先支持OpenSlide格式
            String fileName = file.getName().toLowerCase();
            
            // 明确排除.tron文件，这些应该由TronSDK解析器处理
            if (fileName.endsWith(".tron")) {
                log.debug("文件 {} 是.tron格式，应由TronSDK解析器处理", filePath);
                return false;
            }
            
            // 明确排除.sdpc文件，这些应该由SqraySlide解析器处理
            if (fileName.endsWith(".sdpc")) {
                log.debug("文件 {} 是.sdpc格式，应由SqraySlide解析器处理", filePath);
                return false;
            }
            
            boolean isSupportedExtension = false;

            // 支持的文件扩展名列表 - 明确包含ndpi, svs等格式
            String[] supportedExtensions = {
                ".svs", ".ndpi", ".tif", ".tiff", ".mrxs", ".vms",
                ".vmu", ".scn", ".bif", ".svslide", ".isyntax"
            };

            for (String ext : supportedExtensions) {
                if (fileName.endsWith(ext)) {
                    isSupportedExtension = true;
                    log.debug("OpenSlide解析器检测到支持的文件扩展名: {} (文件: {})", ext, filePath);
                    break;
                }
            }

            // 如果是明确支持的扩展名，优先返回true（除非文件有问题）
            if (isSupportedExtension) {
                log.info("OpenSlide解析器确认支持文件: {} (基于扩展名)", filePath);
                // 对于支持的扩展名，直接返回true，避免被其他解析器抢占
                return true;
            }

            if (!isSupportedExtension) {
                // 即使扩展名不在支持列表中，也继续尝试检测
                log.debug("文件扩展名不在OpenSlide支持列表中，尝试深度检测: {}", filePath);
            }

            // 使用OpenSlide检测文件格式
            try {
                String vendor = OpenSlide.detectVendor(file);
                if (vendor != null && !vendor.isEmpty()) {
                    return true;
                } else {
                    log.warn("OpenSlide自动检测不支持该文件，尝试深度检查: {}", filePath);
                    
                    // 尝试深度检查
                    boolean deepCheckResult = deepCheckFile(filePath);
                    if (deepCheckResult) {
                        return true;
                    } else {
                        log.warn("深度检查失败，文件不可用: {}", filePath);
                        return false;
                    }
                }
            } catch (UnsatisfiedLinkError e) {
                log.warn("OpenSlide.detectVendor检测失败(库加载异常)，尝试深度检查: {}, 错误: {}", filePath, e.getMessage());
            } catch (IllegalArgumentException e) {
                log.warn("OpenSlide.detectVendor检测失败(参数异常)，尝试深度检查: {}, 错误: {}", filePath, e.getMessage());
            } catch (Exception e) {
                log.warn("OpenSlide.detectVendor检测失败，尝试深度检查: {}, 错误: {}", filePath, e.getMessage());
            }

            // 如果检测失败，尝试深度检查
            boolean deepCheckResult = tryDeepCheck(filePath);
            if (deepCheckResult) {
                return true;
            } else {
                log.warn("深度检查失败，文件不可用: {}", filePath);
                return false;
            }
        } catch (Exception e) {
            log.error("检查文件格式异常: {}, 错误: {}", filePath, e.getMessage());
            return false;
        }
    }

    /**
     * 尝试深度检查文件
     *
     * @param filePath 文件路径
     * @return 如果文件可用返回true，否则返回false
     */
    private boolean tryDeepCheck(String filePath) {
        boolean deepCheckResult = deepCheckFile(filePath);
        if (deepCheckResult) {
            log.info("深度检查通过，文件可用: {}", filePath);
            return true;
        } else {
            log.warn("深度检查失败，文件不可用: {}", filePath);
            return false;
        }
    }

    @Override
    public Object openSlide(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new SlideException("文件路径不能为空");
        }

        log.info("OpenSlideParserAdapter.openSlide 开始打开文件: {}", filePath);

        try {
            File file = new File(filePath);
            if (!file.exists() || !file.isFile()) {
                log.error("文件不存在或不是有效文件: {}", filePath);
                throw new SlideException("文件不存在或不是有效文件: " + filePath);
            }

            long fileSize = file.length();
            log.info("文件大小: {} bytes", fileSize);

            String fileName = file.getName();
            String fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
            log.info("文件扩展名: {}", fileExtension);

            // 检测文件供应商
            String vendor = null;
            try {
                log.debug("调用 OpenSlide.detectVendor 检测文件供应商...");
                vendor = OpenSlide.detectVendor(file);
                log.info("检测到文件供应商: {}", vendor != null ? vendor : "未知");
            } catch (UnsatisfiedLinkError e) {
                log.error("OpenSlide库加载失败: {}", e.getMessage(), e);
                throw new SlideException("OpenSlide库加载失败，请检查库路径配置: " + e.getMessage(), e);
            } catch (Exception e) {
                log.warn("检测文件供应商失败: {}, 错误: {}", filePath, e.getMessage());
            }

            // 打开文件
            log.debug("调用 OpenSlide 构造函数打开文件...");
            OpenSlide slide;
            try {
                slide = new OpenSlide(file);
                log.info("OpenSlide 对象创建成功");
            } catch (UnsatisfiedLinkError e) {
                log.error("OpenSlide库链接失败: {}", e.getMessage(), e);
                throw new SlideException("OpenSlide库链接失败，请检查库文件是否正确安装: " + e.getMessage(), e);
            } catch (IOException e) {
                log.error("OpenSlide打开文件IO异常: {}", e.getMessage(), e);
                throw new SlideException("打开文件IO异常: " + e.getMessage(), e);
            } catch (Exception e) {
                log.error("OpenSlide打开文件异常: {}", e.getMessage(), e);
                throw new SlideException("打开文件异常: " + e.getMessage(), e);
            }

            // 验证文件是否成功打开
            try {
                long width = slide.getLevel0Width();
                long height = slide.getLevel0Height();
                int levelCount = slide.getLevelCount();
                
                log.info("文件打开成功 - 尺寸: {}x{}, 层级数: {}", width, height, levelCount);
                
                if (width <= 0 || height <= 0) {
                    log.error("文件尺寸无效: {}x{}", width, height);
                    throw new SlideException("文件尺寸无效: " + width + "x" + height);
                }
                
                if (levelCount <= 0) {
                    log.error("层级数无效: {}", levelCount);
                    throw new SlideException("层级数无效: " + levelCount);
                }
                
            } catch (Exception e) {
                log.error("验证文件信息失败: {}", e.getMessage(), e);
                try {
                    slide.close();
                } catch (Exception closeEx) {
                    log.warn("关闭异常文件时出错: {}", closeEx.getMessage());
                }
                throw new SlideException("验证文件信息失败: " + e.getMessage(), e);
            }

            return slide;
        } catch (SlideException e) {
            // 重新抛出 SlideException
            throw e;
        } catch (Exception e) {
            log.error("打开文件失败: {}, 错误: {}", filePath, e.getMessage(), e);
            throw new SlideException("打开文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void closeSlide(Object slide) {
        if (slide instanceof OpenSlide) {
            try {
                ((OpenSlide) slide).close();
            } catch (Exception e) {
                log.error("关闭OpenSlide对象失败: {}", e.getMessage(), e);
            }
        }
    }

    @Override
    public Map<String, Object> getSlideInfo(Object slide) {
        OpenSlide openSlide = validateSlide(slide);
        Map<String, Object> info = new HashMap<>();

        // 转换OpenSlide的属性
        Map<String, String> properties = openSlide.getProperties();
        for (Map.Entry<String, String> entry : properties.entrySet()) {
            info.put(entry.getKey(), entry.getValue());
        }

        // 添加基本尺寸信息
        info.put("width", openSlide.getLevel0Width());
        info.put("height", openSlide.getLevel0Height());
        info.put("levelCount", openSlide.getLevelCount());

        // 检查是否为NDPI格式
        String vendor = properties.get("openslide.vendor");
        if ("hamamatsu".equalsIgnoreCase(vendor)) {
            // 添加NDPI特有的信息
            info.put("format", "ndpi");
            info.put("vendor", "hamamatsu");
        }

        return info;
    }

    @Override
    public Map<String, Object> getLevelInfo(Object slide, int level) {
        OpenSlide openSlide = validateSlide(slide);
        Map<String, Object> levelInfo = new HashMap<>();

        int levelCount = openSlide.getLevelCount();
        if (level < 0 || level >= levelCount) {
            throw new SlideException("无效的层级索引: " + level + "，有效范围: 0-" + (levelCount - 1));
        }

        // 获取层级尺寸
        levelInfo.put("width", openSlide.getLevelWidth(level));
        levelInfo.put("height", openSlide.getLevelHeight(level));

        // 获取层级下采样比例
        double downsample = openSlide.getLevelDownsample(level);
        levelInfo.put("downsample", downsample);

        return levelInfo;
    }

    @Override
    public byte[] getTileJpeg(Object slide, int x, int y, int level) {
        OpenSlide openSlide = validateSlide(slide);

        try {
            // 只在DEBUG级别记录瓦片读取信息
            if (log.isDebugEnabled()) {
                log.debug("读取瓦片: level={}, x={}, y={}, size={}", level, x, y, TILE_SIZE);
            }
            
            // 获取图像边界信息，修复边界检查
            long level0Width = openSlide.getLevel0Width();
            long level0Height = openSlide.getLevel0Height();
            
            // 修复边界检查：确保瓦片坐标在图像范围内
            if (x >= level0Width || y >= level0Height || x < 0 || y < 0) {
                log.debug("瓦片坐标超出图像边界: ({}, {}) 超出范围 (0, 0) - ({}, {})", 
                        x, y, level0Width - 1, level0Height - 1);
                // 创建空白瓦片
                BufferedImage emptyTile = new BufferedImage(TILE_SIZE, TILE_SIZE, BufferedImage.TYPE_INT_RGB);
                Graphics2D g = emptyTile.createGraphics();
                g.setColor(Color.WHITE);
                g.fillRect(0, 0, TILE_SIZE, TILE_SIZE);
                g.dispose();
                return convertToJpeg(emptyTile, "空白瓦片");
            }
            
            // 添加图像尺寸检查，防止过大图像导致IllegalArgumentException
            int maxImageDimension = 30000; // 设置最大尺寸限制为30000像素
            if (x > maxImageDimension || y > maxImageDimension) {
                log.error("请求的坐标超出限制: x={}, y={}, 最大允许值={}", x, y, maxImageDimension);
                throw new SlideException("请求的坐标超出限制，可能导致内存溢出");
            }
            
            // 检查是否需要处理边界瓦片（部分超出图像边界的瓦片）
            double downsample = openSlide.getLevelDownsample(level);
            long level0TileX = x;
            long level0TileY = y;
            long level0TileWidth = (long) Math.ceil(TILE_SIZE * downsample);
            long level0TileHeight = (long) Math.ceil(TILE_SIZE * downsample);
            
            boolean needsPadding = false;
            int actualTileWidth = TILE_SIZE;
            int actualTileHeight = TILE_SIZE;
            
            // 检查是否超出右边界
            if (level0TileX + level0TileWidth > level0Width) {
                long availableWidth = level0Width - level0TileX;
                actualTileWidth = (int) Math.ceil(availableWidth / downsample);
                needsPadding = true;
            }
            
            // 检查是否超出下边界
            if (level0TileY + level0TileHeight > level0Height) {
                long availableHeight = level0Height - level0TileY;
                actualTileHeight = (int) Math.ceil(availableHeight / downsample);
                needsPadding = true;
            }

            // 读取区域（使用调整后的尺寸）
            BufferedImage image = openSlide.readRegion(x, y, level, actualTileWidth, actualTileHeight);

            if (image == null) {
                log.error("OpenSlide读取瓦片返回null");
                // 创建空白瓦片
                BufferedImage emptyTile = new BufferedImage(TILE_SIZE, TILE_SIZE, BufferedImage.TYPE_INT_RGB);
                Graphics2D g = emptyTile.createGraphics();
                g.setColor(Color.WHITE);
                g.fillRect(0, 0, TILE_SIZE, TILE_SIZE);
                g.dispose();
                return convertToJpeg(emptyTile, "空白瓦片");
            }
            
            // 修复NDPI边界瓦片：如果需要填充到标准瓦片尺寸，进行白色背景填充
            if (needsPadding && (image.getWidth() < TILE_SIZE || image.getHeight() < TILE_SIZE)) {
                if (log.isDebugEnabled()) {
                    log.debug("NDPI边界瓦片修复：需要填充瓦片从 {}x{} 到 {}x{}", 
                            image.getWidth(), image.getHeight(), TILE_SIZE, TILE_SIZE);
                }
                
                // 创建标准尺寸的瓦片，使用白色背景
                BufferedImage paddedTile = new BufferedImage(TILE_SIZE, TILE_SIZE, BufferedImage.TYPE_INT_RGB);
                Graphics2D g = paddedTile.createGraphics();
                
                // 设置白色背景，解决黑色边框问题
                g.setColor(Color.WHITE);
                g.fillRect(0, 0, TILE_SIZE, TILE_SIZE);
                
                // 将实际读取的图像绘制到左上角
                g.drawImage(image, 0, 0, null);
                g.dispose();
                
                image = paddedTile;
            }

            // 转换为RGB格式
            BufferedImage rgbImage = convertToRgbImage(image);

            // 如果图像过大，进行压缩
            if (rgbImage.getWidth() > 8000 || rgbImage.getHeight() > 8000) {
                // 计算新尺寸，保持宽高比
                double scale = Math.min(8000.0 / rgbImage.getWidth(), 8000.0 / rgbImage.getHeight());
                int newWidth = (int) (rgbImage.getWidth() * scale);
                int newHeight = (int) (rgbImage.getHeight() * scale);

                BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
                Graphics2D g2d = scaledImage.createGraphics();
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.drawImage(rgbImage, 0, 0, newWidth, newHeight, null);
                g2d.dispose();

                rgbImage = scaledImage;
            }

            // 尝试使用ImageIO写入JPEG
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                boolean success = ImageIO.write(rgbImage, "JPEG", baos);
                if (success) {
                    byte[] jpegData = baos.toByteArray();
                    if (jpegData != null && jpegData.length > 0) {
                        return jpegData;
                    } else {
                        log.warn("使用ImageIO转换JPEG成功但数据为空");
                    }
                } else {
                    log.warn("使用ImageIO写入JPEG格式失败，尝试备用方法");
                }
            } catch (IOException e) {
                log.warn("使用ImageIO写入JPEG异常: {}", e.getMessage());
            }

            // 转换为JPEG
            return convertToJpeg(rgbImage, "瓦片");
        } catch (IOException e) {
            log.error("获取瓦片失败(IO异常): {}", e.getMessage(), e);
            throw new SlideException("获取瓦片失败(IO异常): " + e.getMessage(), e);
        } catch (IllegalArgumentException e) {
            log.error("获取瓦片失败(参数异常): {}", e.getMessage(), e);
            throw new SlideException("获取瓦片失败(参数异常): " + e.getMessage(), e);
        } catch (UnsatisfiedLinkError e) {
            log.error("获取瓦片失败(库加载异常): {}", e.getMessage(), e);
            throw new SlideException("获取瓦片失败(库加载异常): " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("获取瓦片失败: {}", e.getMessage(), e);
            throw new SlideException("获取瓦片失败: " + e.getMessage(), e);
        }
    }

    @Override
    public byte[] getThumbnailJpeg(Object slide) {
        OpenSlide openSlide = validateSlide(slide);

        try {
            // 获取最高级别（最低分辨率）作为缩略图
            int maxLevel = openSlide.getLevelCount() - 1;
            long width = openSlide.getLevelWidth(maxLevel);
            long height = openSlide.getLevelHeight(maxLevel);

            // 添加图像尺寸检查，防止过大图像导致IllegalArgumentException
            int maxImageDimension = 30000; // 设置最大尺寸限制为30000像素
            if (width > maxImageDimension || height > maxImageDimension) {
                log.error("图像尺寸过大: {}x{}, 最大允许值={}", width, height, maxImageDimension);
                
                // 创建一个灰色图像作为替代
                BufferedImage errorImage = new BufferedImage(MAX_THUMBNAIL_SIZE, MAX_THUMBNAIL_SIZE, BufferedImage.TYPE_INT_RGB);
                java.awt.Graphics2D g = errorImage.createGraphics();
                g.setColor(new java.awt.Color(220, 220, 220)); // 灰色背景
                g.fillRect(0, 0, MAX_THUMBNAIL_SIZE, MAX_THUMBNAIL_SIZE);
                g.setColor(java.awt.Color.RED);
                g.drawRect(10, 10, MAX_THUMBNAIL_SIZE - 20, MAX_THUMBNAIL_SIZE - 20);
                g.drawLine(10, 10, MAX_THUMBNAIL_SIZE - 10, MAX_THUMBNAIL_SIZE - 10);
                g.drawLine(10, MAX_THUMBNAIL_SIZE - 10, MAX_THUMBNAIL_SIZE - 10, 10);
                g.dispose();
                
                // 转换为JPEG
                return convertToJpeg(errorImage, "错误缩略图");
            }

            // 限制最大尺寸
            int thumbWidth = (int) width;
            int thumbHeight = (int) height;

            if (thumbWidth > MAX_THUMBNAIL_SIZE || thumbHeight > MAX_THUMBNAIL_SIZE) {
                double scale = Math.min((double) MAX_THUMBNAIL_SIZE / thumbWidth, (double) MAX_THUMBNAIL_SIZE / thumbHeight);
                thumbWidth = (int) (thumbWidth * scale);
                thumbHeight = (int) (thumbHeight * scale);
            }

            // 直接读取整个最高层级作为缩略图
            BufferedImage fullImage = openSlide.readRegion(0, 0, maxLevel, (int)width, (int)height);

            if (fullImage == null) {
                log.error("OpenSlide读取区域返回null");
                throw new SlideException("无法读取缩略图区域");
            }

            // 如果需要缩放
            if (thumbWidth != width || thumbHeight != height) {
                // 创建缩略图，使用RGB格式
                BufferedImage thumbnail = new BufferedImage(thumbWidth, thumbHeight, BufferedImage.TYPE_INT_RGB);
                thumbnail.createGraphics().drawImage(fullImage, 0, 0, thumbWidth, thumbHeight, null);
                fullImage = thumbnail;
            } else {
                // 如果不需要缩放，也转换为RGB格式
                fullImage = convertToRgbImage(fullImage);
            }

            // 转换为JPEG
            return convertToJpeg(fullImage, "缩略图");
        } catch (IOException e) {
            log.error("获取缩略图失败(IO异常): {}", e.getMessage(), e);
            throw new SlideException("获取缩略图失败(IO异常): " + e.getMessage(), e);
        } catch (IllegalArgumentException e) {
            log.error("获取缩略图失败(参数异常): {}", e.getMessage(), e);
            throw new SlideException("获取缩略图失败(参数异常): " + e.getMessage(), e);
        } catch (UnsatisfiedLinkError e) {
            log.error("获取缩略图失败(库加载异常): {}", e.getMessage(), e);
            throw new SlideException("获取缩略图失败(库加载异常): " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("获取缩略图失败: {}", e.getMessage(), e);
            throw new SlideException("获取缩略图失败: " + e.getMessage(), e);
        }
    }

    @Override
    public byte[] getLabelJpeg(Object slide) {
        OpenSlide openSlide = validateSlide(slide);
        try {
            // 获取所有关联图像
            Map<String, org.openslide.AssociatedImage> associatedImages = null;
            try {
                associatedImages = openSlide.getAssociatedImages();
                
                if (associatedImages == null || associatedImages.isEmpty()) {
                    log.warn("未找到任何关联图像，可能因为OpenSlide库版本问题或文件格式不支持");
                }
            } catch (Exception e) {
                log.error("获取关联图像列表失败: {}", e.getMessage(), e);
                associatedImages = new HashMap<>();
            }
            
            // 1. 优先获取label图
            if (associatedImages != null && associatedImages.containsKey("label")) {
                org.openslide.AssociatedImage labelImg = associatedImages.get("label");
                if (labelImg != null) {
                    try {
                        BufferedImage bufferedImage = labelImg.toBufferedImage();
                        if (bufferedImage != null) {
                            byte[] jpegData = convertToJpeg(bufferedImage, "label图");
                            return jpegData;
                        } else {
                            log.warn("labelImg.toBufferedImage() 结果为null，无法生成label图");
                        }
                    } catch (Exception e) {
                        log.error("处理label图失败: {}", e.getMessage(), e);
                    }
                }
            }
            
            // 2. 如果没有label图，尝试获取macro图
            if (associatedImages != null && associatedImages.containsKey("macro")) {
                org.openslide.AssociatedImage macroImg = associatedImages.get("macro");
                if (macroImg != null) {
                    try {
                        BufferedImage bufferedImage = macroImg.toBufferedImage();
                        if (bufferedImage != null) {
                            
                            // 对于macro图，尝试裁剪左侧1/3部分（通常包含标签信息）
                            int originalWidth = bufferedImage.getWidth();
                            int cropWidth = originalWidth / 3;
                            if (cropWidth > 50) { // 确保裁剪区域有意义
                                BufferedImage croppedImage = bufferedImage.getSubimage(0, 0, cropWidth, bufferedImage.getHeight());
                                byte[] jpegData = convertToJpeg(croppedImage, "裁剪macro图");
                                return jpegData;
                            } else {
                                // 如果裁剪区域太小，使用完整macro图
                                byte[] jpegData = convertToJpeg(bufferedImage, "完整macro图");
                                return jpegData;
                            }
                        } else {
                            log.warn("macroImg.toBufferedImage() 结果为null");
                        }
                    } catch (Exception e) {
                        log.error("处理macro图失败: {}", e.getMessage(), e);
                    }
                }
            }
            
            // 3. 如果都没有，返回空白图像
            log.warn("未能获取到标签图或宏观图，返回空白图像");
            return createEmptyJpeg();
            
        } catch (Exception e) {
            log.error("获取标签图失败: {}", e.getMessage(), e);
            return createEmptyJpeg();
        }
    }

    @Override
    public String getParserName() {
        return PARSER_NAME;
    }

    @Override
    public byte[] getRegionJpeg(Object slide, int x, int y, int width, int height, int level) {
        log.info("OpenSlideParserAdapter.getRegionJpeg 开始获取区域: 坐标=({}, {}), 尺寸={}x{}, 层级={}", 
                x, y, width, height, level);
        
        OpenSlide openSlide = validateSlide(slide);
        
        // 添加SVS格式特殊调试信息
        try {
            Map<String, String> properties = openSlide.getProperties();
            String vendor = properties.get("openslide.vendor");
            String comment = properties.get("openslide.comment");
            log.info("SVS调试信息: vendor={}, comment={}, 总层级数={}", 
                    vendor, comment, openSlide.getLevelCount());
            
            // 输出每个层级的详细信息
            for (int i = 0; i < openSlide.getLevelCount(); i++) {
                log.debug("SVS层级{}: 尺寸={}x{}, 下采样比例={}", 
                        i, openSlide.getLevelWidth(i), openSlide.getLevelHeight(i), 
                        openSlide.getLevelDownsample(i));
            }
        } catch (Exception e) {
            log.warn("获取SVS调试信息失败: {}", e.getMessage());
        }

        try {
            // 添加图像尺寸检查，防止过大图像导致IllegalArgumentException
            int maxImageDimension = 30000; // 设置最大尺寸限制为30000像素
            
            // 检查Java数组大小限制 (Integer.MAX_VALUE - 8)
            long pixelCount = (long)width * (long)height;
            int maxArraySize = Integer.MAX_VALUE - 8; // Java数组最大大小限制
            
            if (pixelCount > maxArraySize || width > maxImageDimension || height > maxImageDimension) {
                log.warn("请求区域尺寸过大: {}x{} = {} 像素, 最大允许值={}, 数组大小限制={}", 
                        width, height, pixelCount, maxImageDimension, maxArraySize);
                
                // 计算缩放比例，确保最大边不超过maxImageDimension，且总像素数不超过maxArraySize
                double scaleFactor1 = Math.min((double)maxImageDimension / width, (double)maxImageDimension / height);
                double scaleFactor2 = Math.sqrt((double)maxArraySize / pixelCount);
                double scale = Math.min(scaleFactor1, scaleFactor2);
                
                int scaledWidth = (int)(width * scale);
                int scaledHeight = (int)(height * scale);
                
                log.info("自动缩放区域尺寸: {}x{} -> {}x{} (缩放比例: {})", 
                        width, height, scaledWidth, scaledHeight, scale);
                
                // 使用缩放后的尺寸
                width = scaledWidth;
                height = scaledHeight;
            }
            
            // 获取图像基本信息
            long level0Width = openSlide.getLevel0Width();
            long level0Height = openSlide.getLevel0Height();
            double downsample = openSlide.getLevelDownsample(level);
            
            // 修复：传入的x,y已经是level 0的坐标，不需要再乘以downsample
            int x0 = x;  // 直接使用传入的x坐标，它已经是level 0坐标
            int y0 = y;  // 直接使用传入的y坐标，它已经是level 0坐标
            
            log.debug("坐标映射: 接收到level 0坐标({},{}), 目标层级={}, 目标层级尺寸={}x{}, 下采样比例={}", 
                    x0, y0, level, width, height, downsample);

            // 修复NDPI边界问题：严格的边界检查和处理
            // 确保坐标不超出边界
            if (x0 >= level0Width || y0 >= level0Height || x0 < 0 || y0 < 0) {
                log.warn("请求坐标超出图像边界: ({}, {}) 超出范围 (0, 0) - ({}, {})", 
                        x0, y0, level0Width - 1, level0Height - 1);
                // 创建空白图像
                BufferedImage emptyImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
                Graphics2D g = emptyImage.createGraphics();
                g.setColor(Color.WHITE);  // 使用白色背景而不是黑色
                g.fillRect(0, 0, width, height);
                g.dispose();
                return convertToJpeg(emptyImage, "空白区域图像");
            }
            
            // 修复：计算在level 0坐标系中实际需要读取的尺寸
            int level0Width_toRead = (int) Math.ceil(width * downsample);
            int level0Height_toRead = (int) Math.ceil(height * downsample);
            
            // 保存原始请求的尺寸
            int originalRequestWidth = width;
            int originalRequestHeight = height;
            boolean needsPadding = false;
            
            // 修复边界溢出处理：调整区域尺寸以确保不超出边界
            if (x0 + level0Width_toRead > level0Width) {
                level0Width_toRead = (int)(level0Width - x0);
                if (level0Width_toRead <= 0) {
                    log.warn("调整后宽度为0或负数: {}", level0Width_toRead);
                    BufferedImage emptyImage = new BufferedImage(originalRequestWidth, originalRequestHeight, BufferedImage.TYPE_INT_RGB);
                    Graphics2D g = emptyImage.createGraphics();
                    g.setColor(Color.WHITE);
                    g.fillRect(0, 0, originalRequestWidth, originalRequestHeight);
                    g.dispose();
                    return convertToJpeg(emptyImage, "空白区域图像");
                }
                // 重新计算目标层级尺寸
                width = (int) Math.ceil(level0Width_toRead / downsample);
                needsPadding = true;
                log.debug("调整宽度: level0宽度={}, 目标层级宽度={} -> {}", level0Width_toRead, originalRequestWidth, width);
            }
            
            if (y0 + level0Height_toRead > level0Height) {
                level0Height_toRead = (int)(level0Height - y0);
                if (level0Height_toRead <= 0) {
                    log.warn("调整后高度为0或负数: {}", level0Height_toRead);
                    BufferedImage emptyImage = new BufferedImage(originalRequestWidth, originalRequestHeight, BufferedImage.TYPE_INT_RGB);
                    Graphics2D g = emptyImage.createGraphics();
                    g.setColor(Color.WHITE);
                    g.fillRect(0, 0, originalRequestWidth, originalRequestHeight);
                    g.dispose();
                    return convertToJpeg(emptyImage, "空白区域图像");
                }
                // 重新计算目标层级尺寸
                height = (int) Math.ceil(level0Height_toRead / downsample);
                needsPadding = true;
                log.debug("调整高度: level0高度={}, 目标层级高度={} -> {}", level0Height_toRead, originalRequestHeight, height);
            }
            
            // 读取区域
            log.debug("开始读取区域: x0={}, y0={}, level={}, width={}, height={}, level0读取尺寸={}x{}", 
                    x0, y0, level, width, height, level0Width_toRead, level0Height_toRead);
                    
            BufferedImage image;
            try {
                // 使用OpenSlide的readRegion方法
                // 注意：这里使用调整后的width和height，它们是目标层级的尺寸
                image = openSlide.readRegion(x0, y0, level, width, height);
                log.debug("OpenSlide.readRegion 调用完成，返回图像尺寸: {}x{}", 
                        image != null ? image.getWidth() : 0, image != null ? image.getHeight() : 0);
            } catch (Exception e) {
                log.error("OpenSlide.readRegion 调用失败: {}", e.getMessage(), e);
                
                // Docker 环境特定的错误处理
                String errorMessage = e.getMessage();
                if (e.getClass().getName().contains("UnsatisfiedLinkError")) {
                    log.error("JNI 库链接错误，可能是 Docker 环境缺少依赖库");
                } else if (errorMessage != null && errorMessage.contains("Failed to read region")) {
                    log.error("读取区域失败，可能是 NDPI 文件格式特定问题");
                    log.error("尝试的坐标: x0={}, y0={}, level={}, width={}, height={}", x0, y0, level, width, height);
                    log.error("文件信息: 总层级数={}, 当前层级尺寸={}x{}", 
                            openSlide.getLevelCount(),
                            openSlide.getLevelWidth(level),
                            openSlide.getLevelHeight(level));
                }
                throw new SlideException("OpenSlide 读取区域失败: " + e.getMessage(), e);
            }
            
            if (image == null) {
                log.error("OpenSlide.readRegion 返回 null，可能原因:");
                log.error("1. 请求的坐标超出图像边界");
                log.error("2. SVS/NDPI 文件内部结构问题");
                log.error("3. OpenSlide 库版本不支持此文件");
                log.error("请求参数: x0={}, y0={}, level={}, width={}, height={}", x0, y0, level, width, height);
                
                // 对于SVS格式，尝试使用备用方案
                log.warn("尝试SVS格式备用读取方案...");
                try {
                    // 检查坐标是否在有效范围内
                    long maxX = openSlide.getLevelWidth(level);
                    long maxY = openSlide.getLevelHeight(level);
                    
                    if (x0 >= 0 && y0 >= 0 && x0 < openSlide.getLevel0Width() && y0 < openSlide.getLevel0Height()) {
                        // 尝试读取更小的区域
                        int fallbackWidth = Math.min(width, (int)(maxX - x0/Math.pow(2, level)));
                        int fallbackHeight = Math.min(height, (int)(maxY - y0/Math.pow(2, level)));
                        
                        if (fallbackWidth > 0 && fallbackHeight > 0) {
                            log.info("SVS备用方案: 尝试读取更小区域 {}x{}", fallbackWidth, fallbackHeight);
                            image = openSlide.readRegion(x0, y0, level, fallbackWidth, fallbackHeight);
                            
                            if (image != null) {
                                log.info("SVS备用方案成功，图像尺寸: {}x{}", image.getWidth(), image.getHeight());
                                
                                // 如果尺寸不匹配，需要填充到原始请求尺寸
                                if (image.getWidth() < originalRequestWidth || image.getHeight() < originalRequestHeight) {
                                    BufferedImage paddedImage = new BufferedImage(originalRequestWidth, originalRequestHeight, BufferedImage.TYPE_INT_RGB);
                                    Graphics2D g = paddedImage.createGraphics();
                                    g.setColor(Color.WHITE);
                                    g.fillRect(0, 0, originalRequestWidth, originalRequestHeight);
                                    g.drawImage(image, 0, 0, null);
                                    g.dispose();
                                    image = paddedImage;
                                }
                            }
                        }
                    }
                } catch (Exception fallbackEx) {
                    log.warn("SVS备用方案也失败: {}", fallbackEx.getMessage());
                }
                
                // 如果所有方案都失败，创建一个错误提示图像
                if (image == null) {
                    BufferedImage errorImage = new BufferedImage(originalRequestWidth, originalRequestHeight, BufferedImage.TYPE_INT_RGB);
                    Graphics2D g = errorImage.createGraphics();
                    g.setColor(Color.WHITE);  // 使用白色背景而不是红色
                    g.fillRect(0, 0, originalRequestWidth, originalRequestHeight);
                    g.setColor(Color.LIGHT_GRAY);
                    g.drawString("SVS读取失败", 10, originalRequestHeight/2);
                    g.dispose();
                    image = errorImage;
                    log.warn("SVS格式创建空白替代图像");
                }
            } else {
                log.debug("成功读取图像: {}x{}, 类型={}", image.getWidth(), image.getHeight(), image.getType());
                
                // 修复NDPI黑色边框问题：如果需要填充到原始请求尺寸，进行白色背景填充
                if (needsPadding && (image.getWidth() < originalRequestWidth || image.getHeight() < originalRequestHeight)) {
                    log.info("NDPI边界修复：需要填充图像从 {}x{} 到 {}x{}", 
                            image.getWidth(), image.getHeight(), originalRequestWidth, originalRequestHeight);
                    
                    // 创建目标尺寸的图像，使用白色背景
                    BufferedImage paddedImage = new BufferedImage(originalRequestWidth, originalRequestHeight, BufferedImage.TYPE_INT_RGB);
                    Graphics2D g = paddedImage.createGraphics();
                    
                    // 设置白色背景，解决黑色边框问题
                    g.setColor(Color.WHITE);
                    g.fillRect(0, 0, originalRequestWidth, originalRequestHeight);
                    
                    // 将实际读取的图像绘制到左上角
                    g.drawImage(image, 0, 0, null);
                    g.dispose();
                    
                    image = paddedImage;
                    log.debug("NDPI边界修复完成：最终图像尺寸 {}x{}", image.getWidth(), image.getHeight());
                }
            }

            // 转换为RGB格式
            BufferedImage rgbImage = convertToRgbImage(image);

            // 转换为JPEG
            return convertToJpeg(rgbImage, "区域图像");
        } catch (IOException e) {
            log.error("获取区域图像失败(IO异常): {}", e.getMessage(), e);
            throw new SlideException("获取区域图像失败(IO异常): " + e.getMessage(), e);
        } catch (IllegalArgumentException e) {
            log.error("获取区域图像失败(参数异常): {}", e.getMessage(), e);
            throw new SlideException("获取区域图像失败(参数异常): " + e.getMessage(), e);
        } catch (UnsatisfiedLinkError e) {
            log.error("获取区域图像失败(库加载异常): {}", e.getMessage(), e);
            throw new SlideException("获取区域图像失败(库加载异常): " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("获取区域图像失败: {}", e.getMessage(), e);
            throw new SlideException("获取区域图像失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getBarcode(Object slide) {
        OpenSlide openSlide = validateSlide(slide);
        return findPropertyValue(openSlide.getProperties(), BARCODE_KEYS, "条形码");
    }

    @Override
    public String getDescription(Object slide) {
        OpenSlide openSlide = validateSlide(slide);
        return findPropertyValue(openSlide.getProperties(), DESCRIPTION_KEYS, "描述");
    }

    @Override
    public Map<String, Object> getScannerInfo(Object slide) {
        OpenSlide openSlide = validateSlide(slide);
        Map<String, String> properties = openSlide.getProperties();
        Map<String, Object> scannerInfo = new HashMap<>();

        String vendor = properties.get("openslide.vendor");
        if (vendor != null && !vendor.isEmpty()) {
            scannerInfo.put("vendor", vendor);
        }

        // 获取扫描仪信息
        String scannerModel = findPropertyValue(properties, SCANNER_MODEL_KEYS, "扫描仪型号");
        String scannerSerial = findPropertyValue(properties, SCANNER_SERIAL_KEYS, "扫描仪序列号");

        if (scannerModel != null) {
            scannerInfo.put("model", scannerModel);
        }
        if (scannerSerial != null) {
            scannerInfo.put("serialNumber", scannerSerial);
        }

        return scannerInfo;
    }

    @Override
    public String getStaining(Object slide) {
        OpenSlide openSlide = validateSlide(slide);
        return findPropertyValue(openSlide.getProperties(), STAINING_KEYS, "染色");
    }

    /**
     * 验证切片对象是否为OpenSlide类型
     *
     * @param slide 切片对象
     * @return OpenSlide对象
     * @throws SlideException 如果对象类型不正确
     */
    private OpenSlide validateSlide(Object slide) {
        if (!(slide instanceof OpenSlide)) {
            throw new SlideException("无效的切片对象类型: " + (slide != null ? slide.getClass().getName() : "null"));
        }
        return (OpenSlide) slide;
    }

    /**
     * 将图像转换为RGB格式
     * 修复：使用白色背景避免黑色边框问题
     *
     * @param image 原始图像
     * @return RGB格式图像
     */
    private BufferedImage convertToRgbImage(@NonNull BufferedImage image) {
        // 如果已经是RGB格式，直接返回
        if (image.getType() == BufferedImage.TYPE_INT_RGB) {
            return image;
        }
        
        // 创建RGB格式图像，使用白色背景
        BufferedImage rgbImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g = rgbImage.createGraphics();
        
        // 设置白色背景，避免透明区域显示为黑色
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, image.getWidth(), image.getHeight());
        
        // 绘制原始图像
        g.drawImage(image, 0, 0, null);
        g.dispose();
        
        return rgbImage;
    }

    /**
     * 将图像转换为JPEG格式的字节数组
     *
     * @param image 图像
     * @param imageType 图像类型(用于日志)
     * @return JPEG字节数组
     */
    private byte[] convertToJpeg(@NonNull BufferedImage image, String imageType) throws IOException {
        // 添加图像有效性检查
        if (image == null) {
            log.error("convertToJpeg: 输入图像为null");
            return createEmptyJpeg();
        }
        
        // 记录原始图像信息，便于排查问题
        log.debug("原始图像信息: 类型={}, 尺寸={}x{}, 颜色模型={}", 
                image.getType(), image.getWidth(), image.getHeight(), 
                image.getColorModel() != null ? image.getColorModel().getClass().getSimpleName() : "null");
        
        // 添加图像尺寸检查，防止过大图像导致内存溢出
        int maxImageDimension = 30000; // 设置最大尺寸限制为30000像素
        if (image.getWidth() > maxImageDimension || image.getHeight() > maxImageDimension) {
            log.error("图像尺寸过大: {}x{}, 最大允许值={}, 用途={}", 
                    image.getWidth(), image.getHeight(), maxImageDimension, imageType);
            
            // 创建一个压缩后的图像
            try {
                int newWidth = Math.min(image.getWidth(), maxImageDimension);
                int newHeight = Math.min(image.getHeight(), maxImageDimension);
                log.info("尝试将大图像压缩到: {}x{}", newWidth, newHeight);
                
                BufferedImage resized = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
                Graphics2D g = resized.createGraphics();
                g.drawImage(image, 0, 0, newWidth, newHeight, null);
                g.dispose();
                image = resized;
            } catch (Exception e) {
                log.error("压缩大图像失败: {}", e.getMessage(), e);
                return createEmptyJpeg();
            }
        }
        
        try {
            // 强制转换为RGB图像（没有Alpha通道），这对JPEG格式至关重要
            BufferedImage rgbImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
            Graphics2D g = rgbImage.createGraphics();
            g.setColor(Color.WHITE); // 设置白色背景
            g.fillRect(0, 0, image.getWidth(), image.getHeight());
            g.drawImage(image, 0, 0, null);
            g.dispose();
            
            // 使用JPEG编码器和特定的压缩参数
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            
            // 方法1：使用ImageIO直接写入（标准方法）
            try {
                boolean success = ImageIO.write(rgbImage, "jpg", output);
                if (success) {
                    byte[] jpegData = output.toByteArray();
                    if (jpegData.length > 0) {
                        return jpegData;
                    } else {
                        log.warn("使用ImageIO转换JPEG成功但数据为空");
                    }
                } else {
                    log.warn("使用ImageIO写入JPEG格式失败，尝试备用方法");
                }
            } catch (Exception e) {
                log.warn("使用ImageIO写入JPEG异常: {}", e.getMessage());
            }
            
            // 方法2：使用JPEG特定编码器（备用方法）
            try {
                output.reset();
                javax.imageio.ImageWriter jpgWriter = ImageIO.getImageWritersByFormatName("jpg").next();
                javax.imageio.ImageWriteParam jpgWriteParam = jpgWriter.getDefaultWriteParam();
                jpgWriteParam.setCompressionMode(javax.imageio.ImageWriteParam.MODE_EXPLICIT);
                jpgWriteParam.setCompressionQuality(0.95f);
                
                javax.imageio.stream.MemoryCacheImageOutputStream outputStream = 
                    new javax.imageio.stream.MemoryCacheImageOutputStream(output);
                jpgWriter.setOutput(outputStream);
                jpgWriter.write(null, new javax.imageio.IIOImage(rgbImage, null, null), jpgWriteParam);
                jpgWriter.dispose();
                
                byte[] jpegData = output.toByteArray();
                if (jpegData.length > 0) {
                    return jpegData;
                } else {
                    log.warn("使用JPEG编码器转换成功但数据为空");
                }
            } catch (Exception e) {
                log.warn("使用JPEG编码器写入异常: {}", e.getMessage());
            }
            
            // 如果两种方法都失败，尝试创建一个简单的替代图像
            log.error("所有JPEG转换方法都失败，创建替代图像");
            BufferedImage fallbackImage = new BufferedImage(
                    Math.min(image.getWidth(), 100), Math.min(image.getHeight(), 100), BufferedImage.TYPE_INT_RGB);
            Graphics2D fallbackG = fallbackImage.createGraphics();
            fallbackG.setColor(Color.WHITE);
            fallbackG.fillRect(0, 0, fallbackImage.getWidth(), fallbackImage.getHeight());
            fallbackG.setColor(Color.BLACK);
            fallbackG.drawRect(0, 0, fallbackImage.getWidth()-1, fallbackImage.getHeight()-1);
            fallbackG.drawString("图像转换失败", 5, fallbackImage.getHeight()/2);
            fallbackG.dispose();
            
            output.reset();
            ImageIO.write(fallbackImage, "jpg", output);
            byte[] fallbackData = output.toByteArray();
            log.debug("创建替代JPEG图像: 大小={}字节", fallbackData.length);
            return fallbackData;
            
        } catch (Exception e) {
            log.error("转换为JPEG格式异常: {}", e.getMessage(), e);
            return createEmptyJpeg();
        }
    }
    
    /**
     * 确保图像是RGB格式
     */
    private BufferedImage ensureRgbFormat(BufferedImage image) {
        if (image.getType() == BufferedImage.TYPE_INT_RGB) {
            return image;
        }
        
        // 转换为RGB格式
        BufferedImage rgbImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = rgbImage.createGraphics();
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();
        
        return rgbImage;
    }

    /**
     * 命令执行结果类
     */
    private static class CommandResult {
        private final int exitCode;
        private final String output;
        private final String error;

        public CommandResult(int exitCode, String output, String error) {
            this.exitCode = exitCode;
            this.output = output;
            this.error = error;
        }

        public boolean isSuccess() {
            return exitCode == 0;
        }

        public int getExitCode() {
            return exitCode;
        }

        public String getOutput() {
            return output;
        }

        // 保留此方法以便将来可能需要使用错误信息
        @SuppressWarnings("unused")
        public String getError() {
            return error;
        }
    }

    /**
     * 执行命令行工具
     *
     * @param command 命令
     * @param args 参数
     * @return 命令执行结果
     */
    private CommandResult executeCommand(String command, String... args) {
        try {
            // 构建命令行
            String[] cmdArray = new String[args.length + 1];
            cmdArray[0] = command;
            System.arraycopy(args, 0, cmdArray, 1, args.length);

            // 使用ProcessBuilder执行命令
            ProcessBuilder pb = new ProcessBuilder(cmdArray);
            pb.redirectErrorStream(true); // 将错误流合并到标准输出流

            Process process = pb.start();

            // 读取标准输出
            StringBuilder outputBuilder = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    outputBuilder.append(line).append("\n");
                }
            }

            // 等待进程结束
            int exitCode = process.waitFor();

            return new CommandResult(exitCode, outputBuilder.toString(), "");
        } catch (IOException e) {
            log.warn("执行命令失败(IO异常): {}, 错误: {}", command, e.getMessage());
            return new CommandResult(-1, "", "IO异常: " + e.getMessage());
        } catch (InterruptedException e) {
            log.warn("执行命令被中断: {}, 错误: {}", command, e.getMessage());
            Thread.currentThread().interrupt(); // 重新设置中断状态
            return new CommandResult(-1, "", "命令执行被中断: " + e.getMessage());
        } catch (Exception e) {
            log.warn("执行命令失败: {}, 错误: {}", command, e.getMessage());
            return new CommandResult(-1, "", e.getMessage());
        }
    }

    /**
     * 查找属性值
     *
     * @param properties 属性Map
     * @param keys 可能的键数组
     * @param infoType 信息类型(用于日志)
     * @return 找到的属性值，未找到则返回null
     */
    private String findPropertyValue(Map<String, String> properties, String[] keys, String infoType) {
        for (String key : keys) {
            String value = properties.get(key);
            if (value != null && !value.isEmpty()) {
                return value;
            }
        }

        return null;
    }

    // 新增方法：生成1x1空白JPEG图片
    private byte[] createEmptyJpeg() {
        try {
            // 创建一个1x1的白色图像
            BufferedImage img = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB);
            // 填充为白色
            Graphics2D g2d = img.createGraphics();
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, 1, 1);
            g2d.dispose();
            
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(img, "jpg", baos);
            return baos.toByteArray();
        } catch (IOException e) {
            log.error("生成空白JPEG失败", e);
            return new byte[0];
        }
    }

    @Override
    public boolean supportsColorCorrection() {
        // OpenSlide不支持原生颜色校正，但可以通过软件实现
        return false;
    }
    
    @Override
    public byte[] getRegionJpegWithColor(Object slide, int x, int y, int width, int height, int level, ColorCorrectionParams colorParams) {
        // OpenSlide不支持原生颜色校正，返回原始图像
        log.debug("OpenSlide不支持原生颜色校正，返回原始区域图像");
        return getRegionJpeg(slide, x, y, width, height, level);
    }
    
    @Override
    public byte[] getTileJpegWithColor(Object slide, int x, int y, int level, ColorCorrectionParams colorParams) {
        // OpenSlide不支持原生颜色校正，返回原始瓦片
        log.debug("OpenSlide不支持原生颜色校正，返回原始瓦片");
        return getTileJpeg(slide, x, y, level);
    }

    @Override
    public byte[] getSlideJpeg(Object slide) {
        OpenSlide openSlide = validateSlide(slide);
        try {
            Map<String, org.openslide.AssociatedImage> associatedImages = openSlide.getAssociatedImages();
            if (associatedImages != null && associatedImages.containsKey("macro")) {
                org.openslide.AssociatedImage macroImg = associatedImages.get("macro");
                if (macroImg != null) {
                    BufferedImage bufferedImage = macroImg.toBufferedImage();
                    if (bufferedImage != null) {
                        // 转为RGB后输出JPEG
                        return convertToJpeg(convertToRgbImage(bufferedImage), "宏观图");
                    }
                }
            }
            // 未获取到宏观图，返回空数组
            return new byte[0];
        } catch (Exception e) {
            log.error("获取宏观图失败: {}", e.getMessage(), e);
            return new byte[0];
        }
    }
}
