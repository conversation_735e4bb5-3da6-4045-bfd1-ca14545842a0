package cn.ccaa.slice.parsers.common;

import java.util.Map;

import lombok.Getter;
import lombok.Setter;

/**
 * 统一切片对象包装器
 * 用于包装不同解析器返回的切片对象，提供统一的访问接口
 * <AUTHOR>
 */
@Getter
public class SlideImageWrapper {

    /**
     * 原始切片对象
     */
    private final Object slide;

    /**
     * 解析器名称
     */
    private final String parserName;

    /**
     * 切片基本信息
     */
    @Setter
    private Map<String, Object> slideInfo;

    /**
     * 构造函数
     *
     * @param slide 原始切片对象
     * @param parserName 解析器名称
     */
    public SlideImageWrapper(Object slide, String parserName) {
        this.slide = slide;
        this.parserName = parserName;
    }

    /**
     * 构造函数
     *
     * @param slide 原始切片对象
     * @param parserName 解析器名称
     * @param slideInfo 切片基本信息
     */
    public SlideImageWrapper(Object slide, String parserName, Map<String, Object> slideInfo) {
        this.slide = slide;
        this.parserName = parserName;
        this.slideInfo = slideInfo;
    }

    /**
     * 安全地获取指定类型的原始切片对象
     *
     * @param <T> 期望的类型
     * @param clazz 期望的类
     * @return 类型转换后的切片对象
     * @throws ClassCastException 如果类型不匹配
     */
    @SuppressWarnings("unchecked")
    public <T> T getSlideAs(Class<T> clazz) {
        if (!clazz.isInstance(slide)) {
            throw new ClassCastException(
                    "切片对象类型不匹配，期望: " + clazz.getName() +
                    "，实际: " + slide.getClass().getName());
        }
        return (T) slide;
    }
}
