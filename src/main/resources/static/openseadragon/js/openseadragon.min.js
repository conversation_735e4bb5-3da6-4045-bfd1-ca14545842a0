//! openseadragon 5.0.1
//! Built on 2024-12-09
//! Git commit: v5.0.1-0-480de92d
//! http://openseadragon.github.io
//! License: http://openseadragon.github.io/license/


function OpenSeadragon(e){return new OpenSeadragon.Viewer(e)}!function(n){n.version={versionStr:"5.0.1",major:parseInt("5",10),minor:parseInt("0",10),revision:parseInt("1",10)};var t={"[object Boolean]":"boolean","[object Number]":"number","[object String]":"string","[object Function]":"function","[object AsyncFunction]":"function","[object Promise]":"promise","[object Array]":"array","[object Date]":"date","[object RegExp]":"regexp","[object Object]":"object"},i=Object.prototype.toString,r=Object.prototype.hasOwnProperty;n.isFunction=function(e){return"function"===n.type(e)};n.isArray=Array.isArray||function(e){return"array"===n.type(e)};n.isWindow=function(e){return e&&"object"==typeof e&&"setInterval"in e};n.type=function(e){return null==e?String(e):t[i.call(e)]||"object"};n.isPlainObject=function(e){if(!e||"object"!==OpenSeadragon.type(e)||e.nodeType||n.isWindow(e))return!1;if(e.constructor&&!r.call(e,"constructor")&&!r.call(e.constructor.prototype,"isPrototypeOf"))return!1;var t;for(var i in e)t=i;return void 0===t||r.call(e,t)};n.isEmptyObject=function(e){for(var t in e)return!1;return!0};n.freezeObject=function(e){Object.freeze?n.freezeObject=Object.freeze:n.freezeObject=function(e){return e};return n.freezeObject(e)};n.supportsCanvas=(e=document.createElement("canvas"),!(!n.isFunction(e.getContext)||!e.getContext("2d")));var e;n.isCanvasTainted=function(e){var t=!1;try{e.getContext("2d").getImageData(0,0,1,1)}catch(e){t=!0}return t};n.supportsAddEventListener=!(!document.documentElement.addEventListener||!document.addEventListener);n.supportsRemoveEventListener=!(!document.documentElement.removeEventListener||!document.removeEventListener);n.supportsEventListenerOptions=function(){var t=0;if(n.supportsAddEventListener)try{var e={get capture(){t++;return!1},get once(){t++;return!1},get passive(){t++;return!1}};window.addEventListener("test",null,e);window.removeEventListener("test",null,e)}catch(e){t=0}return 3<=t}();n.getCurrentPixelDensityRatio=function(){if(n.supportsCanvas){var e=document.createElement("canvas").getContext("2d");var t=window.devicePixelRatio||1;e=e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return Math.max(t,1)/e}return 1};n.pixelDensityRatio=n.getCurrentPixelDensityRatio()}(OpenSeadragon);!function(u){u.extend=function(){var e,t,i,n,r,o=arguments[0]||{},s=arguments.length,a=!1,l=1;if("boolean"==typeof o){a=o;o=arguments[1]||{};l=2}"object"==typeof o||OpenSeadragon.isFunction(o)||(o={});if(s===l){o=this;--l}for(;l<s;l++)if(null!==(e=arguments[l])||void 0!==e)for(t in e){var h=Object.getOwnPropertyDescriptor(e,t);if(void 0!==h){if(h.get||h.set)Object.defineProperty(o,t,h);else if(o!==(i=h.value))if(a&&i&&(OpenSeadragon.isPlainObject(i)||(n=OpenSeadragon.isArray(i)))){h=o[t];if(n){n=!1;r=h&&OpenSeadragon.isArray(h)?h:[]}else r=h&&OpenSeadragon.isPlainObject(h)?h:{};o[t]=OpenSeadragon.extend(a,r,i)}else void 0!==i&&(o[t]=i)}else u.console.warn('Could not copy inherited property "'+t+'".')}return o};u.extend(u,{DEFAULT_SETTINGS:{xmlPath:null,tileSources:null,tileHost:null,initialPage:0,crossOriginPolicy:!1,ajaxWithCredentials:!1,loadTilesWithAjax:!1,ajaxHeaders:{},splitHashDataForPost:!1,panHorizontal:!0,panVertical:!0,constrainDuringPan:!1,wrapHorizontal:!1,wrapVertical:!1,visibilityRatio:.5,minPixelRatio:.5,defaultZoomLevel:0,minZoomLevel:null,maxZoomLevel:null,homeFillsViewer:!1,clickTimeThreshold:300,clickDistThreshold:5,dblClickTimeThreshold:300,dblClickDistThreshold:20,springStiffness:6.5,animationTime:1.2,gestureSettingsMouse:{dragToPan:!0,scrollToZoom:!0,clickToZoom:!0,dblClickToZoom:!1,dblClickDragToZoom:!1,pinchToZoom:!1,zoomToRefPoint:!0,flickEnabled:!1,flickMinSpeed:120,flickMomentum:.25,pinchRotate:!1},gestureSettingsTouch:{dragToPan:!0,scrollToZoom:!1,clickToZoom:!1,dblClickToZoom:!0,dblClickDragToZoom:!0,pinchToZoom:!0,zoomToRefPoint:!0,flickEnabled:!0,flickMinSpeed:120,flickMomentum:.25,pinchRotate:!1},gestureSettingsPen:{dragToPan:!0,scrollToZoom:!1,clickToZoom:!0,dblClickToZoom:!1,dblClickDragToZoom:!1,pinchToZoom:!1,zoomToRefPoint:!0,flickEnabled:!1,flickMinSpeed:120,flickMomentum:.25,pinchRotate:!1},gestureSettingsUnknown:{dragToPan:!0,scrollToZoom:!1,clickToZoom:!1,dblClickToZoom:!0,dblClickDragToZoom:!1,pinchToZoom:!0,zoomToRefPoint:!0,flickEnabled:!0,flickMinSpeed:120,flickMomentum:.25,pinchRotate:!1},zoomPerClick:2,zoomPerScroll:1.2,zoomPerDblClickDrag:1.2,zoomPerSecond:1,blendTime:0,alwaysBlend:!1,autoHideControls:!0,immediateRender:!1,minZoomImageRatio:.9,maxZoomPixelRatio:1.1,smoothTileEdgesMinZoom:1.1,iOSDevice:function(){if("object"!=typeof navigator)return!1;var e=navigator.userAgent;return"string"==typeof e&&(-1!==e.indexOf("iPhone")||-1!==e.indexOf("iPad")||-1!==e.indexOf("iPod"))}(),pixelsPerWheelLine:40,pixelsPerArrowPress:40,autoResize:!0,preserveImageSizeOnResize:!1,minScrollDeltaTime:50,rotationIncrement:90,maxTilesPerFrame:1,showSequenceControl:!0,sequenceControlAnchor:null,preserveViewport:!1,preserveOverlays:!1,navPrevNextWrap:!1,showNavigationControl:!0,navigationControlAnchor:null,showZoomControl:!0,showHomeControl:!0,showFullPageControl:!0,showRotationControl:!1,showFlipControl:!1,controlsFadeDelay:2e3,controlsFadeLength:1500,mouseNavEnabled:!0,showNavigator:!1,navigatorElement:null,navigatorId:null,navigatorPosition:null,navigatorSizeRatio:.2,navigatorMaintainSizeRatio:!1,navigatorTop:null,navigatorLeft:null,navigatorHeight:null,navigatorWidth:null,navigatorAutoResize:!0,navigatorAutoFade:!0,navigatorRotate:!0,navigatorBackground:"#000",navigatorOpacity:.8,navigatorBorderColor:"#555",navigatorDisplayRegionColor:"#900",degrees:0,flipped:!1,overlayPreserveContentDirection:!0,opacity:1,compositeOperation:null,drawer:["webgl","canvas","html"],drawerOptions:{webgl:{},canvas:{},html:{},custom:{}},preload:!1,imageSmoothingEnabled:!0,placeholderFillStyle:null,subPixelRoundingForTransparency:null,showReferenceStrip:!1,referenceStripScroll:"horizontal",referenceStripElement:null,referenceStripHeight:null,referenceStripWidth:null,referenceStripPosition:"BOTTOM_LEFT",referenceStripSizeRatio:.2,collectionRows:3,collectionColumns:0,collectionLayout:"horizontal",collectionMode:!1,collectionTileSize:800,collectionTileMargin:80,imageLoaderLimit:0,maxImageCacheCount:200,timeout:3e4,tileRetryMax:0,tileRetryDelay:2500,prefixUrl:"/images/",navImages:{zoomIn:{REST:"zoomin_rest.png",GROUP:"zoomin_grouphover.png",HOVER:"zoomin_hover.png",DOWN:"zoomin_pressed.png"},zoomOut:{REST:"zoomout_rest.png",GROUP:"zoomout_grouphover.png",HOVER:"zoomout_hover.png",DOWN:"zoomout_pressed.png"},home:{REST:"home_rest.png",GROUP:"home_grouphover.png",HOVER:"home_hover.png",DOWN:"home_pressed.png"},fullpage:{REST:"fullpage_rest.png",GROUP:"fullpage_grouphover.png",HOVER:"fullpage_hover.png",DOWN:"fullpage_pressed.png"},rotateleft:{REST:"rotateleft_rest.png",GROUP:"rotateleft_grouphover.png",HOVER:"rotateleft_hover.png",DOWN:"rotateleft_pressed.png"},rotateright:{REST:"rotateright_rest.png",GROUP:"rotateright_grouphover.png",HOVER:"rotateright_hover.png",DOWN:"rotateright_pressed.png"},flip:{REST:"flip_rest.png",GROUP:"flip_grouphover.png",HOVER:"flip_hover.png",DOWN:"flip_pressed.png"},previous:{REST:"previous_rest.png",GROUP:"previous_grouphover.png",HOVER:"previous_hover.png",DOWN:"previous_pressed.png"},next:{REST:"next_rest.png",GROUP:"next_grouphover.png",HOVER:"next_hover.png",DOWN:"next_pressed.png"}},debugMode:!1,debugGridColor:["#437AB2","#1B9E77","#D95F02","#7570B3","#E7298A","#66A61E","#E6AB02","#A6761D","#666666"],silenceMultiImageWarnings:!1},delegate:function(t,i){return function(){var e=arguments;return i.apply(t,e=void 0===e?[]:e)}},BROWSERS:{UNKNOWN:0,IE:1,FIREFOX:2,SAFARI:3,CHROME:4,OPERA:5,EDGE:6,CHROMEEDGE:7},SUBPIXEL_ROUNDING_OCCURRENCES:{NEVER:0,ONLY_AT_REST:1,ALWAYS:2},_viewers:new Map,getViewer:function(e){return u._viewers.get(this.getElement(e))},getElement:function(e){return e="string"==typeof e?document.getElementById(e):e},getElementPosition:function(e){var t,i,n=new u.Point;i=r(e=u.getElement(e),t="fixed"===u.getElementStyle(e).position);for(;i;){n.x+=e.offsetLeft;n.y+=e.offsetTop;t&&(n=n.plus(u.getPageScroll()));i=r(e=i,t="fixed"===u.getElementStyle(e).position)}return n},getElementOffset:function(e){var t,i=(e=u.getElement(e))&&e.ownerDocument,n={top:0,left:0};if(!i)return new u.Point;t=i.documentElement;void 0!==e.getBoundingClientRect&&(n=e.getBoundingClientRect());i=i===i.window?i:9===i.nodeType&&(i.defaultView||i.parentWindow);return new u.Point(n.left+(i.pageXOffset||t.scrollLeft)-(t.clientLeft||0),n.top+(i.pageYOffset||t.scrollTop)-(t.clientTop||0))},getElementSize:function(e){e=u.getElement(e);return new u.Point(e.clientWidth,e.clientHeight)},getElementStyle:document.documentElement.currentStyle?function(e){return(e=u.getElement(e)).currentStyle}:function(e){e=u.getElement(e);return window.getComputedStyle(e,"")},getCssPropertyWithVendorPrefix:function(e){var a={};u.getCssPropertyWithVendorPrefix=function(e){if(void 0!==a[e])return a[e];var t=document.createElement("div").style;var i=null;if(void 0!==t[e])i=e;else{var n=["Webkit","Moz","MS","O","webkit","moz","ms","o"];var r=u.capitalizeFirstLetter(e);for(var o=0;o<n.length;o++){var s=n[o]+r;if(void 0!==t[s]){i=s;break}}}return a[e]=i};return u.getCssPropertyWithVendorPrefix(e)},capitalizeFirstLetter:function(e){return e.charAt(0).toUpperCase()+e.slice(1)},positiveModulo:function(e,t){e%=t;e<0&&(e+=t);return e},pointInElement:function(e,t){e=u.getElement(e);var i=u.getElementOffset(e),e=u.getElementSize(e);return t.x>=i.x&&t.x<i.x+e.x&&t.y<i.y+e.y&&t.y>=i.y},getMousePosition:function(e){if("number"==typeof e.pageX)u.getMousePosition=function(e){var t=new u.Point;t.x=e.pageX;t.y=e.pageY;return t};else{if("number"!=typeof e.clientX)throw new Error("Unknown event mouse position, no known technique.");u.getMousePosition=function(e){var t=new u.Point;t.x=e.clientX+document.body.scrollLeft+document.documentElement.scrollLeft;t.y=e.clientY+document.body.scrollTop+document.documentElement.scrollTop;return t}}return u.getMousePosition(e)},getPageScroll:function(){var e=document.documentElement||{},t=document.body||{};if("number"==typeof window.pageXOffset)u.getPageScroll=function(){return new u.Point(window.pageXOffset,window.pageYOffset)};else if(t.scrollLeft||t.scrollTop)u.getPageScroll=function(){return new u.Point(document.body.scrollLeft,document.body.scrollTop)};else{if(!e.scrollLeft&&!e.scrollTop)return new u.Point(0,0);u.getPageScroll=function(){return new u.Point(document.documentElement.scrollLeft,document.documentElement.scrollTop)}}return u.getPageScroll()},setPageScroll:function(e){if(void 0!==window.scrollTo)u.setPageScroll=function(e){window.scrollTo(e.x,e.y)};else{var t=u.getPageScroll();if(t.x===e.x&&t.y===e.y)return;document.body.scrollLeft=e.x;document.body.scrollTop=e.y;var i=u.getPageScroll();if(i.x!==t.x&&i.y!==t.y){u.setPageScroll=function(e){document.body.scrollLeft=e.x;document.body.scrollTop=e.y};return}document.documentElement.scrollLeft=e.x;document.documentElement.scrollTop=e.y;if((i=u.getPageScroll()).x!==t.x&&i.y!==t.y){u.setPageScroll=function(e){document.documentElement.scrollLeft=e.x;document.documentElement.scrollTop=e.y};return}u.setPageScroll=function(e){}}u.setPageScroll(e)},getWindowSize:function(){var e=document.documentElement||{},t=document.body||{};if("number"==typeof window.innerWidth)u.getWindowSize=function(){return new u.Point(window.innerWidth,window.innerHeight)};else if(e.clientWidth||e.clientHeight)u.getWindowSize=function(){return new u.Point(document.documentElement.clientWidth,document.documentElement.clientHeight)};else{if(!t.clientWidth&&!t.clientHeight)throw new Error("Unknown window size, no known technique.");u.getWindowSize=function(){return new u.Point(document.body.clientWidth,document.body.clientHeight)}}return u.getWindowSize()},makeCenteredNode:function(e){e=u.getElement(e);var t=[u.makeNeutralElement("div"),u.makeNeutralElement("div"),u.makeNeutralElement("div")];u.extend(t[0].style,{display:"table",height:"100%",width:"100%"});u.extend(t[1].style,{display:"table-row"});u.extend(t[2].style,{display:"table-cell",verticalAlign:"middle",textAlign:"center"});t[0].appendChild(t[1]);t[1].appendChild(t[2]);t[2].appendChild(e);return t[0]},makeNeutralElement:function(e){var t=document.createElement(e),e=t.style;e.background="transparent none";e.border="none";e.margin="0px";e.padding="0px";e.position="static";return t},now:function(){Date.now?u.now=Date.now:u.now=function(){return(new Date).getTime()};return u.now()},makeTransparentImage:function(e){var t=u.makeNeutralElement("img");t.src=e;return t},setElementOpacity:function(e,t,i){e=u.getElement(e);i&&!u.Browser.alpha&&(t=Math.round(t));if(u.Browser.opacity)e.style.opacity=t<1?t:"";else if(t<1){t=Math.round(100*t);e.style.filter="alpha(opacity="+t+")"}else e.style.filter=""},setElementTouchActionNone:function(e){void 0!==(e=u.getElement(e)).style.touchAction?e.style.touchAction="none":void 0!==e.style.msTouchAction&&(e.style.msTouchAction="none")},setElementPointerEvents:function(e,t){void 0!==(e=u.getElement(e)).style&&void 0!==e.style.pointerEvents&&(e.style.pointerEvents=t)},setElementPointerEventsNone:function(e){u.setElementPointerEvents(e,"none")},addClass:function(e,t){(e=u.getElement(e)).className?-1===(" "+e.className+" ").indexOf(" "+t+" ")&&(e.className+=" "+t):e.className=t},indexOf:function(e,t,i){Array.prototype.indexOf?this.indexOf=function(e,t,i){return e.indexOf(t,i)}:this.indexOf=function(e,t,i){var n,r,i=i||0;if(!e)throw new TypeError;if(0===(r=e.length)||r<=i)return-1;for(n=i=i<0?r-Math.abs(i):i;n<r;n++)if(e[n]===t)return n;return-1};return this.indexOf(e,t,i)},removeClass:function(e,t){var i,n,r=[];i=(e=u.getElement(e)).className.split(/\s+/);for(n=0;n<i.length;n++)i[n]&&i[n]!==t&&r.push(i[n]);e.className=r.join(" ")},normalizeEventListenerOptions:function(e){return void 0!==e?"boolean"==typeof e?u.supportsEventListenerOptions?{capture:e}:e:u.supportsEventListenerOptions?e:void 0!==e.capture&&e.capture:!!u.supportsEventListenerOptions&&{capture:!1}},addEvent:function(){if(u.supportsAddEventListener)return function(e,t,i,n){n=u.normalizeEventListenerOptions(n);(e=u.getElement(e)).addEventListener(t,i,n)};if(document.documentElement.attachEvent&&document.attachEvent)return function(e,t,i){(e=u.getElement(e)).attachEvent("on"+t,i)};throw new Error("No known event model.")}(),removeEvent:function(){if(u.supportsRemoveEventListener)return function(e,t,i,n){n=u.normalizeEventListenerOptions(n);(e=u.getElement(e)).removeEventListener(t,i,n)};if(document.documentElement.detachEvent&&document.detachEvent)return function(e,t,i){(e=u.getElement(e)).detachEvent("on"+t,i)};throw new Error("No known event model.")}(),cancelEvent:function(e){e.preventDefault()},eventIsCanceled:function(e){return e.defaultPrevented},stopEvent:function(e){e.stopPropagation()},createCallback:function(i,n){console.error("The createCallback function is deprecated and will be removed in future versions. Please use alternativeFunction instead.");var e,r=[];for(e=2;e<arguments.length;e++)r.push(arguments[e]);return function(){var e,t=r.concat([]);for(e=0;e<arguments.length;e++)t.push(arguments[e]);return n.apply(i,t)}},getUrlParameter:function(e){e=a[e];return e||null},getUrlProtocol:function(e){e=e.match(/^([a-z]+:)\/\//i);return null===e?window.location.protocol:e[1].toLowerCase()},createAjaxRequest:function(){if(window.XMLHttpRequest){u.createAjaxRequest=function(){return new XMLHttpRequest};return new XMLHttpRequest}throw new Error("Browser doesn't support XMLHttpRequest.")},makeAjaxRequest:function(e,t,i){var n;var r;var o;var s;if(u.isPlainObject(e)){t=e.success;i=e.error;n=e.withCredentials;r=e.headers;o=e.responseType||null;s=e.postData||null;e=e.url}var a=u.getUrlProtocol(e);var l=u.createAjaxRequest();if(!u.isFunction(t))throw new Error("makeAjaxRequest requires a success callback");l.onreadystatechange=function(){if(4===l.readyState){l.onreadystatechange=function(){};200<=l.status&&l.status<300||0===l.status&&"http:"!==a&&"https:"!==a?t(l):u.isFunction(i)?i(l):u.console.error("AJAX request returned %d: %s",l.status,e)}};var h=s?"POST":"GET";try{l.open(h,e,!0);o&&(l.responseType=o);if(r)for(var c in r)Object.prototype.hasOwnProperty.call(r,c)&&r[c]&&l.setRequestHeader(c,r[c]);n&&(l.withCredentials=!0);l.send(s)}catch(e){u.console.error("%s while making AJAX request: %s",e.name,e.message);l.onreadystatechange=function(){};u.isFunction(i)&&i(l,e)}return l},jsonp:function(e){var i,t=e.url,n=document.head||document.getElementsByTagName("head")[0]||document.documentElement,r=e.callbackName||"openseadragon"+u.now(),o=window[r],s=e.param||"callback",a=e.callback;t=t.replace(/(=)\?(&|$)|\?\?/i,"$1"+r+"$2");t+=(/\?/.test(t)?"&":"?")+s+"="+r;window[r]=function(e){if(o)window[r]=o;else try{delete window[r]}catch(e){}a&&u.isFunction(a)&&a(e)};i=document.createElement("script");void 0===e.async&&!1===e.async||(i.async="async");e.scriptCharset&&(i.charset=e.scriptCharset);i.src=t;i.onload=i.onreadystatechange=function(e,t){if(t||!i.readyState||/loaded|complete/.test(i.readyState)){i.onload=i.onreadystatechange=null;n&&i.parentNode&&n.removeChild(i);i=void 0}};n.insertBefore(i,n.firstChild)},createFromDZI:function(){throw"OpenSeadragon.createFromDZI is deprecated, use Viewer.open."},parseXml:function(e){if(!window.DOMParser)throw new Error("Browser doesn't support XML DOM.");u.parseXml=function(e){return(new DOMParser).parseFromString(e,"text/xml")};return u.parseXml(e)},parseJSON:function(e){u.parseJSON=window.JSON.parse;return u.parseJSON(e)},imageFormatSupported:function(e){return!!t[(e=e||"").toLowerCase()]},setImageFormatsSupported:function(e){u.extend(t,e)}});function e(e){}u.console=window.console||{log:e,debug:e,info:e,warn:e,error:e,assert:e};var t={avif:!0,bmp:!(u.Browser={vendor:u.BROWSERS.UNKNOWN,version:0,alpha:!0}),jpeg:!0,jpg:!0,png:!0,tif:!1,wdp:!1,webp:!0},a={};!function(){var e=navigator.appVersion,t=navigator.userAgent;switch(navigator.appName){case"Microsoft Internet Explorer":if(window.attachEvent&&window.ActiveXObject){u.Browser.vendor=u.BROWSERS.IE;u.Browser.version=parseFloat(t.substring(t.indexOf("MSIE")+5,t.indexOf(";",t.indexOf("MSIE"))))}break;case"Netscape":if(window.addEventListener)if(0<=t.indexOf("Edge")){u.Browser.vendor=u.BROWSERS.EDGE;u.Browser.version=parseFloat(t.substring(t.indexOf("Edge")+5))}else if(0<=t.indexOf("Edg")){u.Browser.vendor=u.BROWSERS.CHROMEEDGE;u.Browser.version=parseFloat(t.substring(t.indexOf("Edg")+4))}else if(0<=t.indexOf("Firefox")){u.Browser.vendor=u.BROWSERS.FIREFOX;u.Browser.version=parseFloat(t.substring(t.indexOf("Firefox")+8))}else if(0<=t.indexOf("Safari")){u.Browser.vendor=0<=t.indexOf("Chrome")?u.BROWSERS.CHROME:u.BROWSERS.SAFARI;u.Browser.version=parseFloat(t.substring(t.substring(0,t.indexOf("Safari")).lastIndexOf("/")+1,t.indexOf("Safari")))}else if(null!==new RegExp("Trident/.*rv:([0-9]{1,}[.0-9]{0,})").exec(t)){u.Browser.vendor=u.BROWSERS.IE;u.Browser.version=parseFloat(RegExp.$1)}break;case"Opera":u.Browser.vendor=u.BROWSERS.OPERA;u.Browser.version=parseFloat(e)}var i,n,r=window.location.search.substring(1).split("&");for(n=0;n<r.length;n++)if(0<(s=(i=r[n]).indexOf("="))){var o=i.substring(0,s),s=i.substring(s+1);try{a[o]=decodeURIComponent(s)}catch(e){u.console.error("Ignoring malformed URL parameter: %s=%s",o,s)}}u.Browser.alpha=!(u.Browser.vendor===u.BROWSERS.CHROME&&u.Browser.version<2);u.Browser.opacity=!0;u.Browser.vendor===u.BROWSERS.IE&&u.console.error("Internet Explorer is not supported by OpenSeadragon")}();!function(e){var t=e.requestAnimationFrame||e.mozRequestAnimationFrame||e.webkitRequestAnimationFrame||e.msRequestAnimationFrame;var i=e.cancelAnimationFrame||e.mozCancelAnimationFrame||e.webkitCancelAnimationFrame||e.msCancelAnimationFrame;if(t&&i){u.requestAnimationFrame=function(){return t.apply(e,arguments)};u.cancelAnimationFrame=function(){return i.apply(e,arguments)}}else{var n,r=[],o=[],s=0;u.requestAnimationFrame=function(e){r.push([++s,e]);n=n||setInterval(function(){if(r.length){var e=u.now();var t=o;o=r;r=t;for(;o.length;)o.shift()[1](e)}else{clearInterval(n);n=void 0}},20);return s};u.cancelAnimationFrame=function(e){var t,i;for(t=0,i=r.length;t<i;t+=1)if(r[t][0]===e){r.splice(t,1);return}for(t=0,i=o.length;t<i;t+=1)if(o[t][0]===e){o.splice(t,1);return}}}}(window);function r(e,t){return t&&e!==document.body?document.body:e.offsetParent}}(OpenSeadragon);!function(e,t){"function"==typeof define&&define.amd?define([],t):"object"==typeof module&&module.exports?module.exports=t():e.OpenSeadragon=t()}(this,function(){return OpenSeadragon});!function(e){e.Mat3=class y{constructor(e){this.values=e=e||[0,0,0,0,0,0,0,0,0]}static makeIdentity(){return new y([1,0,0,0,1,0,0,0,1])}static makeTranslation(e,t){return new y([1,0,0,0,1,0,e,t,1])}static makeRotation(e){var t=Math.cos(e);e=Math.sin(e);return new y([t,-e,0,e,t,0,0,0,1])}static makeScaling(e,t){return new y([e,0,0,0,t,0,0,0,1])}multiply(e){var t=this.values;var i=e.values;var n=t[0];var r=t[1];var o=t[2];var s=t[3];var a=t[4];var l=t[5];var h=t[6];var c=t[7];var u=t[8];var d=i[0];var p=i[1];var g=i[2];var m=i[3];var v=i[4];var f=i[5];e=i[6];t=i[7];i=i[8];return new y([d*n+p*s+g*h,d*r+p*a+g*c,d*o+p*l+g*u,m*n+v*s+f*h,m*r+v*a+f*c,m*o+v*l+f*u,e*n+t*s+i*h,e*r+t*a+i*c,e*o+t*l+i*u])}}}(OpenSeadragon);!function(t){var e={supportsFullScreen:!1,isFullScreen:function(){return!1},getFullScreenElement:function(){return null},requestFullScreen:function(){},exitFullScreen:function(){},cancelFullScreen:function(){},fullScreenEventName:"",fullScreenErrorEventName:""};if(document.exitFullscreen){e.supportsFullScreen=!0;e.getFullScreenElement=function(){return document.fullscreenElement};e.requestFullScreen=function(e){return e.requestFullscreen().catch(function(e){t.console.error("Fullscreen request failed: ",e)})};e.exitFullScreen=function(){document.exitFullscreen().catch(function(e){t.console.error("Error while exiting fullscreen: ",e)})};e.fullScreenEventName="fullscreenchange";e.fullScreenErrorEventName="fullscreenerror"}else if(document.msExitFullscreen){e.supportsFullScreen=!0;e.getFullScreenElement=function(){return document.msFullscreenElement};e.requestFullScreen=function(e){return e.msRequestFullscreen()};e.exitFullScreen=function(){document.msExitFullscreen()};e.fullScreenEventName="MSFullscreenChange";e.fullScreenErrorEventName="MSFullscreenError"}else if(document.webkitExitFullscreen){e.supportsFullScreen=!0;e.getFullScreenElement=function(){return document.webkitFullscreenElement};e.requestFullScreen=function(e){return e.webkitRequestFullscreen()};e.exitFullScreen=function(){document.webkitExitFullscreen()};e.fullScreenEventName="webkitfullscreenchange";e.fullScreenErrorEventName="webkitfullscreenerror"}else if(document.webkitCancelFullScreen){e.supportsFullScreen=!0;e.getFullScreenElement=function(){return document.webkitCurrentFullScreenElement};e.requestFullScreen=function(e){return e.webkitRequestFullScreen()};e.exitFullScreen=function(){document.webkitCancelFullScreen()};e.fullScreenEventName="webkitfullscreenchange";e.fullScreenErrorEventName="webkitfullscreenerror"}else if(document.mozCancelFullScreen){e.supportsFullScreen=!0;e.getFullScreenElement=function(){return document.mozFullScreenElement};e.requestFullScreen=function(e){return e.mozRequestFullScreen()};e.exitFullScreen=function(){document.mozCancelFullScreen()};e.fullScreenEventName="mozfullscreenchange";e.fullScreenErrorEventName="mozfullscreenerror"}e.isFullScreen=function(){return null!==e.getFullScreenElement()};e.cancelFullScreen=function(){t.console.error("cancelFullScreen is deprecated. Use exitFullScreen instead.");e.exitFullScreen()};t.extend(t,e)}(OpenSeadragon);!function(a){a.EventSource=function(){this.events={};this._rejectedEventList={}};a.EventSource.prototype={addOnceHandler:function(t,i,e,n,r){var o=this;n=n||1;var s=0;function a(e){++s===n&&o.removeHandler(t,a);return i(e)}return this.addHandler(t,a,e,r)},addHandler:function(e,t,i,n){if(Object.prototype.hasOwnProperty.call(this._rejectedEventList,e)){a.console.error(`Error adding handler for ${e}. `+this._rejectedEventList[e]);return!1}var r=this.events[e];r||(this.events[e]=r=[]);if(t&&a.isFunction(t)){var o=r.length,s={handler:t,userData:i||null,priority:n||0};r[o]=s;for(;0<o&&r[o-1].priority<r[o].priority;){r[o]=r[o-1];r[o-1]=s;o--}}return!0},removeHandler:function(e,t){var i,n=this.events[e],r=[];if(n&&a.isArray(n)){for(i=0;i<n.length;i++)n[i].handler!==t&&r.push(n[i]);this.events[e]=r}},numberOfHandlers:function(e){e=this.events[e];return e?e.length:0},removeAllHandlers:function(e){if(e)this.events[e]=[];else for(var t in this.events)this.events[t]=[]},getHandler:function(e){var r=this.events[e];if(!r||!r.length)return null;r=1===r.length?[r[0]]:Array.apply(null,r);return function(e,t){var i,n=r.length;for(i=0;i<n;i++)if(r[i]){t.eventSource=e;t.userData=r[i].userData;r[i].handler(t)}}},raiseEvent:function(e,t){if(Object.prototype.hasOwnProperty.call(this._rejectedEventList,e)){a.console.error(`Error adding handler for ${e}. `+this._rejectedEventList[e]);return!1}e=this.getHandler(e);e&&e(this,t||{});return!0},rejectEventHandler(e,t=""){this._rejectedEventList[e]=t},allowEventHandler(e){delete this._rejectedEventList[e]}}}(OpenSeadragon);!function(c){var n=[];var u={};c.MouseTracker=function(e){n.push(this);var t=arguments;c.isPlainObject(e)||(e={element:t[0],clickTimeThreshold:t[1],clickDistThreshold:t[2]});this.hash=Math.random();this.element=c.getElement(e.element);this.clickTimeThreshold=e.clickTimeThreshold||c.DEFAULT_SETTINGS.clickTimeThreshold;this.clickDistThreshold=e.clickDistThreshold||c.DEFAULT_SETTINGS.clickDistThreshold;this.dblClickTimeThreshold=e.dblClickTimeThreshold||c.DEFAULT_SETTINGS.dblClickTimeThreshold;this.dblClickDistThreshold=e.dblClickDistThreshold||c.DEFAULT_SETTINGS.dblClickDistThreshold;this.userData=e.userData||null;this.stopDelay=e.stopDelay||50;this.preProcessEventHandler=e.preProcessEventHandler||null;this.contextMenuHandler=e.contextMenuHandler||null;this.enterHandler=e.enterHandler||null;this.leaveHandler=e.leaveHandler||null;this.exitHandler=e.exitHandler||null;this.overHandler=e.overHandler||null;this.outHandler=e.outHandler||null;this.pressHandler=e.pressHandler||null;this.nonPrimaryPressHandler=e.nonPrimaryPressHandler||null;this.releaseHandler=e.releaseHandler||null;this.nonPrimaryReleaseHandler=e.nonPrimaryReleaseHandler||null;this.moveHandler=e.moveHandler||null;this.scrollHandler=e.scrollHandler||null;this.clickHandler=e.clickHandler||null;this.dblClickHandler=e.dblClickHandler||null;this.dragHandler=e.dragHandler||null;this.dragEndHandler=e.dragEndHandler||null;this.pinchHandler=e.pinchHandler||null;this.stopHandler=e.stopHandler||null;this.keyDownHandler=e.keyDownHandler||null;this.keyUpHandler=e.keyUpHandler||null;this.keyHandler=e.keyHandler||null;this.focusHandler=e.focusHandler||null;this.blurHandler=e.blurHandler||null;var i=this;u[this.hash]={click:function(e){!function(e,t){var i={originalEvent:t,eventType:"click",pointerType:"mouse",isEmulated:!1};H(e,i);i.preventDefault&&!i.defaultPrevented&&c.cancelEvent(t);i.stopPropagation&&c.stopEvent(t)}(i,e)},dblclick:function(e){!function(e,t){var i={originalEvent:t,eventType:"dblclick",pointerType:"mouse",isEmulated:!1};H(e,i);i.preventDefault&&!i.defaultPrevented&&c.cancelEvent(t);i.stopPropagation&&c.stopEvent(t)}(i,e)},keydown:function(e){!function(e,t){var i=null;var n={originalEvent:t,eventType:"keydown",pointerType:"",isEmulated:!1};H(e,n);if(e.keyDownHandler&&!n.preventGesture&&!n.defaultPrevented){i={eventSource:e,keyCode:t.keyCode||t.charCode,ctrl:t.ctrlKey,shift:t.shiftKey,alt:t.altKey,meta:t.metaKey,originalEvent:t,preventDefault:n.preventDefault||n.defaultPrevented,userData:e.userData};e.keyDownHandler(i)}(i&&i.preventDefault||n.preventDefault&&!n.defaultPrevented)&&c.cancelEvent(t);n.stopPropagation&&c.stopEvent(t)}(i,e)},keyup:function(e){!function(e,t){var i=null;var n={originalEvent:t,eventType:"keyup",pointerType:"",isEmulated:!1};H(e,n);if(e.keyUpHandler&&!n.preventGesture&&!n.defaultPrevented){i={eventSource:e,keyCode:t.keyCode||t.charCode,ctrl:t.ctrlKey,shift:t.shiftKey,alt:t.altKey,meta:t.metaKey,originalEvent:t,preventDefault:n.preventDefault||n.defaultPrevented,userData:e.userData};e.keyUpHandler(i)}(i&&i.preventDefault||n.preventDefault&&!n.defaultPrevented)&&c.cancelEvent(t);n.stopPropagation&&c.stopEvent(t)}(i,e)},keypress:function(e){!function(e,t){var i=null;var n={originalEvent:t,eventType:"keypress",pointerType:"",isEmulated:!1};H(e,n);if(e.keyHandler&&!n.preventGesture&&!n.defaultPrevented){i={eventSource:e,keyCode:t.keyCode||t.charCode,ctrl:t.ctrlKey,shift:t.shiftKey,alt:t.altKey,meta:t.metaKey,originalEvent:t,preventDefault:n.preventDefault||n.defaultPrevented,userData:e.userData};e.keyHandler(i)}(i&&i.preventDefault||n.preventDefault&&!n.defaultPrevented)&&c.cancelEvent(t);n.stopPropagation&&c.stopEvent(t)}(i,e)},focus:function(e){!function(e,t){var i={originalEvent:t,eventType:"focus",pointerType:"",isEmulated:!1};H(e,i);e.focusHandler&&!i.preventGesture&&e.focusHandler({eventSource:e,originalEvent:t,userData:e.userData})}(i,e)},blur:function(e){!function(e,t){var i={originalEvent:t,eventType:"blur",pointerType:"",isEmulated:!1};H(e,i);e.blurHandler&&!i.preventGesture&&e.blurHandler({eventSource:e,originalEvent:t,userData:e.userData})}(i,e)},contextmenu:function(e){!function(e,t){var i=null;var n={originalEvent:t,eventType:"contextmenu",pointerType:"mouse",isEmulated:!1};H(e,n);if(e.contextMenuHandler&&!n.preventGesture&&!n.defaultPrevented){i={eventSource:e,position:T(y(t),e.element),originalEvent:n.originalEvent,preventDefault:n.preventDefault||n.defaultPrevented,userData:e.userData};e.contextMenuHandler(i)}(i&&i.preventDefault||n.preventDefault&&!n.defaultPrevented)&&c.cancelEvent(t);n.stopPropagation&&c.stopEvent(t)}(i,e)},wheel:function(e){E(i,e,e)},mousewheel:function(e){_(i,e)},DOMMouseScroll:function(e){_(i,e)},MozMousePixelScroll:function(e){_(i,e)},losecapture:function(e){!function(e,t){var i={id:c.MouseTracker.mousePointerId,type:"mouse"};var n={originalEvent:t,eventType:"lostpointercapture",pointerType:"mouse",isEmulated:!1};H(e,n);t.target===e.element&&z(e,i,!1);n.stopPropagation&&c.stopEvent(t)}(i,e)},mouseenter:function(e){S(i,e)},mouseleave:function(e){P(i,e)},mouseover:function(e){R(i,e)},mouseout:function(e){b(i,e)},mousedown:function(e){C(i,e)},mouseup:function(e){D(i,e)},mousemove:function(e){O(i,e)},touchstart:function(e){!function(e,t){var i,n,r,o=t.changedTouches.length,s=e.getActivePointersListByType("touch");i=c.now();s.getLength()>t.touches.length-o&&c.console.warn("Tracked touch contact count doesn't match event.touches.length");var a={originalEvent:t,eventType:"pointerdown",pointerType:"touch",isEmulated:!1};H(e,a);for(n=0;n<o;n++){r={id:t.changedTouches[n].identifier,type:"touch",isPrimary:0===s.getLength(),currentPos:y(t.changedTouches[n]),currentTime:i};A(e,a,r);L(e,a,r,0);z(e,r,!0)}a.preventDefault&&!a.defaultPrevented&&c.cancelEvent(t);a.stopPropagation&&c.stopEvent(t)}(i,e)},touchend:function(e){!function(e,t){var i,n,r,o=t.changedTouches.length;i=c.now();var s={originalEvent:t,eventType:"pointerup",pointerType:"touch",isEmulated:!1};H(e,s);for(n=0;n<o;n++){r={id:t.changedTouches[n].identifier,type:"touch",currentPos:y(t.changedTouches[n]),currentTime:i};N(e,s,r,0);z(e,r,!1);M(e,s,r)}s.preventDefault&&!s.defaultPrevented&&c.cancelEvent(t);s.stopPropagation&&c.stopEvent(t)}(i,e)},touchmove:function(e){!function(e,t){var i,n,r,o=t.changedTouches.length;i=c.now();var s={originalEvent:t,eventType:"pointermove",pointerType:"touch",isEmulated:!1};H(e,s);for(n=0;n<o;n++){r={id:t.changedTouches[n].identifier,type:"touch",currentPos:y(t.changedTouches[n]),currentTime:i};U(e,s,r)}s.preventDefault&&!s.defaultPrevented&&c.cancelEvent(t);s.stopPropagation&&c.stopEvent(t)}(i,e)},touchcancel:function(e){!function(e,t){var i,n,r=t.changedTouches.length;var o={originalEvent:t,eventType:"pointercancel",pointerType:"touch",isEmulated:!1};H(e,o);for(i=0;i<r;i++){n={id:t.changedTouches[i].identifier,type:"touch"};W(e,0,n)}o.stopPropagation&&c.stopEvent(t)}(i,e)},gesturestart:function(e){!(e=e,c.eventIsCanceled(e)||e.preventDefault())},gesturechange:function(e){!(e=e,c.eventIsCanceled(e)||e.preventDefault())},gotpointercapture:function(e){!function(e,t){var i={originalEvent:t,eventType:"gotpointercapture",pointerType:v(t),isEmulated:!1};H(e,i);t.target===e.element&&z(e,{id:t.pointerId,type:v(t)},!0);i.stopPropagation&&c.stopEvent(t)}(i,e)},lostpointercapture:function(e){!function(e,t){var i={originalEvent:t,eventType:"lostpointercapture",pointerType:v(t),isEmulated:!1};H(e,i);t.target===e.element&&z(e,{id:t.pointerId,type:v(t)},!1);i.stopPropagation&&c.stopEvent(t)}(i,e)},pointerenter:function(e){S(i,e)},pointerleave:function(e){P(i,e)},pointerover:function(e){R(i,e)},pointerout:function(e){b(i,e)},pointerdown:function(e){C(i,e)},pointerup:function(e){D(i,e)},pointermove:function(e){O(i,e)},pointercancel:function(e){!function(e,t){var i={id:t.pointerId,type:v(t)};var n={originalEvent:t,eventType:"pointercancel",pointerType:i.type,isEmulated:!1};H(e,n);W(e,0,i);n.stopPropagation&&c.stopEvent(t)}(i,e)},pointerupcaptured:function(e){!function(e,t){e.getActivePointersListByType(v(t)).getById(t.pointerId)&&I(e,t);c.stopEvent(t)}(i,e)},pointermovecaptured:function(e){!function(e,t){e.getActivePointersListByType(v(t)).getById(t.pointerId)&&B(e,t);c.stopEvent(t)}(i,e)},tracking:!1,activePointersLists:[],lastClickPos:null,dblClickTimeOut:null,pinchGPoints:[],lastPinchDist:0,currentPinchDist:0,lastPinchCenter:null,currentPinchCenter:null,sentDragEvent:!1};this.hasGestureHandlers=!!(this.pressHandler||this.nonPrimaryPressHandler||this.releaseHandler||this.nonPrimaryReleaseHandler||this.clickHandler||this.dblClickHandler||this.dragHandler||this.dragEndHandler||this.pinchHandler);this.hasScrollHandler=!!this.scrollHandler;c.MouseTracker.havePointerEvents&&c.setElementPointerEvents(this.element,"auto");this.exitHandler&&c.console.error("MouseTracker.exitHandler is deprecated. Use MouseTracker.leaveHandler instead.");e.startDisabled||this.setTracking(!0)};c.MouseTracker.prototype={destroy:function(){var e;t(this);this.element=null;for(e=0;e<n.length;e++)if(n[e]===this){n.splice(e,1);break}u[this.hash]=null;delete u[this.hash]},isTracking:function(){return u[this.hash].tracking},setTracking:function(e){(e?function(e){var t,i,n=u[e.hash];if(!n.tracking){for(i=0;i<c.MouseTracker.subscribeEvents.length;i++){t=c.MouseTracker.subscribeEvents[i];c.addEvent(e.element,t,n[t],t===c.MouseTracker.wheelEventName&&{passive:!1,capture:!1})}r(e);n.tracking=!0}}:t)(this);return this},getActivePointersListByType:function(e){var t,i,n=u[this.hash],r=n?n.activePointersLists.length:0;for(t=0;t<r;t++)if(n.activePointersLists[t].type===e)return n.activePointersLists[t];i=new c.MouseTracker.GesturePointList(e);n&&n.activePointersLists.push(i);return i},getActivePointerCount:function(){var e,t=u[this.hash],i=t.activePointersLists.length,n=0;for(e=0;e<i;e++)n+=t.activePointersLists[e].getLength();return n},preProcessEventHandler:function(){},contextMenuHandler:function(){},enterHandler:function(){},leaveHandler:function(){},exitHandler:function(){},overHandler:function(){},outHandler:function(){},pressHandler:function(){},nonPrimaryPressHandler:function(){},releaseHandler:function(){},nonPrimaryReleaseHandler:function(){},moveHandler:function(){},scrollHandler:function(){},clickHandler:function(){},dblClickHandler:function(){},dragHandler:function(){},dragEndHandler:function(){},pinchHandler:function(){},stopHandler:function(){},keyDownHandler:function(){},keyUpHandler:function(){},keyHandler:function(){},focusHandler:function(){},blurHandler:function(){}};var o=function(){try{return window.self!==window.top}catch(e){return!0}}();function s(e){try{return e.addEventListener&&e.removeEventListener}catch(e){return}}c.MouseTracker.gesturePointVelocityTracker=(l=[],d=h=0,{addPoint:function(e,t){e=a(e,t);l.push({guid:e,gPoint:t,lastPos:t.currentPos});if(1===l.length){d=c.now();h=window.setInterval(i,50)}},removePoint:function(e,t){var i,n=a(e,t),r=l.length;for(i=0;i<r;i++)if(l[i].guid===n){l.splice(i,1);0===--r&&window.clearInterval(h);break}}});function a(e,t){return e.hash.toString()+t.type+t.id.toString()}function i(){var e,t,i,n,r,o=l.length,s=c.now();n=s-d;d=s;for(e=0;e<o;e++){(i=(t=l[e]).gPoint).direction=Math.atan2(i.currentPos.y-t.lastPos.y,i.currentPos.x-t.lastPos.x);r=t.lastPos.distanceTo(i.currentPos);t.lastPos=i.currentPos;i.speed=.75*(1e3*r/(1+n))+.25*i.speed}}var l,h,d;c.MouseTracker.captureElement=document;c.MouseTracker.wheelEventName="onwheel"in document.createElement("div")?"wheel":void 0!==document.onmousewheel?"mousewheel":"DOMMouseScroll";c.MouseTracker.subscribeEvents=["click","dblclick","keydown","keyup","keypress","focus","blur","contextmenu",c.MouseTracker.wheelEventName];"DOMMouseScroll"===c.MouseTracker.wheelEventName&&c.MouseTracker.subscribeEvents.push("MozMousePixelScroll");if(window.PointerEvent){c.MouseTracker.havePointerEvents=!0;c.MouseTracker.subscribeEvents.push("pointerenter","pointerleave","pointerover","pointerout","pointerdown","pointerup","pointermove","pointercancel");c.MouseTracker.havePointerCapture=(e=document.createElement("div"),c.isFunction(e.setPointerCapture)&&c.isFunction(e.releasePointerCapture));c.MouseTracker.havePointerCapture&&c.MouseTracker.subscribeEvents.push("gotpointercapture","lostpointercapture")}else{c.MouseTracker.havePointerEvents=!1;c.MouseTracker.subscribeEvents.push("mouseenter","mouseleave","mouseover","mouseout","mousedown","mouseup","mousemove");c.MouseTracker.mousePointerId="legacy-mouse";c.MouseTracker.havePointerCapture=(e=document.createElement("div"),c.isFunction(e.setCapture)&&c.isFunction(e.releaseCapture));c.MouseTracker.havePointerCapture&&c.MouseTracker.subscribeEvents.push("losecapture");"ontouchstart"in window&&c.MouseTracker.subscribeEvents.push("touchstart","touchend","touchmove","touchcancel");"ongesturestart"in window&&c.MouseTracker.subscribeEvents.push("gesturestart","gesturechange")}var e;c.MouseTracker.GesturePointList=function(e){this._gPoints=[];this.type=e;this.buttons=0;this.contacts=0;this.clicks=0;this.captureCount=0};c.MouseTracker.GesturePointList.prototype={getLength:function(){return this._gPoints.length},asArray:function(){return this._gPoints},add:function(e){return this._gPoints.push(e)},removeById:function(e){var t,i=this._gPoints.length;for(t=0;t<i;t++)if(this._gPoints[t].id===e){this._gPoints.splice(t,1);break}return this._gPoints.length},getByIndex:function(e){return e<this._gPoints.length?this._gPoints[e]:null},getById:function(e){var t,i=this._gPoints.length;for(t=0;t<i;t++)if(this._gPoints[t].id===e)return this._gPoints[t];return null},getPrimary:function(e){var t,i=this._gPoints.length;for(t=0;t<i;t++)if(this._gPoints[t].isPrimary)return this._gPoints[t];return null},addContact:function(){++this.contacts;if(1<this.contacts&&("mouse"===this.type||"pen"===this.type)){c.console.warn("GesturePointList.addContact() Implausible contacts value");this.contacts=1}},removeContact:function(){--this.contacts;this.contacts<0&&(this.contacts=0)}};function r(e){var t,i,n,r,o,s=u[e.hash],a=s.activePointersLists.length;for(t=0;t<a;t++)if(0<(n=s.activePointersLists[t]).getLength()){o=[];r=n.asArray();for(i=0;i<r.length;i++)o.push(r[i]);for(i=0;i<o.length;i++)F(e,n,o[i])}for(t=0;t<a;t++)s.activePointersLists.pop();s.sentDragEvent=!1}function t(e){var t,i,n=u[e.hash];if(n.tracking){for(i=0;i<c.MouseTracker.subscribeEvents.length;i++){t=c.MouseTracker.subscribeEvents[i];c.removeEvent(e.element,t,n[t],!1)}r(e);n.tracking=!1}}function p(e,t){e=u[e.hash];if("pointerevent"===t)return{upName:"pointerup",upHandler:e.pointerupcaptured,moveName:"pointermove",moveHandler:e.pointermovecaptured};if("mouse"===t)return{upName:"pointerup",upHandler:e.pointerupcaptured,moveName:"pointermove",moveHandler:e.pointermovecaptured};if("touch"===t)return{upName:"touchend",upHandler:e.touchendcaptured,moveName:"touchmove",moveHandler:e.touchmovecaptured};throw new Error("MouseTracker.getCaptureEventParams: Unknown pointer type.")}function g(e,t){var i;if(c.MouseTracker.havePointerCapture)if(c.MouseTracker.havePointerEvents){if(!(i=e.getActivePointersListByType(t.type).getById(t.id))||!i.captured)return;try{e.element.releasePointerCapture(t.id)}catch(e){}}else e.element.releaseCapture();else{i=p(e,c.MouseTracker.havePointerEvents?"pointerevent":t.type);o&&s(window.top)&&c.removeEvent(window.top,i.upName,i.upHandler,!0);c.removeEvent(c.MouseTracker.captureElement,i.moveName,i.moveHandler,!0);c.removeEvent(c.MouseTracker.captureElement,i.upName,i.upHandler,!0)}z(e,t,!1)}function m(e){return c.MouseTracker.havePointerEvents?e.pointerId:c.MouseTracker.mousePointerId}function v(e){return c.MouseTracker.havePointerEvents&&e.pointerType?e.pointerType:"mouse"}function f(e){return!c.MouseTracker.havePointerEvents||e.isPrimary}function y(e){return c.getMousePosition(e)}function w(e,t){return T(y(e),t)}function T(e,t){t=c.getElementOffset(t);return e.minus(t)}function x(e,t){return new c.Point((e.x+t.x)/2,(e.y+t.y)/2)}function _(e,t){var i={target:t.target||t.srcElement,type:"wheel",shiftKey:t.shiftKey||!1,clientX:t.clientX,clientY:t.clientY,pageX:t.pageX||t.clientX,pageY:t.pageY||t.clientY,deltaMode:"MozMousePixelScroll"===t.type?0:1,deltaX:0,deltaZ:0};"mousewheel"===c.MouseTracker.wheelEventName?i.deltaY=-t.wheelDelta/c.DEFAULT_SETTINGS.pixelsPerWheelLine:i.deltaY=t.detail;E(e,i,t)}function E(e,t,i){var n,r;var o=null;n=t.deltaY?t.deltaY<0?1:-1:0;H(e,r={originalEvent:t,eventType:"wheel",pointerType:"mouse",isEmulated:t!==i});if(e.scrollHandler&&!r.preventGesture&&!r.defaultPrevented){o={eventSource:e,pointerType:"mouse",position:w(t,e.element),scroll:n,shift:t.shiftKey,isTouchEvent:!1,originalEvent:i,preventDefault:r.preventDefault||r.defaultPrevented,userData:e.userData};e.scrollHandler(o)}r.stopPropagation&&c.stopEvent(i);(o&&o.preventDefault||r.preventDefault&&!r.defaultPrevented)&&c.cancelEvent(i)}function S(e,t){var i={id:m(t),type:v(t),isPrimary:f(t),currentPos:y(t),currentTime:c.now()};t={originalEvent:t,eventType:"pointerenter",pointerType:i.type,isEmulated:!1};H(e,t);A(e,t,i)}function P(e,t){var i={id:m(t),type:v(t),isPrimary:f(t),currentPos:y(t),currentTime:c.now()};t={originalEvent:t,eventType:"pointerleave",pointerType:i.type,isEmulated:!1};H(e,t);M(e,t,i)}function R(e,t){var i={id:m(t),type:v(t),isPrimary:f(t),currentPos:y(t),currentTime:c.now()};var n={originalEvent:t,eventType:"pointerover",pointerType:i.type,isEmulated:!1};H(e,n);!function(e,t,i){var n,r;n=e.getActivePointersListByType(i.type);if(r=n.getById(i.id))i=r;else{i.captured=!1;i.insideElementPressed=!1}e.overHandler&&e.overHandler({eventSource:e,pointerType:i.type,position:T(i.currentPos,e.element),buttons:n.buttons,pointers:e.getActivePointerCount(),insideElementPressed:i.insideElementPressed,buttonDownAny:0!==n.buttons,isTouchEvent:"touch"===i.type,originalEvent:t.originalEvent,userData:e.userData})}(e,n,i);n.preventDefault&&!n.defaultPrevented&&c.cancelEvent(t);n.stopPropagation&&c.stopEvent(t)}function b(e,t){var i={id:m(t),type:v(t),isPrimary:f(t),currentPos:y(t),currentTime:c.now()};var n={originalEvent:t,eventType:"pointerout",pointerType:i.type,isEmulated:!1};H(e,n);!function(e,t,i){var n,r;n=e.getActivePointersListByType(i.type);if(r=n.getById(i.id))i=r;else{i.captured=!1;i.insideElementPressed=!1}e.outHandler&&e.outHandler({eventSource:e,pointerType:i.type,position:i.currentPos&&T(i.currentPos,e.element),buttons:n.buttons,pointers:e.getActivePointerCount(),insideElementPressed:i.insideElementPressed,buttonDownAny:0!==n.buttons,isTouchEvent:"touch"===i.type,originalEvent:t.originalEvent,userData:e.userData})}(e,n,i);n.preventDefault&&!n.defaultPrevented&&c.cancelEvent(t);n.stopPropagation&&c.stopEvent(t)}function C(e,t){var i={id:m(t),type:v(t),isPrimary:f(t),currentPos:y(t),currentTime:c.now()};var n=c.MouseTracker.havePointerEvents&&"touch"===i.type;var r={originalEvent:t,eventType:"pointerdown",pointerType:i.type,isEmulated:!1};H(e,r);L(e,r,i,t.button);r.preventDefault&&!r.defaultPrevented&&c.cancelEvent(t);r.stopPropagation&&c.stopEvent(t);r.shouldCapture&&(n?z(e,i,!0):function(e,t){var i;if(c.MouseTracker.havePointerCapture)if(c.MouseTracker.havePointerEvents)try{e.element.setPointerCapture(t.id)}catch(e){c.console.warn("setPointerCapture() called on invalid pointer ID");return}else e.element.setCapture(!0);else{i=p(e,c.MouseTracker.havePointerEvents?"pointerevent":t.type);o&&s(window.top)&&c.addEvent(window.top,i.upName,i.upHandler,!0);c.addEvent(c.MouseTracker.captureElement,i.upName,i.upHandler,!0);c.addEvent(c.MouseTracker.captureElement,i.moveName,i.moveHandler,!0)}z(e,t,!0)}(e,i))}function D(e,t){I(e,t)}function I(e,t){var i;var n={originalEvent:t,eventType:"pointerup",pointerType:(i={id:m(t),type:v(t),isPrimary:f(t),currentPos:y(t),currentTime:c.now()}).type,isEmulated:!1};H(e,n);N(e,n,i,t.button);n.preventDefault&&!n.defaultPrevented&&c.cancelEvent(t);n.stopPropagation&&c.stopEvent(t);n.shouldReleaseCapture&&(t.target===e.element?g(e,i):z(e,i,!1))}function O(e,t){B(e,t)}function B(e,t){var i={id:m(t),type:v(t),isPrimary:f(t),currentPos:y(t),currentTime:c.now()};var n={originalEvent:t,eventType:"pointermove",pointerType:i.type,isEmulated:!1};H(e,n);U(e,n,i);n.preventDefault&&!n.defaultPrevented&&c.cancelEvent(t);n.stopPropagation&&c.stopEvent(t)}function k(e,t){t.speed=0;t.direction=0;t.contactPos=t.currentPos;t.contactTime=t.currentTime;t.lastPos=t.currentPos;t.lastTime=t.currentTime;return e.add(t)}function F(e,t,i){var n;var r=t.getById(i.id);if(r){if(r.captured){c.console.warn("stopTrackingPointer() called on captured pointer");g(e,r)}t.removeContact();n=t.removeById(i.id)}else n=t.getLength();return n}function H(e,t){t.eventSource=e;t.eventPhase=t.originalEvent&&void 0!==t.originalEvent.eventPhase?t.originalEvent.eventPhase:0;t.defaultPrevented=c.eventIsCanceled(t.originalEvent);t.shouldCapture=!1;t.shouldReleaseCapture=!1;t.userData=e.userData;!function(e,t){switch(t.eventType){case"pointermove":t.isStoppable=!0;t.isCancelable=!0;t.preventDefault=!1;t.preventGesture=!e.hasGestureHandlers;t.stopPropagation=!1;break;case"pointerover":case"pointerout":case"contextmenu":case"keydown":case"keyup":case"keypress":t.isStoppable=!0;t.isCancelable=!0;t.preventDefault=!1;t.preventGesture=!1;t.stopPropagation=!1;break;case"pointerdown":case"pointerup":t.isStoppable=!0;t.isCancelable=!0;t.preventDefault=!1;t.preventGesture=!e.hasGestureHandlers;t.stopPropagation=!1;break;case"wheel":t.isStoppable=!0;t.isCancelable=!0;t.preventDefault=!1;t.preventGesture=!e.hasScrollHandler;t.stopPropagation=!1;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":t.isStoppable=!0;t.isCancelable=!1;t.preventDefault=!1;t.preventGesture=!1;t.stopPropagation=!1;break;case"click":t.isStoppable=!0;t.isCancelable=!0;t.preventDefault=!!e.clickHandler;t.preventGesture=!1;t.stopPropagation=!1;break;case"dblclick":t.isStoppable=!0;t.isCancelable=!0;t.preventDefault=!!e.dblClickHandler;t.preventGesture=!1;t.stopPropagation=!1;break;default:t.isStoppable=!1;t.isCancelable=!1;t.preventDefault=!1;t.preventGesture=!1;t.stopPropagation=!1}}(e,t);e.preProcessEventHandler&&e.preProcessEventHandler(t)}function z(e,t,i){e=e.getActivePointersListByType(t.type);t=e.getById(t.id);if(t){if(i&&!t.captured){t.captured=!0;e.captureCount++}else if(!i&&t.captured){t.captured=!1;e.captureCount--;if(e.captureCount<0){e.captureCount=0;c.console.warn("updatePointerCaptured() - pointsList.captureCount went negative")}}}else c.console.warn("updatePointerCaptured() called on untracked pointer")}function A(e,t,i){var n,r=e.getActivePointersListByType(i.type);if(n=r.getById(i.id)){n.insideElement=!0;n.lastPos=n.currentPos;n.lastTime=n.currentTime;n.currentPos=i.currentPos;n.currentTime=i.currentTime;i=n}else{i.captured=!1;i.insideElementPressed=!1;i.insideElement=!0;k(r,i)}e.enterHandler&&e.enterHandler({eventSource:e,pointerType:i.type,position:T(i.currentPos,e.element),buttons:r.buttons,pointers:e.getActivePointerCount(),insideElementPressed:i.insideElementPressed,buttonDownAny:0!==r.buttons,isTouchEvent:"touch"===i.type,originalEvent:t.originalEvent,userData:e.userData})}function M(e,t,i){var n,r=e.getActivePointersListByType(i.type);if(n=r.getById(i.id)){if(n.captured){n.insideElement=!1;n.lastPos=n.currentPos;n.lastTime=n.currentTime;n.currentPos=i.currentPos;n.currentTime=i.currentTime}else F(e,r,n);i=n}else{i.captured=!1;i.insideElementPressed=!1}if(e.leaveHandler||e.exitHandler){t={eventSource:e,pointerType:i.type,position:i.currentPos&&T(i.currentPos,e.element),buttons:r.buttons,pointers:e.getActivePointerCount(),insideElementPressed:i.insideElementPressed,buttonDownAny:0!==r.buttons,isTouchEvent:"touch"===i.type,originalEvent:t.originalEvent,userData:e.userData};e.leaveHandler&&e.leaveHandler(t);e.exitHandler&&e.exitHandler(t)}}function L(e,t,i,n){var r,o=u[e.hash],s=e.getActivePointersListByType(i.type);void 0!==t.originalEvent.buttons?s.buttons=t.originalEvent.buttons:0===n?s.buttons|=1:1===n?s.buttons|=4:2===n?s.buttons|=2:3===n?s.buttons|=8:4===n?s.buttons|=16:5===n&&(s.buttons|=32);if(0===n){if(r=s.getById(i.id)){r.insideElementPressed=!0;r.insideElement=!0;r.originalTarget=t.originalEvent.target;r.contactPos=i.currentPos;r.contactTime=i.currentTime;r.lastPos=r.currentPos;r.lastTime=r.currentTime;r.currentPos=i.currentPos;r.currentTime=i.currentTime;i=r}else{i.captured=!1;i.insideElementPressed=!0;i.insideElement=!0;i.originalTarget=t.originalEvent.target;k(s,i)}s.addContact();if(t.preventGesture||t.defaultPrevented){t.shouldCapture=!1;t.shouldReleaseCapture=!1}else{t.shouldCapture=!0;t.shouldReleaseCapture=!1;t.preventDefault=!0;(e.dragHandler||e.dragEndHandler||e.pinchHandler)&&c.MouseTracker.gesturePointVelocityTracker.addPoint(e,i);if(1===s.contacts)e.pressHandler&&!t.preventGesture&&e.pressHandler({eventSource:e,pointerType:i.type,position:T(i.contactPos,e.element),buttons:s.buttons,isTouchEvent:"touch"===i.type,originalEvent:t.originalEvent,userData:e.userData});else if(2===s.contacts&&e.pinchHandler&&"touch"===i.type){o.pinchGPoints=s.asArray();o.lastPinchDist=o.currentPinchDist=o.pinchGPoints[0].currentPos.distanceTo(o.pinchGPoints[1].currentPos);o.lastPinchCenter=o.currentPinchCenter=x(o.pinchGPoints[0].currentPos,o.pinchGPoints[1].currentPos)}}}else{t.shouldCapture=!1;t.shouldReleaseCapture=!1;if(e.nonPrimaryPressHandler&&!t.preventGesture&&!t.defaultPrevented){t.preventDefault=!0;e.nonPrimaryPressHandler({eventSource:e,pointerType:i.type,position:T(i.currentPos,e.element),button:n,buttons:s.buttons,isTouchEvent:"touch"===i.type,originalEvent:t.originalEvent,userData:e.userData})}}}function N(e,t,i,n){var r,o,s,a=u[e.hash],l=e.getActivePointersListByType(i.type),h=!1;void 0!==t.originalEvent.buttons?l.buttons=t.originalEvent.buttons:0===n?l.buttons^=-2:1===n?l.buttons^=-5:2===n?l.buttons^=-3:3===n?l.buttons^=-9:4===n?l.buttons^=-17:5===n&&(l.buttons^=-33);t.shouldCapture=!1;if(0===n){if(o=l.getById(i.id)){l.removeContact();o.captured&&(h=!0);o.lastPos=o.currentPos;o.lastTime=o.currentTime;o.currentPos=i.currentPos;o.currentTime=i.currentTime;o.insideElement||F(e,l,o);r=o.currentPos;s=o.currentTime}else{i.captured=!1;i.insideElementPressed=!1;i.insideElement=!0;k(l,i);o=i}if(!t.preventGesture&&!t.defaultPrevented)if(h){t.shouldReleaseCapture=!0;t.preventDefault=!0;(e.dragHandler||e.dragEndHandler||e.pinchHandler)&&c.MouseTracker.gesturePointVelocityTracker.removePoint(e,o);if(0===l.contacts){e.releaseHandler&&r&&e.releaseHandler({eventSource:e,pointerType:o.type,position:T(r,e.element),buttons:l.buttons,insideElementPressed:o.insideElementPressed,insideElementReleased:o.insideElement,isTouchEvent:"touch"===o.type,originalEvent:t.originalEvent,userData:e.userData});e.dragEndHandler&&a.sentDragEvent&&e.dragEndHandler({eventSource:e,pointerType:o.type,position:T(o.currentPos,e.element),speed:o.speed,direction:o.direction,shift:t.originalEvent.shiftKey,isTouchEvent:"touch"===o.type,originalEvent:t.originalEvent,userData:e.userData});a.sentDragEvent=!1;if((e.clickHandler||e.dblClickHandler)&&o.insideElement){s=s-o.contactTime<=e.clickTimeThreshold&&o.contactPos.distanceTo(r)<=e.clickDistThreshold;e.clickHandler&&e.clickHandler({eventSource:e,pointerType:o.type,position:T(o.currentPos,e.element),quick:s,shift:t.originalEvent.shiftKey,isTouchEvent:"touch"===o.type,originalEvent:t.originalEvent,originalTarget:o.originalTarget,userData:e.userData});if(e.dblClickHandler&&s){l.clicks++;if(1===l.clicks){a.lastClickPos=r;a.dblClickTimeOut=setTimeout(function(){l.clicks=0},e.dblClickTimeThreshold)}else if(2===l.clicks){clearTimeout(a.dblClickTimeOut);l.clicks=0;a.lastClickPos.distanceTo(r)<=e.dblClickDistThreshold&&e.dblClickHandler({eventSource:e,pointerType:o.type,position:T(o.currentPos,e.element),shift:t.originalEvent.shiftKey,isTouchEvent:"touch"===o.type,originalEvent:t.originalEvent,userData:e.userData});a.lastClickPos=null}}}}else if(2===l.contacts&&e.pinchHandler&&"touch"===o.type){a.pinchGPoints=l.asArray();a.lastPinchDist=a.currentPinchDist=a.pinchGPoints[0].currentPos.distanceTo(a.pinchGPoints[1].currentPos);a.lastPinchCenter=a.currentPinchCenter=x(a.pinchGPoints[0].currentPos,a.pinchGPoints[1].currentPos)}}else{t.shouldReleaseCapture=!1;if(e.releaseHandler&&r){e.releaseHandler({eventSource:e,pointerType:o.type,position:T(r,e.element),buttons:l.buttons,insideElementPressed:o.insideElementPressed,insideElementReleased:o.insideElement,isTouchEvent:"touch"===o.type,originalEvent:t.originalEvent,userData:e.userData});t.preventDefault=!0}}}else{t.shouldReleaseCapture=!1;if(e.nonPrimaryReleaseHandler&&!t.preventGesture&&!t.defaultPrevented){t.preventDefault=!0;e.nonPrimaryReleaseHandler({eventSource:e,pointerType:i.type,position:T(i.currentPos,e.element),button:n,buttons:l.buttons,isTouchEvent:"touch"===i.type,originalEvent:t.originalEvent,userData:e.userData})}}}function U(n,r,o){var e,t,i=u[n.hash],s=n.getActivePointersListByType(o.type);void 0!==r.originalEvent.buttons&&(s.buttons=r.originalEvent.buttons);if(e=s.getById(o.id)){e.lastPos=e.currentPos;e.lastTime=e.currentTime;e.currentPos=o.currentPos;e.currentTime=o.currentTime;r.shouldCapture=!1;r.shouldReleaseCapture=!1;if(n.stopHandler&&"mouse"===o.type){clearTimeout(n.stopTimeOut);n.stopTimeOut=setTimeout(function(){e=n,t=r.originalEvent,i=o.type,e.stopHandler&&e.stopHandler({eventSource:e,pointerType:i,position:w(t,e.element),buttons:e.getActivePointersListByType(i).buttons,isTouchEvent:"touch"===i,originalEvent:t,userData:e.userData});var e,t,i},n.stopDelay)}if(0===s.contacts)n.moveHandler&&n.moveHandler({eventSource:n,pointerType:o.type,position:T(o.currentPos,n.element),buttons:s.buttons,isTouchEvent:"touch"===o.type,originalEvent:r.originalEvent,userData:n.userData});else if(1===s.contacts){if(n.moveHandler){e=s.asArray()[0];n.moveHandler({eventSource:n,pointerType:e.type,position:T(e.currentPos,n.element),buttons:s.buttons,isTouchEvent:"touch"===e.type,originalEvent:r.originalEvent,userData:n.userData})}if(n.dragHandler&&!r.preventGesture&&!r.defaultPrevented){t=(e=s.asArray()[0]).currentPos.minus(e.lastPos);n.dragHandler({eventSource:n,pointerType:e.type,position:T(e.currentPos,n.element),buttons:s.buttons,delta:t,speed:e.speed,direction:e.direction,shift:r.originalEvent.shiftKey,isTouchEvent:"touch"===e.type,originalEvent:r.originalEvent,userData:n.userData});r.preventDefault=!0;i.sentDragEvent=!0}}else if(2===s.contacts){if(n.moveHandler){e=s.asArray();n.moveHandler({eventSource:n,pointerType:e[0].type,position:T(x(e[0].currentPos,e[1].currentPos),n.element),buttons:s.buttons,isTouchEvent:"touch"===e[0].type,originalEvent:r.originalEvent,userData:n.userData})}if(n.pinchHandler&&"touch"===o.type&&!r.preventGesture&&!r.defaultPrevented&&(t=i.pinchGPoints[0].currentPos.distanceTo(i.pinchGPoints[1].currentPos))!==i.currentPinchDist){i.lastPinchDist=i.currentPinchDist;i.currentPinchDist=t;i.lastPinchCenter=i.currentPinchCenter;i.currentPinchCenter=x(i.pinchGPoints[0].currentPos,i.pinchGPoints[1].currentPos);n.pinchHandler({eventSource:n,pointerType:"touch",gesturePoints:i.pinchGPoints,lastCenter:T(i.lastPinchCenter,n.element),center:T(i.currentPinchCenter,n.element),lastDistance:i.lastPinchDist,distance:i.currentPinchDist,shift:r.originalEvent.shiftKey,originalEvent:r.originalEvent,userData:n.userData});r.preventDefault=!0}}}}function W(e,t,i){var n=e.getActivePointersListByType(i.type);(i=n.getById(i.id))&&F(e,n,i)}}(OpenSeadragon);!function(r){r.ControlAnchor={NONE:0,TOP_LEFT:1,TOP_RIGHT:2,BOTTOM_RIGHT:3,BOTTOM_LEFT:4,ABSOLUTE:5};r.Control=function(e,t,i){var n=e.parentNode;if("number"==typeof t){r.console.error("Passing an anchor directly into the OpenSeadragon.Control constructor is deprecated; please use an options object instead.  Support for this deprecated variant is scheduled for removal in December 2013");t={anchor:t}}t.attachToViewer=void 0===t.attachToViewer||t.attachToViewer;this.autoFade=void 0===t.autoFade||t.autoFade;this.element=e;this.anchor=t.anchor;this.container=i;if(this.anchor===r.ControlAnchor.ABSOLUTE){this.wrapper=r.makeNeutralElement("div");this.wrapper.style.position="absolute";this.wrapper.style.top="number"==typeof t.top?t.top+"px":t.top;this.wrapper.style.left="number"==typeof t.left?t.left+"px":t.left;this.wrapper.style.height="number"==typeof t.height?t.height+"px":t.height;this.wrapper.style.width="number"==typeof t.width?t.width+"px":t.width;this.wrapper.style.margin="0px";this.wrapper.style.padding="0px";this.element.style.position="relative";this.element.style.top="0px";this.element.style.left="0px";this.element.style.height="100%";this.element.style.width="100%"}else{this.wrapper=r.makeNeutralElement("div");this.wrapper.style.display="inline-block";this.anchor===r.ControlAnchor.NONE&&(this.wrapper.style.width=this.wrapper.style.height="100%")}this.wrapper.appendChild(this.element);t.attachToViewer?this.anchor===r.ControlAnchor.TOP_RIGHT||this.anchor===r.ControlAnchor.BOTTOM_RIGHT?this.container.insertBefore(this.wrapper,this.container.firstChild):this.container.appendChild(this.wrapper):n.appendChild(this.wrapper)};r.Control.prototype={destroy:function(){this.wrapper.removeChild(this.element);this.anchor!==r.ControlAnchor.NONE&&this.container.removeChild(this.wrapper)},isVisible:function(){return"none"!==this.wrapper.style.display},setVisible:function(e){this.wrapper.style.display=e?this.anchor===r.ControlAnchor.ABSOLUTE?"block":"inline-block":"none"},setOpacity:function(e){r.setElementOpacity(this.wrapper,e,!0)}}}(OpenSeadragon);!function(r){r.ControlDock=function(e){var t,i,n=["topleft","topright","bottomright","bottomleft"];r.extend(!0,this,{id:"controldock-"+r.now()+"-"+Math.floor(1e6*Math.random()),container:r.makeNeutralElement("div"),controls:[]},e);this.container.onsubmit=function(){return!1};if(this.element){this.element=r.getElement(this.element);this.element.appendChild(this.container);"static"===r.getElementStyle(this.element).position&&(this.element.style.position="relative");this.container.style.width="100%";this.container.style.height="100%"}for(i=0;i<n.length;i++){this.controls[t=n[i]]=r.makeNeutralElement("div");this.controls[t].style.position="absolute";t.match("left")&&(this.controls[t].style.left="0px");t.match("right")&&(this.controls[t].style.right="0px");t.match("top")&&(this.controls[t].style.top="0px");t.match("bottom")&&(this.controls[t].style.bottom="0px")}this.container.appendChild(this.controls.topleft);this.container.appendChild(this.controls.topright);this.container.appendChild(this.controls.bottomright);this.container.appendChild(this.controls.bottomleft)};r.ControlDock.prototype={addControl:function(e,t){var i=null;if(!(0<=n(this,e=r.getElement(e)))){switch(t.anchor){case r.ControlAnchor.TOP_RIGHT:i=this.controls.topright;e.style.position="relative";e.style.paddingRight="0px";e.style.paddingTop="0px";break;case r.ControlAnchor.BOTTOM_RIGHT:i=this.controls.bottomright;e.style.position="relative";e.style.paddingRight="0px";e.style.paddingBottom="0px";break;case r.ControlAnchor.BOTTOM_LEFT:i=this.controls.bottomleft;e.style.position="relative";e.style.paddingLeft="0px";e.style.paddingBottom="0px";break;case r.ControlAnchor.TOP_LEFT:i=this.controls.topleft;e.style.position="relative";e.style.paddingLeft="0px";e.style.paddingTop="0px";break;case r.ControlAnchor.ABSOLUTE:i=this.container;e.style.margin="0px";e.style.padding="0px";break;default:case r.ControlAnchor.NONE:i=this.container;e.style.margin="0px";e.style.padding="0px"}this.controls.push(new r.Control(e,t,i));e.style.display="inline-block"}},removeControl:function(e){e=n(this,e=r.getElement(e));if(0<=e){this.controls[e].destroy();this.controls.splice(e,1)}return this},clearControls:function(){for(;0<this.controls.length;)this.controls.pop().destroy();return this},areControlsEnabled:function(){var e;for(e=this.controls.length-1;0<=e;e--)if(this.controls[e].isVisible())return!0;return!1},setControlsEnabled:function(e){var t;for(t=this.controls.length-1;0<=t;t--)this.controls[t].setVisible(e);return this}};function n(e,t){var i,n=e.controls;for(i=n.length-1;0<=i;i--)if(n[i].element===t)return i;return-1}}(OpenSeadragon);!function(e){e.Placement=e.freezeObject({CENTER:0,TOP_LEFT:1,TOP:2,TOP_RIGHT:3,RIGHT:4,BOTTOM_RIGHT:5,BOTTOM:6,BOTTOM_LEFT:7,LEFT:8,properties:{0:{isLeft:!1,isHorizontallyCentered:!0,isRight:!1,isTop:!1,isVerticallyCentered:!0,isBottom:!1},1:{isLeft:!0,isHorizontallyCentered:!1,isRight:!1,isTop:!0,isVerticallyCentered:!1,isBottom:!1},2:{isLeft:!1,isHorizontallyCentered:!0,isRight:!1,isTop:!0,isVerticallyCentered:!1,isBottom:!1},3:{isLeft:!1,isHorizontallyCentered:!1,isRight:!0,isTop:!0,isVerticallyCentered:!1,isBottom:!1},4:{isLeft:!1,isHorizontallyCentered:!1,isRight:!0,isTop:!1,isVerticallyCentered:!0,isBottom:!1},5:{isLeft:!1,isHorizontallyCentered:!1,isRight:!0,isTop:!1,isVerticallyCentered:!1,isBottom:!0},6:{isLeft:!1,isHorizontallyCentered:!0,isRight:!1,isTop:!1,isVerticallyCentered:!1,isBottom:!0},7:{isLeft:!0,isHorizontallyCentered:!1,isRight:!1,isTop:!1,isVerticallyCentered:!1,isBottom:!0},8:{isLeft:!0,isHorizontallyCentered:!1,isRight:!1,isTop:!1,isVerticallyCentered:!0,isBottom:!1}}})}(OpenSeadragon);!function(m){var c={};var s=1;m.Viewer=function(i){var e,t=arguments,n=this;if((i=!m.isPlainObject(i)?{id:t[0],xmlPath:1<t.length?t[1]:void 0,prefixUrl:2<t.length?t[2]:void 0,controls:3<t.length?t[3]:void 0,overlays:4<t.length?t[4]:void 0}:i).config){m.extend(!0,i,i.config);delete i.config}i.drawerOptions=Object.assign({},["useCanvas"].reduce((e,t)=>{e[t]=i[t];delete i[t];return e},{}),i.drawerOptions);m.extend(!0,this,{id:i.id,hash:i.hash||s++,initialPage:0,element:null,container:null,canvas:null,overlays:[],overlaysContainer:null,previousBody:[],customControls:[],source:null,drawer:null,world:null,viewport:null,navigator:null,collectionViewport:null,collectionDrawer:null,navImages:null,buttonGroup:null,profiler:null},m.DEFAULT_SETTINGS,i);if(void 0===this.hash)throw new Error("A hash must be defined, either by specifying options.id or options.hash.");void 0!==c[this.hash]&&m.console.warn("Hash "+this.hash+" has already been used.");c[this.hash]={fsBoundsDelta:new m.Point(1,1),prevContainerSize:null,animating:!1,forceRedraw:!1,needsResize:!1,forceResize:!1,mouseInside:!1,group:null,zooming:!1,zoomFactor:null,lastZoomTime:null,fullPage:!1,onfullscreenchange:null,lastClickTime:null,draggingToZoom:!1};this._sequenceIndex=0;this._firstOpen=!0;this._updateRequestId=null;this._loadQueue=[];this.currentOverlays=[];this._updatePixelDensityRatioBind=null;this._lastScrollTime=m.now();m.EventSource.call(this);this.addHandler("open-failed",function(e){e=m.getString("Errors.OpenFailed",e.eventSource,e.message);n._showMessage(e)});m.ControlDock.call(this,i);this.xmlPath&&(this.tileSources=[this.xmlPath]);this.element=this.element||document.getElementById(this.id);this.canvas=m.makeNeutralElement("div");this.canvas.className="openseadragon-canvas";!function(e){e.width="100%";e.height="100%";e.overflow="hidden";e.position="absolute";e.top="0px";e.left="0px"}(this.canvas.style);m.setElementTouchActionNone(this.canvas);""!==i.tabIndex&&(this.canvas.tabIndex=void 0===i.tabIndex?0:i.tabIndex);this.container.className="openseadragon-container";!function(e){e.width="100%";e.height="100%";e.position="relative";e.overflow="hidden";e.left="0px";e.top="0px";e.textAlign="left"}(this.container.style);m.setElementTouchActionNone(this.container);this.container.insertBefore(this.canvas,this.container.firstChild);this.element.appendChild(this.container);this.bodyWidth=document.body.style.width;this.bodyHeight=document.body.style.height;this.bodyOverflow=document.body.style.overflow;this.docOverflow=document.documentElement.style.overflow;this.innerTracker=new m.MouseTracker({userData:"Viewer.innerTracker",element:this.canvas,startDisabled:!this.mouseNavEnabled,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,dblClickTimeThreshold:this.dblClickTimeThreshold,dblClickDistThreshold:this.dblClickDistThreshold,contextMenuHandler:m.delegate(this,d),keyDownHandler:m.delegate(this,p),keyHandler:m.delegate(this,g),clickHandler:m.delegate(this,y),dblClickHandler:m.delegate(this,w),dragHandler:m.delegate(this,T),dragEndHandler:m.delegate(this,x),enterHandler:m.delegate(this,_),leaveHandler:m.delegate(this,E),pressHandler:m.delegate(this,S),releaseHandler:m.delegate(this,P),nonPrimaryPressHandler:m.delegate(this,R),nonPrimaryReleaseHandler:m.delegate(this,b),scrollHandler:m.delegate(this,O),pinchHandler:m.delegate(this,C),focusHandler:m.delegate(this,D),blurHandler:m.delegate(this,I)});this.outerTracker=new m.MouseTracker({userData:"Viewer.outerTracker",element:this.container,startDisabled:!this.mouseNavEnabled,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,dblClickTimeThreshold:this.dblClickTimeThreshold,dblClickDistThreshold:this.dblClickDistThreshold,enterHandler:m.delegate(this,B),leaveHandler:m.delegate(this,k)});this.toolbar&&(this.toolbar=new m.ControlDock({element:this.toolbar}));this.bindStandardControls();c[this.hash].prevContainerSize=a(this.container);if(window.ResizeObserver){this._autoResizePolling=!1;this._resizeObserver=new ResizeObserver(function(){c[n.hash].needsResize=!0});this._resizeObserver.observe(this.container,{})}else this._autoResizePolling=!0;this.world=new m.World({viewer:this});this.world.addHandler("add-item",function(e){n.source=n.world.getItemAt(0).source;c[n.hash].forceRedraw=!0;n._updateRequestId||(n._updateRequestId=l(n,F))});this.world.addHandler("remove-item",function(e){n.world.getItemCount()?n.source=n.world.getItemAt(0).source:n.source=null;c[n.hash].forceRedraw=!0});this.world.addHandler("metrics-change",function(e){n.viewport&&n.viewport._setContentBounds(n.world.getHomeBounds(),n.world.getContentFactor())});this.world.addHandler("item-index-change",function(e){n.source=n.world.getItemAt(0).source});this.viewport=new m.Viewport({containerSize:c[this.hash].prevContainerSize,springStiffness:this.springStiffness,animationTime:this.animationTime,minZoomImageRatio:this.minZoomImageRatio,maxZoomPixelRatio:this.maxZoomPixelRatio,visibilityRatio:this.visibilityRatio,wrapHorizontal:this.wrapHorizontal,wrapVertical:this.wrapVertical,defaultZoomLevel:this.defaultZoomLevel,minZoomLevel:this.minZoomLevel,maxZoomLevel:this.maxZoomLevel,viewer:this,degrees:this.degrees,flipped:this.flipped,overlayPreserveContentDirection:this.overlayPreserveContentDirection,navigatorRotate:this.navigatorRotate,homeFillsViewer:this.homeFillsViewer,margins:this.viewportMargins,silenceMultiImageWarnings:this.silenceMultiImageWarnings});this.viewport._setContentBounds(this.world.getHomeBounds(),this.world.getContentFactor());this.imageLoader=new m.ImageLoader({jobLimit:this.imageLoaderLimit,timeout:i.timeout,tileRetryMax:this.tileRetryMax,tileRetryDelay:this.tileRetryDelay});this.tileCache=new m.TileCache({maxImageCacheCount:this.maxImageCacheCount});if(Object.prototype.hasOwnProperty.call(this.drawerOptions,"useCanvas")){m.console.error('useCanvas is deprecated, use the "drawer" option to indicate preferred drawer(s)');this.drawerOptions.useCanvas||(this.drawer=m.HTMLDrawer);delete this.drawerOptions.useCanvas}let r=Array.isArray(this.drawer)?this.drawer:[this.drawer];if(0===r.length){r=[m.DEFAULT_SETTINGS.drawer].flat();m.console.warn("No valid drawers were selected. Using the default value.")}this.drawer=null;for(const o of r)if(this.requestDrawer(o,{mainDrawer:!0,redrawImmediately:!1}))break;if(!this.drawer){m.console.error("No drawer could be created!");throw"Error with creating the selected drawer(s)"}this.drawer.setImageSmoothingEnabled(this.imageSmoothingEnabled);this.overlaysContainer=m.makeNeutralElement("div");this.canvas.appendChild(this.overlaysContainer);if(!this.drawer.canRotate()){if(this.rotateLeft){e=this.buttonGroup.buttons.indexOf(this.rotateLeft);this.buttonGroup.buttons.splice(e,1);this.buttonGroup.element.removeChild(this.rotateLeft.element)}if(this.rotateRight){e=this.buttonGroup.buttons.indexOf(this.rotateRight);this.buttonGroup.buttons.splice(e,1);this.buttonGroup.element.removeChild(this.rotateRight.element)}}this._addUpdatePixelDensityRatioEvent();this.showNavigator&&(this.navigator=new m.Navigator({element:this.navigatorElement,id:this.navigatorId,position:this.navigatorPosition,sizeRatio:this.navigatorSizeRatio,maintainSizeRatio:this.navigatorMaintainSizeRatio,top:this.navigatorTop,left:this.navigatorLeft,width:this.navigatorWidth,height:this.navigatorHeight,autoResize:this.navigatorAutoResize,autoFade:this.navigatorAutoFade,prefixUrl:this.prefixUrl,viewer:this,navigatorRotate:this.navigatorRotate,background:this.navigatorBackground,opacity:this.navigatorOpacity,borderColor:this.navigatorBorderColor,displayRegionColor:this.navigatorDisplayRegionColor,crossOriginPolicy:this.crossOriginPolicy,animationTime:this.animationTime,drawer:this.drawer.getType(),loadTilesWithAjax:this.loadTilesWithAjax,ajaxHeaders:this.ajaxHeaders,ajaxWithCredentials:this.ajaxWithCredentials}));this.sequenceMode&&this.bindSequenceControls();this.tileSources&&this.open(this.tileSources);for(e=0;e<this.customControls.length;e++)this.addControl(this.customControls[e].id,{anchor:this.customControls[e].anchor});m.requestAnimationFrame(function(){u(n)});m._viewers.set(this.element,this)};m.extend(m.Viewer.prototype,m.EventSource.prototype,m.ControlDock.prototype,{isOpen:function(){return!!this.world.getItemCount()},openDzi:function(e){m.console.error("[Viewer.openDzi] this function is deprecated; use Viewer.open() instead.");return this.open(e)},openTileSource:function(e){m.console.error("[Viewer.openTileSource] this function is deprecated; use Viewer.open() instead.");return this.open(e)},get buttons(){m.console.warn("Viewer.buttons is deprecated; Please use Viewer.buttonGroup");return this.buttonGroup},open:function(i,e){var r=this;this.close();if(!i)return this;if(this.sequenceMode&&m.isArray(i)){if(this.referenceStrip){this.referenceStrip.destroy();this.referenceStrip=null}void 0===e||isNaN(e)||(this.initialPage=e);this.tileSources=i;this._sequenceIndex=Math.max(0,Math.min(this.tileSources.length-1,this.initialPage));if(this.tileSources.length){this.open(this.tileSources[this._sequenceIndex]);this.showReferenceStrip&&this.addReferenceStrip()}this._updateSequenceButtons(this._sequenceIndex);return this}if(!(i=!m.isArray(i)?[i]:i).length)return this;this._opening=!0;var n=i.length;var o=0;var s=0;var a;var l=function(){if(o+s===n)if(o){if(r._firstOpen||!r.preserveViewport){r.viewport.goHome(!0);r.viewport.update()}r._firstOpen=!1;var e=i[0];e.tileSource&&(e=e.tileSource);if(r.overlays&&!r.preserveOverlays)for(var t=0;t<r.overlays.length;t++)r.currentOverlays[t]=h(r,r.overlays[t]);r._drawOverlays();r._opening=!1;r.raiseEvent("open",{source:e})}else{r._opening=!1;r.raiseEvent("open-failed",a)}};for(var t=0;t<i.length;t++)!function(i){if(void 0!==(i=!m.isPlainObject(i)||!i.tileSource?{tileSource:i}:i).index){m.console.error("[Viewer.open] setting indexes here is not supported; use addTiledImage instead");delete i.index}void 0===i.collectionImmediately&&(i.collectionImmediately=!0);var n=i.success;i.success=function(e){o++;if(i.tileSource.overlays)for(var t=0;t<i.tileSource.overlays.length;t++)r.addOverlay(i.tileSource.overlays[t]);n&&n(e);l()};var t=i.error;i.error=function(e){s++;a=a||e;t&&t(e);l()};r.addTiledImage(i)}(i[t]);return this},close:function(){if(!c[this.hash])return this;this._opening=!1;this.navigator&&this.navigator.close();if(!this.preserveOverlays){this.clearOverlays();this.overlaysContainer.innerHTML=""}c[this.hash].animating=!1;this.world.removeAll();this.imageLoader.clear();this.raiseEvent("close");return this},destroy:function(){if(c[this.hash]){this.raiseEvent("before-destroy");this._removeUpdatePixelDensityRatioEvent();this.close();this.clearOverlays();this.overlaysContainer.innerHTML="";this._resizeObserver&&this._resizeObserver.disconnect();if(this.referenceStrip){this.referenceStrip.destroy();this.referenceStrip=null}if(null!==this._updateRequestId){m.cancelAnimationFrame(this._updateRequestId);this._updateRequestId=null}this.drawer&&this.drawer.destroy();if(this.navigator){this.navigator.destroy();c[this.navigator.hash]=null;delete c[this.navigator.hash];this.navigator=null}if(this.buttonGroup)this.buttonGroup.destroy();else if(this.customButtons)for(;this.customButtons.length;)this.customButtons.pop().destroy();this.paging&&this.paging.destroy();if(this.element)for(;this.element.firstChild;)this.element.removeChild(this.element.firstChild);this.container.onsubmit=null;this.clearControls();this.innerTracker&&this.innerTracker.destroy();this.outerTracker&&this.outerTracker.destroy();c[this.hash]=null;delete c[this.hash];this.canvas=null;this.container=null;m._viewers.delete(this.element);this.element=null;this.raiseEvent("destroy");this.removeAllHandlers()}},requestDrawer(e,t){var i=(t=m.extend(!0,{mainDrawer:!0,redrawImmediately:!0,drawerOptions:null},t)).mainDrawer;var n=t.redrawImmediately;t=t.drawerOptions;const r=this.drawer;let o=null;if(e&&e.prototype instanceof m.DrawerBase){o=e;e="custom"}else"string"==typeof e&&(o=m.determineDrawer(e));o||m.console.warn("Unsupported drawer! Drawer must be an existing string type, or a class that extends OpenSeadragon.DrawerBase.");if(o&&o.isSupported()){r&&i&&r.destroy();e=new o({viewer:this,viewport:this.viewport,element:this.canvas,debugGridColor:this.debugGridColor,options:t||this.drawerOptions[e]});if(i){this.drawer=e;n&&this.forceRedraw()}return e}return!1},isMouseNavEnabled:function(){return this.innerTracker.isTracking()},setMouseNavEnabled:function(e){this.innerTracker.setTracking(e);this.outerTracker.setTracking(e);this.raiseEvent("mouse-enabled",{enabled:e});return this},areControlsEnabled:function(){var e,t=this.controls.length;for(e=0;e<this.controls.length;e++)t=t&&this.controls[e].isVisible();return t},setControlsEnabled:function(e){(e?n:u)(this);this.raiseEvent("controls-enabled",{enabled:e});return this},setDebugMode:function(e){for(var t=0;t<this.world.getItemCount();t++)this.world.getItemAt(t).debugMode=e;this.debugMode=e;this.forceRedraw()},setAjaxHeaders:function(e,t){if(m.isPlainObject(e=null===e?{}:e)){void 0===t&&(t=!0);this.ajaxHeaders=e;if(t){for(var i=0;i<this.world.getItemCount();i++)this.world.getItemAt(i)._updateAjaxHeaders(!0);this.navigator&&this.navigator.setAjaxHeaders(this.ajaxHeaders,!0);if(this.referenceStrip&&this.referenceStrip.miniViewers)for(var n in this.referenceStrip.miniViewers)this.referenceStrip.miniViewers[n].setAjaxHeaders(this.ajaxHeaders,!0)}}else console.error("[Viewer.setAjaxHeaders] Ignoring invalid headers, must be a plain object")},addButton:function(e){this.buttonGroup.addButton(e)},isFullPage:function(){return c[this.hash]&&c[this.hash].fullPage},setFullPage:function(e){var t,i,n=document.body,r=n.style,o=document.documentElement.style,s=this;if(e===this.isFullPage())return this;var a={fullPage:e,preventDefaultAction:!1};this.raiseEvent("pre-full-page",a);if(a.preventDefaultAction)return this;if(e&&this.element){this.elementSize=m.getElementSize(this.element);this.pageScroll=m.getPageScroll();this.elementMargin=this.element.style.margin;this.element.style.margin="0";this.elementPadding=this.element.style.padding;this.element.style.padding="0";this.bodyMargin=r.margin;this.docMargin=o.margin;r.margin="0";o.margin="0";this.bodyPadding=r.padding;this.docPadding=o.padding;r.padding="0";o.padding="0";this.bodyWidth=r.width;this.docWidth=o.width;r.width="100%";o.width="100%";this.bodyHeight=r.height;this.docHeight=o.height;r.height="100%";o.height="100%";this.bodyDisplay=r.display;r.display="block";this.previousBody=[];c[this.hash].prevElementParent=this.element.parentNode;c[this.hash].prevNextSibling=this.element.nextSibling;c[this.hash].prevElementWidth=this.element.style.width;c[this.hash].prevElementHeight=this.element.style.height;t=n.childNodes.length;for(i=0;i<t;i++){this.previousBody.push(n.childNodes[0]);n.removeChild(n.childNodes[0])}if(this.toolbar&&this.toolbar.element){this.toolbar.parentNode=this.toolbar.element.parentNode;this.toolbar.nextSibling=this.toolbar.element.nextSibling;n.appendChild(this.toolbar.element);m.addClass(this.toolbar.element,"fullpage")}m.addClass(this.element,"fullpage");n.appendChild(this.element);this.element.style.height="100vh";this.element.style.width="100vw";this.toolbar&&this.toolbar.element&&(this.element.style.height=m.getElementSize(this.element).y-m.getElementSize(this.toolbar.element).y+"px");c[this.hash].fullPage=!0;m.delegate(this,B)({})}else{this.element.style.margin=this.elementMargin;this.element.style.padding=this.elementPadding;r.margin=this.bodyMargin;o.margin=this.docMargin;r.padding=this.bodyPadding;o.padding=this.docPadding;r.width=this.bodyWidth;o.width=this.docWidth;r.height=this.bodyHeight;o.height=this.docHeight;r.display=this.bodyDisplay;n.removeChild(this.element);t=this.previousBody.length;for(i=0;i<t;i++)n.appendChild(this.previousBody.shift());m.removeClass(this.element,"fullpage");c[this.hash].prevElementParent.insertBefore(this.element,c[this.hash].prevNextSibling);if(this.toolbar&&this.toolbar.element){n.removeChild(this.toolbar.element);m.removeClass(this.toolbar.element,"fullpage");this.toolbar.parentNode.insertBefore(this.toolbar.element,this.toolbar.nextSibling);delete this.toolbar.parentNode;delete this.toolbar.nextSibling}this.element.style.width=c[this.hash].prevElementWidth;this.element.style.height=c[this.hash].prevElementHeight;var l=0;var h=function(){m.setPageScroll(s.pageScroll);var e=m.getPageScroll();++l<10&&(e.x!==s.pageScroll.x||e.y!==s.pageScroll.y)&&m.requestAnimationFrame(h)};m.requestAnimationFrame(h);c[this.hash].fullPage=!1;m.delegate(this,k)({})}this.navigator&&this.viewport&&this.navigator.update(this.viewport);this.raiseEvent("full-page",{fullPage:e});return this},setFullScreen:function(e){var t=this;if(!m.supportsFullScreen)return this.setFullPage(e);if(m.isFullScreen()===e)return this;var i={fullScreen:e,preventDefaultAction:!1};this.raiseEvent("pre-full-screen",i);if(i.preventDefaultAction)return this;if(e){this.setFullPage(!0);if(!this.isFullPage())return this;this.fullPageStyleWidth=this.element.style.width;this.fullPageStyleHeight=this.element.style.height;this.element.style.width="100%";this.element.style.height="100%";var n=function(){var e=m.isFullScreen();if(!e){m.removeEvent(document,m.fullScreenEventName,n);m.removeEvent(document,m.fullScreenErrorEventName,n);t.setFullPage(!1);if(t.isFullPage()){t.element.style.width=t.fullPageStyleWidth;t.element.style.height=t.fullPageStyleHeight}}t.navigator&&t.viewport&&setTimeout(function(){t.navigator.update(t.viewport)});t.raiseEvent("full-screen",{fullScreen:e})};m.addEvent(document,m.fullScreenEventName,n);m.addEvent(document,m.fullScreenErrorEventName,n);m.requestFullScreen(document.body)}else m.exitFullScreen();return this},isVisible:function(){return"hidden"!==this.container.style.visibility},isFullScreen:function(){return m.isFullScreen()&&this.isFullPage()},setVisible:function(e){this.container.style.visibility=e?"":"hidden";this.raiseEvent("visible",{visible:e});return this},addTiledImage:function(i){m.console.assert(i,"[Viewer.addTiledImage] options is required");m.console.assert(i.tileSource,"[Viewer.addTiledImage] options.tileSource is required");m.console.assert(!i.replace||-1<i.index&&i.index<this.world.getItemCount(),"[Viewer.addTiledImage] if options.replace is used, options.index must be a valid index in Viewer.world");var n=this;i.replace&&(i.replaceItem=n.world.getItemAt(i.index));this._hideMessage();void 0===i.placeholderFillStyle&&(i.placeholderFillStyle=this.placeholderFillStyle);void 0===i.opacity&&(i.opacity=this.opacity);void 0===i.preload&&(i.preload=this.preload);void 0===i.compositeOperation&&(i.compositeOperation=this.compositeOperation);void 0===i.crossOriginPolicy&&(i.crossOriginPolicy=(void 0!==i.tileSource.crossOriginPolicy?i.tileSource:this).crossOriginPolicy);void 0===i.ajaxWithCredentials&&(i.ajaxWithCredentials=this.ajaxWithCredentials);void 0===i.loadTilesWithAjax&&(i.loadTilesWithAjax=this.loadTilesWithAjax);m.isPlainObject(i.ajaxHeaders)||(i.ajaxHeaders={});var r={options:i};function t(e){for(var t=0;t<n._loadQueue.length;t++)if(n._loadQueue[t]===r){n._loadQueue.splice(t,1);break}0===n._loadQueue.length&&o(r);n.raiseEvent("add-item-failed",e);i.error&&i.error(e)}function o(e){if(n.collectionMode){n.world.arrange({immediately:e.options.collectionImmediately,rows:n.collectionRows,columns:n.collectionColumns,layout:n.collectionLayout,tileSize:n.collectionTileSize,tileMargin:n.collectionTileMargin});n.world.setAutoRefigureSizes(!0)}}if(m.isArray(i.tileSource))setTimeout(function(){t({message:"[Viewer.addTiledImage] Sequences can not be added; add them one at a time instead.",source:i.tileSource,options:i})});else{this._loadQueue.push(r);!function(i,n,r,o,s){var a=i;if("string"===m.type(n))if(n.match(/^\s*<.*>\s*$/))n=m.parseXml(n);else if(n.match(/^\s*[{[].*[}\]]\s*$/))try{var e=m.parseJSON(n);n=e}catch(e){}function l(e,t){if(e.ready)o(e);else{e.addHandler("ready",function(){o(e)});e.addHandler("open-failed",function(e){s({message:e.message,source:t})})}}setTimeout(function(){if("string"===m.type(n))(n=new m.TileSource({url:n,crossOriginPolicy:(void 0!==r.crossOriginPolicy?r:i).crossOriginPolicy,ajaxWithCredentials:i.ajaxWithCredentials,ajaxHeaders:r.ajaxHeaders||i.ajaxHeaders,splitHashDataForPost:i.splitHashDataForPost,success:function(e){o(e.tileSource)}})).addHandler("open-failed",function(e){s(e)});else if(m.isPlainObject(n)||n.nodeType){void 0!==n.crossOriginPolicy||void 0===r.crossOriginPolicy&&void 0===i.crossOriginPolicy||(n.crossOriginPolicy=(void 0!==r.crossOriginPolicy?r:i).crossOriginPolicy);void 0===n.ajaxWithCredentials&&(n.ajaxWithCredentials=i.ajaxWithCredentials);if(m.isFunction(n.getTileUrl)){var e=new m.TileSource(n);e.getTileUrl=n.getTileUrl;o(e)}else{var t=m.TileSource.determineType(a,n);if(t){e=t.prototype.configure.apply(a,[n]);l(new t(e),n)}else s({message:"Unable to load TileSource",source:n})}}else l(n,n)})}(this,i.tileSource,i,function(e){r.tileSource=e;s()},function(e){e.options=i;t(e);s()})}function s(){var e,t;for(;n._loadQueue.length&&(e=n._loadQueue[0]).tileSource;){n._loadQueue.splice(0,1);if(e.options.replace){var i=n.world.getIndexOfItem(e.options.replaceItem);-1!==i&&(e.options.index=i);n.world.removeItem(e.options.replaceItem)}t=new m.TiledImage({viewer:n,source:e.tileSource,viewport:n.viewport,drawer:n.drawer,tileCache:n.tileCache,imageLoader:n.imageLoader,x:e.options.x,y:e.options.y,width:e.options.width,height:e.options.height,fitBounds:e.options.fitBounds,fitBoundsPlacement:e.options.fitBoundsPlacement,clip:e.options.clip,placeholderFillStyle:e.options.placeholderFillStyle,opacity:e.options.opacity,preload:e.options.preload,degrees:e.options.degrees,flipped:e.options.flipped,compositeOperation:e.options.compositeOperation,springStiffness:n.springStiffness,animationTime:n.animationTime,minZoomImageRatio:n.minZoomImageRatio,wrapHorizontal:n.wrapHorizontal,wrapVertical:n.wrapVertical,maxTilesPerFrame:n.maxTilesPerFrame,immediateRender:n.immediateRender,blendTime:n.blendTime,alwaysBlend:n.alwaysBlend,minPixelRatio:n.minPixelRatio,smoothTileEdgesMinZoom:n.smoothTileEdgesMinZoom,iOSDevice:n.iOSDevice,crossOriginPolicy:e.options.crossOriginPolicy,ajaxWithCredentials:e.options.ajaxWithCredentials,loadTilesWithAjax:e.options.loadTilesWithAjax,ajaxHeaders:e.options.ajaxHeaders,debugMode:n.debugMode,subPixelRoundingForTransparency:n.subPixelRoundingForTransparency});n.collectionMode&&n.world.setAutoRefigureSizes(!1);if(n.navigator){i=m.extend({},e.options,{replace:!1,originalTiledImage:t,tileSource:e.tileSource});n.navigator.addTiledImage(i)}n.world.addItem(t,{index:e.options.index});0===n._loadQueue.length&&o(e);1!==n.world.getItemCount()||n.preserveViewport||n.viewport.goHome(!0);e.options.success&&e.options.success({item:t})}}},addSimpleImage:function(e){m.console.assert(e,"[Viewer.addSimpleImage] options is required");m.console.assert(e.url,"[Viewer.addSimpleImage] options.url is required");e=m.extend({},e,{tileSource:{type:"image",url:e.url}});delete e.url;this.addTiledImage(e)},addLayer:function(t){var i=this;m.console.error("[Viewer.addLayer] this function is deprecated; use Viewer.addTiledImage() instead.");var e=m.extend({},t,{success:function(e){i.raiseEvent("add-layer",{options:t,drawer:e.item})},error:function(e){i.raiseEvent("add-layer-failed",e)}});this.addTiledImage(e);return this},getLayerAtLevel:function(e){m.console.error("[Viewer.getLayerAtLevel] this function is deprecated; use World.getItemAt() instead.");return this.world.getItemAt(e)},getLevelOfLayer:function(e){m.console.error("[Viewer.getLevelOfLayer] this function is deprecated; use World.getIndexOfItem() instead.");return this.world.getIndexOfItem(e)},getLayersCount:function(){m.console.error("[Viewer.getLayersCount] this function is deprecated; use World.getItemCount() instead.");return this.world.getItemCount()},setLayerLevel:function(e,t){m.console.error("[Viewer.setLayerLevel] this function is deprecated; use World.setItemIndex() instead.");return this.world.setItemIndex(e,t)},removeLayer:function(e){m.console.error("[Viewer.removeLayer] this function is deprecated; use World.removeItem() instead.");return this.world.removeItem(e)},forceRedraw:function(){c[this.hash].forceRedraw=!0;return this},forceResize:function(){c[this.hash].needsResize=!0;c[this.hash].forceResize=!0},bindSequenceControls:function(){var e=m.delegate(this,v),t=m.delegate(this,f),i=m.delegate(this,this.goToNextPage),n=m.delegate(this,this.goToPreviousPage),r=this.navImages,o=!0;if(this.showSequenceControl){(this.previousButton||this.nextButton)&&(o=!1);this.previousButton=new m.Button({element:this.previousButton?m.getElement(this.previousButton):null,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,tooltip:m.getString("Tooltips.PreviousPage"),srcRest:H(this.prefixUrl,r.previous.REST),srcGroup:H(this.prefixUrl,r.previous.GROUP),srcHover:H(this.prefixUrl,r.previous.HOVER),srcDown:H(this.prefixUrl,r.previous.DOWN),onRelease:n,onFocus:e,onBlur:t});this.nextButton=new m.Button({element:this.nextButton?m.getElement(this.nextButton):null,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,tooltip:m.getString("Tooltips.NextPage"),srcRest:H(this.prefixUrl,r.next.REST),srcGroup:H(this.prefixUrl,r.next.GROUP),srcHover:H(this.prefixUrl,r.next.HOVER),srcDown:H(this.prefixUrl,r.next.DOWN),onRelease:i,onFocus:e,onBlur:t});this.navPrevNextWrap||this.previousButton.disable();this.tileSources&&this.tileSources.length||this.nextButton.disable();if(o){this.paging=new m.ButtonGroup({buttons:[this.previousButton,this.nextButton],clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold});this.pagingControl=this.paging.element;this.toolbar?this.toolbar.addControl(this.pagingControl,{anchor:m.ControlAnchor.BOTTOM_RIGHT}):this.addControl(this.pagingControl,{anchor:this.sequenceControlAnchor||m.ControlAnchor.TOP_LEFT})}}return this},bindStandardControls:function(){var e=m.delegate(this,z),t=m.delegate(this,M),i=m.delegate(this,L),n=m.delegate(this,A),r=m.delegate(this,N),o=m.delegate(this,W),s=m.delegate(this,G),a=m.delegate(this,V),l=m.delegate(this,j),h=m.delegate(this,q),c=m.delegate(this,v),u=m.delegate(this,f),d=this.navImages,p=[],g=!0;if(this.showNavigationControl){(this.zoomInButton||this.zoomOutButton||this.homeButton||this.fullPageButton||this.rotateLeftButton||this.rotateRightButton||this.flipButton)&&(g=!1);if(this.showZoomControl){p.push(this.zoomInButton=new m.Button({element:this.zoomInButton?m.getElement(this.zoomInButton):null,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,tooltip:m.getString("Tooltips.ZoomIn"),srcRest:H(this.prefixUrl,d.zoomIn.REST),srcGroup:H(this.prefixUrl,d.zoomIn.GROUP),srcHover:H(this.prefixUrl,d.zoomIn.HOVER),srcDown:H(this.prefixUrl,d.zoomIn.DOWN),onPress:e,onRelease:t,onClick:i,onEnter:e,onExit:t,onFocus:c,onBlur:u}));p.push(this.zoomOutButton=new m.Button({element:this.zoomOutButton?m.getElement(this.zoomOutButton):null,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,tooltip:m.getString("Tooltips.ZoomOut"),srcRest:H(this.prefixUrl,d.zoomOut.REST),srcGroup:H(this.prefixUrl,d.zoomOut.GROUP),srcHover:H(this.prefixUrl,d.zoomOut.HOVER),srcDown:H(this.prefixUrl,d.zoomOut.DOWN),onPress:n,onRelease:t,onClick:r,onEnter:n,onExit:t,onFocus:c,onBlur:u}))}this.showHomeControl&&p.push(this.homeButton=new m.Button({element:this.homeButton?m.getElement(this.homeButton):null,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,tooltip:m.getString("Tooltips.Home"),srcRest:H(this.prefixUrl,d.home.REST),srcGroup:H(this.prefixUrl,d.home.GROUP),srcHover:H(this.prefixUrl,d.home.HOVER),srcDown:H(this.prefixUrl,d.home.DOWN),onRelease:o,onFocus:c,onBlur:u}));this.showFullPageControl&&p.push(this.fullPageButton=new m.Button({element:this.fullPageButton?m.getElement(this.fullPageButton):null,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,tooltip:m.getString("Tooltips.FullPage"),srcRest:H(this.prefixUrl,d.fullpage.REST),srcGroup:H(this.prefixUrl,d.fullpage.GROUP),srcHover:H(this.prefixUrl,d.fullpage.HOVER),srcDown:H(this.prefixUrl,d.fullpage.DOWN),onRelease:s,onFocus:c,onBlur:u}));if(this.showRotationControl){p.push(this.rotateLeftButton=new m.Button({element:this.rotateLeftButton?m.getElement(this.rotateLeftButton):null,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,tooltip:m.getString("Tooltips.RotateLeft"),srcRest:H(this.prefixUrl,d.rotateleft.REST),srcGroup:H(this.prefixUrl,d.rotateleft.GROUP),srcHover:H(this.prefixUrl,d.rotateleft.HOVER),srcDown:H(this.prefixUrl,d.rotateleft.DOWN),onRelease:a,onFocus:c,onBlur:u}));p.push(this.rotateRightButton=new m.Button({element:this.rotateRightButton?m.getElement(this.rotateRightButton):null,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,tooltip:m.getString("Tooltips.RotateRight"),srcRest:H(this.prefixUrl,d.rotateright.REST),srcGroup:H(this.prefixUrl,d.rotateright.GROUP),srcHover:H(this.prefixUrl,d.rotateright.HOVER),srcDown:H(this.prefixUrl,d.rotateright.DOWN),onRelease:l,onFocus:c,onBlur:u}))}this.showFlipControl&&p.push(this.flipButton=new m.Button({element:this.flipButton?m.getElement(this.flipButton):null,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,tooltip:m.getString("Tooltips.Flip"),srcRest:H(this.prefixUrl,d.flip.REST),srcGroup:H(this.prefixUrl,d.flip.GROUP),srcHover:H(this.prefixUrl,d.flip.HOVER),srcDown:H(this.prefixUrl,d.flip.DOWN),onRelease:h,onFocus:c,onBlur:u}));if(g){this.buttonGroup=new m.ButtonGroup({buttons:p,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold});this.navControl=this.buttonGroup.element;this.addHandler("open",m.delegate(this,U));(this.toolbar||this).addControl(this.navControl,{anchor:this.navigationControlAnchor||m.ControlAnchor.TOP_LEFT})}else this.customButtons=p}return this},currentPage:function(){return this._sequenceIndex},goToPage:function(e){if(this.tileSources&&0<=e&&e<this.tileSources.length){this._sequenceIndex=e;this._updateSequenceButtons(e);this.open(this.tileSources[e]);this.referenceStrip&&this.referenceStrip.setFocus(e);this.raiseEvent("page",{page:e})}return this},addOverlay:function(e,t,i,n){i=m.isPlainObject(e)?e:{element:e,location:t,placement:i,onDraw:n};e=m.getElement(i.element);if(0<=r(this.currentOverlays,e))return this;n=h(this,i);this.currentOverlays.push(n);n.drawHTML(this.overlaysContainer,this.viewport);this.raiseEvent("add-overlay",{element:e,location:i.location,placement:i.placement});return this},updateOverlay:function(e,t,i){var n;e=m.getElement(e);if(0<=(n=r(this.currentOverlays,e))){this.currentOverlays[n].update(t,i);c[this.hash].forceRedraw=!0;this.raiseEvent("update-overlay",{element:e,location:t,placement:i})}return this},removeOverlay:function(e){var t;e=m.getElement(e);if(0<=(t=r(this.currentOverlays,e))){this.currentOverlays[t].destroy();this.currentOverlays.splice(t,1);c[this.hash].forceRedraw=!0;this.raiseEvent("remove-overlay",{element:e})}return this},clearOverlays:function(){for(;0<this.currentOverlays.length;)this.currentOverlays.pop().destroy();c[this.hash].forceRedraw=!0;this.raiseEvent("clear-overlay",{});return this},getOverlayById:function(e){e=m.getElement(e);return 0<=(e=r(this.currentOverlays,e))?this.currentOverlays[e]:null},_updateSequenceButtons:function(e){this.nextButton&&(this.tileSources&&this.tileSources.length-1!==e?this.nextButton.enable():this.navPrevNextWrap||this.nextButton.disable());this.previousButton&&(0<e?this.previousButton.enable():this.navPrevNextWrap||this.previousButton.disable())},_showMessage:function(e){this._hideMessage();var t=m.makeNeutralElement("div");t.appendChild(document.createTextNode(e));this.messageDiv=m.makeCenteredNode(t);m.addClass(this.messageDiv,"openseadragon-message");this.container.appendChild(this.messageDiv)},_hideMessage:function(){var e=this.messageDiv;if(e){e.parentNode.removeChild(e);delete this.messageDiv}},gestureSettingsByDeviceType:function(e){switch(e){case"mouse":return this.gestureSettingsMouse;case"touch":return this.gestureSettingsTouch;case"pen":return this.gestureSettingsPen;default:return this.gestureSettingsUnknown}},_drawOverlays:function(){var e,t=this.currentOverlays.length;for(e=0;e<t;e++)this.currentOverlays[e].drawHTML(this.overlaysContainer,this.viewport)},_cancelPendingImages:function(){this._loadQueue=[]},removeReferenceStrip:function(){this.showReferenceStrip=!1;if(this.referenceStrip){this.referenceStrip.destroy();this.referenceStrip=null}},addReferenceStrip:function(){this.showReferenceStrip=!0;if(this.sequenceMode){if(!this.referenceStrip&&this.tileSources.length&&1<this.tileSources.length){this.referenceStrip=new m.ReferenceStrip({id:this.referenceStripElement,position:this.referenceStripPosition,sizeRatio:this.referenceStripSizeRatio,scroll:this.referenceStripScroll,height:this.referenceStripHeight,width:this.referenceStripWidth,tileSources:this.tileSources,prefixUrl:this.prefixUrl,viewer:this});this.referenceStrip.setFocus(this._sequenceIndex)}}else m.console.warn('Attempting to display a reference strip while "sequenceMode" is off.')},_addUpdatePixelDensityRatioEvent:function(){this._updatePixelDensityRatioBind=this._updatePixelDensityRatio.bind(this);m.addEvent(window,"resize",this._updatePixelDensityRatioBind)},_removeUpdatePixelDensityRatioEvent:function(){m.removeEvent(window,"resize",this._updatePixelDensityRatioBind)},_updatePixelDensityRatio:function(){var e=m.pixelDensityRatio;var t=m.getCurrentPixelDensityRatio();if(e!==t){m.pixelDensityRatio=t;this.forceResize()}},goToPreviousPage:function(){var e=this._sequenceIndex-1;this.navPrevNextWrap&&e<0&&(e+=this.tileSources.length);this.goToPage(e)},goToNextPage:function(){var e=this._sequenceIndex+1;this.navPrevNextWrap&&e>=this.tileSources.length&&(e=0);this.goToPage(e)},isAnimating:function(){return c[this.hash].animating}});function a(e){e=m.getElement(e);return new m.Point(0===e.clientWidth?1:e.clientWidth,0===e.clientHeight?1:e.clientHeight)}function h(e,t){if(t instanceof m.Overlay)return t;var i=null;if(t.element)i=m.getElement(t.element);else{var n=t.id||"openseadragon-overlay-"+Math.floor(1e7*Math.random());(i=m.getElement(t.id))||((i=document.createElement("a")).href="#/overlay/"+n);i.id=n;m.addClass(i,t.className||"openseadragon-overlay")}var r=t.location;var o=t.width;var s=t.height;if(!r){n=t.x;var a=t.y;if(void 0!==t.px){e=e.viewport.imageToViewportRectangle(new m.Rect(t.px,t.py,o||0,s||0));n=e.x;a=e.y;o=void 0!==o?e.width:void 0;s=void 0!==s?e.height:void 0}r=new m.Point(n,a)}a=t.placement;a&&"string"===m.type(a)&&(a=m.Placement[t.placement.toUpperCase()]);return new m.Overlay({element:i,location:r,placement:a,onDraw:t.onDraw,checkResize:t.checkResize,width:o,height:s,rotationMode:t.rotationMode})}function r(e,t){var i;for(i=e.length-1;0<=i;i--)if(e[i].element===t)return i;return-1}function l(e,t){return m.requestAnimationFrame(function(){t(e)})}function o(e){m.requestAnimationFrame(function(){!function(e){var t,i,n;if(e.controlsShouldFade){t=m.now();t=t-e.controlsFadeBeginTime;i=1-t/e.controlsFadeLength;i=Math.min(1,i);i=Math.max(0,i);for(n=e.controls.length-1;0<=n;n--)e.controls[n].autoFade&&e.controls[n].setOpacity(i);0<i&&o(e)}}(e)})}function u(e){if(e.autoHideControls){e.controlsShouldFade=!0;e.controlsFadeBeginTime=m.now()+e.controlsFadeDelay;window.setTimeout(function(){o(e)},e.controlsFadeDelay)}}function n(e){var t;e.controlsShouldFade=!1;for(t=e.controls.length-1;0<=t;t--)e.controls[t].setOpacity(1)}function v(){n(this)}function f(){u(this)}function d(e){var t={tracker:e.eventSource,position:e.position,originalEvent:e.originalEvent,preventDefault:e.preventDefault};this.raiseEvent("canvas-contextmenu",t);e.preventDefault=t.preventDefault}function p(e){var t={originalEvent:e.originalEvent,preventDefaultAction:!1,preventVerticalPan:e.preventVerticalPan||!this.panVertical,preventHorizontalPan:e.preventHorizontalPan||!this.panHorizontal};this.raiseEvent("canvas-key",t);if(t.preventDefaultAction||e.ctrl||e.alt||e.meta)e.preventDefault=!1;else switch(e.keyCode){case 38:if(!t.preventVerticalPan){e.shift?this.viewport.zoomBy(1.1):this.viewport.panBy(this.viewport.deltaPointsFromPixels(new m.Point(0,-this.pixelsPerArrowPress)));this.viewport.applyConstraints()}e.preventDefault=!0;break;case 40:if(!t.preventVerticalPan){e.shift?this.viewport.zoomBy(.9):this.viewport.panBy(this.viewport.deltaPointsFromPixels(new m.Point(0,this.pixelsPerArrowPress)));this.viewport.applyConstraints()}e.preventDefault=!0;break;case 37:if(!t.preventHorizontalPan){this.viewport.panBy(this.viewport.deltaPointsFromPixels(new m.Point(-this.pixelsPerArrowPress,0)));this.viewport.applyConstraints()}e.preventDefault=!0;break;case 39:if(!t.preventHorizontalPan){this.viewport.panBy(this.viewport.deltaPointsFromPixels(new m.Point(this.pixelsPerArrowPress,0)));this.viewport.applyConstraints()}e.preventDefault=!0;break;case 187:this.viewport.zoomBy(1.1);this.viewport.applyConstraints();e.preventDefault=!0;break;case 189:this.viewport.zoomBy(.9);this.viewport.applyConstraints();e.preventDefault=!0;break;case 48:this.viewport.goHome();this.viewport.applyConstraints();e.preventDefault=!0;break;case 87:if(!t.preventVerticalPan){e.shift?this.viewport.zoomBy(1.1):this.viewport.panBy(this.viewport.deltaPointsFromPixels(new m.Point(0,-40)));this.viewport.applyConstraints()}e.preventDefault=!0;break;case 83:if(!t.preventVerticalPan){e.shift?this.viewport.zoomBy(.9):this.viewport.panBy(this.viewport.deltaPointsFromPixels(new m.Point(0,40)));this.viewport.applyConstraints()}e.preventDefault=!0;break;case 65:if(!t.preventHorizontalPan){this.viewport.panBy(this.viewport.deltaPointsFromPixels(new m.Point(-40,0)));this.viewport.applyConstraints()}e.preventDefault=!0;break;case 68:if(!t.preventHorizontalPan){this.viewport.panBy(this.viewport.deltaPointsFromPixels(new m.Point(40,0)));this.viewport.applyConstraints()}e.preventDefault=!0;break;case 82:e.shift?this.viewport.flipped?this.viewport.setRotation(this.viewport.getRotation()+this.rotationIncrement):this.viewport.setRotation(this.viewport.getRotation()-this.rotationIncrement):this.viewport.flipped?this.viewport.setRotation(this.viewport.getRotation()-this.rotationIncrement):this.viewport.setRotation(this.viewport.getRotation()+this.rotationIncrement);this.viewport.applyConstraints();e.preventDefault=!0;break;case 70:this.viewport.toggleFlip();e.preventDefault=!0;break;case 74:this.goToPreviousPage();break;case 75:this.goToNextPage();break;default:e.preventDefault=!1}}function g(e){e={originalEvent:e.originalEvent};this.raiseEvent("canvas-key-press",e)}function y(e){document.activeElement===this.canvas||this.canvas.focus();this.viewport.flipped&&(e.position.x=this.viewport.getContainerSize().x-e.position.x);var t={tracker:e.eventSource,position:e.position,quick:e.quick,shift:e.shift,originalEvent:e.originalEvent,originalTarget:e.originalTarget,preventDefaultAction:!1};this.raiseEvent("canvas-click",t);if(!t.preventDefaultAction&&this.viewport&&e.quick){if(!0===(t=this.gestureSettingsByDeviceType(e.pointerType)).clickToZoom){this.viewport.zoomBy(e.shift?1/this.zoomPerClick:this.zoomPerClick,t.zoomToRefPoint?this.viewport.pointFromPixel(e.position,!0):null);this.viewport.applyConstraints()}if(t.dblClickDragToZoom)if(!0===c[this.hash].draggingToZoom){c[this.hash].lastClickTime=null;c[this.hash].draggingToZoom=!1}else c[this.hash].lastClickTime=m.now()}}function w(e){var t;var i={tracker:e.eventSource,position:e.position,shift:e.shift,originalEvent:e.originalEvent,preventDefaultAction:!1};this.raiseEvent("canvas-double-click",i);if(!i.preventDefaultAction&&this.viewport&&(t=this.gestureSettingsByDeviceType(e.pointerType)).dblClickToZoom){this.viewport.zoomBy(e.shift?1/this.zoomPerClick:this.zoomPerClick,t.zoomToRefPoint?this.viewport.pointFromPixel(e.position,!0):null);this.viewport.applyConstraints()}}function T(e){var t;var i={tracker:e.eventSource,pointerType:e.pointerType,position:e.position,delta:e.delta,speed:e.speed,direction:e.direction,shift:e.shift,originalEvent:e.originalEvent,preventDefaultAction:!1};this.raiseEvent("canvas-drag",i);t=this.gestureSettingsByDeviceType(e.pointerType);if(!i.preventDefaultAction&&this.viewport)if(t.dblClickDragToZoom&&c[this.hash].draggingToZoom){var n=Math.pow(this.zoomPerDblClickDrag,e.delta.y/50);this.viewport.zoomBy(n)}else if(t.dragToPan&&!c[this.hash].draggingToZoom){this.panHorizontal||(e.delta.x=0);this.panVertical||(e.delta.y=0);this.viewport.flipped&&(e.delta.x=-e.delta.x);if(this.constrainDuringPan){i=this.viewport.deltaPointsFromPixels(e.delta.negate());this.viewport.centerSpringX.target.value+=i.x;this.viewport.centerSpringY.target.value+=i.y;n=this.viewport.getConstrainedBounds();this.viewport.centerSpringX.target.value-=i.x;this.viewport.centerSpringY.target.value-=i.y;n.xConstrained&&(e.delta.x=0);n.yConstrained&&(e.delta.y=0)}this.viewport.panBy(this.viewport.deltaPointsFromPixels(e.delta.negate()),t.flickEnabled&&!this.constrainDuringPan)}}function x(e){var t;var i={tracker:e.eventSource,pointerType:e.pointerType,position:e.position,speed:e.speed,direction:e.direction,shift:e.shift,originalEvent:e.originalEvent,preventDefaultAction:!1};this.raiseEvent("canvas-drag-end",i);t=this.gestureSettingsByDeviceType(e.pointerType);if(!i.preventDefaultAction&&this.viewport){if(!c[this.hash].draggingToZoom&&t.dragToPan&&t.flickEnabled&&e.speed>=t.flickMinSpeed){var n=0;this.panHorizontal&&(n=t.flickMomentum*e.speed*Math.cos(e.direction));i=0;this.panVertical&&(i=t.flickMomentum*e.speed*Math.sin(e.direction));e=this.viewport.pixelFromPoint(this.viewport.getCenter(!0));i=this.viewport.pointFromPixel(new m.Point(e.x-n,e.y-i));this.viewport.panTo(i,!1)}this.viewport.applyConstraints()}t.dblClickDragToZoom&&!0===c[this.hash].draggingToZoom&&(c[this.hash].draggingToZoom=!1)}function _(e){this.raiseEvent("canvas-enter",{tracker:e.eventSource,pointerType:e.pointerType,position:e.position,buttons:e.buttons,pointers:e.pointers,insideElementPressed:e.insideElementPressed,buttonDownAny:e.buttonDownAny,originalEvent:e.originalEvent})}function E(e){this.raiseEvent("canvas-exit",{tracker:e.eventSource,pointerType:e.pointerType,position:e.position,buttons:e.buttons,pointers:e.pointers,insideElementPressed:e.insideElementPressed,buttonDownAny:e.buttonDownAny,originalEvent:e.originalEvent})}function S(e){this.raiseEvent("canvas-press",{tracker:e.eventSource,pointerType:e.pointerType,position:e.position,insideElementPressed:e.insideElementPressed,insideElementReleased:e.insideElementReleased,originalEvent:e.originalEvent});if(this.gestureSettingsByDeviceType(e.pointerType).dblClickDragToZoom){var t=c[this.hash].lastClickTime;e=m.now();if(null!==t){e-t<this.dblClickTimeThreshold&&(c[this.hash].draggingToZoom=!0);c[this.hash].lastClickTime=null}}}function P(e){this.raiseEvent("canvas-release",{tracker:e.eventSource,pointerType:e.pointerType,position:e.position,insideElementPressed:e.insideElementPressed,insideElementReleased:e.insideElementReleased,originalEvent:e.originalEvent})}function R(e){this.raiseEvent("canvas-nonprimary-press",{tracker:e.eventSource,position:e.position,pointerType:e.pointerType,button:e.button,buttons:e.buttons,originalEvent:e.originalEvent})}function b(e){this.raiseEvent("canvas-nonprimary-release",{tracker:e.eventSource,position:e.position,pointerType:e.pointerType,button:e.button,buttons:e.buttons,originalEvent:e.originalEvent})}function C(e){var t,i;var n={tracker:e.eventSource,pointerType:e.pointerType,gesturePoints:e.gesturePoints,lastCenter:e.lastCenter,center:e.center,lastDistance:e.lastDistance,distance:e.distance,shift:e.shift,originalEvent:e.originalEvent,preventDefaultPanAction:!1,preventDefaultZoomAction:!1,preventDefaultRotateAction:!1};this.raiseEvent("canvas-pinch",n);if(this.viewport){if((r=this.gestureSettingsByDeviceType(e.pointerType)).pinchToZoom&&(!n.preventDefaultPanAction||!n.preventDefaultZoomAction)){t=this.viewport.pointFromPixel(e.center,!0);if(r.zoomToRefPoint&&!n.preventDefaultPanAction){i=this.viewport.pointFromPixel(e.lastCenter,!0).minus(t);this.panHorizontal||(i.x=0);this.panVertical||(i.y=0);this.viewport.panBy(i,!0)}n.preventDefaultZoomAction||this.viewport.zoomBy(e.distance/e.lastDistance,t,!0);this.viewport.applyConstraints()}if(r.pinchRotate&&!n.preventDefaultRotateAction){var r=Math.atan2(e.gesturePoints[0].currentPos.y-e.gesturePoints[1].currentPos.y,e.gesturePoints[0].currentPos.x-e.gesturePoints[1].currentPos.x);n=Math.atan2(e.gesturePoints[0].lastPos.y-e.gesturePoints[1].lastPos.y,e.gesturePoints[0].lastPos.x-e.gesturePoints[1].lastPos.x);t=this.viewport.pointFromPixel(e.center,!0);this.viewport.rotateTo(this.viewport.getRotation(!0)+(r-n)*(180/Math.PI),t,!0)}}}function D(e){this.raiseEvent("canvas-focus",{tracker:e.eventSource,originalEvent:e.originalEvent})}function I(e){this.raiseEvent("canvas-blur",{tracker:e.eventSource,originalEvent:e.originalEvent})}function O(e){var t,i,n;if((n=m.now())-this._lastScrollTime>this.minScrollDeltaTime){this._lastScrollTime=n;t={tracker:e.eventSource,position:e.position,scroll:e.scroll,shift:e.shift,originalEvent:e.originalEvent,preventDefaultAction:!1,preventDefault:!0};this.raiseEvent("canvas-scroll",t);if(!t.preventDefaultAction&&this.viewport){this.viewport.flipped&&(e.position.x=this.viewport.getContainerSize().x-e.position.x);if((i=this.gestureSettingsByDeviceType(e.pointerType)).scrollToZoom){n=Math.pow(this.zoomPerScroll,e.scroll);this.viewport.zoomBy(n,i.zoomToRefPoint?this.viewport.pointFromPixel(e.position,!0):null);this.viewport.applyConstraints()}}e.preventDefault=t.preventDefault}else e.preventDefault=!0}function B(e){c[this.hash].mouseInside=!0;n(this);this.raiseEvent("container-enter",{tracker:e.eventSource,pointerType:e.pointerType,position:e.position,buttons:e.buttons,pointers:e.pointers,insideElementPressed:e.insideElementPressed,buttonDownAny:e.buttonDownAny,originalEvent:e.originalEvent})}function k(e){if(e.pointers<1){c[this.hash].mouseInside=!1;c[this.hash].animating||u(this)}this.raiseEvent("container-exit",{tracker:e.eventSource,pointerType:e.pointerType,position:e.position,buttons:e.buttons,pointers:e.pointers,insideElementPressed:e.insideElementPressed,buttonDownAny:e.buttonDownAny,originalEvent:e.originalEvent})}function F(e){!function(e){if(!e._opening&&c[e.hash]){if(e.autoResize||c[e.hash].forceResize){if(e._autoResizePolling){i=a(e.container);var t=c[e.hash].prevContainerSize;i.equals(t)||(c[e.hash].needsResize=!0)}c[e.hash].needsResize&&function(e,t){var i=e.viewport;var n=i.getZoom();var r=i.getCenter();i.resize(t,e.preserveImageSizeOnResize);i.panTo(r,!0);var o;if(e.preserveImageSizeOnResize)o=c[e.hash].prevContainerSize.x/t.x;else{var s=new m.Point(0,0);r=new m.Point(c[e.hash].prevContainerSize.x,c[e.hash].prevContainerSize.y).distanceTo(s);s=new m.Point(t.x,t.y).distanceTo(s);o=s/r*c[e.hash].prevContainerSize.x/t.x}i.zoomTo(n*o,null,!0);c[e.hash].prevContainerSize=t;c[e.hash].forceRedraw=!0;c[e.hash].needsResize=!1;c[e.hash].forceResize=!1}(e,i||a(e.container))}t=e.viewport.update();var i=e.world.update(t)||t;t&&e.raiseEvent("viewport-change");e.referenceStrip&&(i=e.referenceStrip.update(e.viewport)||i);t=c[e.hash].animating;if(!t&&i){e.raiseEvent("animation-start");n(e)}t=t&&!i;t&&(c[e.hash].animating=!1);if(i||t||c[e.hash].forceRedraw||e.world.needsDraw()){!function(e){e.imageLoader.clear();e.world.draw();e.raiseEvent("update-viewport",{})}(e);e._drawOverlays();e.navigator&&e.navigator.update(e.viewport);c[e.hash].forceRedraw=!1;i&&e.raiseEvent("animation")}if(t){e.raiseEvent("animation-finish");c[e.hash].mouseInside||u(e)}c[e.hash].animating=i}}(e);e.isOpen()?e._updateRequestId=l(e,F):e._updateRequestId=!1}function H(e,t){return e?e+t:t}function z(){c[this.hash].lastZoomTime=m.now();c[this.hash].zoomFactor=this.zoomPerSecond;c[this.hash].zooming=!0;i(this)}function A(){c[this.hash].lastZoomTime=m.now();c[this.hash].zoomFactor=1/this.zoomPerSecond;c[this.hash].zooming=!0;i(this)}function M(){c[this.hash].zooming=!1}function i(e){m.requestAnimationFrame(m.delegate(e,t))}function t(){var e,t;if(c[this.hash].zooming&&this.viewport){t=(e=m.now())-c[this.hash].lastZoomTime;t=Math.pow(c[this.hash].zoomFactor,t/1e3);this.viewport.zoomBy(t);this.viewport.applyConstraints();c[this.hash].lastZoomTime=e;i(this)}}function L(){if(this.viewport){c[this.hash].zooming=!1;this.viewport.zoomBy(+this.zoomPerClick);this.viewport.applyConstraints()}}function N(){if(this.viewport){c[this.hash].zooming=!1;this.viewport.zoomBy(1/this.zoomPerClick);this.viewport.applyConstraints()}}function U(){if(this.buttonGroup){this.buttonGroup.emulateEnter();this.buttonGroup.emulateLeave()}}function W(){this.viewport&&this.viewport.goHome()}function G(){this.isFullPage()&&!m.isFullScreen()?this.setFullPage(!1):this.setFullScreen(!this.isFullPage());this.buttonGroup&&this.buttonGroup.emulateLeave();this.fullPageButton.element.focus();this.viewport&&this.viewport.applyConstraints()}function V(){if(this.viewport){var e=this.viewport.getRotation();this.viewport.flipped?e+=this.rotationIncrement:e-=this.rotationIncrement;this.viewport.setRotation(e)}}function j(){if(this.viewport){var e=this.viewport.getRotation();this.viewport.flipped?e-=this.rotationIncrement:e+=this.rotationIncrement;this.viewport.setRotation(e)}}function q(){this.viewport.toggleFlip()}m.determineDrawer=function(e){for(var t in OpenSeadragon){const i=OpenSeadragon[t],n=i.prototype;if(n&&n instanceof OpenSeadragon.DrawerBase&&m.isFunction(n.getType)&&n.getType.call(i)===e)return i}return null}}(OpenSeadragon);!function(o){o.Navigator=function(i){var e,t=i.viewer,n=this;if(i.element||i.id){if(i.element){i.id&&o.console.warn("Given option.id for Navigator was ignored since option.element was provided and is being used instead.");i.element.id?i.id=i.element.id:i.id="navigator-"+o.now();this.element=i.element}else this.element=document.getElementById(i.id);i.controlOptions={anchor:o.ControlAnchor.NONE,attachToViewer:!1,autoFade:!1}}else{i.id="navigator-"+o.now();this.element=o.makeNeutralElement("div");i.controlOptions={anchor:o.ControlAnchor.TOP_RIGHT,attachToViewer:!0,autoFade:i.autoFade};if(i.position)if("BOTTOM_RIGHT"===i.position)i.controlOptions.anchor=o.ControlAnchor.BOTTOM_RIGHT;else if("BOTTOM_LEFT"===i.position)i.controlOptions.anchor=o.ControlAnchor.BOTTOM_LEFT;else if("TOP_RIGHT"===i.position)i.controlOptions.anchor=o.ControlAnchor.TOP_RIGHT;else if("TOP_LEFT"===i.position)i.controlOptions.anchor=o.ControlAnchor.TOP_LEFT;else if("ABSOLUTE"===i.position){i.controlOptions.anchor=o.ControlAnchor.ABSOLUTE;i.controlOptions.top=i.top;i.controlOptions.left=i.left;i.controlOptions.height=i.height;i.controlOptions.width=i.width}}this.element.id=i.id;this.element.className+=" navigator";(i=o.extend(!0,{sizeRatio:o.DEFAULT_SETTINGS.navigatorSizeRatio},i,{element:this.element,tabIndex:-1,showNavigator:!1,mouseNavEnabled:!1,showNavigationControl:!1,showSequenceControl:!1,immediateRender:!0,blendTime:0,animationTime:i.animationTime,autoResize:!1,minZoomImageRatio:1,background:i.background,opacity:i.opacity,borderColor:i.borderColor,displayRegionColor:i.displayRegionColor})).minPixelRatio=this.minPixelRatio=t.minPixelRatio;o.setElementTouchActionNone(this.element);this.borderWidth=2;this.fudge=new o.Point(1,1);this.totalBorderWidths=new o.Point(2*this.borderWidth,2*this.borderWidth).minus(this.fudge);i.controlOptions.anchor!==o.ControlAnchor.NONE&&function(e,t){e.margin="0px";e.border=t+"px solid "+i.borderColor;e.padding="0px";e.background=i.background;e.opacity=i.opacity;e.overflow="hidden"}(this.element.style,this.borderWidth);this.displayRegion=o.makeNeutralElement("div");this.displayRegion.id=this.element.id+"-displayregion";this.displayRegion.className="displayregion";!function(e,t){e.position="relative";e.top="0px";e.left="0px";e.fontSize="0px";e.overflow="hidden";e.border=t+"px solid "+i.displayRegionColor;e.margin="0px";e.padding="0px";e.background="transparent";e.float="left";e.cssFloat="left";e.zIndex=999999999;e.cursor="default";e.boxSizing="content-box"}(this.displayRegion.style,this.borderWidth);o.setElementPointerEventsNone(this.displayRegion);o.setElementTouchActionNone(this.displayRegion);this.displayRegionContainer=o.makeNeutralElement("div");this.displayRegionContainer.id=this.element.id+"-displayregioncontainer";this.displayRegionContainer.className="displayregioncontainer";this.displayRegionContainer.style.width="100%";this.displayRegionContainer.style.height="100%";o.setElementPointerEventsNone(this.displayRegionContainer);o.setElementTouchActionNone(this.displayRegionContainer);t.addControl(this.element,i.controlOptions);this._resizeWithViewer=i.controlOptions.anchor!==o.ControlAnchor.ABSOLUTE&&i.controlOptions.anchor!==o.ControlAnchor.NONE;if(i.width&&i.height){this.setWidth(i.width);this.setHeight(i.height)}else if(this._resizeWithViewer){e=o.getElementSize(t.element);this.element.style.height=Math.round(e.y*i.sizeRatio)+"px";this.element.style.width=Math.round(e.x*i.sizeRatio)+"px";this.oldViewerSize=e;e=o.getElementSize(this.element);this.elementArea=e.x*e.y}this.oldContainerSize=new o.Point(0,0);o.Viewer.apply(this,[i]);this.displayRegionContainer.appendChild(this.displayRegion);this.element.getElementsByTagName("div")[0].appendChild(this.displayRegionContainer);function r(e,t){c(n.displayRegionContainer,e);c(n.displayRegion,-e);n.viewport.setRotation(e,t)}if(i.navigatorRotate){r(i.viewer.viewport?i.viewer.viewport.getRotation():i.viewer.degrees||0,!0);i.viewer.addHandler("rotate",function(e){r(e.degrees,e.immediately)})}this.innerTracker.destroy();this.innerTracker=new o.MouseTracker({userData:"Navigator.innerTracker",element:this.element,dragHandler:o.delegate(this,a),clickHandler:o.delegate(this,s),releaseHandler:o.delegate(this,l),scrollHandler:o.delegate(this,h),preProcessEventHandler:function(e){"wheel"===e.eventType&&(e.preventDefault=!0)}});this.outerTracker.userData="Navigator.outerTracker";o.setElementPointerEventsNone(this.canvas);o.setElementPointerEventsNone(this.container);this.addHandler("reset-size",function(){n.viewport&&n.viewport.goHome(!0)});t.world.addHandler("item-index-change",function(t){window.setTimeout(function(){var e=n.world.getItemAt(t.previousIndex);n.world.setItemIndex(e,t.newIndex)},1)});t.world.addHandler("remove-item",function(e){e=e.item;e=n._getMatchingItem(e);e&&n.world.removeItem(e)});this.update(t.viewport)};o.extend(o.Navigator.prototype,o.EventSource.prototype,o.Viewer.prototype,{updateSize:function(){if(this.viewport){var e=new o.Point(0===this.container.clientWidth?1:this.container.clientWidth,0===this.container.clientHeight?1:this.container.clientHeight);if(!e.equals(this.oldContainerSize)){this.viewport.resize(e,!0);this.viewport.goHome(!0);this.oldContainerSize=e;this.world.update();this.world.draw();this.update(this.viewer.viewport)}}},setWidth:function(e){this.width=e;this.element.style.width="number"==typeof e?e+"px":e;this._resizeWithViewer=!1;this.updateSize()},setHeight:function(e){this.height=e;this.element.style.height="number"==typeof e?e+"px":e;this._resizeWithViewer=!1;this.updateSize()},setFlip:function(e){this.viewport.setFlip(e);this.setDisplayTransform(this.viewer.viewport.getFlip()?"scale(-1,1)":"scale(1,1)");return this},setDisplayTransform:function(e){i(this.canvas,e);i(this.element,e)},update:function(e){var t,i;e=e||this.viewer.viewport;t=o.getElementSize(this.viewer.element);if(this._resizeWithViewer&&t.x&&t.y&&!t.equals(this.oldViewerSize)){this.oldViewerSize=t;if(this.maintainSizeRatio||!this.elementArea){i=t.x*this.sizeRatio;r=t.y*this.sizeRatio}else{i=Math.sqrt(this.elementArea*(t.x/t.y));r=this.elementArea/i}this.element.style.width=Math.round(i)+"px";this.element.style.height=Math.round(r)+"px";this.elementArea||(this.elementArea=i*r);this.updateSize()}if(e&&this.viewport){i=e.getBoundsNoRotate(!0);r=this.viewport.pixelFromPointNoRotate(i.getTopLeft(),!1);i=this.viewport.pixelFromPointNoRotate(i.getBottomRight(),!1).minus(this.totalBorderWidths);if(!this.navigatorRotate){var n=e.getRotation(!0);c(this.displayRegion,-n)}e=this.displayRegion.style;e.display=this.world.getItemCount()?"block":"none";e.top=r.y.toFixed(2)+"px";e.left=r.x.toFixed(2)+"px";n=i.x-r.x;var r=i.y-r.y;e.width=Math.round(Math.max(n,0))+"px";e.height=Math.round(Math.max(r,0))+"px"}},addTiledImage:function(e){var n=this;var r=e.originalTiledImage;delete e.original;e=o.extend({},e,{success:function(e){var t=e.item;t._originalForNavigator=r;n._matchBounds(t,r,!0);n._matchOpacity(t,r);n._matchCompositeOperation(t,r);function i(){n._matchBounds(t,r)}r.addHandler("bounds-change",i);r.addHandler("clip-change",i);r.addHandler("opacity-change",function(){n._matchOpacity(t,r)});r.addHandler("composite-operation-change",function(){n._matchCompositeOperation(t,r)})}});return o.Viewer.prototype.addTiledImage.apply(this,[e])},destroy:function(){return o.Viewer.prototype.destroy.apply(this)},_getMatchingItem:function(e){var t=this.world.getItemCount();var i;for(var n=0;n<t;n++)if((i=this.world.getItemAt(n))._originalForNavigator===e)return i;return null},_matchBounds:function(e,t,i){var n=t.getBoundsNoRotate();e.setPosition(n.getTopLeft(),i);e.setWidth(n.width,i);e.setRotation(t.getRotation(),i);e.setClip(t.getClip());e.setFlip(t.getFlip())},_matchOpacity:function(e,t){e.setOpacity(t.opacity)},_matchCompositeOperation:function(e,t){e.setCompositeOperation(t.compositeOperation)}});function s(e){var t={tracker:e.eventSource,position:e.position,quick:e.quick,shift:e.shift,originalEvent:e.originalEvent,preventDefaultAction:!1};this.viewer.raiseEvent("navigator-click",t);if(!t.preventDefaultAction&&e.quick&&this.viewer.viewport&&(this.panVertical||this.panHorizontal)){this.viewer.viewport.flipped&&(e.position.x=this.viewport.getContainerSize().x-e.position.x);e=this.viewport.pointFromPixel(e.position);this.panVertical?this.panHorizontal||(e.x=this.viewer.viewport.getCenter(!0).x):e.y=this.viewer.viewport.getCenter(!0).y;this.viewer.viewport.panTo(e);this.viewer.viewport.applyConstraints()}}function a(e){var t={tracker:e.eventSource,position:e.position,delta:e.delta,speed:e.speed,direction:e.direction,shift:e.shift,originalEvent:e.originalEvent,preventDefaultAction:!1};this.viewer.raiseEvent("navigator-drag",t);if(!t.preventDefaultAction&&this.viewer.viewport){this.panHorizontal||(e.delta.x=0);this.panVertical||(e.delta.y=0);this.viewer.viewport.flipped&&(e.delta.x=-e.delta.x);this.viewer.viewport.panBy(this.viewport.deltaPointsFromPixels(e.delta));this.viewer.constrainDuringPan&&this.viewer.viewport.applyConstraints()}}function l(e){e.insideElementPressed&&this.viewer.viewport&&this.viewer.viewport.applyConstraints()}function h(e){var t={tracker:e.eventSource,position:e.position,scroll:e.scroll,shift:e.shift,originalEvent:e.originalEvent,preventDefault:e.preventDefault};this.viewer.raiseEvent("navigator-scroll",t);e.preventDefault=t.preventDefault}function c(e,t){i(e,"rotate("+t+"deg)")}function i(e,t){e.style.webkitTransform=t;e.style.mozTransform=t;e.style.msTransform=t;e.style.oTransform=t;e.style.transform=t}}(OpenSeadragon);!function(s){var a={Errors:{Dzc:"Sorry, we don't support Deep Zoom Collections!",Dzi:"Hmm, this doesn't appear to be a valid Deep Zoom Image.",Xml:"Hmm, this doesn't appear to be a valid Deep Zoom Image.",ImageFormat:"Sorry, we don't support {0}-based Deep Zoom Images.",Security:"It looks like a security restriction stopped us from loading this Deep Zoom Image.",Status:"This space unintentionally left blank ({0} {1}).",OpenFailed:"Unable to open {0}: {1}"},Tooltips:{FullPage:"Toggle full page",Home:"Go home",ZoomIn:"Zoom in",ZoomOut:"Zoom out",NextPage:"Next page",PreviousPage:"Previous page",RotateLeft:"Rotate left",RotateRight:"Rotate right",Flip:"Flip Horizontally"}};s.extend(s,{getString:function(e){var t,i=e.split("."),n=null,r=arguments,o=a;for(t=0;t<i.length-1;t++)o=o[i[t]]||{};if("string"!=typeof(n=o[i[t]])){s.console.error("Untranslated source string:",e);n=""}return n.replace(/\{\d+\}/g,function(e){e=parseInt(e.match(/\d+/),10)+1;return e<r.length?r[e]:""})},setString:function(e,t){var i,n=e.split("."),r=a;for(i=0;i<n.length-1;i++){r[n[i]]||(r[n[i]]={});r=r[n[i]]}r[n[i]]=t}})}(OpenSeadragon);!function(o){o.Point=function(e,t){this.x="number"==typeof e?e:0;this.y="number"==typeof t?t:0};o.Point.prototype={clone:function(){return new o.Point(this.x,this.y)},plus:function(e){return new o.Point(this.x+e.x,this.y+e.y)},minus:function(e){return new o.Point(this.x-e.x,this.y-e.y)},times:function(e){return new o.Point(this.x*e,this.y*e)},divide:function(e){return new o.Point(this.x/e,this.y/e)},negate:function(){return new o.Point(-this.x,-this.y)},distanceTo:function(e){return Math.sqrt(Math.pow(this.x-e.x,2)+Math.pow(this.y-e.y,2))},squaredDistanceTo:function(e){return Math.pow(this.x-e.x,2)+Math.pow(this.y-e.y,2)},apply:function(e){return new o.Point(e(this.x),e(this.y))},equals:function(e){return e instanceof o.Point&&this.x===e.x&&this.y===e.y},rotate:function(e,t){t=t||new o.Point(0,0);var i;var n;if(e%90==0)switch(o.positiveModulo(e,360)){case 0:i=1;n=0;break;case 90:i=0;n=1;break;case 180:i=-1;n=0;break;case 270:i=0;n=-1}else{var r=e*Math.PI/180;i=Math.cos(r);n=Math.sin(r)}r=i*(this.x-t.x)-n*(this.y-t.y)+t.x;t=n*(this.x-t.x)+i*(this.y-t.y)+t.y;return new o.Point(r,t)},toString:function(){return"("+Math.round(100*this.x)/100+","+Math.round(100*this.y)/100+")"}}}(OpenSeadragon);!function(h){h.TileSource=function(e,t,i,n,r,o){var s=this;var a,l=arguments;l=h.isPlainObject(e)?e:{width:l[0],height:l[1],tileSize:l[2],tileOverlap:l[3],minLevel:l[4],maxLevel:l[5]};h.EventSource.call(this);h.extend(!0,this,l);if(!this.success)for(a=0;a<arguments.length;a++)if(h.isFunction(arguments[a])){this.success=arguments[a];break}this.success&&this.addHandler("ready",function(e){s.success(e)});"string"===h.type(e)&&(this.url=e);if(this.url){this.aspectRatio=1;this.dimensions=new h.Point(10,10);this._tileWidth=0;this._tileHeight=0;this.tileOverlap=0;this.minLevel=0;this.maxLevel=0;this.ready=!1;this.getImageInfo(this.url)}else{this.ready=!0;this.aspectRatio=l.width&&l.height?l.width/l.height:1;this.dimensions=new h.Point(l.width,l.height);if(this.tileSize){this._tileWidth=this._tileHeight=this.tileSize;delete this.tileSize}else{if(this.tileWidth){this._tileWidth=this.tileWidth;delete this.tileWidth}else this._tileWidth=0;if(this.tileHeight){this._tileHeight=this.tileHeight;delete this.tileHeight}else this._tileHeight=0}this.tileOverlap=l.tileOverlap||0;this.minLevel=l.minLevel||0;this.maxLevel=void 0!==l.maxLevel&&null!==l.maxLevel?l.maxLevel:l.width&&l.height?Math.ceil(Math.log(Math.max(l.width,l.height))/Math.log(2)):0;this.success&&h.isFunction(this.success)&&this.success(this)}};h.TileSource.prototype={getTileSize:function(e){h.console.error("[TileSource.getTileSize] is deprecated. Use TileSource.getTileWidth() and TileSource.getTileHeight() instead");return this._tileWidth},getTileWidth:function(e){return this._tileWidth||this.getTileSize(e)},getTileHeight:function(e){return this._tileHeight||this.getTileSize(e)},setMaxLevel:function(e){this.maxLevel=e;this._memoizeLevelScale()},getLevelScale:function(e){this._memoizeLevelScale();return this.getLevelScale(e)},_memoizeLevelScale:function(){var e,t={};for(e=0;e<=this.maxLevel;e++)t[e]=1/Math.pow(2,this.maxLevel-e);this.getLevelScale=function(e){return t[e]}},getNumTiles:function(e){var t=this.getLevelScale(e),i=Math.ceil(t*this.dimensions.x/this.getTileWidth(e)),e=Math.ceil(t*this.dimensions.y/this.getTileHeight(e));return new h.Point(i,e)},getPixelRatio:function(e){var t=this.dimensions.times(this.getLevelScale(e)),e=1/t.x*h.pixelDensityRatio,t=1/t.y*h.pixelDensityRatio;return new h.Point(e,t)},getClosestLevel:function(){var e,t;for(e=this.minLevel+1;e<=this.maxLevel&&!(1<(t=this.getNumTiles(e)).x||1<t.y);e++);return e-1},getTileAtPoint:function(e,t){var i=0<=t.x&&t.x<=1&&0<=t.y&&t.y<=1/this.aspectRatio;h.console.assert(i,"[TileSource.getTileAtPoint] must be called with a valid point.");var n=this.dimensions.x*this.getLevelScale(e);i=t.x*n;n=t.y*n;i=Math.floor(i/this.getTileWidth(e));n=Math.floor(n/this.getTileHeight(e));1<=t.x&&(i=this.getNumTiles(e).x-1);t.y>=1/this.aspectRatio-1e-15&&(n=this.getNumTiles(e).y-1);return new h.Point(i,n)},getTileBounds:function(e,t,i,n){var r=this.dimensions.times(this.getLevelScale(e)),o=this.getTileWidth(e),s=this.getTileHeight(e),a=0===t?0:o*t-this.tileOverlap,e=0===i?0:s*i-this.tileOverlap,t=o+(0===t?1:2)*this.tileOverlap,s=s+(0===i?1:2)*this.tileOverlap,i=1/r.x;t=Math.min(t,r.x-a);s=Math.min(s,r.y-e);return n?new h.Rect(0,0,t,s):new h.Rect(a*i,e*i,t*i,s*i)},getImageInfo:function(n){var t,i,e,r,o,s=this;n&&-1<(o=(r=(e=n.split("/"))[e.length-1]).lastIndexOf("."))&&(e[e.length-1]=r.slice(0,o));var a=null;if(this.splitHashDataForPost){var l=n.indexOf("#");if(-1!==l){a=n.substring(l+1);n=n.substr(0,l)}}t=function(e){"string"==typeof e&&(e=h.parseXml(e));var t=h.TileSource.determineType(s,e,n);if(t){void 0===(i=t.prototype.configure.apply(s,[e,n,a])).ajaxWithCredentials&&(i.ajaxWithCredentials=s.ajaxWithCredentials);i=new t(i);s.ready=!0;s.raiseEvent("ready",{tileSource:i})}else s.raiseEvent("open-failed",{message:"Unable to load TileSource",source:n})};if(n.match(/\.js$/)){l=n.split("/").pop().replace(".js","");h.jsonp({url:n,async:!1,callbackName:l,callback:t})}else h.makeAjaxRequest({url:n,postData:a,withCredentials:this.ajaxWithCredentials,headers:this.ajaxHeaders,success:function(e){e=function(t){var e,i,n=t.responseText,r=t.status;{if(!t)throw new Error(h.getString("Errors.Security"));if(200!==t.status&&0!==t.status){r=t.status;e=404===r?"Not Found":t.statusText;throw new Error(h.getString("Errors.Status",r,e))}}if(n.match(/^\s*<.*/))try{i=t.responseXML&&t.responseXML.documentElement?t.responseXML:h.parseXml(n)}catch(e){i=t.responseText}else if(n.match(/\s*[{[].*/))try{i=h.parseJSON(n)}catch(e){i=n}else i=n;return i}(e);t(e)},error:function(e,t){var i;try{i="HTTP "+e.status+" attempting to load TileSource: "+n}catch(e){i=(void 0!==t&&t.toString?t.toString():"Unknown error")+" attempting to load TileSource: "+n}h.console.error(i);s.raiseEvent("open-failed",{message:i,source:n,postData:a})}})},supports:function(e,t){return!1},configure:function(e,t,i){throw new Error("Method not implemented.")},getTileUrl:function(e,t,i){throw new Error("Method not implemented.")},getTilePostData:function(e,t,i){return null},getTileAjaxHeaders:function(e,t,i){return{}},getTileHashKey:function(e,t,i,n,r,o){function s(e){return r?e+"+"+JSON.stringify(r):e}return s("string"!=typeof n?e+"/"+t+"_"+i:n)},tileExists:function(e,t,i){var n=this.getNumTiles(e);return e>=this.minLevel&&e<=this.maxLevel&&0<=t&&0<=i&&t<n.x&&i<n.y},hasTransparency:function(e,t,i,n){return!!e||t.match(".png")},downloadTileStart:function(t){var i=t.userData,r=new Image;i.image=r;i.request=null;function o(e){if(r){r.onload=r.onerror=r.onabort=null;t.finish(e?null:r,i.request,e)}else t.finish(null,i.request,"Image load failed: undefined Image instance.")}r.onload=function(){o()};r.onabort=r.onerror=function(){o("Image load aborted.")};if(t.loadWithAjax)i.request=h.makeAjaxRequest({url:t.src,withCredentials:t.ajaxWithCredentials,headers:t.ajaxHeaders,responseType:"arraybuffer",postData:t.postData,success:function(t){var i;try{i=new window.Blob([t.response])}catch(e){var n=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder;if("TypeError"===e.name&&n){n=new n;n.append(t.response);i=n.getBlob()}}0===i.size?o("Empty image response."):r.src=(window.URL||window.webkitURL).createObjectURL(i)},error:function(e){o("Image load aborted - XHR error")}});else{!1!==t.crossOriginPolicy&&(r.crossOrigin=t.crossOriginPolicy);r.src=t.src}},downloadTileAbort:function(e){e.userData.request&&e.userData.request.abort();var t=e.userData.image;e.userData.image&&(t.onload=t.onerror=t.onabort=null)},createTileCache:function(e,t,i){e._data=t},destroyTileCache:function(e){e._data=null;e._renderedContext=null},getTileCacheData:function(e){return e._data},getTileCacheDataAsImage:function(e){return e._data},getTileCacheDataAsContext2D:function(e){if(!e._renderedContext){var t=document.createElement("canvas");t.width=e._data.width;t.height=e._data.height;e._renderedContext=t.getContext("2d");e._renderedContext.drawImage(e._data,0,0);e._data=null}return e._renderedContext}};h.extend(!0,h.TileSource.prototype,h.EventSource.prototype);h.TileSource.determineType=function(e,t,i){for(var n in OpenSeadragon)if(n.match(/.+TileSource$/)&&h.isFunction(OpenSeadragon[n])&&h.isFunction(OpenSeadragon[n].prototype.supports)&&OpenSeadragon[n].prototype.supports.call(e,t,i))return OpenSeadragon[n];h.console.error("No TileSource was able to open %s %s",i,t);return null}}(OpenSeadragon);!function(p){p.DziTileSource=function(e,t,i,n,r,o,s,a,l){var h,c,u,d;d=p.isPlainObject(e)?e:{width:e,height:t,tileSize:i,tileOverlap:n,tilesUrl:r,fileFormat:o,displayRects:s,minLevel:a,maxLevel:l};this._levelRects={};this.tilesUrl=d.tilesUrl;this.fileFormat=d.fileFormat;this.displayRects=d.displayRects;if(this.displayRects)for(h=this.displayRects.length-1;0<=h;h--)for(u=(c=this.displayRects[h]).minLevel;u<=c.maxLevel;u++){this._levelRects[u]||(this._levelRects[u]=[]);this._levelRects[u].push(c)}p.TileSource.apply(this,[d])};p.extend(p.DziTileSource.prototype,p.TileSource.prototype,{supports:function(e,t){var i;e.Image?i=e.Image.xmlns:e.documentElement&&("Image"!==e.documentElement.localName&&"Image"!==e.documentElement.tagName||(i=e.documentElement.namespaceURI));return-1!==(i=(i||"").toLowerCase()).indexOf("schemas.microsoft.com/deepzoom/2008")||-1!==i.indexOf("schemas.microsoft.com/deepzoom/2009")},configure:function(e,t,i){e=(p.isPlainObject(e)?u:function(e,t){if(!t||!t.documentElement)throw new Error(p.getString("Errors.Xml"));var i,n,r,o,s,a=t.documentElement,l=a.localName||a.tagName,h=t.documentElement.namespaceURI,t=null,c=[];if("Image"===l)try{void 0===(o=a.getElementsByTagName("Size")[0])&&(o=a.getElementsByTagNameNS(h,"Size")[0]);t={Image:{xmlns:"http://schemas.microsoft.com/deepzoom/2008",Url:a.getAttribute("Url"),Format:a.getAttribute("Format"),DisplayRect:null,Overlap:parseInt(a.getAttribute("Overlap"),10),TileSize:parseInt(a.getAttribute("TileSize"),10),Size:{Height:parseInt(o.getAttribute("Height"),10),Width:parseInt(o.getAttribute("Width"),10)}}};if(!p.imageFormatSupported(t.Image.Format))throw new Error(p.getString("Errors.ImageFormat",t.Image.Format.toUpperCase()));void 0===(i=a.getElementsByTagName("DisplayRect"))&&(i=a.getElementsByTagNameNS(h,"DisplayRect")[0]);for(s=0;s<i.length;s++){n=i[s];void 0===(r=n.getElementsByTagName("Rect")[0])&&(r=n.getElementsByTagNameNS(h,"Rect")[0]);c.push({Rect:{X:parseInt(r.getAttribute("X"),10),Y:parseInt(r.getAttribute("Y"),10),Width:parseInt(r.getAttribute("Width"),10),Height:parseInt(r.getAttribute("Height"),10),MinLevel:parseInt(n.getAttribute("MinLevel"),10),MaxLevel:parseInt(n.getAttribute("MaxLevel"),10)}})}c.length&&(t.Image.DisplayRect=c);return u(0,t)}catch(e){throw e instanceof Error?e:new Error(p.getString("Errors.Dzi"))}else{if("Collection"===l)throw new Error(p.getString("Errors.Dzc"));if("Error"===l){a=a.getElementsByTagName("Message")[0].firstChild.nodeValue;throw new Error(a)}}throw new Error(p.getString("Errors.Dzi"))})(this,e);if(t&&!e.tilesUrl){e.tilesUrl=t.replace(/([^/]+?)(\.(dzi|xml|js)?(\?[^/]*)?)?\/?$/,"$1_files/");-1!==t.search(/\.(dzi|xml|js)\?/)?e.queryParams=t.match(/\?.*/):e.queryParams=""}return e},getTileUrl:function(e,t,i){return[this.tilesUrl,e,"/",t,"_",i,".",this.fileFormat,this.queryParams].join("")},tileExists:function(e,t,i){var n,r,o,s,a,l,h=this._levelRects[e];if(this.minLevel&&e<this.minLevel||this.maxLevel&&e>this.maxLevel)return!1;if(!h||!h.length)return!0;for(l=h.length-1;0<=l;l--)if(!(e<(n=h[l]).minLevel||e>n.maxLevel)){a=this.getLevelScale(e);r=n.x*a;o=n.y*a;s=r+n.width*a;a=o+n.height*a;r=Math.floor(r/this._tileWidth);o=Math.floor(o/this._tileWidth);s=Math.ceil(s/this._tileWidth);a=Math.ceil(a/this._tileWidth);if(r<=t&&t<s&&o<=i&&i<a)return!0}return!1}});function u(e,t){var i,n,r=t.Image,o=r.Url,s=r.Format,a=r.Size,l=r.DisplayRect||[],h=parseInt(a.Width,10),c=parseInt(a.Height,10),a=parseInt(r.TileSize,10),r=parseInt(r.Overlap,10),u=[];for(n=0;n<l.length;n++){i=l[n].Rect;u.push(new p.DisplayRect(parseInt(i.X,10),parseInt(i.Y,10),parseInt(i.Width,10),parseInt(i.Height,10),parseInt(i.MinLevel,10),parseInt(i.MaxLevel,10)))}return p.extend(!0,{width:h,height:c,tileSize:a,tileOverlap:r,minLevel:null,maxLevel:null,tilesUrl:o,fileFormat:s,displayRects:u},t)}}(OpenSeadragon);!function(h){h.IIIFTileSource=function(e){h.extend(!0,this,e);this._id=this["@id"]||this.id||this.identifier||null;if(!(this.height&&this.width&&this._id))throw new Error("IIIF required parameters (width, height, or id) not provided.");e.tileSizePerScaleFactor={};this.tileFormat=this.tileFormat||"jpg";this.version=e.version;if(this.tile_width&&this.tile_height){e.tileWidth=this.tile_width;e.tileHeight=this.tile_height}else if(this.tile_width)e.tileSize=this.tile_width;else if(this.tile_height)e.tileSize=this.tile_height;else if(this.tiles)if(1===this.tiles.length){e.tileWidth=this.tiles[0].width;e.tileHeight=this.tiles[0].height||this.tiles[0].width;this.scale_factors=this.tiles[0].scaleFactors}else{this.scale_factors=[];for(var t=0;t<this.tiles.length;t++)for(var i=0;i<this.tiles[t].scaleFactors.length;i++){var n=this.tiles[t].scaleFactors[i];this.scale_factors.push(n);e.tileSizePerScaleFactor[n]={width:this.tiles[t].width,height:this.tiles[t].height||this.tiles[t].width}}}else if(c(e)){var r=Math.min(this.height,this.width),o=[256,512,1024],s=[];for(var a=0;a<o.length;a++)o[a]<=r&&s.push(o[a]);0<s.length?e.tileSize=Math.max.apply(null,s):e.tileSize=r}else if(this.sizes&&0<this.sizes.length){this.emulateLegacyImagePyramid=!0;e.levels=u(this);h.extend(!0,e,{width:e.levels[e.levels.length-1].width,height:e.levels[e.levels.length-1].height,tileSize:Math.max(e.height,e.width),tileOverlap:0,minLevel:0,maxLevel:e.levels.length-1});this.levels=e.levels}else h.console.error("Nothing in the info.json to construct image pyramids from");if(!e.maxLevel&&!this.emulateLegacyImagePyramid)if(this.scale_factors){var l=Math.max.apply(null,this.scale_factors);e.maxLevel=Math.round(Math.log(l)*Math.LOG2E)}else e.maxLevel=Number(Math.round(Math.log(Math.max(this.width,this.height),2)));if(this.sizes){l=this.sizes.length;if(l===e.maxLevel||l===e.maxLevel+1){this.levelSizes=this.sizes.slice().sort((e,t)=>e.width-t.width);l===e.maxLevel&&this.levelSizes.push({width:this.width,height:this.height})}}h.TileSource.apply(this,[e])};h.extend(h.IIIFTileSource.prototype,h.TileSource.prototype,{supports:function(e,t){return!(!e.protocol||"http://iiif.io/api/image"!==e.protocol)||(!(!e["@context"]||"http://library.stanford.edu/iiif/image-api/1.1/context.json"!==e["@context"]&&"http://iiif.io/api/image/1/context.json"!==e["@context"])||(!(!e.profile||0!==e.profile.indexOf("http://library.stanford.edu/iiif/image-api/compliance.html"))||(!!(e.identifier&&e.width&&e.height)||!(!e.documentElement||"info"!==e.documentElement.tagName||"http://library.stanford.edu/iiif/image-api/ns/"!==e.documentElement.namespaceURI))))},configure:function(e,t,i){if(h.isPlainObject(e)){if(e["@context"]){var n=e["@context"];if(Array.isArray(n))for(var r=0;r<n.length;r++)if("string"==typeof n[r]&&(/^http:\/\/iiif\.io\/api\/image\/[1-3]\/context\.json$/.test(n[r])||"http://library.stanford.edu/iiif/image-api/1.1/context.json"===n[r])){n=n[r];break}switch(n){case"http://iiif.io/api/image/1/context.json":case"http://library.stanford.edu/iiif/image-api/1.1/context.json":e.version=1;break;case"http://iiif.io/api/image/2/context.json":e.version=2;break;case"http://iiif.io/api/image/3/context.json":e.version=3;break;default:h.console.error("Data has a @context property which contains no known IIIF context URI.")}}else{e["@context"]="http://iiif.io/api/image/1.0/context.json";e["@id"]=t.replace("/info.json","");e.version=1}if(e.preferredFormats)for(var o=0;o<e.preferredFormats.length;o++)if(OpenSeadragon.imageFormatSupported(e.preferredFormats[o])){e.tileFormat=e.preferredFormats[o];break}return e}var s=function(e){if(!e||!e.documentElement)throw new Error(h.getString("Errors.Xml"));var t=e.documentElement,i=t.tagName,e=null;if("info"===i)try{!function e(t,i,n){var r,o;if(3===t.nodeType&&n){(o=t.nodeValue.trim()).match(/^\d*$/)&&(o=Number(o));if(i[n]){h.isArray(i[n])||(i[n]=[i[n]]);i[n].push(o)}else i[n]=o}else if(1===t.nodeType)for(r=0;r<t.childNodes.length;r++)e(t.childNodes[r],i,t.nodeName)}(t,e={});return e}catch(e){throw e instanceof Error?e:new Error(h.getString("Errors.IIIF"))}throw new Error(h.getString("Errors.IIIF"))}(e);s["@context"]="http://iiif.io/api/image/1.0/context.json";s["@id"]=t.replace("/info.xml","");s.version=1;return s},getTileWidth:function(e){if(this.emulateLegacyImagePyramid)return h.TileSource.prototype.getTileWidth.call(this,e);e=Math.pow(2,this.maxLevel-e);return this.tileSizePerScaleFactor&&this.tileSizePerScaleFactor[e]?this.tileSizePerScaleFactor[e].width:this._tileWidth},getTileHeight:function(e){if(this.emulateLegacyImagePyramid)return h.TileSource.prototype.getTileHeight.call(this,e);e=Math.pow(2,this.maxLevel-e);return this.tileSizePerScaleFactor&&this.tileSizePerScaleFactor[e]?this.tileSizePerScaleFactor[e].height:this._tileHeight},getLevelScale:function(e){if(this.emulateLegacyImagePyramid){var t=NaN;return t=0<this.levels.length&&e>=this.minLevel&&e<=this.maxLevel?this.levels[e].width/this.levels[this.maxLevel].width:t}return h.TileSource.prototype.getLevelScale.call(this,e)},getNumTiles:function(e){if(this.emulateLegacyImagePyramid)return this.getLevelScale(e)?new h.Point(1,1):new h.Point(0,0);if(this.levelSizes){var t=this.levelSizes[e];var i=Math.ceil(t.width/this.getTileWidth(e)),t=Math.ceil(t.height/this.getTileHeight(e));return new h.Point(i,t)}return h.TileSource.prototype.getNumTiles.call(this,e)},getTileAtPoint:function(e,t){if(this.emulateLegacyImagePyramid)return new h.Point(0,0);if(this.levelSizes){var i=0<=t.x&&t.x<=1&&0<=t.y&&t.y<=1/this.aspectRatio;h.console.assert(i,"[TileSource.getTileAtPoint] must be called with a valid point.");var n=this.levelSizes[e].width;i=t.x*n;n=t.y*n;i=Math.floor(i/this.getTileWidth(e));n=Math.floor(n/this.getTileHeight(e));1<=t.x&&(i=this.getNumTiles(e).x-1);t.y>=1/this.aspectRatio-1e-15&&(n=this.getNumTiles(e).y-1);return new h.Point(i,n)}return h.TileSource.prototype.getTileAtPoint.call(this,e,t)},getTileUrl:function(e,t,i){if(this.emulateLegacyImagePyramid){var n=null;return n=0<this.levels.length&&e>=this.minLevel&&e<=this.maxLevel?this.levels[e].url:n}var r,o,s,a,l,h,c,u,d=Math.pow(.5,this.maxLevel-e);if(this.levelSizes){r=this.levelSizes[e].width;o=this.levelSizes[e].height}else{r=Math.ceil(this.width*d);o=Math.ceil(this.height*d)}c=this.getTileWidth(e);u=this.getTileHeight(e);a=Math.round(c/d);l=Math.round(u/d);n=1===this.version?"native."+this.tileFormat:"default."+this.tileFormat;if(r<c&&o<u){h=2===this.version&&r===this.width?"full":3===this.version&&r===this.width&&o===this.height?"max":3===this.version?r+","+o:r+",";s="full"}else{e=t*a;d=i*l;a=Math.min(a,this.width-e);l=Math.min(l,this.height-d);s=0===t&&0===i&&a===this.width&&l===this.height?"full":[e,d,a,l].join(",");c=Math.min(c,r-t*c);u=Math.min(u,o-i*u);h=2===this.version&&c===this.width?"full":3===this.version&&c===this.width&&u===this.height?"max":3===this.version?c+","+u:c+","}return[this._id,s,h,"0",n].join("/")},__testonly__:{canBeTiled:c,constructLevels:u}});function c(e){var t=Array.isArray(e.profile)?e.profile[0]:e.profile;var i=-1!==["http://library.stanford.edu/iiif/image-api/compliance.html#level0","http://library.stanford.edu/iiif/image-api/1.1/compliance.html#level0","http://iiif.io/api/image/2/level0.json","level0","https://iiif.io/api/image/3/level0.json"].indexOf(t);t=!1;2===e.version&&1<e.profile.length&&e.profile[1].supports&&(t=-1!==e.profile[1].supports.indexOf("sizeByW"));3===e.version&&e.extraFeatures&&(t=-1!==e.extraFeatures.indexOf("sizeByWh"));return!i||t}function u(e){var t=[];for(var i=0;i<e.sizes.length;i++)t.push({url:e._id+"/full/"+e.sizes[i].width+","+(3===e.version?e.sizes[i].height:"")+"/0/default."+e.tileFormat,width:e.sizes[i].width,height:e.sizes[i].height});return t.sort(function(e,t){return e.width-t.width})}}(OpenSeadragon);!function(s){s.OsmTileSource=function(e,t,i,n,r){var o;if(!(o=s.isPlainObject(e)?e:{width:e,height:t,tileSize:i,tileOverlap:n,tilesUrl:r}).width||!o.height){o.width=65572864;o.height=65572864}if(!o.tileSize){o.tileSize=256;o.tileOverlap=0}o.tilesUrl||(o.tilesUrl="http://tile.openstreetmap.org/");o.minLevel=8;s.TileSource.apply(this,[o])};s.extend(s.OsmTileSource.prototype,s.TileSource.prototype,{supports:function(e,t){return e.type&&"openstreetmaps"===e.type},configure:function(e,t,i){return e},getTileUrl:function(e,t,i){return this.tilesUrl+(e-8)+"/"+t+"/"+i+".png"}})}(OpenSeadragon);!function(h){h.TmsTileSource=function(e,t,i,n,r){var o;o=h.isPlainObject(e)?e:{width:e,height:t,tileSize:i,tileOverlap:n,tilesUrl:r};var s,a=256*Math.ceil(o.width/256),l=256*Math.ceil(o.height/256);s=l<a?a/256:l/256;o.maxLevel=Math.ceil(Math.log(s)/Math.log(2))-1;o.tileSize=256;o.width=a;o.height=l;h.TileSource.apply(this,[o])};h.extend(h.TmsTileSource.prototype,h.TileSource.prototype,{supports:function(e,t){return e.type&&"tiledmapservice"===e.type},configure:function(e,t,i){return e},getTileUrl:function(e,t,i){var n=this.getNumTiles(e).y-1;return this.tilesUrl+e+"/"+t+"/"+(n-i)+".png"}})}(OpenSeadragon);!function(e){e.ZoomifyTileSource=function(e){void 0===e.tileSize&&(e.tileSize=256);if(void 0===e.fileFormat){e.fileFormat="jpg";this.fileFormat=e.fileFormat}var t={x:e.width,y:e.height};e.imageSizes=[{x:e.width,y:e.height}];e.gridSize=[this._getGridSize(e.width,e.height,e.tileSize)];for(;parseInt(t.x,10)>e.tileSize||parseInt(t.y,10)>e.tileSize;){t.x=Math.floor(t.x/2);t.y=Math.floor(t.y/2);e.imageSizes.push({x:t.x,y:t.y});e.gridSize.push(this._getGridSize(t.x,t.y,e.tileSize))}e.imageSizes.reverse();e.gridSize.reverse();e.minLevel=0;e.maxLevel=e.gridSize.length-1;OpenSeadragon.TileSource.apply(this,[e])};e.extend(e.ZoomifyTileSource.prototype,e.TileSource.prototype,{_getGridSize:function(e,t,i){return{x:Math.ceil(e/i),y:Math.ceil(t/i)}},_calculateAbsoluteTileNumber:function(e,t,i){var n=0;var r={};for(var o=0;o<e;o++)n+=(r=this.gridSize[o]).x*r.y;return n+=(r=this.gridSize[e]).x*i+t},supports:function(e,t){return e.type&&"zoomifytileservice"===e.type},configure:function(e,t,i){return e},getTileUrl:function(e,t,i){var n=this._calculateAbsoluteTileNumber(e,t,i);n=Math.floor(n/256);return this.tilesUrl+"TileGroup"+n+"/"+e+"-"+t+"-"+i+"."+this.fileFormat}})}(OpenSeadragon);!function(a){a.LegacyTileSource=function(e){var t,i,n;(t=a.isArray(e)?{type:"legacy-image-pyramid",levels:e}:t).levels=function(e){var t,i,n=[];for(i=0;i<e.length;i++)(t=e[i]).height&&t.width&&t.url?n.push({url:t.url,width:Number(t.width),height:Number(t.height)}):a.console.error("Unsupported image format: %s",t.url||"<no URL>");return n.sort(function(e,t){return e.height-t.height})}(t.levels);if(0<t.levels.length){i=t.levels[t.levels.length-1].width;n=t.levels[t.levels.length-1].height}else{n=i=0;a.console.error("No supported image formats found")}a.extend(!0,t,{width:i,height:n,tileSize:Math.max(n,i),tileOverlap:0,minLevel:0,maxLevel:0<t.levels.length?t.levels.length-1:0});a.TileSource.apply(this,[t]);this.levels=t.levels};a.extend(a.LegacyTileSource.prototype,a.TileSource.prototype,{supports:function(e,t){return e.type&&"legacy-image-pyramid"===e.type||e.documentElement&&"legacy-image-pyramid"===e.documentElement.getAttribute("type")},configure:function(e,t,i){return a.isPlainObject(e)?e.levels:function(e){if(!e||!e.documentElement)throw new Error(a.getString("Errors.Xml"));var t,i,n=e.documentElement,r=n.tagName,o=null,s=[];if("image"===r)try{o={type:n.getAttribute("type"),levels:[]};s=n.getElementsByTagName("level");for(i=0;i<s.length;i++){t=s[i];o.levels.push({url:t.getAttribute("url"),width:parseInt(t.getAttribute("width"),10),height:parseInt(t.getAttribute("height"),10)})}return o.levels}catch(e){throw e instanceof Error?e:new Error("Unknown error parsing Legacy Image Pyramid XML.")}else{if("collection"===r)throw new Error("Legacy Image Pyramid Collections not yet supported.");if("error"===r)throw new Error("Error: "+e)}throw new Error("Unknown element "+r)}(e)},getLevelScale:function(e){var t=NaN;return t=0<this.levels.length&&e>=this.minLevel&&e<=this.maxLevel?this.levels[e].width/this.levels[this.maxLevel].width:t},getNumTiles:function(e){return this.getLevelScale(e)?new a.Point(1,1):new a.Point(0,0)},getTileUrl:function(e,t,i){var n=null;return n=0<this.levels.length&&e>=this.minLevel&&e<=this.maxLevel?this.levels[e].url:n}})}(OpenSeadragon);!function(a){a.ImageTileSource=function(e){e=a.extend({buildPyramid:!0,crossOriginPolicy:!1,ajaxWithCredentials:!1},e);a.TileSource.apply(this,[e])};a.extend(a.ImageTileSource.prototype,a.TileSource.prototype,{supports:function(e,t){return e.type&&"image"===e.type},configure:function(e,t,i){return e},getImageInfo:function(e){var t=this._image=new Image;var i=this;this.crossOriginPolicy&&(t.crossOrigin=this.crossOriginPolicy);this.ajaxWithCredentials&&(t.useCredentials=this.ajaxWithCredentials);a.addEvent(t,"load",function(){i.width=t.naturalWidth;i.height=t.naturalHeight;i.aspectRatio=i.width/i.height;i.dimensions=new a.Point(i.width,i.height);i._tileWidth=i.width;i._tileHeight=i.height;i.tileOverlap=0;i.minLevel=0;i.levels=i._buildLevels();i.maxLevel=i.levels.length-1;i.ready=!0;i.raiseEvent("ready",{tileSource:i})});a.addEvent(t,"error",function(){i.raiseEvent("open-failed",{message:"Error loading image at "+e,source:e})});t.src=e},getLevelScale:function(e){var t=NaN;return t=e>=this.minLevel&&e<=this.maxLevel?this.levels[e].width/this.levels[this.maxLevel].width:t},getNumTiles:function(e){return this.getLevelScale(e)?new a.Point(1,1):new a.Point(0,0)},getTileUrl:function(e,t,i){var n=null;return n=e>=this.minLevel&&e<=this.maxLevel?this.levels[e].url:n},getContext2D:function(e,t,i){var n=null;return n=e>=this.minLevel&&e<=this.maxLevel?this.levels[e].context2D:n},destroy:function(e){this._freeupCanvasMemory(e)},_buildLevels:function(){var e=[{url:this._image.src,width:this._image.naturalWidth,height:this._image.naturalHeight}];if(!this.buildPyramid||!a.supportsCanvas){delete this._image;return e}var t=this._image.naturalWidth;var i=this._image.naturalHeight;var n=document.createElement("canvas");var r=n.getContext("2d");n.width=t;n.height=i;r.drawImage(this._image,0,0,t,i);e[0].context2D=r;delete this._image;if(a.isCanvasTainted(n))return e;for(;2<=t&&2<=i;){t=Math.floor(t/2);i=Math.floor(i/2);var o=document.createElement("canvas");var s=o.getContext("2d");o.width=t;o.height=i;s.drawImage(n,0,0,t,i);e.splice(0,0,{context2D:s,width:t,height:i});n=o;r=s}return e},_freeupCanvasMemory:function(e){for(var t=0;t<this.levels.length;t++)if(this.levels[t].context2D){this.levels[t].context2D.canvas.height=0;this.levels[t].context2D.canvas.width=0;e&&e.raiseEvent("image-unloaded",{context2D:this.levels[t].context2D})}}})}(OpenSeadragon);!function(r){r.TileSourceCollection=function(e,t,i,n){r.console.error("TileSourceCollection is deprecated; use World instead")}}(OpenSeadragon);!function(i){i.ButtonState={REST:0,GROUP:1,HOVER:2,DOWN:3};i.Button=function(e){var t=this;i.EventSource.call(this);i.extend(!0,this,{tooltip:null,srcRest:null,srcGroup:null,srcHover:null,srcDown:null,clickTimeThreshold:i.DEFAULT_SETTINGS.clickTimeThreshold,clickDistThreshold:i.DEFAULT_SETTINGS.clickDistThreshold,fadeDelay:0,fadeLength:2e3,onPress:null,onRelease:null,onClick:null,onEnter:null,onExit:null,onFocus:null,onBlur:null,userData:null},e);this.element=e.element||i.makeNeutralElement("div");if(!e.element){this.imgRest=i.makeTransparentImage(this.srcRest);this.imgGroup=i.makeTransparentImage(this.srcGroup);this.imgHover=i.makeTransparentImage(this.srcHover);this.imgDown=i.makeTransparentImage(this.srcDown);this.imgRest.alt=this.imgGroup.alt=this.imgHover.alt=this.imgDown.alt=this.tooltip;i.setElementPointerEventsNone(this.imgRest);i.setElementPointerEventsNone(this.imgGroup);i.setElementPointerEventsNone(this.imgHover);i.setElementPointerEventsNone(this.imgDown);this.element.style.position="relative";i.setElementTouchActionNone(this.element);this.imgGroup.style.position=this.imgHover.style.position=this.imgDown.style.position="absolute";this.imgGroup.style.top=this.imgHover.style.top=this.imgDown.style.top="0px";this.imgGroup.style.left=this.imgHover.style.left=this.imgDown.style.left="0px";this.imgHover.style.visibility=this.imgDown.style.visibility="hidden";this.element.appendChild(this.imgRest);this.element.appendChild(this.imgGroup);this.element.appendChild(this.imgHover);this.element.appendChild(this.imgDown)}this.addHandler("press",this.onPress);this.addHandler("release",this.onRelease);this.addHandler("click",this.onClick);this.addHandler("enter",this.onEnter);this.addHandler("exit",this.onExit);this.addHandler("focus",this.onFocus);this.addHandler("blur",this.onBlur);this.currentState=i.ButtonState.GROUP;this.fadeBeginTime=null;this.shouldFade=!1;this.element.style.display="inline-block";this.element.style.position="relative";this.element.title=this.tooltip;this.tracker=new i.MouseTracker({userData:"Button.tracker",element:this.element,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,enterHandler:function(e){if(e.insideElementPressed){r(t,i.ButtonState.DOWN);t.raiseEvent("enter",{originalEvent:e.originalEvent})}else e.buttonDownAny||r(t,i.ButtonState.HOVER)},focusHandler:function(e){t.tracker.enterHandler(e);t.raiseEvent("focus",{originalEvent:e.originalEvent})},leaveHandler:function(e){o(t,i.ButtonState.GROUP);e.insideElementPressed&&t.raiseEvent("exit",{originalEvent:e.originalEvent})},blurHandler:function(e){t.tracker.leaveHandler(e);t.raiseEvent("blur",{originalEvent:e.originalEvent})},pressHandler:function(e){r(t,i.ButtonState.DOWN);t.raiseEvent("press",{originalEvent:e.originalEvent})},releaseHandler:function(e){if(e.insideElementPressed&&e.insideElementReleased){o(t,i.ButtonState.HOVER);t.raiseEvent("release",{originalEvent:e.originalEvent})}else e.insideElementPressed?o(t,i.ButtonState.GROUP):r(t,i.ButtonState.HOVER)},clickHandler:function(e){e.quick&&t.raiseEvent("click",{originalEvent:e.originalEvent})},keyHandler:function(e){if(13===e.keyCode){t.raiseEvent("click",{originalEvent:e.originalEvent});t.raiseEvent("release",{originalEvent:e.originalEvent});e.preventDefault=!0}else e.preventDefault=!1}});o(this,i.ButtonState.REST)};i.extend(i.Button.prototype,i.EventSource.prototype,{notifyGroupEnter:function(){r(this,i.ButtonState.GROUP)},notifyGroupExit:function(){o(this,i.ButtonState.REST)},disable:function(){this.notifyGroupExit();this.element.disabled=!0;this.tracker.setTracking(!1);i.setElementOpacity(this.element,.2,!0)},enable:function(){this.element.disabled=!1;this.tracker.setTracking(!0);i.setElementOpacity(this.element,1,!0);this.notifyGroupEnter()},destroy:function(){if(this.imgRest){this.element.removeChild(this.imgRest);this.imgRest=null}if(this.imgGroup){this.element.removeChild(this.imgGroup);this.imgGroup=null}if(this.imgHover){this.element.removeChild(this.imgHover);this.imgHover=null}if(this.imgDown){this.element.removeChild(this.imgDown);this.imgDown=null}this.removeAllHandlers();this.tracker.destroy();this.element=null}});function n(e){i.requestAnimationFrame(function(){!function(e){var t;if(e.shouldFade){t=i.now();t=t-e.fadeBeginTime;t=1-t/e.fadeLength;t=Math.min(1,t);t=Math.max(0,t);e.imgGroup&&i.setElementOpacity(e.imgGroup,t,!0);0<t&&n(e)}}(e)})}function r(e,t){if(!e.element.disabled){if(t>=i.ButtonState.GROUP&&e.currentState===i.ButtonState.REST){!function(e){e.shouldFade=!1;e.imgGroup&&i.setElementOpacity(e.imgGroup,1,!0)}(e);e.currentState=i.ButtonState.GROUP}if(t>=i.ButtonState.HOVER&&e.currentState===i.ButtonState.GROUP){e.imgHover&&(e.imgHover.style.visibility="");e.currentState=i.ButtonState.HOVER}if(t>=i.ButtonState.DOWN&&e.currentState===i.ButtonState.HOVER){e.imgDown&&(e.imgDown.style.visibility="");e.currentState=i.ButtonState.DOWN}}}function o(e,t){if(!e.element.disabled){if(t<=i.ButtonState.HOVER&&e.currentState===i.ButtonState.DOWN){e.imgDown&&(e.imgDown.style.visibility="hidden");e.currentState=i.ButtonState.HOVER}if(t<=i.ButtonState.GROUP&&e.currentState===i.ButtonState.HOVER){e.imgHover&&(e.imgHover.style.visibility="hidden");e.currentState=i.ButtonState.GROUP}if(t<=i.ButtonState.REST&&e.currentState===i.ButtonState.GROUP){!function(e){e.shouldFade=!0;e.fadeBeginTime=i.now()+e.fadeDelay;window.setTimeout(function(){n(e)},e.fadeDelay)}(e);e.currentState=i.ButtonState.REST}}}}(OpenSeadragon);!function(r){r.ButtonGroup=function(e){r.extend(!0,this,{buttons:[],clickTimeThreshold:r.DEFAULT_SETTINGS.clickTimeThreshold,clickDistThreshold:r.DEFAULT_SETTINGS.clickDistThreshold,labelText:""},e);var t,i=this.buttons.concat([]),n=this;this.element=e.element||r.makeNeutralElement("div");if(!e.group){this.element.style.display="inline-block";for(t=0;t<i.length;t++)this.element.appendChild(i[t].element)}r.setElementTouchActionNone(this.element);this.tracker=new r.MouseTracker({userData:"ButtonGroup.tracker",element:this.element,clickTimeThreshold:this.clickTimeThreshold,clickDistThreshold:this.clickDistThreshold,enterHandler:function(e){var t;for(t=0;t<n.buttons.length;t++)n.buttons[t].notifyGroupEnter()},leaveHandler:function(e){var t;if(!e.insideElementPressed)for(t=0;t<n.buttons.length;t++)n.buttons[t].notifyGroupExit()}})};r.ButtonGroup.prototype={addButton:function(e){this.buttons.push(e);this.element.appendChild(e.element)},emulateEnter:function(){this.tracker.enterHandler({eventSource:this.tracker})},emulateLeave:function(){this.tracker.leaveHandler({eventSource:this.tracker})},destroy:function(){for(;this.buttons.length;){var e=this.buttons.pop();this.element.removeChild(e.element);e.destroy()}this.tracker.destroy();this.element=null}}}(OpenSeadragon);!function(v){v.Rect=function(e,t,i,n,r){this.x="number"==typeof e?e:0;this.y="number"==typeof t?t:0;this.width="number"==typeof i?i:0;this.height="number"==typeof n?n:0;this.degrees="number"==typeof r?r:0;this.degrees=v.positiveModulo(this.degrees,360);var o,s;if(270<=this.degrees){o=this.getTopRight();this.x=o.x;this.y=o.y;s=this.height;this.height=this.width;this.width=s;this.degrees-=270}else if(180<=this.degrees){o=this.getBottomRight();this.x=o.x;this.y=o.y;this.degrees-=180}else if(90<=this.degrees){o=this.getBottomLeft();this.x=o.x;this.y=o.y;s=this.height;this.height=this.width;this.width=s;this.degrees-=90}};v.Rect.fromSummits=function(e,t,i){var n=e.distanceTo(t);var r=e.distanceTo(i);i=t.minus(e);t=Math.atan(i.y/i.x);i.x<0?t+=Math.PI:i.y<0&&(t+=2*Math.PI);return new v.Rect(e.x,e.y,n,r,t/Math.PI*180)};v.Rect.prototype={clone:function(){return new v.Rect(this.x,this.y,this.width,this.height,this.degrees)},getAspectRatio:function(){return this.width/this.height},getTopLeft:function(){return new v.Point(this.x,this.y)},getBottomRight:function(){return new v.Point(this.x+this.width,this.y+this.height).rotate(this.degrees,this.getTopLeft())},getTopRight:function(){return new v.Point(this.x+this.width,this.y).rotate(this.degrees,this.getTopLeft())},getBottomLeft:function(){return new v.Point(this.x,this.y+this.height).rotate(this.degrees,this.getTopLeft())},getCenter:function(){return new v.Point(this.x+this.width/2,this.y+this.height/2).rotate(this.degrees,this.getTopLeft())},getSize:function(){return new v.Point(this.width,this.height)},equals:function(e){return e instanceof v.Rect&&this.x===e.x&&this.y===e.y&&this.width===e.width&&this.height===e.height&&this.degrees===e.degrees},times:function(e){return new v.Rect(this.x*e,this.y*e,this.width*e,this.height*e,this.degrees)},translate:function(e){return new v.Rect(this.x+e.x,this.y+e.y,this.width,this.height,this.degrees)},union:function(e){var t=this.getBoundingBox();var i=e.getBoundingBox();var n=Math.min(t.x,i.x);var r=Math.min(t.y,i.y);e=Math.max(t.x+t.width,i.x+i.width);i=Math.max(t.y+t.height,i.y+i.height);return new v.Rect(n,r,e-n,i-r)},intersection:function(e){var s=1e-10;var t=[];var i=this.getTopLeft();e.containsPoint(i,s)&&t.push(i);i=this.getTopRight();e.containsPoint(i,s)&&t.push(i);i=this.getBottomLeft();e.containsPoint(i,s)&&t.push(i);i=this.getBottomRight();e.containsPoint(i,s)&&t.push(i);i=e.getTopLeft();this.containsPoint(i,s)&&t.push(i);i=e.getTopRight();this.containsPoint(i,s)&&t.push(i);i=e.getBottomLeft();this.containsPoint(i,s)&&t.push(i);i=e.getBottomRight();this.containsPoint(i,s)&&t.push(i);var n=this._getSegments();var r=e._getSegments();for(var o=0;o<n.length;o++){var a=n[o];for(var l=0;l<r.length;l++){var h=r[l];h=function(e,t,i,n){var r=t.minus(e);var o=n.minus(i);t=-o.x*r.y+r.x*o.y;if(0==t)return null;n=(r.x*(e.y-i.y)-r.y*(e.x-i.x))/t;t=(o.x*(e.y-i.y)-o.y*(e.x-i.x))/t;if(-s<=n&&n<=1-s&&-s<=t&&t<=1-s)return new v.Point(e.x+t*r.x,e.y+t*r.y);return null}(a[0],a[1],h[0],h[1]);h&&t.push(h)}}if(0===t.length)return null;var c=t[0].x;var u=t[0].x;var d=t[0].y;var p=t[0].y;for(var g=1;g<t.length;g++){var m=t[g];m.x<c&&(c=m.x);m.x>u&&(u=m.x);m.y<d&&(d=m.y);m.y>p&&(p=m.y)}return new v.Rect(c,d,u-c,p-d)},_getSegments:function(){var e=this.getTopLeft();var t=this.getTopRight();var i=this.getBottomLeft();var n=this.getBottomRight();return[[e,t],[t,n],[n,i],[i,e]]},rotate:function(e,t){if(0===(e=v.positiveModulo(e,360)))return this.clone();t=t||this.getCenter();var i=this.getTopLeft().rotate(e,t);e=this.getTopRight().rotate(e,t).minus(i);e=e.apply(function(e){return Math.abs(e)<1e-15?0:e});t=Math.atan(e.y/e.x);e.x<0?t+=Math.PI:e.y<0&&(t+=2*Math.PI);return new v.Rect(i.x,i.y,this.width,this.height,t/Math.PI*180)},getBoundingBox:function(){if(0===this.degrees)return this.clone();var e=this.getTopLeft();var t=this.getTopRight();var i=this.getBottomLeft();var n=this.getBottomRight();var r=Math.min(e.x,t.x,i.x,n.x);var o=Math.max(e.x,t.x,i.x,n.x);var s=Math.min(e.y,t.y,i.y,n.y);n=Math.max(e.y,t.y,i.y,n.y);return new v.Rect(r,s,o-r,n-s)},getIntegerBoundingBox:function(){var e=this.getBoundingBox();var t=Math.floor(e.x);var i=Math.floor(e.y);var n=Math.ceil(e.width+e.x-t);e=Math.ceil(e.height+e.y-i);return new v.Rect(t,i,n,e)},containsPoint:function(e,t){t=t||0;var i=this.getTopLeft();var n=this.getTopRight();var r=this.getBottomLeft();var o=n.minus(i);var s=r.minus(i);return(e.x-i.x)*o.x+(e.y-i.y)*o.y>=-t&&(e.x-n.x)*o.x+(e.y-n.y)*o.y<=t&&(e.x-i.x)*s.x+(e.y-i.y)*s.y>=-t&&(e.x-r.x)*s.x+(e.y-r.y)*s.y<=t},toString:function(){return"["+Math.round(100*this.x)/100+", "+Math.round(100*this.y)/100+", "+Math.round(100*this.width)/100+"x"+Math.round(100*this.height)/100+", "+Math.round(100*this.degrees)/100+"deg]"}}}(OpenSeadragon);!function(h){var s={};h.ReferenceStrip=function(e){var t,i,n,r=e.viewer,o=h.getElementSize(r.element);if(!e.id){e.id="referencestrip-"+h.now();this.element=h.makeNeutralElement("div");this.element.id=e.id;this.element.className="referencestrip"}e=h.extend(!0,{sizeRatio:h.DEFAULT_SETTINGS.referenceStripSizeRatio,position:h.DEFAULT_SETTINGS.referenceStripPosition,scroll:h.DEFAULT_SETTINGS.referenceStripScroll,clickTimeThreshold:h.DEFAULT_SETTINGS.clickTimeThreshold},e,{element:this.element});h.extend(this,e);s[this.id]={animating:!1};this.minPixelRatio=this.viewer.minPixelRatio;this.element.tabIndex=0;(i=this.element.style).marginTop="0px";i.marginRight="0px";i.marginBottom="0px";i.marginLeft="0px";i.left="0px";i.bottom="0px";i.border="0px";i.background="#000";i.position="relative";h.setElementTouchActionNone(this.element);h.setElementOpacity(this.element,.8);this.viewer=r;this.tracker=new h.MouseTracker({userData:"ReferenceStrip.tracker",element:this.element,clickHandler:h.delegate(this,a),dragHandler:h.delegate(this,l),scrollHandler:h.delegate(this,c),enterHandler:h.delegate(this,d),leaveHandler:h.delegate(this,p),keyDownHandler:h.delegate(this,g),keyHandler:h.delegate(this,m),preProcessEventHandler:function(e){"wheel"===e.eventType&&(e.preventDefault=!0)}});if(e.width&&e.height){this.element.style.width=e.width+"px";this.element.style.height=e.height+"px";r.addControl(this.element,{anchor:h.ControlAnchor.BOTTOM_LEFT})}else if("horizontal"===e.scroll){this.element.style.width=o.x*e.sizeRatio*r.tileSources.length+12*r.tileSources.length+"px";this.element.style.height=o.y*e.sizeRatio+"px";r.addControl(this.element,{anchor:h.ControlAnchor.BOTTOM_LEFT})}else{this.element.style.height=o.y*e.sizeRatio*r.tileSources.length+12*r.tileSources.length+"px";this.element.style.width=o.x*e.sizeRatio+"px";r.addControl(this.element,{anchor:h.ControlAnchor.TOP_LEFT})}this.panelWidth=o.x*this.sizeRatio+8;this.panelHeight=o.y*this.sizeRatio+8;this.panels=[];this.miniViewers={};for(n=0;n<r.tileSources.length;n++){(t=h.makeNeutralElement("div")).id=this.element.id+"-"+n;t.style.width=this.panelWidth+"px";t.style.height=this.panelHeight+"px";t.style.display="inline";t.style.float="left";t.style.cssFloat="left";t.style.padding="2px";h.setElementTouchActionNone(t);h.setElementPointerEventsNone(t);this.element.appendChild(t);t.activePanel=!1;this.panels.push(t)}u(this,"vertical"===this.scroll?o.y:o.x,0);this.setFocus(0)};h.ReferenceStrip.prototype={setFocus:function(e){var t,i=this.element.querySelector("#"+this.element.id+"-"+e),n=h.getElementSize(this.viewer.canvas),r=Number(this.element.style.width.replace("px","")),o=Number(this.element.style.height.replace("px","")),s=-Number(this.element.style.marginLeft.replace("px","")),a=-Number(this.element.style.marginTop.replace("px",""));if(this.currentSelected!==i){this.currentSelected&&(this.currentSelected.style.background="#000");this.currentSelected=i;this.currentSelected.style.background="#999";if("horizontal"===this.scroll){if((t=Number(e)*(this.panelWidth+3))>s+n.x-this.panelWidth){t=Math.min(t,r-n.x);this.element.style.marginLeft=-t+"px";u(this,n.x,-t)}else if(t<s){t=Math.max(0,t-n.x/2);this.element.style.marginLeft=-t+"px";u(this,n.x,-t)}}else if((t=Number(e)*(this.panelHeight+3))>a+n.y-this.panelHeight){t=Math.min(t,o-n.y);this.element.style.marginTop=-t+"px";u(this,n.y,-t)}else if(t<a){t=Math.max(0,t-n.y/2);this.element.style.marginTop=-t+"px";u(this,n.y,-t)}this.currentPage=e;d.call(this,{eventSource:this.tracker})}},update:function(){return!!s[this.id].animating},destroy:function(){if(this.miniViewers)for(var e in this.miniViewers)this.miniViewers[e].destroy();this.tracker.destroy();this.element&&this.viewer.removeControl(this.element)}};function a(e){if(e.quick){e="horizontal"===this.scroll?Math.floor(e.position.x/(this.panelWidth+4)):Math.floor(e.position.y/this.panelHeight);this.viewer.goToPage(e)}this.element.focus()}function l(e){this.dragging=!0;if(this.element){var t=Number(this.element.style.marginLeft.replace("px","")),i=Number(this.element.style.marginTop.replace("px","")),n=Number(this.element.style.width.replace("px","")),r=Number(this.element.style.height.replace("px","")),o=h.getElementSize(this.viewer.canvas);if("horizontal"===this.scroll){if(0<-e.delta.x){if(t>-(n-o.x)){this.element.style.marginLeft=t+2*e.delta.x+"px";u(this,o.x,t+2*e.delta.x)}}else if(-e.delta.x<0&&t<0){this.element.style.marginLeft=t+2*e.delta.x+"px";u(this,o.x,t+2*e.delta.x)}}else if(0<-e.delta.y){if(i>-(r-o.y)){this.element.style.marginTop=i+2*e.delta.y+"px";u(this,o.y,i+2*e.delta.y)}}else if(-e.delta.y<0&&i<0){this.element.style.marginTop=i+2*e.delta.y+"px";u(this,o.y,i+2*e.delta.y)}}}function c(e){if(this.element){var t=Number(this.element.style.marginLeft.replace("px","")),i=Number(this.element.style.marginTop.replace("px","")),n=Number(this.element.style.width.replace("px","")),r=Number(this.element.style.height.replace("px","")),o=h.getElementSize(this.viewer.canvas);if("horizontal"===this.scroll){if(0<e.scroll){if(t>-(n-o.x)){this.element.style.marginLeft=t-60*e.scroll+"px";u(this,o.x,t-60*e.scroll)}}else if(e.scroll<0&&t<0){this.element.style.marginLeft=t-60*e.scroll+"px";u(this,o.x,t-60*e.scroll)}}else if(e.scroll<0){if(i>o.y-r){this.element.style.marginTop=i+60*e.scroll+"px";u(this,o.y,i+60*e.scroll)}}else if(0<e.scroll&&i<0){this.element.style.marginTop=i+60*e.scroll+"px";u(this,o.y,i+60*e.scroll)}e.preventDefault=!0}}function u(e,t,i){var n,r,o,s,a;n="horizontal"===e.scroll?e.panelWidth:e.panelHeight;r=Math.ceil(t/n)+5;for(s=r=(r=(o=Math.ceil((Math.abs(i)+t)/n)+1)-r)<0?0:r;s<o&&s<e.panels.length;s++)if(!(a=e.panels[s]).activePanel){var l=e.viewer.tileSources[s];l=l.referenceStripThumbnailUrl?{type:"image",url:l.referenceStripThumbnailUrl}:l;l=new h.Viewer({id:a.id,tileSources:[l],element:a,navigatorSizeRatio:e.sizeRatio,showNavigator:!1,mouseNavEnabled:!1,showNavigationControl:!1,showSequenceControl:!1,immediateRender:!0,blendTime:0,animationTime:0,loadTilesWithAjax:e.viewer.loadTilesWithAjax,ajaxHeaders:e.viewer.ajaxHeaders,drawer:"canvas"});h.setElementPointerEventsNone(l.canvas);h.setElementPointerEventsNone(l.container);l.innerTracker.setTracking(!1);l.outerTracker.setTracking(!1);e.miniViewers[a.id]=l;a.activePanel=!0}}function d(e){e=e.eventSource.element;"horizontal"===this.scroll?e.style.marginBottom="0px":e.style.marginLeft="0px"}function p(e){e=e.eventSource.element;"horizontal"===this.scroll?e.style.marginBottom="-"+h.getElementSize(e).y/2+"px":e.style.marginLeft="-"+h.getElementSize(e).x/2+"px"}function g(e){if(e.ctrl||e.alt||e.meta)e.preventDefault=!1;else switch(e.keyCode){case 38:c.call(this,{eventSource:this.tracker,position:null,scroll:1,shift:null});e.preventDefault=!0;break;case 40:case 37:c.call(this,{eventSource:this.tracker,position:null,scroll:-1,shift:null});e.preventDefault=!0;break;case 39:c.call(this,{eventSource:this.tracker,position:null,scroll:1,shift:null});e.preventDefault=!0;break;default:e.preventDefault=!1}}function m(e){if(e.ctrl||e.alt||e.meta)e.preventDefault=!1;else switch(e.keyCode){case 61:c.call(this,{eventSource:this.tracker,position:null,scroll:1,shift:null});e.preventDefault=!0;break;case 45:c.call(this,{eventSource:this.tracker,position:null,scroll:-1,shift:null});e.preventDefault=!0;break;case 48:case 119:case 87:c.call(this,{eventSource:this.tracker,position:null,scroll:1,shift:null});e.preventDefault=!0;break;case 115:case 83:case 97:c.call(this,{eventSource:this.tracker,position:null,scroll:-1,shift:null});e.preventDefault=!0;break;case 100:c.call(this,{eventSource:this.tracker,position:null,scroll:1,shift:null});e.preventDefault=!0;break;default:e.preventDefault=!1}}}(OpenSeadragon);!function(s){s.DisplayRect=function(e,t,i,n,r,o){s.Rect.apply(this,[e,t,i,n]);this.minLevel=r;this.maxLevel=o};s.extend(s.DisplayRect.prototype,s.Rect.prototype)}(OpenSeadragon);!function(r){r.Spring=function(e){var t=arguments;"object"!=typeof e&&(e={initial:t.length&&"number"==typeof t[0]?t[0]:void 0,springStiffness:1<t.length?t[1].springStiffness:5,animationTime:1<t.length?t[1].animationTime:1.5});r.console.assert("number"==typeof e.springStiffness&&0!==e.springStiffness,"[OpenSeadragon.Spring] options.springStiffness must be a non-zero number");r.console.assert("number"==typeof e.animationTime&&0<=e.animationTime,"[OpenSeadragon.Spring] options.animationTime must be a number greater than or equal to 0");if(e.exponential){this._exponential=!0;delete e.exponential}r.extend(!0,this,e);this.current={value:"number"==typeof this.initial?this.initial:this._exponential?0:1,time:r.now()};r.console.assert(!this._exponential||0!==this.current.value,"[OpenSeadragon.Spring] value must be non-zero for exponential springs");this.start={value:this.current.value,time:this.current.time};this.target={value:this.current.value,time:this.current.time};if(this._exponential){this.start._logValue=Math.log(this.start.value);this.target._logValue=Math.log(this.target.value);this.current._logValue=Math.log(this.current.value)}};r.Spring.prototype={resetTo:function(e){r.console.assert(!this._exponential||0!==e,"[OpenSeadragon.Spring.resetTo] target must be non-zero for exponential springs");this.start.value=this.target.value=this.current.value=e;this.start.time=this.target.time=this.current.time=r.now();if(this._exponential){this.start._logValue=Math.log(this.start.value);this.target._logValue=Math.log(this.target.value);this.current._logValue=Math.log(this.current.value)}},springTo:function(e){r.console.assert(!this._exponential||0!==e,"[OpenSeadragon.Spring.springTo] target must be non-zero for exponential springs");this.start.value=this.current.value;this.start.time=this.current.time;this.target.value=e;this.target.time=this.start.time+1e3*this.animationTime;if(this._exponential){this.start._logValue=Math.log(this.start.value);this.target._logValue=Math.log(this.target.value)}},shiftBy:function(e){this.start.value+=e;this.target.value+=e;if(this._exponential){r.console.assert(0!==this.target.value&&0!==this.start.value,"[OpenSeadragon.Spring.shiftBy] spring value must be non-zero for exponential springs");this.start._logValue=Math.log(this.start.value);this.target._logValue=Math.log(this.target.value)}},setExponential:function(e){this._exponential=e;if(this._exponential){r.console.assert(0!==this.current.value&&0!==this.target.value&&0!==this.start.value,"[OpenSeadragon.Spring.setExponential] spring value must be non-zero for exponential springs");this.start._logValue=Math.log(this.start.value);this.target._logValue=Math.log(this.target.value);this.current._logValue=Math.log(this.current.value)}},update:function(){this.current.time=r.now();let e,t;if(this._exponential){e=this.start._logValue;t=this.target._logValue}else{e=this.start.value;t=this.target.value}if(this.current.time>=this.target.time)this.current.value=this.target.value;else{i=e+(t-e)*(i=this.springStiffness,n=(this.current.time-this.start.time)/(this.target.time-this.start.time),(1-Math.exp(i*-n))/(1-Math.exp(-i)));this._exponential?this.current.value=Math.exp(i):this.current.value=i}var i,n;return this.current.value!==this.target.value},isAtTargetValue:function(){return this.current.value===this.target.value}}}(OpenSeadragon);!function(n){n.ImageJob=function(e){n.extend(!0,this,{timeout:n.DEFAULT_SETTINGS.timeout,jobId:null,tries:0},e);this.data=null;this.userData={};this.errorMsg=null};n.ImageJob.prototype={start:function(){this.tries++;var e=this;var t=this.abort;this.jobId=window.setTimeout(function(){e.finish(null,null,"Image load exceeded timeout ("+e.timeout+" ms)")},this.timeout);this.abort=function(){e.source.downloadTileAbort(e);"function"==typeof t&&t()};this.source.downloadTileStart(this)},finish:function(e,t,i){this.data=e;this.request=t;this.errorMsg=i;this.jobId&&window.clearTimeout(this.jobId);this.callback(this)}};n.ImageLoader=function(e){n.extend(!0,this,{jobLimit:n.DEFAULT_SETTINGS.imageLoaderLimit,timeout:n.DEFAULT_SETTINGS.timeout,jobQueue:[],failedTiles:[],jobsInProgress:0},e)};n.ImageLoader.prototype={addJob:function(t){if(!t.source){n.console.error("ImageLoader.prototype.addJob() requires [options.source]. TileSource since new API defines how images are fetched. Creating a dummy TileSource.");var e=n.TileSource.prototype;t.source={downloadTileStart:e.downloadTileStart,downloadTileAbort:e.downloadTileAbort}}var i=this,e={src:t.src,tile:t.tile||{},source:t.source,loadWithAjax:t.loadWithAjax,ajaxHeaders:t.loadWithAjax?t.ajaxHeaders:null,crossOriginPolicy:t.crossOriginPolicy,ajaxWithCredentials:t.ajaxWithCredentials,postData:t.postData,callback:function(e){!function(e,t,i){""!==t.errorMsg&&(null===t.data||void 0===t.data)&&t.tries<1+e.tileRetryMax&&e.failedTiles.push(t);var n;e.jobsInProgress--;if((!e.jobLimit||e.jobsInProgress<e.jobLimit)&&0<e.jobQueue.length){(n=e.jobQueue.shift()).start();e.jobsInProgress++}if(0<e.tileRetryMax&&0===e.jobQueue.length&&(!e.jobLimit||e.jobsInProgress<e.jobLimit)&&0<e.failedTiles.length){n=e.failedTiles.shift();setTimeout(function(){n.start()},e.tileRetryDelay);e.jobsInProgress++}i(t.data,t.errorMsg,t.request)}(i,e,t.callback)},abort:t.abort,timeout:this.timeout},e=new n.ImageJob(e);if(!this.jobLimit||this.jobsInProgress<this.jobLimit){e.start();this.jobsInProgress++}else this.jobQueue.push(e)},clear:function(){for(var e=0;e<this.jobQueue.length;e++){var t=this.jobQueue[e];"function"==typeof t.abort&&t.abort()}this.jobQueue=[]}}}(OpenSeadragon);!function(d){d.Tile=function(e,t,i,n,r,o,s,a,l,h,c,u){this.level=e;this.x=t;this.y=i;this.bounds=n;this.positionedBounds=new OpenSeadragon.Rect(n.x,n.y,n.width,n.height);this.sourceBounds=h;this.exists=r;this._url=o;this.postData=c;this.context2D=s;this.loadWithAjax=a;this.ajaxHeaders=l;if(void 0===u){d.console.warn("Tile constructor needs 'cacheKey' variable: creation tile cache in Tile class is deprecated. TileSource.prototype.getTileHashKey will be used.");u=d.TileSource.prototype.getTileHashKey(e,t,i,o,l,c)}this.cacheKey=u;this.loaded=!1;this.loading=!1;this.element=null;this.imgElement=null;this.style=null;this.position=null;this.size=null;this.flipped=!1;this.blendStart=null;this.opacity=null;this.squaredDistance=null;this.visibility=null;this.hasTransparency=!1;this.beingDrawn=!1;this.lastTouchTime=0;this.isRightMost=!1;this.isBottomMost=!1};d.Tile.prototype={toString:function(){return this.level+"/"+this.x+"_"+this.y},_hasTransparencyChannel:function(){console.warn("Tile.prototype._hasTransparencyChannel() has been deprecated and will be removed in the future. Use TileSource.prototype.hasTransparency() instead.");return!!this.context2D||this.getUrl().match(".png")},get image(){d.console.error("[Tile.image] property has been deprecated. Use [Tile.prototype.getImage] instead.");return this.getImage()},get url(){d.console.error("[Tile.url] property has been deprecated. Use [Tile.prototype.getUrl] instead.");return this.getUrl()},getImage:function(){return this.cacheImageRecord.getImage()},getUrl:function(){return"function"==typeof this._url?this._url():this._url},getCanvasContext:function(){return this.context2D||this.cacheImageRecord&&this.cacheImageRecord.getRenderedContext()},getScaleForEdgeSmoothing:function(){var e;if(this.cacheImageRecord)e=this.cacheImageRecord.getRenderedContext();else{if(!this.context2D){d.console.warn("[Tile.drawCanvas] attempting to get tile scale %s when tile's not cached",this.toString());return 1}e=this.context2D}return e.canvas.width/(this.size.x*d.pixelDensityRatio)},getTranslationForEdgeSmoothing:function(e,t,i){var n=Math.max(1,Math.ceil((i.x-t.x)/2));t=Math.max(1,Math.ceil((i.y-t.y)/2));return new d.Point(n,t).minus(this.position.times(d.pixelDensityRatio).times(e||1).apply(function(e){return e%1}))},unload:function(){this.imgElement&&this.imgElement.parentNode&&this.imgElement.parentNode.removeChild(this.imgElement);this.element&&this.element.parentNode&&this.element.parentNode.removeChild(this.element);this.element=null;this.imgElement=null;this.loaded=!1;this.loading=!1}}}(OpenSeadragon);!function(l){l.OverlayPlacement=l.Placement;l.OverlayRotationMode=l.freezeObject({NO_ROTATION:1,EXACT:2,BOUNDING_BOX:3});l.Overlay=function(e,t,i){i=l.isPlainObject(e)?e:{element:e,location:t,placement:i};this.elementWrapper=document.createElement("div");this.element=i.element;this.elementWrapper.appendChild(this.element);this.element.id?this.elementWrapper.id="overlay-wrapper-"+this.element.id:this.elementWrapper.id="overlay-wrapper";this.style=this.elementWrapper.style;this._init(i)};l.Overlay.prototype={_init:function(e){this.location=e.location;this.placement=void 0===e.placement?l.Placement.TOP_LEFT:e.placement;this.onDraw=e.onDraw;this.checkResize=void 0===e.checkResize||e.checkResize;this.width=void 0===e.width?null:e.width;this.height=void 0===e.height?null:e.height;this.rotationMode=e.rotationMode||l.OverlayRotationMode.EXACT;if(this.location instanceof l.Rect){this.width=this.location.width;this.height=this.location.height;this.location=this.location.getTopLeft();this.placement=l.Placement.TOP_LEFT}this.scales=null!==this.width&&null!==this.height;this.bounds=new l.Rect(this.location.x,this.location.y,this.width,this.height);this.position=this.location},adjust:function(e,t){var i=l.Placement.properties[this.placement];if(i){i.isHorizontallyCentered?e.x-=t.x/2:i.isRight&&(e.x-=t.x);i.isVerticallyCentered?e.y-=t.y/2:i.isBottom&&(e.y-=t.y)}},destroy:function(){var e=this.elementWrapper;var t=this.style;if(e.parentNode){e.parentNode.removeChild(e);if(e.prevElementParent){t.display="none";document.body.appendChild(e)}}this.onDraw=null;t.top="";t.left="";t.position="";null!==this.width&&(t.width="");null!==this.height&&(t.height="");var i=l.getCssPropertyWithVendorPrefix("transformOrigin");e=l.getCssPropertyWithVendorPrefix("transform");if(i&&e){t[i]="";t[e]=""}},drawHTML:function(e,t){var i=this.elementWrapper;if(i.parentNode!==e){i.prevElementParent=i.parentNode;i.prevNextSibling=i.nextSibling;e.appendChild(i);this.style.position="absolute";this.size=l.getElementSize(this.elementWrapper)}var n=this._getOverlayPositionAndSize(t);var r=n.position;var o=this.size=n.size;var s="";t.overlayPreserveContentDirection&&(s=t.flipped?" scaleX(-1)":" scaleX(1)");var a=t.flipped?-n.rotate:n.rotate;e=t.flipped?" scaleX(-1)":"";if(this.onDraw)this.onDraw(r,o,this.element);else{i=this.style;n=this.element.style;n.display="block";i.left=r.x+"px";i.top=r.y+"px";null!==this.width&&(n.width=o.x+"px");null!==this.height&&(n.height=o.y+"px");r=l.getCssPropertyWithVendorPrefix("transformOrigin");o=l.getCssPropertyWithVendorPrefix("transform");if(r&&o)if(a&&!t.flipped){n[o]="";i[r]=this._getTransformOrigin();i[o]="rotate("+a+"deg)"}else if(!a&&t.flipped){n[o]=s;i[r]=this._getTransformOrigin();i[o]=e}else if(a&&t.flipped){n[o]=s;i[r]=this._getTransformOrigin();i[o]="rotate("+a+"deg)"+e}else{n[o]="";i[r]="";i[o]=""}i.display="flex"}},_getOverlayPositionAndSize:function(e){var t=e.pixelFromPoint(this.location,!0);var i=this._getSizeInPixels(e);this.adjust(t,i);var n=0;if(e.getRotation(!0)&&this.rotationMode!==l.OverlayRotationMode.NO_ROTATION)if(this.rotationMode===l.OverlayRotationMode.BOUNDING_BOX&&null!==this.width&&null!==this.height){var r=new l.Rect(t.x,t.y,i.x,i.y);r=this._getBoundingBox(r,e.getRotation(!0));t=r.getTopLeft();i=r.getSize()}else n=e.getRotation(!0);e.flipped&&(t.x=e.getContainerSize().x-t.x);return{position:t,size:i,rotate:n}},_getSizeInPixels:function(e){var t=this.size.x;var i=this.size.y;if(null!==this.width||null!==this.height){var n=e.deltaPixelsFromPointsNoRotate(new l.Point(this.width||0,this.height||0),!0);null!==this.width&&(t=n.x);null!==this.height&&(i=n.y)}if(this.checkResize&&(null===this.width||null===this.height)){n=this.size=l.getElementSize(this.elementWrapper);null===this.width&&(t=n.x);null===this.height&&(i=n.y)}return new l.Point(t,i)},_getBoundingBox:function(e,t){var i=this._getPlacementPoint(e);return e.rotate(t,i).getBoundingBox()},_getPlacementPoint:function(e){var t=new l.Point(e.x,e.y);var i=l.Placement.properties[this.placement];if(i){i.isHorizontallyCentered?t.x+=e.width/2:i.isRight&&(t.x+=e.width);i.isVerticallyCentered?t.y+=e.height/2:i.isBottom&&(t.y+=e.height)}return t},_getTransformOrigin:function(){var e="";var t=l.Placement.properties[this.placement];if(!t)return e;t.isLeft?e="left":t.isRight&&(e="right");t.isTop?e+=" top":t.isBottom&&(e+=" bottom");return e},update:function(e,t){t=l.isPlainObject(e)?e:{location:e,placement:t};this._init({location:t.location||this.location,placement:(void 0!==t.placement?t:this).placement,onDraw:t.onDraw||this.onDraw,checkResize:t.checkResize||this.checkResize,width:(void 0!==t.width?t:this).width,height:(void 0!==t.height?t:this).height,rotationMode:t.rotationMode||this.rotationMode})},getBounds:function(e){l.console.assert(e,"A viewport must now be passed to Overlay.getBounds.");var t=this.width;var i=this.height;if(null===t||null===i){var n=e.deltaPointsFromPixelsNoRotate(this.size,!0);null===t&&(t=n.x);null===i&&(i=n.y)}n=this.location.clone();this.adjust(n,new l.Point(t,i));return this._adjustBoundsForRotation(e,new l.Rect(n.x,n.y,t,i))},_adjustBoundsForRotation:function(e,t){if(!e||0===e.getRotation(!0)||this.rotationMode===l.OverlayRotationMode.EXACT)return t;if(this.rotationMode!==l.OverlayRotationMode.BOUNDING_BOX)return t.rotate(-e.getRotation(!0),this._getPlacementPoint(t));if(null===this.width||null===this.height)return t;t=this._getOverlayPositionAndSize(e);return e.viewerElementToViewportRectangle(new l.Rect(t.position.x,t.position.y,t.size.x,t.size.y))}}}(OpenSeadragon);!function(i){const n=i;n.DrawerBase=class{constructor(e){i.console.assert(e.viewer,"[Drawer] options.viewer is required");i.console.assert(e.viewport,"[Drawer] options.viewport is required");i.console.assert(e.element,"[Drawer] options.element is required");this.viewer=e.viewer;this.viewport=e.viewport;this.debugGridColor="string"==typeof e.debugGridColor?[e.debugGridColor]:e.debugGridColor||i.DEFAULT_SETTINGS.debugGridColor;this.options=e.options||{};this.container=i.getElement(e.element);this._renderingTarget=this._createDrawingElement();this.canvas.style.width="100%";this.canvas.style.height="100%";this.canvas.style.position="absolute";this.canvas.style.left="0";i.setElementOpacity(this.canvas,this.viewer.opacity,!0);i.setElementPointerEventsNone(this.canvas);i.setElementTouchActionNone(this.canvas);this.container.style.textAlign="left";this.container.appendChild(this.canvas);this._checkForAPIOverrides()}get canvas(){return this._renderingTarget}get element(){i.console.error("Drawer.element is deprecated. Use Drawer.container instead.");return this.container}getType(){i.console.error("Drawer.getType must be implemented by child class")}static isSupported(){i.console.error("Drawer.isSupported must be implemented by child class")}_createDrawingElement(){i.console.error("Drawer._createDrawingElement must be implemented by child class");return null}draw(e){i.console.error("Drawer.draw must be implemented by child class")}canRotate(){i.console.error("Drawer.canRotate must be implemented by child class")}destroy(){i.console.error("Drawer.destroy must be implemented by child class")}minimumOverlapRequired(e){return!1}setImageSmoothingEnabled(e){i.console.error("Drawer.setImageSmoothingEnabled must be implemented by child class")}drawDebuggingRect(e){i.console.warn("[drawer].drawDebuggingRect is not implemented by this drawer")}clear(){i.console.warn("[drawer].clear() is deprecated. The drawer is responsible for clearing itself as needed before drawing tiles.")}_checkForAPIOverrides(){if(this._createDrawingElement===i.DrawerBase.prototype._createDrawingElement)throw new Error("[drawer]._createDrawingElement must be implemented by child class");if(this.draw===i.DrawerBase.prototype.draw)throw new Error("[drawer].draw must be implemented by child class");if(this.canRotate===i.DrawerBase.prototype.canRotate)throw new Error("[drawer].canRotate must be implemented by child class");if(this.destroy===i.DrawerBase.prototype.destroy)throw new Error("[drawer].destroy must be implemented by child class");if(this.setImageSmoothingEnabled===i.DrawerBase.prototype.setImageSmoothingEnabled)throw new Error("[drawer].setImageSmoothingEnabled must be implemented by child class")}viewportToDrawerRectangle(e){var t=this.viewport.pixelFromPointNoRotate(e.getTopLeft(),!0);e=this.viewport.deltaPixelsFromPointsNoRotate(e.getSize(),!0);return new i.Rect(t.x*i.pixelDensityRatio,t.y*i.pixelDensityRatio,e.x*i.pixelDensityRatio,e.y*i.pixelDensityRatio)}viewportCoordToDrawerCoord(e){e=this.viewport.pixelFromPointNoRotate(e,!0);return new i.Point(e.x*i.pixelDensityRatio,e.y*i.pixelDensityRatio)}_calculateCanvasSize(){var e=i.pixelDensityRatio;var t=this.viewport.getContainerSize();return new n.Point(Math.round(t.x*e),Math.round(t.y*e))}_raiseTiledImageDrawnEvent(e,t){this.viewer&&this.viewer.raiseEvent("tiled-image-drawn",{tiledImage:e,tiles:t})}_raiseDrawerErrorEvent(e,t){this.viewer&&this.viewer.raiseEvent("drawer-error",{tiledImage:e,drawer:this,error:t})}}}(OpenSeadragon);!function(n){var e=n;class t extends e.DrawerBase{constructor(e){super(e);this.viewer.rejectEventHandler("tile-drawing","The HTMLDrawer does not raise the tile-drawing event");this.viewer.allowEventHandler("tile-drawn")}static isSupported(){return!0}getType(){return"html"}minimumOverlapRequired(e){return!0}_createDrawingElement(){return n.makeNeutralElement("div")}draw(e){var t=this;this._prepareNewFrame();e.forEach(function(e){0!==e.opacity&&t._drawTiles(e)})}canRotate(){return!1}destroy(){this.container.removeChild(this.canvas)}setImageSmoothingEnabled(){}_prepareNewFrame(){this.canvas.innerHTML=""}_drawTiles(e){var t=e.getTilesToDraw().map(e=>e.tile);if(0!==e.opacity&&(0!==t.length||e.placeholderFillStyle))for(var i=t.length-1;0<=i;i--){var n=t[i];this._drawTile(n);this.viewer&&this.viewer.raiseEvent("tile-drawn",{tiledImage:e,tile:n})}}_drawTile(e){n.console.assert(e,"[Drawer._drawTile] tile is required");let t=this.canvas;if(e.cacheImageRecord)if(e.loaded){if(!e.element){var i=e.getImage();if(!i)return;e.element=n.makeNeutralElement("div");e.imgElement=i.cloneNode();e.imgElement.style.msInterpolationMode="nearest-neighbor";e.imgElement.style.width="100%";e.imgElement.style.height="100%";e.style=e.element.style;e.style.position="absolute"}e.element.parentNode!==t&&t.appendChild(e.element);e.imgElement.parentNode!==e.element&&e.element.appendChild(e.imgElement);e.style.top=e.position.y+"px";e.style.left=e.position.x+"px";e.style.height=e.size.y+"px";e.style.width=e.size.x+"px";e.flipped&&(e.style.transform="scaleX(-1)");n.setElementOpacity(e.element,e.opacity)}else n.console.warn("Attempting to draw tile %s when it's not yet loaded.",e.toString());else n.console.warn("[Drawer._drawTileToHTML] attempting to draw tile %s when it's not cached",e.toString())}}n.HTMLDrawer=t}(OpenSeadragon);!function(p){var e=p;class t extends e.DrawerBase{constructor(e){super(e);this.context=this.canvas.getContext("2d");this.sketchCanvas=null;this.sketchContext=null;this._imageSmoothingEnabled=!0;this.viewer.allowEventHandler("tile-drawn");this.viewer.allowEventHandler("tile-drawing")}static isSupported(){return p.supportsCanvas}getType(){return"canvas"}_createDrawingElement(){let e=p.makeNeutralElement("canvas");var t=this._calculateCanvasSize();e.width=t.x;e.height=t.y;return e}draw(e){this._prepareNewFrame();this.viewer.viewport.getFlip()!==this._viewportFlipped&&this._flip();for(const t of e)0!==t.opacity&&this._drawTiles(t)}canRotate(){return!0}destroy(){this.canvas.width=1;this.canvas.height=1;this.sketchCanvas=null;this.sketchContext=null;this.container.removeChild(this.canvas)}minimumOverlapRequired(e){return!0}setImageSmoothingEnabled(e){this._imageSmoothingEnabled=!!e;this._updateImageSmoothingEnabled(this.context);this.viewer.forceRedraw()}drawDebuggingRect(e){var t=this.context;t.save();t.lineWidth=2*p.pixelDensityRatio;t.strokeStyle=this.debugGridColor[0];t.fillStyle=this.debugGridColor[0];t.strokeRect(e.x*p.pixelDensityRatio,e.y*p.pixelDensityRatio,e.width*p.pixelDensityRatio,e.height*p.pixelDensityRatio);t.restore()}get _viewportFlipped(){return this.context.getTransform().a<0}_raiseTileDrawingEvent(e,t,i,n){this.viewer.raiseEvent("tile-drawing",{tiledImage:e,context:t,tile:i,rendered:n})}_prepareNewFrame(){var e=this._calculateCanvasSize();if(this.canvas.width!==e.x||this.canvas.height!==e.y){this.canvas.width=e.x;this.canvas.height=e.y;this._updateImageSmoothingEnabled(this.context);if(null!==this.sketchCanvas){e=this._calculateSketchCanvasSize();this.sketchCanvas.width=e.x;this.sketchCanvas.height=e.y;this._updateImageSmoothingEnabled(this.sketchContext)}}this._clear()}_clear(e,t){e=this._getContext(e);if(t)e.clearRect(t.x,t.y,t.width,t.height);else{t=e.canvas;e.clearRect(0,0,t.width,t.height)}}_drawTiles(i){var e=i.getTilesToDraw().map(e=>e.tile);if(0!==i.opacity&&(0!==e.length||i.placeholderFillStyle)){var t=e[0];var n;t&&(n=i.opacity<1||i.compositeOperation&&"source-over"!==i.compositeOperation||!i._isBottomItem()&&i.source.hasTransparency(t.context2D,t.getUrl(),t.ajaxHeaders,t.postData));var r;var o;var s=this.viewport.getZoom(!0);var a=i.viewportToImageZoom(s);if(1<e.length&&a>i.smoothTileEdgesMinZoom&&!i.iOSDevice&&i.getRotation(!0)%360==0){n=!0;r=t.getScaleForEdgeSmoothing();o=t.getTranslationForEdgeSmoothing(r,this._getCanvasSize(!1),this._getCanvasSize(!0))}var l;if(n){r||(l=(l=this.viewport.viewportToViewerElementRectangle(i.getClippedBounds(!0)).getIntegerBoundingBox()).times(p.pixelDensityRatio));this._clear(!0,l)}r||this._setRotations(i,n);s=!1;if(i._clip){this._saveContext(n);a=i.imageToViewportRectangle(i._clip,!0);a=a.rotate(-i.getRotation(!0),i._getRotationPoint(!0));a=this.viewportToDrawerRectangle(a);r&&(a=a.times(r));o&&(a=a.translate(o));this._setClip(a,n);s=!0}if(i._croppingPolygons){var h=this;s||this._saveContext(n);try{var c=i._croppingPolygons.map(function(e){return e.map(function(e){e=i.imageToViewportCoordinates(e.x,e.y,!0).rotate(-i.getRotation(!0),i._getRotationPoint(!0));e=h.viewportCoordToDrawerCoord(e);r&&(e=e.times(r));return e=o?e.plus(o):e})});this._clipWithPolygons(c,n)}catch(e){p.console.error(e)}s=!0}i._hasOpaqueTile=!1;if(i.placeholderFillStyle&&!1===i._hasOpaqueTile){let e=this.viewportToDrawerRectangle(i.getBoundsNoRotate(!0));r&&(e=e.times(r));o&&(e=e.translate(o));let t=null;t="function"==typeof i.placeholderFillStyle?i.placeholderFillStyle(i,this.context):i.placeholderFillStyle;this._drawRectangle(e,t,n)}c=function(e){if("number"==typeof e)return v(e);if(!e||!p.Browser)return g;var t=e[p.Browser.vendor];m(t)&&(t=e["*"]);return v(t)}(i.subPixelRoundingForTransparency);var u=!1;c===p.SUBPIXEL_ROUNDING_OCCURRENCES.ALWAYS?u=!0:c===p.SUBPIXEL_ROUNDING_OCCURRENCES.ONLY_AT_REST&&(u=!(this.viewer&&this.viewer.isAnimating()));for(var d=0;d<e.length;d++){t=e[d];this._drawTile(t,i,n,r,o,u,i.source);this.viewer&&this.viewer.raiseEvent("tile-drawn",{tiledImage:i,tile:t})}s&&this._restoreContext(n);if(!r){i.getRotation(!0)%360!=0&&this._restoreRotationChanges(n);this.viewport.getRotation(!0)%360!=0&&this._restoreRotationChanges(n)}if(n){r&&this._setRotations(i);this.blendSketch({opacity:i.opacity,scale:r,translate:o,compositeOperation:i.compositeOperation,bounds:l});if(r){i.getRotation(!0)%360!=0&&this._restoreRotationChanges(!1);this.viewport.getRotation(!0)%360!=0&&this._restoreRotationChanges(!1)}}this._drawDebugInfo(i,e);this._raiseTiledImageDrawnEvent(i,e)}}_drawDebugInfo(e,t){if(e.debugMode)for(var i=t.length-1;0<=i;i--){var n=t[i];try{this._drawDebugInfoOnTile(n,t.length,i,e)}catch(e){p.console.error(e)}}}_clipWithPolygons(e,t){var i=this._getContext(t);i.beginPath();for(const o of e)for(var[n,r]of o.entries())i[0===n?"moveTo":"lineTo"](r.x,r.y);i.clip()}_drawTile(e,t,i,n,r,o,s){p.console.assert(e,"[Drawer._drawTile] tile is required");p.console.assert(t,"[Drawer._drawTile] drawingHandler is required");i=this._getContext(i);this._drawTileToCanvas(e,i,t,n=n||1,r,o,s)}_drawTileToCanvas(e,t,i,n,r,o,s){var a,l=e.position.times(p.pixelDensityRatio),h=e.size.times(p.pixelDensityRatio);if(e.context2D||e.cacheImageRecord){a=e.getCanvasContext();if(e.loaded&&a){t.save();if("number"==typeof n&&1!==n){l=l.times(n);h=h.times(n)}r instanceof p.Point&&(l=l.plus(r));if(1===t.globalAlpha&&e.hasTransparency){if(o){l.x=Math.round(l.x);l.y=Math.round(l.y);h.x=Math.round(h.x);h.y=Math.round(h.y)}t.clearRect(l.x,l.y,h.x,h.y)}this._raiseTileDrawingEvent(i,t,e,a);var c,u;if(e.sourceBounds){c=Math.min(e.sourceBounds.width,a.canvas.width);u=Math.min(e.sourceBounds.height,a.canvas.height)}else{c=a.canvas.width;u=a.canvas.height}t.translate(l.x+h.x/2,0);e.flipped&&t.scale(-1,1);t.drawImage(a.canvas,0,0,c,u,-h.x/2,l.y,h.x,h.y);t.restore()}else p.console.warn("Attempting to draw tile %s when it's not yet loaded.",e.toString())}else p.console.warn("[Drawer._drawTileToCanvas] attempting to draw tile %s when it's not cached",e.toString())}_getContext(e){var t=this.context;if(e){if(null===this.sketchCanvas){this.sketchCanvas=document.createElement("canvas");e=this._calculateSketchCanvasSize();this.sketchCanvas.width=e.x;this.sketchCanvas.height=e.y;this.sketchContext=this.sketchCanvas.getContext("2d");if(0===this.viewport.getRotation()){var i=this;this.viewer.addHandler("rotate",function e(){if(0!==i.viewport.getRotation()){i.viewer.removeHandler("rotate",e);var t=i._calculateSketchCanvasSize();i.sketchCanvas.width=t.x;i.sketchCanvas.height=t.y}})}this._updateImageSmoothingEnabled(this.sketchContext)}t=this.sketchContext}return t}_saveContext(e){this._getContext(e).save()}_restoreContext(e){this._getContext(e).restore()}_setClip(e,t){t=this._getContext(t);t.beginPath();t.rect(e.x,e.y,e.width,e.height);t.clip()}_drawRectangle(e,t,i){i=this._getContext(i);i.save();i.fillStyle=t;i.fillRect(e.x,e.y,e.width,e.height);i.restore()}blendSketch(e,t,i,n){var r=e;e=(r=!p.isPlainObject(r)?{opacity:e,scale:t,translate:i,compositeOperation:n}:r).opacity;n=r.compositeOperation;var o=r.bounds;this.context.save();this.context.globalAlpha=e;n&&(this.context.globalCompositeOperation=n);if(o){if(o.x<0){o.width+=o.x;o.x=0}o.x+o.width>this.canvas.width&&(o.width=this.canvas.width-o.x);if(o.y<0){o.height+=o.y;o.y=0}o.y+o.height>this.canvas.height&&(o.height=this.canvas.height-o.y);this.context.drawImage(this.sketchCanvas,o.x,o.y,o.width,o.height,o.x,o.y,o.width,o.height)}else{t=r.scale||1;e=(i=r.translate)instanceof p.Point?i:new p.Point(0,0);n=0;o=0;if(i){r=this.sketchCanvas.width-this.canvas.width;i=this.sketchCanvas.height-this.canvas.height;n=Math.round(r/2);o=Math.round(i/2)}this.context.drawImage(this.sketchCanvas,e.x-n*t,e.y-o*t,(this.canvas.width+2*n)*t,(this.canvas.height+2*o)*t,-n,-o,this.canvas.width+2*n,this.canvas.height+2*o)}this.context.restore()}_drawDebugInfoOnTile(e,t,i,n){var r=this.viewer.world.getIndexOfItem(n)%this.debugGridColor.length;var o=this.context;o.save();o.lineWidth=2*p.pixelDensityRatio;o.font="small-caps bold "+13*p.pixelDensityRatio+"px arial";o.strokeStyle=this.debugGridColor[r];o.fillStyle=this.debugGridColor[r];this._setRotations(n);this._viewportFlipped&&this._flip({point:e.position.plus(e.size.divide(2))});o.strokeRect(e.position.x*p.pixelDensityRatio,e.position.y*p.pixelDensityRatio,e.size.x*p.pixelDensityRatio,e.size.y*p.pixelDensityRatio);var s=(e.position.x+e.size.x/2)*p.pixelDensityRatio;var a=(e.position.y+e.size.y/2)*p.pixelDensityRatio;o.translate(s,a);r=this.viewport.getRotation(!0);o.rotate(Math.PI/180*-r);o.translate(-s,-a);if(0===e.x&&0===e.y){o.fillText("Zoom: "+this.viewport.getZoom(),e.position.x*p.pixelDensityRatio,(e.position.y-30)*p.pixelDensityRatio);o.fillText("Pan: "+this.viewport.getBounds().toString(),e.position.x*p.pixelDensityRatio,(e.position.y-20)*p.pixelDensityRatio)}o.fillText("Level: "+e.level,(e.position.x+10)*p.pixelDensityRatio,(e.position.y+20)*p.pixelDensityRatio);o.fillText("Column: "+e.x,(e.position.x+10)*p.pixelDensityRatio,(e.position.y+30)*p.pixelDensityRatio);o.fillText("Row: "+e.y,(e.position.x+10)*p.pixelDensityRatio,(e.position.y+40)*p.pixelDensityRatio);o.fillText("Order: "+i+" of "+t,(e.position.x+10)*p.pixelDensityRatio,(e.position.y+50)*p.pixelDensityRatio);o.fillText("Size: "+e.size.toString(),(e.position.x+10)*p.pixelDensityRatio,(e.position.y+60)*p.pixelDensityRatio);o.fillText("Position: "+e.position.toString(),(e.position.x+10)*p.pixelDensityRatio,(e.position.y+70)*p.pixelDensityRatio);this.viewport.getRotation(!0)%360!=0&&this._restoreRotationChanges();n.getRotation(!0)%360!=0&&this._restoreRotationChanges();o.restore()}_updateImageSmoothingEnabled(e){e.msImageSmoothingEnabled=this._imageSmoothingEnabled;e.imageSmoothingEnabled=this._imageSmoothingEnabled}_getCanvasSize(e){e=this._getContext(e).canvas;return new p.Point(e.width,e.height)}_getCanvasCenter(){return new p.Point(this.canvas.width/2,this.canvas.height/2)}_setRotations(e,t=!1){var i=!1;if(this.viewport.getRotation(!0)%360!=0){this._offsetForRotation({degrees:this.viewport.getRotation(!0),useSketch:t,saveContext:i});i=!1}e.getRotation(!0)%360!=0&&this._offsetForRotation({degrees:e.getRotation(!0),point:this.viewport.pixelFromPointNoRotate(e._getRotationPoint(!0),!0),useSketch:t,saveContext:i})}_offsetForRotation(e){var t=e.point?e.point.times(p.pixelDensityRatio):this._getCanvasCenter();var i=this._getContext(e.useSketch);i.save();i.translate(t.x,t.y);i.rotate(Math.PI/180*e.degrees);i.translate(-t.x,-t.y)}_flip(e){var t=(e=e||{}).point?e.point.times(p.pixelDensityRatio):this._getCanvasCenter();e=this._getContext(e.useSketch);e.translate(t.x,0);e.scale(-1,1);e.translate(-t.x,0)}_restoreRotationChanges(e){this._getContext(e).restore()}_calculateCanvasSize(){var e=p.pixelDensityRatio;var t=this.viewport.getContainerSize();return{x:Math.round(t.x*e),y:Math.round(t.y*e)}}_calculateSketchCanvasSize(){var e=this._calculateCanvasSize();if(0===this.viewport.getRotation())return e;e=Math.ceil(Math.sqrt(e.x*e.x+e.y*e.y));return{x:e,y:e}}}p.CanvasDrawer=t;var g=p.SUBPIXEL_ROUNDING_OCCURRENCES.NEVER;function m(e){return e!==p.SUBPIXEL_ROUNDING_OCCURRENCES.ALWAYS&&e!==p.SUBPIXEL_ROUNDING_OCCURRENCES.ONLY_AT_REST&&e!==p.SUBPIXEL_ROUNDING_OCCURRENCES.NEVER}function v(e){return m(e)?g:e}}(OpenSeadragon);!function(w){const a=w;a.WebGLDrawer=class extends a.DrawerBase{constructor(e){super(e);this._destroyed=!1;this._TextureMap=new Map;this._TileMap=new Map;this._gl=null;this._firstPass=null;this._secondPass=null;this._glFrameBuffer=null;this._renderToTexture=null;this._glFramebufferToCanvasTransform=null;this._outputCanvas=null;this._outputContext=null;this._clippingCanvas=null;this._clippingContext=null;this._renderingCanvas=null;this._backupCanvasDrawer=null;this._imageSmoothingEnabled=!0;this._boundToTileReady=e=>this._tileReadyHandler(e);this._boundToImageUnloaded=e=>this._imageUnloadedHandler(e);this.viewer.addHandler("tile-ready",this._boundToTileReady);this.viewer.addHandler("image-unloaded",this._boundToImageUnloaded);this.viewer.rejectEventHandler("tile-drawn","The WebGLDrawer does not raise the tile-drawn event");this.viewer.rejectEventHandler("tile-drawing","The WebGLDrawer does not raise the tile-drawing event");this._setupCanvases();this._setupRenderer();this.context=this._outputContext}destroy(){if(!this._destroyed){let t=this._gl;var i=t.getParameter(t.MAX_TEXTURE_IMAGE_UNITS);for(let e=0;e<i;++e){t.activeTexture(t.TEXTURE0+e);t.bindTexture(t.TEXTURE_2D,null);t.bindTexture(t.TEXTURE_CUBE_MAP,null)}t.bindBuffer(t.ARRAY_BUFFER,null);t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,null);t.bindRenderbuffer(t.RENDERBUFFER,null);t.bindFramebuffer(t.FRAMEBUFFER,null);this._unloadTextures();t.deleteBuffer(this._secondPass.bufferOutputPosition);t.deleteFramebuffer(this._glFrameBuffer);this._renderingCanvas.width=this._renderingCanvas.height=1;this._clippingCanvas.width=this._clippingCanvas.height=1;this._outputCanvas.width=this._outputCanvas.height=1;this._renderingCanvas=null;this._clippingCanvas=this._clippingContext=null;this._outputCanvas=this._outputContext=null;let e=t.getExtension("WEBGL_lose_context");e&&e.loseContext();this.viewer.removeHandler("tile-ready",this._boundToTileReady);this.viewer.removeHandler("image-unloaded",this._boundToImageUnloaded);this.viewer.removeHandler("resize",this._resizeHandler);this._gl=null;if(this._backupCanvasDrawer){this._backupCanvasDrawer.destroy();this._backupCanvasDrawer=null}this.container.removeChild(this.canvas);this.viewer.drawer===this&&(this.viewer.drawer=null);this._destroyed=!0}}canRotate(){return!0}static isSupported(){let e=document.createElement("canvas");let t=w.isFunction(e.getContext)&&e.getContext("webgl");let i=t&&t.getExtension("WEBGL_lose_context");i&&i.loseContext();return!!t}getType(){return"webgl"}minimumOverlapRequired(e){return e.isTainted()}_createDrawingElement(){let e=w.makeNeutralElement("canvas");var t=this._calculateCanvasSize();e.width=t.x;e.height=t.y;return e}_getBackupCanvasDrawer(){if(!this._backupCanvasDrawer){this._backupCanvasDrawer=this.viewer.requestDrawer("canvas",{mainDrawer:!1});this._backupCanvasDrawer.canvas.style.setProperty("visibility","hidden")}return this._backupCanvasDrawer}draw(e){let v=this._gl;var t=this.viewport.getBoundsNoRotateWithMargins(!0);let i=t,n=new a.Point(t.x+t.width/2,t.y+t.height/2),r=this.viewport.getRotation(!0)*Math.PI/180;var o=this.viewport.flipped?-1:1;t=w.Mat3.makeTranslation(-n.x,-n.y);let s=w.Mat3.makeScaling(2/i.width*o,-2/i.height);o=w.Mat3.makeRotation(-r);let f=s.multiply(o).multiply(t);v.bindFramebuffer(v.FRAMEBUFFER,null);v.clear(v.COLOR_BUFFER_BIT);this._outputContext.clearRect(0,0,this._outputCanvas.width,this._outputCanvas.height);let y=!1;e.forEach((s,e)=>{if(s.isTainted()){if(y){this._outputContext.drawImage(this._renderingCanvas,0,0);v.bindFramebuffer(v.FRAMEBUFFER,null);v.clear(v.COLOR_BUFFER_BIT);y=!1}const n=this._getBackupCanvasDrawer();n.draw([s]);this._outputContext.drawImage(n.canvas,0,0)}else{let o=s.getTilesToDraw();s.placeholderFillStyle&&!1===s._hasOpaqueTile&&this._drawPlaceholder(s);if(0!==o.length&&0!==s.getOpacity()){var t=o[0];var i=s.compositeOperation||this.viewer.compositeOperation||s._clip||s._croppingPolygons||s.debugMode;var a=i||s.opacity<1||t.hasTransparency;if(i){y&&this._outputContext.drawImage(this._renderingCanvas,0,0);v.bindFramebuffer(v.FRAMEBUFFER,null);v.clear(v.COLOR_BUFFER_BIT)}v.useProgram(this._firstPass.shaderProgram);if(a){v.bindFramebuffer(v.FRAMEBUFFER,this._glFrameBuffer);v.clear(v.COLOR_BUFFER_BIT)}else v.bindFramebuffer(v.FRAMEBUFFER,null);let n=f;var l=s.getRotation(!0);if(l%360!=0){t=w.Mat3.makeRotation(-l*Math.PI/180);l=s.getBoundsNoRotate(!0).getCenter();let e=w.Mat3.makeTranslation(l.x,l.y);l=w.Mat3.makeTranslation(-l.x,-l.y);l=e.multiply(t).multiply(l);n=f.multiply(l)}var h=this._gl.getParameter(this._gl.MAX_TEXTURE_IMAGE_UNITS);if(h<=0)throw new Error(`WegGL error: bad value for gl parameter MAX_TEXTURE_IMAGE_UNITS (${h}). This could happen
                        if too many contexts have been created and not released, or there is another problem with the graphics card.`);var c=new Float32Array(12*h);var u=new Array(h);let r=new Array(h);var d=new Array(h);for(let i=0;i<o.length;i++){let e=o[i].tile;var p=i%h;var g=1+p;var m=e.getCanvasContext();let t=m?this._TextureMap.get(m.canvas):null;if(!t){this._tileReadyHandler({tile:e,tiledImage:s});t=m?this._TextureMap.get(m.canvas):null}t&&this._getTileData(e,s,t,n,p,c,u,r,d);if(g===h||i===o.length-1){for(let e=0;e<=g;e++){v.activeTexture(v.TEXTURE0+e);v.bindTexture(v.TEXTURE_2D,u[e])}v.bindBuffer(v.ARRAY_BUFFER,this._firstPass.bufferTexturePosition);v.bufferData(v.ARRAY_BUFFER,c,v.DYNAMIC_DRAW);r.forEach((e,t)=>{v.uniformMatrix3fv(this._firstPass.uTransformMatrices[t],!1,e)});v.uniform1fv(this._firstPass.uOpacities,new Float32Array(d));v.bindBuffer(v.ARRAY_BUFFER,this._firstPass.bufferOutputPosition);v.vertexAttribPointer(this._firstPass.aOutputPosition,2,v.FLOAT,!1,0,0);v.bindBuffer(v.ARRAY_BUFFER,this._firstPass.bufferTexturePosition);v.vertexAttribPointer(this._firstPass.aTexturePosition,2,v.FLOAT,!1,0,0);v.bindBuffer(v.ARRAY_BUFFER,this._firstPass.bufferIndex);v.vertexAttribPointer(this._firstPass.aIndex,1,v.FLOAT,!1,0,0);v.drawArrays(v.TRIANGLES,0,6*g)}}if(a){v.useProgram(this._secondPass.shaderProgram);v.bindFramebuffer(v.FRAMEBUFFER,null);v.activeTexture(v.TEXTURE0);v.bindTexture(v.TEXTURE_2D,this._renderToTexture);this._gl.uniform1f(this._secondPass.uOpacityMultiplier,s.opacity);v.bindBuffer(v.ARRAY_BUFFER,this._secondPass.bufferTexturePosition);v.vertexAttribPointer(this._secondPass.aTexturePosition,2,v.FLOAT,!1,0,0);v.bindBuffer(v.ARRAY_BUFFER,this._secondPass.bufferOutputPosition);v.vertexAttribPointer(this._secondPass.aOutputPosition,2,v.FLOAT,!1,0,0);v.drawArrays(v.TRIANGLES,0,6)}y=!0;if(i){this._applyContext2dPipeline(s,o,e);y=!1;v.bindFramebuffer(v.FRAMEBUFFER,null);v.clear(v.COLOR_BUFFER_BIT)}0===e&&this._raiseTiledImageDrawnEvent(s,o.map(e=>e.tile))}}});y&&this._outputContext.drawImage(this._renderingCanvas,0,0)}setImageSmoothingEnabled(e){if(this._imageSmoothingEnabled!==e){this._imageSmoothingEnabled=e;this._unloadTextures();this.viewer.world.draw()}}drawDebuggingRect(e){let t=this._outputContext;t.save();t.lineWidth=2*w.pixelDensityRatio;t.strokeStyle=this.debugGridColor[0];t.fillStyle=this.debugGridColor[0];t.strokeRect(e.x*w.pixelDensityRatio,e.y*w.pixelDensityRatio,e.width*w.pixelDensityRatio,e.height*w.pixelDensityRatio);t.restore()}_getTextureDataFromTile(e){return e.getCanvasContext().canvas}_applyContext2dPipeline(e,t,i){this._outputContext.save();this._outputContext.globalCompositeOperation=0===i?null:e.compositeOperation||this.viewer.compositeOperation;if(e._croppingPolygons||e._clip){this._renderToClippingCanvas(e);this._outputContext.drawImage(this._clippingCanvas,0,0)}else this._outputContext.drawImage(this._renderingCanvas,0,0);this._outputContext.restore();if(e.debugMode){i=this.viewer.viewport.getFlip();i&&this._flip();this._drawDebugInfo(t,e,i);i&&this._flip()}}_getTileData(e,t,i,n,r,o,s,a,l){var h=i.texture;var c=i.position;o.set(c,12*r);i=this._calculateOverlapFraction(e,t);o=e.positionedBounds.width*i.x;c=e.positionedBounds.height*i.y;t=e.positionedBounds.x+(0===e.x?0:o);i=e.positionedBounds.y+(0===e.y?0:c);o=e.positionedBounds.x+e.positionedBounds.width-(e.isRightMost?0:o);c=e.positionedBounds.y+e.positionedBounds.height-(e.isBottomMost?0:c);let u=new w.Mat3([o-t,0,0,0,c-i,0,t,i,1]);if(e.flipped){let e=w.Mat3.makeTranslation(.5,0);i=w.Mat3.makeTranslation(-.5,0);i=e.multiply(w.Mat3.makeScaling(-1,1)).multiply(i);u=u.multiply(i)}n=n.multiply(u);l[r]=e.opacity;s[r]=h;a[r]=n.values}_textureFilter(){return this._imageSmoothingEnabled?this._gl.LINEAR:this._gl.NEAREST}_setupRenderer(){let e=this._gl;e||w.console.error("_setupCanvases must be called before _setupRenderer");this._unitQuad=this._makeQuadVertexBuffer(0,1,0,1);this._makeFirstPassShaderProgram();this._makeSecondPassShaderProgram();this._renderToTexture=e.createTexture();e.activeTexture(e.TEXTURE0);e.bindTexture(e.TEXTURE_2D,this._renderToTexture);e.texImage2D(e.TEXTURE_2D,0,e.RGBA,this._renderingCanvas.width,this._renderingCanvas.height,0,e.RGBA,e.UNSIGNED_BYTE,null);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,this._textureFilter());e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE);this._glFrameBuffer=e.createFramebuffer();e.bindFramebuffer(e.FRAMEBUFFER,this._glFrameBuffer);e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,this._renderToTexture,0);e.enable(e.BLEND);e.blendFunc(e.ONE,e.ONE_MINUS_SRC_ALPHA)}_makeFirstPassShaderProgram(){let t=this._glNumTextures=this._gl.getParameter(this._gl.MAX_TEXTURE_IMAGE_UNITS);var e=`
            attribute vec2 a_output_position;
            attribute vec2 a_texture_position;
            attribute float a_index;

            ${[...Array(t).keys()].map(e=>`uniform mat3 u_matrix_${e};`).join("\n")} // create a uniform mat3 for each potential tile to draw

            varying vec2 v_texture_position;
            varying float v_image_index;

            void main() {

                mat3 transform_matrix; // value will be set by the if/elses in makeConditional()

                ${[...Array(t).keys()].map(e=>`${0<e?"else ":""}if(int(a_index) == ${e}) { transform_matrix = u_matrix_${e}; }`).join("\n")}

                gl_Position = vec4(transform_matrix * vec3(a_output_position, 1), 1);

                v_texture_position = a_texture_position;
                v_image_index = a_index;
            }
            `;var i=`
            precision mediump float;

            // our textures
            uniform sampler2D u_images[${t}];
            // our opacities
            uniform float u_opacities[${t}];

            // the varyings passed in from the vertex shader.
            varying vec2 v_texture_position;
            varying float v_image_index;

            void main() {
                // can't index directly with a variable, need to use a loop iterator hack
                for(int i = 0; i < ${t}; ++i){
                    if(i == int(v_image_index)){
                        gl_FragColor = texture2D(u_images[i], v_texture_position) * u_opacities[i];
                    }
                }
            }
            `;let n=this._gl;let r=this.constructor.initShaderProgram(n,e,i);n.useProgram(r);this._firstPass={shaderProgram:r,aOutputPosition:n.getAttribLocation(r,"a_output_position"),aTexturePosition:n.getAttribLocation(r,"a_texture_position"),aIndex:n.getAttribLocation(r,"a_index"),uTransformMatrices:[...Array(this._glNumTextures).keys()].map(e=>n.getUniformLocation(r,"u_matrix_"+e)),uImages:n.getUniformLocation(r,"u_images"),uOpacities:n.getUniformLocation(r,"u_opacities"),bufferOutputPosition:n.createBuffer(),bufferTexturePosition:n.createBuffer(),bufferIndex:n.createBuffer()};n.uniform1iv(this._firstPass.uImages,[...Array(t).keys()]);let o=new Float32Array(12*t);for(let e=0;e<t;++e)o.set(Float32Array.from(this._unitQuad),12*e);n.bindBuffer(n.ARRAY_BUFFER,this._firstPass.bufferOutputPosition);n.bufferData(n.ARRAY_BUFFER,o,n.STATIC_DRAW);n.enableVertexAttribArray(this._firstPass.aOutputPosition);n.bindBuffer(n.ARRAY_BUFFER,this._firstPass.bufferTexturePosition);n.enableVertexAttribArray(this._firstPass.aTexturePosition);n.bindBuffer(n.ARRAY_BUFFER,this._firstPass.bufferIndex);i=[...Array(this._glNumTextures).keys()].map(e=>Array(6).fill(e)).flat();n.bufferData(n.ARRAY_BUFFER,new Float32Array(i),n.STATIC_DRAW);n.enableVertexAttribArray(this._firstPass.aIndex)}_makeSecondPassShaderProgram(){let e=this._gl;var t=this.constructor.initShaderProgram(e,`
            attribute vec2 a_output_position;
            attribute vec2 a_texture_position;

            uniform mat3 u_matrix;

            varying vec2 v_texture_position;

            void main() {
                gl_Position = vec4(u_matrix * vec3(a_output_position, 1), 1);

                v_texture_position = a_texture_position;
            }
            `,`
            precision mediump float;

            // our texture
            uniform sampler2D u_image;

            // the texCoords passed in from the vertex shader.
            varying vec2 v_texture_position;

            // the opacity multiplier for the image
            uniform float u_opacity_multiplier;

            void main() {
                gl_FragColor = texture2D(u_image, v_texture_position);
                gl_FragColor *= u_opacity_multiplier;
            }
            `);e.useProgram(t);this._secondPass={shaderProgram:t,aOutputPosition:e.getAttribLocation(t,"a_output_position"),aTexturePosition:e.getAttribLocation(t,"a_texture_position"),uMatrix:e.getUniformLocation(t,"u_matrix"),uImage:e.getUniformLocation(t,"u_image"),uOpacityMultiplier:e.getUniformLocation(t,"u_opacity_multiplier"),bufferOutputPosition:e.createBuffer(),bufferTexturePosition:e.createBuffer()};e.bindBuffer(e.ARRAY_BUFFER,this._secondPass.bufferOutputPosition);e.bufferData(e.ARRAY_BUFFER,this._unitQuad,e.STATIC_DRAW);e.enableVertexAttribArray(this._secondPass.aOutputPosition);e.bindBuffer(e.ARRAY_BUFFER,this._secondPass.bufferTexturePosition);e.bufferData(e.ARRAY_BUFFER,this._unitQuad,e.DYNAMIC_DRAW);e.enableVertexAttribArray(this._secondPass.aTexturePosition);t=w.Mat3.makeScaling(2,2).multiply(w.Mat3.makeTranslation(-.5,-.5));e.uniformMatrix3fv(this._secondPass.uMatrix,!1,t.values)}_resizeRenderer(){let e=this._gl;var t=this._renderingCanvas.width;var i=this._renderingCanvas.height;e.viewport(0,0,t,i);e.deleteTexture(this._renderToTexture);this._renderToTexture=e.createTexture();e.activeTexture(e.TEXTURE0);e.bindTexture(e.TEXTURE_2D,this._renderToTexture);e.texImage2D(e.TEXTURE_2D,0,e.RGBA,t,i,0,e.RGBA,e.UNSIGNED_BYTE,null);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,this._textureFilter());e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE);e.bindFramebuffer(e.FRAMEBUFFER,this._glFrameBuffer);e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,this._renderToTexture,0)}_setupCanvases(){let t=this;this._outputCanvas=this.canvas;this._outputContext=this._outputCanvas.getContext("2d");this._renderingCanvas=document.createElement("canvas");this._clippingCanvas=document.createElement("canvas");this._clippingContext=this._clippingCanvas.getContext("2d");this._renderingCanvas.width=this._clippingCanvas.width=this._outputCanvas.width;this._renderingCanvas.height=this._clippingCanvas.height=this._outputCanvas.height;this._gl=this._renderingCanvas.getContext("webgl");this._resizeHandler=function(){if(t._outputCanvas!==t.viewer.drawer.canvas){t._outputCanvas.style.width=t.viewer.drawer.canvas.clientWidth+"px";t._outputCanvas.style.height=t.viewer.drawer.canvas.clientHeight+"px"}var e=t._calculateCanvasSize();if(t._outputCanvas.width!==e.x||t._outputCanvas.height!==e.y){t._outputCanvas.width=e.x;t._outputCanvas.height=e.y}t._renderingCanvas.style.width=t._outputCanvas.clientWidth+"px";t._renderingCanvas.style.height=t._outputCanvas.clientHeight+"px";t._renderingCanvas.width=t._clippingCanvas.width=t._outputCanvas.width;t._renderingCanvas.height=t._clippingCanvas.height=t._outputCanvas.height;t._resizeRenderer()};this.viewer.addHandler("resize",this._resizeHandler)}_makeQuadVertexBuffer(e,t,i,n){return new Float32Array([e,n,t,n,e,i,e,i,t,n,t,i])}_tileReadyHandler(r){let o=r.tile;let s=r.tiledImage;if(!s.isTainted()){var a=o.getCanvasContext();var l=a&&a.canvas;if(l&&!w.isCanvasTainted(l)){if(!this._TextureMap.get(l)){let e=this._gl;var h=e.createTexture();let t;var c=s.source.tileOverlap;let i,n;if(o.sourceBounds){i=Math.min(o.sourceBounds.width,l.width)/l.width;n=Math.min(o.sourceBounds.height,l.height)/l.height}else{i=1;n=1}if(0<c){var u=this._calculateOverlapFraction(o,s);var d=(0===o.x?0:u.x)*i;r=(0===o.y?0:u.y)*n;c=(o.isRightMost?1:1-u.x)*i;u=(o.isBottomMost?1:1-u.y)*n;t=this._makeQuadVertexBuffer(d,c,r,u)}else t=1===i&&1===n?this._unitQuad:this._makeQuadVertexBuffer(0,i,0,n);u={texture:h,position:t};this._TextureMap.set(l,u);e.activeTexture(e.TEXTURE0);e.bindTexture(e.TEXTURE_2D,h);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE);e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,this._textureFilter());e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,this._textureFilter());this._uploadImageData(a)}}else if(!s.isTainted()){s.setTainted(!0);w.console.warn("WebGL cannot be used to draw this TiledImage because it has tainted data. Does crossOriginPolicy need to be set?");this._raiseDrawerErrorEvent(s,"Tainted data cannot be used by the WebGLDrawer. Falling back to CanvasDrawer for this TiledImage.")}}}_calculateOverlapFraction(e,t){var i=t.source.tileOverlap;var n=e.sourceBounds.width;t=e.sourceBounds.height;return{x:i/(n+((0===e.x?0:i)+(e.isRightMost?0:i))),y:i/(t+((0===e.y?0:i)+(e.isBottomMost?0:i)))}}_unloadTextures(){let e=Array.from(this._TextureMap.keys());e.forEach(e=>{this._cleanupImageData(e)})}_uploadImageData(e){let t=this._gl;var i=e.canvas;try{if(!i)throw e;t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,i)}catch(e){w.console.error("Error uploading image data to WebGL",e)}}_imageUnloadedHandler(e){e=e.context2D.canvas;this._cleanupImageData(e)}_cleanupImageData(e){var t=this._TextureMap.get(e);this._TextureMap.delete(e);t&&this._gl.deleteTexture(t.texture)}_setClip(){}_renderToClippingCanvas(t){this._clippingContext.clearRect(0,0,this._clippingCanvas.width,this._clippingCanvas.height);this._clippingContext.save();if(this.viewer.viewport.getFlip()){var e=new w.Point(this.canvas.width/2,this.canvas.height/2);this._clippingContext.translate(e.x,0);this._clippingContext.scale(-1,1);this._clippingContext.translate(-e.x,0)}if(t._clip){const i=[{x:t._clip.x,y:t._clip.y},{x:t._clip.x+t._clip.width,y:t._clip.y},{x:t._clip.x+t._clip.width,y:t._clip.y+t._clip.height},{x:t._clip.x,y:t._clip.y+t._clip.height}];let e=i.map(e=>{e=t.imageToViewportCoordinates(e.x,e.y,!0).rotate(this.viewer.viewport.getRotation(!0),this.viewer.viewport.getCenter(!0));return this.viewportCoordToDrawerCoord(e)});this._clippingContext.beginPath();e.forEach((e,t)=>{this._clippingContext[0===t?"moveTo":"lineTo"](e.x,e.y)});this._clippingContext.clip();this._setClip()}if(t._croppingPolygons){let e=t._croppingPolygons.map(e=>e.map(e=>{e=t.imageToViewportCoordinates(e.x,e.y,!0).rotate(this.viewer.viewport.getRotation(!0),this.viewer.viewport.getCenter(!0));return this.viewportCoordToDrawerCoord(e)}));this._clippingContext.beginPath();e.forEach(e=>{e.forEach((e,t)=>{this._clippingContext[0===t?"moveTo":"lineTo"](e.x,e.y)})});this._clippingContext.clip()}if(this.viewer.viewport.getFlip()){e=new w.Point(this.canvas.width/2,this.canvas.height/2);this._clippingContext.translate(e.x,0);this._clippingContext.scale(-1,1);this._clippingContext.translate(-e.x,0)}this._clippingContext.drawImage(this._renderingCanvas,0,0);this._clippingContext.restore()}_setRotations(e){var t=!1;if(this.viewport.getRotation(!0)%360!=0){this._offsetForRotation({degrees:this.viewport.getRotation(!0),saveContext:t});t=!1}e.getRotation(!0)%360!=0&&this._offsetForRotation({degrees:e.getRotation(!0),point:this.viewport.pixelFromPointNoRotate(e._getRotationPoint(!0),!0),saveContext:t})}_offsetForRotation(e){var t=e.point?e.point.times(w.pixelDensityRatio):this._getCanvasCenter();var i=this._outputContext;i.save();i.translate(t.x,t.y);i.rotate(Math.PI/180*e.degrees);i.translate(-t.x,-t.y)}_flip(e){var t=(e=e||{}).point?e.point.times(w.pixelDensityRatio):this._getCanvasCenter();e=this._outputContext;e.translate(t.x,0);e.scale(-1,1);e.translate(-t.x,0)}_drawDebugInfo(e,t,i){for(var n=e.length-1;0<=n;n--){var r=e[n].tile;try{this._drawDebugInfoOnTile(r,e.length,n,t,i)}catch(e){w.console.error(e)}}}_drawDebugInfoOnTile(e,t,i,n,r){var o=this.viewer.world.getIndexOfItem(n)%this.debugGridColor.length;var s=this.context;s.save();s.lineWidth=2*w.pixelDensityRatio;s.font="small-caps bold "+13*w.pixelDensityRatio+"px arial";s.strokeStyle=this.debugGridColor[o];s.fillStyle=this.debugGridColor[o];this._setRotations(n);r&&this._flip({point:e.position.plus(e.size.divide(2))});s.strokeRect(e.position.x*w.pixelDensityRatio,e.position.y*w.pixelDensityRatio,e.size.x*w.pixelDensityRatio,e.size.y*w.pixelDensityRatio);var a=(e.position.x+e.size.x/2)*w.pixelDensityRatio;o=(e.position.y+e.size.y/2)*w.pixelDensityRatio;s.translate(a,o);r=this.viewport.getRotation(!0);s.rotate(Math.PI/180*-r);s.translate(-a,-o);if(0===e.x&&0===e.y){s.fillText("Zoom: "+this.viewport.getZoom(),e.position.x*w.pixelDensityRatio,(e.position.y-30)*w.pixelDensityRatio);s.fillText("Pan: "+this.viewport.getBounds().toString(),e.position.x*w.pixelDensityRatio,(e.position.y-20)*w.pixelDensityRatio)}s.fillText("Level: "+e.level,(e.position.x+10)*w.pixelDensityRatio,(e.position.y+20)*w.pixelDensityRatio);s.fillText("Column: "+e.x,(e.position.x+10)*w.pixelDensityRatio,(e.position.y+30)*w.pixelDensityRatio);s.fillText("Row: "+e.y,(e.position.x+10)*w.pixelDensityRatio,(e.position.y+40)*w.pixelDensityRatio);s.fillText("Order: "+i+" of "+t,(e.position.x+10)*w.pixelDensityRatio,(e.position.y+50)*w.pixelDensityRatio);s.fillText("Size: "+e.size.toString(),(e.position.x+10)*w.pixelDensityRatio,(e.position.y+60)*w.pixelDensityRatio);s.fillText("Position: "+e.position.toString(),(e.position.x+10)*w.pixelDensityRatio,(e.position.y+70)*w.pixelDensityRatio);this.viewport.getRotation(!0)%360!=0&&this._restoreRotationChanges();n.getRotation(!0)%360!=0&&this._restoreRotationChanges();s.restore()}_drawPlaceholder(e){var t=e.getBounds(!0);var i=this.viewportToDrawerRectangle(e.getBounds(!0));const n=this._outputContext;let r;r="function"==typeof e.placeholderFillStyle?e.placeholderFillStyle(e,n):e.placeholderFillStyle;this._offsetForRotation({degrees:this.viewer.viewport.getRotation(!0)});n.fillStyle=r;n.translate(i.x,i.y);n.rotate(Math.PI/180*t.degrees);n.translate(-i.x,-i.y);n.fillRect(i.x,i.y,i.width,i.height);this._restoreRotationChanges()}_getCanvasCenter(){return new w.Point(this.canvas.width/2,this.canvas.height/2)}_restoreRotationChanges(){this._outputContext.restore()}static initShaderProgram(e,t,i){function n(e,t,i){t=e.createShader(t);e.shaderSource(t,i);e.compileShader(t);if(e.getShaderParameter(t,e.COMPILE_STATUS))return t;w.console.error("An error occurred compiling the shaders: "+e.getShaderInfoLog(t));e.deleteShader(t);return null}var r=n(e,e.VERTEX_SHADER,t);t=n(e,e.FRAGMENT_SHADER,i);i=e.createProgram();e.attachShader(i,r);e.attachShader(i,t);e.linkProgram(i);if(e.getProgramParameter(i,e.LINK_STATUS))return i;w.console.error("Unable to initialize the shader program: "+e.getProgramInfoLog(i));return null}}}(OpenSeadragon);!function(h){h.Viewport=function(e){var t=arguments;if((e=t.length&&t[0]instanceof h.Point?{containerSize:t[0],contentSize:t[1],config:t[2]}:e).config){h.extend(!0,e,e.config);delete e.config}this._margins=h.extend({left:0,top:0,right:0,bottom:0},e.margins||{});delete e.margins;e.initialDegrees=e.degrees;delete e.degrees;h.extend(!0,this,{containerSize:null,contentSize:null,zoomPoint:null,rotationPivot:null,viewer:null,springStiffness:h.DEFAULT_SETTINGS.springStiffness,animationTime:h.DEFAULT_SETTINGS.animationTime,minZoomImageRatio:h.DEFAULT_SETTINGS.minZoomImageRatio,maxZoomPixelRatio:h.DEFAULT_SETTINGS.maxZoomPixelRatio,visibilityRatio:h.DEFAULT_SETTINGS.visibilityRatio,wrapHorizontal:h.DEFAULT_SETTINGS.wrapHorizontal,wrapVertical:h.DEFAULT_SETTINGS.wrapVertical,defaultZoomLevel:h.DEFAULT_SETTINGS.defaultZoomLevel,minZoomLevel:h.DEFAULT_SETTINGS.minZoomLevel,maxZoomLevel:h.DEFAULT_SETTINGS.maxZoomLevel,initialDegrees:h.DEFAULT_SETTINGS.degrees,flipped:h.DEFAULT_SETTINGS.flipped,homeFillsViewer:h.DEFAULT_SETTINGS.homeFillsViewer,silenceMultiImageWarnings:h.DEFAULT_SETTINGS.silenceMultiImageWarnings},e);this._updateContainerInnerSize();this.centerSpringX=new h.Spring({initial:0,springStiffness:this.springStiffness,animationTime:this.animationTime});this.centerSpringY=new h.Spring({initial:0,springStiffness:this.springStiffness,animationTime:this.animationTime});this.zoomSpring=new h.Spring({exponential:!0,initial:1,springStiffness:this.springStiffness,animationTime:this.animationTime});this.degreesSpring=new h.Spring({initial:e.initialDegrees,springStiffness:this.springStiffness,animationTime:this.animationTime});this._oldCenterX=this.centerSpringX.current.value;this._oldCenterY=this.centerSpringY.current.value;this._oldZoom=this.zoomSpring.current.value;this._oldDegrees=this.degreesSpring.current.value;this._setContentBounds(new h.Rect(0,0,1,1),1);this.goHome(!0);this.update()};h.Viewport.prototype={get degrees(){h.console.warn("Accessing [Viewport.degrees] is deprecated. Use viewport.getRotation instead.");return this.getRotation()},set degrees(e){h.console.warn("Setting [Viewport.degrees] is deprecated. Use viewport.rotateTo, viewport.rotateBy, or viewport.setRotation instead.");this.rotateTo(e)},resetContentSize:function(e){h.console.assert(e,"[Viewport.resetContentSize] contentSize is required");h.console.assert(e instanceof h.Point,"[Viewport.resetContentSize] contentSize must be an OpenSeadragon.Point");h.console.assert(0<e.x,"[Viewport.resetContentSize] contentSize.x must be greater than 0");h.console.assert(0<e.y,"[Viewport.resetContentSize] contentSize.y must be greater than 0");this._setContentBounds(new h.Rect(0,0,1,e.y/e.x),e.x);return this},setHomeBounds:function(e,t){h.console.error("[Viewport.setHomeBounds] this function is deprecated; The content bounds should not be set manually.");this._setContentBounds(e,t)},_setContentBounds:function(e,t){h.console.assert(e,"[Viewport._setContentBounds] bounds is required");h.console.assert(e instanceof h.Rect,"[Viewport._setContentBounds] bounds must be an OpenSeadragon.Rect");h.console.assert(0<e.width,"[Viewport._setContentBounds] bounds.width must be greater than 0");h.console.assert(0<e.height,"[Viewport._setContentBounds] bounds.height must be greater than 0");this._contentBoundsNoRotate=e.clone();this._contentSizeNoRotate=this._contentBoundsNoRotate.getSize().times(t);this._contentBounds=e.rotate(this.getRotation()).getBoundingBox();this._contentSize=this._contentBounds.getSize().times(t);this._contentAspectRatio=this._contentSize.x/this._contentSize.y;this.viewer&&this.viewer.raiseEvent("reset-size",{contentSize:this._contentSizeNoRotate.clone(),contentFactor:t,homeBounds:this._contentBoundsNoRotate.clone(),contentBounds:this._contentBounds.clone()})},getHomeZoom:function(){if(this.defaultZoomLevel)return this.defaultZoomLevel;var e=this._contentAspectRatio/this.getAspectRatio();return(this.homeFillsViewer?1<=e?e:1:1<=e?1:e)/this._contentBounds.width},getHomeBounds:function(){return this.getHomeBoundsNoRotate().rotate(-this.getRotation())},getHomeBoundsNoRotate:function(){var e=this._contentBounds.getCenter();var t=1/this.getHomeZoom();var i=t/this.getAspectRatio();return new h.Rect(e.x-t/2,e.y-i/2,t,i)},goHome:function(e){this.viewer&&this.viewer.raiseEvent("home",{immediately:e});return this.fitBounds(this.getHomeBounds(),e)},getMinZoom:function(){var e=this.getHomeZoom();return this.minZoomLevel||this.minZoomImageRatio*e},getMaxZoom:function(){var e=this.maxZoomLevel;if(!e){e=this._contentSize.x*this.maxZoomPixelRatio/this._containerInnerSize.x;e/=this._contentBounds.width}return Math.max(e,this.getHomeZoom())},getAspectRatio:function(){return this._containerInnerSize.x/this._containerInnerSize.y},getContainerSize:function(){return new h.Point(this.containerSize.x,this.containerSize.y)},getMargins:function(){return h.extend({},this._margins)},setMargins:function(e){h.console.assert("object"===h.type(e),"[Viewport.setMargins] margins must be an object");this._margins=h.extend({left:0,top:0,right:0,bottom:0},e);this._updateContainerInnerSize();this.viewer&&this.viewer.forceRedraw()},getBounds:function(e){return this.getBoundsNoRotate(e).rotate(-this.getRotation(e))},getBoundsNoRotate:function(e){var t=this.getCenter(e);var i=1/this.getZoom(e);e=i/this.getAspectRatio();return new h.Rect(t.x-i/2,t.y-e/2,i,e)},getBoundsWithMargins:function(e){return this.getBoundsNoRotateWithMargins(e).rotate(-this.getRotation(e),this.getCenter(e))},getBoundsNoRotateWithMargins:function(e){var t=this.getBoundsNoRotate(e);e=this._containerInnerSize.x*this.getZoom(e);t.x-=this._margins.left/e;t.y-=this._margins.top/e;t.width+=(this._margins.left+this._margins.right)/e;t.height+=(this._margins.top+this._margins.bottom)/e;return t},getCenter:function(e){var t,i,n,r=new h.Point(this.centerSpringX.current.value,this.centerSpringY.current.value),o=new h.Point(this.centerSpringX.target.value,this.centerSpringY.target.value);if(e)return r;if(!this.zoomPoint)return o;t=this.pixelFromPoint(this.zoomPoint,!0);e=(i=1/(n=this.getZoom()))/this.getAspectRatio();e=new h.Rect(r.x-i/2,r.y-e/2,i,e);n=this._pixelFromPoint(this.zoomPoint,e).minus(t).rotate(-this.getRotation(!0)).divide(this._containerInnerSize.x*n);return o.plus(n)},getZoom:function(e){return(e?this.zoomSpring.current:this.zoomSpring.target).value},_applyZoomConstraints:function(e){return Math.max(Math.min(e,this.getMaxZoom()),this.getMinZoom())},_applyBoundaryConstraints:function(e){var t=this.viewportToViewerElementRectangle(e).getBoundingBox();var i=this.viewportToViewerElementRectangle(this._contentBoundsNoRotate).getBoundingBox();var n=!1;var r=!1;if(!this.wrapHorizontal){var o=t.x+t.width;var s=i.x+i.width;var a;a=t.width>i.width?this.visibilityRatio*i.width:this.visibilityRatio*t.width;o=i.x-o+a;s=s-t.x-a;if(a>i.width){t.x+=(o+s)/2;n=!0}else if(s<0){t.x+=s;n=!0}else if(0<o){t.x+=o;n=!0}}if(!this.wrapVertical){var l=t.y+t.height;s=i.y+i.height;o=t.height>i.height?this.visibilityRatio*i.height:this.visibilityRatio*t.height;l=i.y-l+o;s=s-t.y-o;if(o>i.height){t.y+=(l+s)/2;r=!0}else if(s<0){t.y+=s;r=!0}else if(0<l){t.y+=l;r=!0}}l=n||r;e=l?this.viewerElementToViewportRectangle(t):e.clone();e.xConstrained=n;e.yConstrained=r;e.constraintApplied=l;return e},_raiseConstraintsEvent:function(e){this.viewer&&this.viewer.raiseEvent("constrain",{immediately:e})},applyConstraints:function(e){var t=this.getZoom();var i=this._applyZoomConstraints(t);t!==i&&this.zoomTo(i,this.zoomPoint,e);i=this.getConstrainedBounds(!1);if(i.constraintApplied){this.fitBounds(i,e);this._raiseConstraintsEvent(e)}return this},ensureVisible:function(e){return this.applyConstraints(e)},_fitBounds:function(e,t){var i=(t=t||{}).immediately||!1;var n=t.constraints||!1;var r=this.getAspectRatio();var o=e.getCenter();var s=new h.Rect(e.x,e.y,e.width,e.height,e.degrees+this.getRotation()).getBoundingBox();s.getAspectRatio()>=r?s.height=s.width/r:s.width=s.height*r;s.x=o.x-s.width/2;s.y=o.y-s.height/2;var a=1/s.width;if(i){this.panTo(o,!0);this.zoomTo(a,null,!0);n&&this.applyConstraints(!0);return this}var l=this.getCenter(!0);t=this.getZoom(!0);this.panTo(l,!0);this.zoomTo(t,null,!0);e=this.getBounds();r=this.getZoom();if(0===r||Math.abs(a/r-1)<1e-8){this.zoomTo(a,null,!0);this.panTo(o,i);n&&this.applyConstraints(!1);return this}if(n){this.panTo(o,!1);a=this._applyZoomConstraints(a);this.zoomTo(a,null,!1);o=this.getConstrainedBounds();this.panTo(l,!0);this.zoomTo(t,null,!0);this.fitBounds(o)}else{r=s.rotate(-this.getRotation()).getTopLeft().times(a).minus(e.getTopLeft().times(r)).divide(a-r);this.zoomTo(a,r,i)}return this},fitBounds:function(e,t){return this._fitBounds(e,{immediately:t,constraints:!1})},fitBoundsWithConstraints:function(e,t){return this._fitBounds(e,{immediately:t,constraints:!0})},fitVertically:function(e){var t=new h.Rect(this._contentBounds.x+this._contentBounds.width/2,this._contentBounds.y,0,this._contentBounds.height);return this.fitBounds(t,e)},fitHorizontally:function(e){var t=new h.Rect(this._contentBounds.x,this._contentBounds.y+this._contentBounds.height/2,this._contentBounds.width,0);return this.fitBounds(t,e)},getConstrainedBounds:function(e){e=this.getBounds(e);return this._applyBoundaryConstraints(e)},panBy:function(e,t){var i=new h.Point(this.centerSpringX.target.value,this.centerSpringY.target.value);return this.panTo(i.plus(e),t)},panTo:function(e,t){if(t){this.centerSpringX.resetTo(e.x);this.centerSpringY.resetTo(e.y)}else{this.centerSpringX.springTo(e.x);this.centerSpringY.springTo(e.y)}this.viewer&&this.viewer.raiseEvent("pan",{center:e,immediately:t});return this},zoomBy:function(e,t,i){return this.zoomTo(this.zoomSpring.target.value*e,t,i)},zoomTo:function(e,t,i){var n=this;this.zoomPoint=t instanceof h.Point&&!isNaN(t.x)&&!isNaN(t.y)?t:null;i?this._adjustCenterSpringsForZoomPoint(function(){n.zoomSpring.resetTo(e)}):this.zoomSpring.springTo(e);this.viewer&&this.viewer.raiseEvent("zoom",{zoom:e,refPoint:t,immediately:i});return this},setRotation:function(e,t){return this.rotateTo(e,null,t)},getRotation:function(e){return(e?this.degreesSpring.current:this.degreesSpring.target).value},setRotationWithPivot:function(e,t,i){return this.rotateTo(e,t,i)},rotateTo:function(e,t,i){if(!this.viewer||!this.viewer.drawer.canRotate())return this;if(this.degreesSpring.target.value===e&&this.degreesSpring.isAtTargetValue())return this;this.rotationPivot=t instanceof h.Point&&!isNaN(t.x)&&!isNaN(t.y)?t:null;if(i)if(this.rotationPivot){if(!(e-this._oldDegrees)){this.rotationPivot=null;return this}this._rotateAboutPivot(e)}else this.degreesSpring.resetTo(e);else{var n=h.positiveModulo(this.degreesSpring.current.value,360);var r=h.positiveModulo(e,360);t=r-n;180<t?r-=360:t<-180&&(r+=360);this.degreesSpring.resetTo(e+(n-r));this.degreesSpring.springTo(e)}this._setContentBounds(this.viewer.world.getHomeBounds(),this.viewer.world.getContentFactor());this.viewer.forceRedraw();this.viewer.raiseEvent("rotate",{degrees:e,immediately:!!i,pivot:this.rotationPivot||this.getCenter()});return this},rotateBy:function(e,t,i){return this.rotateTo(this.degreesSpring.target.value+e,t,i)},resize:function(e,t){var i,n=this.getBoundsNoRotate(),r=n;this.containerSize.x=e.x;this.containerSize.y=e.y;this._updateContainerInnerSize();if(t){i=e.x/this.containerSize.x;r.width=n.width*i;r.height=r.width/this.getAspectRatio()}this.viewer&&this.viewer.raiseEvent("resize",{newContainerSize:e,maintain:t});r=this.fitBounds(r,!0);this.viewer&&this.viewer.raiseEvent("after-resize",{newContainerSize:e,maintain:t});return r},_updateContainerInnerSize:function(){this._containerInnerSize=new h.Point(Math.max(1,this.containerSize.x-(this._margins.left+this._margins.right)),Math.max(1,this.containerSize.y-(this._margins.top+this._margins.bottom)))},update:function(){var e=this;this._adjustCenterSpringsForZoomPoint(function(){e.zoomSpring.update()});this.degreesSpring.isAtTargetValue()&&(this.rotationPivot=null);this.centerSpringX.update();this.centerSpringY.update();this.rotationPivot?this._rotateAboutPivot(!0):this.degreesSpring.update();var t=this.centerSpringX.current.value!==this._oldCenterX||this.centerSpringY.current.value!==this._oldCenterY||this.zoomSpring.current.value!==this._oldZoom||this.degreesSpring.current.value!==this._oldDegrees;this._oldCenterX=this.centerSpringX.current.value;this._oldCenterY=this.centerSpringY.current.value;this._oldZoom=this.zoomSpring.current.value;this._oldDegrees=this.degreesSpring.current.value;return t||!this.zoomSpring.isAtTargetValue()||!this.centerSpringX.isAtTargetValue()||!this.centerSpringY.isAtTargetValue()||!this.degreesSpring.isAtTargetValue()},_rotateAboutPivot:function(e){var t=!0===e;var i=this.rotationPivot.minus(this.getCenter());this.centerSpringX.shiftBy(i.x);this.centerSpringY.shiftBy(i.y);t?this.degreesSpring.update():this.degreesSpring.resetTo(e);e=this.degreesSpring.current.value-this._oldDegrees;e=i.rotate(-1*e).times(-1);this.centerSpringX.shiftBy(e.x);this.centerSpringY.shiftBy(e.y)},_adjustCenterSpringsForZoomPoint:function(e){if(this.zoomPoint){var t=this.pixelFromPoint(this.zoomPoint,!0);e();t=this.pixelFromPoint(this.zoomPoint,!0).minus(t);t=this.deltaPointsFromPixels(t,!0);this.centerSpringX.shiftBy(t.x);this.centerSpringY.shiftBy(t.y);this.zoomSpring.isAtTargetValue()&&(this.zoomPoint=null)}else e()},deltaPixelsFromPointsNoRotate:function(e,t){return e.times(this._containerInnerSize.x*this.getZoom(t))},deltaPixelsFromPoints:function(e,t){return this.deltaPixelsFromPointsNoRotate(e.rotate(this.getRotation(t)),t)},deltaPointsFromPixelsNoRotate:function(e,t){return e.divide(this._containerInnerSize.x*this.getZoom(t))},deltaPointsFromPixels:function(e,t){return this.deltaPointsFromPixelsNoRotate(e,t).rotate(-this.getRotation(t))},pixelFromPointNoRotate:function(e,t){return this._pixelFromPointNoRotate(e,this.getBoundsNoRotate(t))},pixelFromPoint:function(e,t){return this._pixelFromPoint(e,this.getBoundsNoRotate(t))},_pixelFromPointNoRotate:function(e,t){return e.minus(t.getTopLeft()).times(this._containerInnerSize.x/t.width).plus(new h.Point(this._margins.left,this._margins.top))},_pixelFromPoint:function(e,t){return this._pixelFromPointNoRotate(e.rotate(this.getRotation(!0),this.getCenter(!0)),t)},pointFromPixelNoRotate:function(e,t){t=this.getBoundsNoRotate(t);return e.minus(new h.Point(this._margins.left,this._margins.top)).divide(this._containerInnerSize.x/t.width).plus(t.getTopLeft())},pointFromPixel:function(e,t){return this.pointFromPixelNoRotate(e,t).rotate(-this.getRotation(t),this.getCenter(t))},_viewportToImageDelta:function(e,t){var i=this._contentBoundsNoRotate.width;return new h.Point(e*this._contentSizeNoRotate.x/i,t*this._contentSizeNoRotate.x/i)},viewportToImageCoordinates:function(e,t){if(e instanceof h.Point)return this.viewportToImageCoordinates(e.x,e.y);if(this.viewer){var i=this.viewer.world.getItemCount();if(1<i)this.silenceMultiImageWarnings||h.console.error("[Viewport.viewportToImageCoordinates] is not accurate with multi-image; use TiledImage.viewportToImageCoordinates instead.");else if(1===i)return this.viewer.world.getItemAt(0).viewportToImageCoordinates(e,t,!0)}return this._viewportToImageDelta(e-this._contentBoundsNoRotate.x,t-this._contentBoundsNoRotate.y)},_imageToViewportDelta:function(e,t){var i=this._contentBoundsNoRotate.width;return new h.Point(e/this._contentSizeNoRotate.x*i,t/this._contentSizeNoRotate.x*i)},imageToViewportCoordinates:function(e,t){if(e instanceof h.Point)return this.imageToViewportCoordinates(e.x,e.y);if(this.viewer){var i=this.viewer.world.getItemCount();if(1<i)this.silenceMultiImageWarnings||h.console.error("[Viewport.imageToViewportCoordinates] is not accurate with multi-image; use TiledImage.imageToViewportCoordinates instead.");else if(1===i)return this.viewer.world.getItemAt(0).imageToViewportCoordinates(e,t,!0)}t=this._imageToViewportDelta(e,t);t.x+=this._contentBoundsNoRotate.x;t.y+=this._contentBoundsNoRotate.y;return t},imageToViewportRectangle:function(e,t,i,n){var r=e;r instanceof h.Rect||(r=new h.Rect(e,t,i,n));if(this.viewer){var o=this.viewer.world.getItemCount();if(1<o)this.silenceMultiImageWarnings||h.console.error("[Viewport.imageToViewportRectangle] is not accurate with multi-image; use TiledImage.imageToViewportRectangle instead.");else if(1===o)return this.viewer.world.getItemAt(0).imageToViewportRectangle(e,t,i,n,!0)}i=this.imageToViewportCoordinates(r.x,r.y);n=this._imageToViewportDelta(r.width,r.height);return new h.Rect(i.x,i.y,n.x,n.y,r.degrees)},viewportToImageRectangle:function(e,t,i,n){var r=e;r instanceof h.Rect||(r=new h.Rect(e,t,i,n));if(this.viewer){var o=this.viewer.world.getItemCount();if(1<o)this.silenceMultiImageWarnings||h.console.error("[Viewport.viewportToImageRectangle] is not accurate with multi-image; use TiledImage.viewportToImageRectangle instead.");else if(1===o)return this.viewer.world.getItemAt(0).viewportToImageRectangle(e,t,i,n,!0)}i=this.viewportToImageCoordinates(r.x,r.y);n=this._viewportToImageDelta(r.width,r.height);return new h.Rect(i.x,i.y,n.x,n.y,r.degrees)},viewerElementToImageCoordinates:function(e){e=this.pointFromPixel(e,!0);return this.viewportToImageCoordinates(e)},imageToViewerElementCoordinates:function(e){e=this.imageToViewportCoordinates(e);return this.pixelFromPoint(e,!0)},windowToImageCoordinates:function(e){h.console.assert(this.viewer,"[Viewport.windowToImageCoordinates] the viewport must have a viewer.");e=e.minus(h.getElementPosition(this.viewer.element));return this.viewerElementToImageCoordinates(e)},imageToWindowCoordinates:function(e){h.console.assert(this.viewer,"[Viewport.imageToWindowCoordinates] the viewport must have a viewer.");return this.imageToViewerElementCoordinates(e).plus(h.getElementPosition(this.viewer.element))},viewerElementToViewportCoordinates:function(e){return this.pointFromPixel(e,!0)},viewportToViewerElementCoordinates:function(e){return this.pixelFromPoint(e,!0)},viewerElementToViewportRectangle:function(e){return h.Rect.fromSummits(this.pointFromPixel(e.getTopLeft(),!0),this.pointFromPixel(e.getTopRight(),!0),this.pointFromPixel(e.getBottomLeft(),!0))},viewportToViewerElementRectangle:function(e){return h.Rect.fromSummits(this.pixelFromPoint(e.getTopLeft(),!0),this.pixelFromPoint(e.getTopRight(),!0),this.pixelFromPoint(e.getBottomLeft(),!0))},windowToViewportCoordinates:function(e){h.console.assert(this.viewer,"[Viewport.windowToViewportCoordinates] the viewport must have a viewer.");e=e.minus(h.getElementPosition(this.viewer.element));return this.viewerElementToViewportCoordinates(e)},viewportToWindowCoordinates:function(e){h.console.assert(this.viewer,"[Viewport.viewportToWindowCoordinates] the viewport must have a viewer.");return this.viewportToViewerElementCoordinates(e).plus(h.getElementPosition(this.viewer.element))},viewportToImageZoom:function(e){if(this.viewer){var t=this.viewer.world.getItemCount();if(1<t)this.silenceMultiImageWarnings||h.console.error("[Viewport.viewportToImageZoom] is not accurate with multi-image.");else if(1===t)return this.viewer.world.getItemAt(0).viewportToImageZoom(e)}t=this._contentSizeNoRotate.x;return e*(this._containerInnerSize.x/t*this._contentBoundsNoRotate.width)},imageToViewportZoom:function(e){if(this.viewer){var t=this.viewer.world.getItemCount();if(1<t)this.silenceMultiImageWarnings||h.console.error("[Viewport.imageToViewportZoom] is not accurate with multi-image. Instead, use [TiledImage.imageToViewportZoom] for the specific image of interest");else if(1===t)return this.viewer.world.getItemAt(0).imageToViewportZoom(e)}return e*(this._contentSizeNoRotate.x/this._containerInnerSize.x/this._contentBoundsNoRotate.width)},toggleFlip:function(){this.setFlip(!this.getFlip());return this},getFlip:function(){return this.flipped},setFlip:function(e){if(this.flipped===e)return this;this.flipped=e;this.viewer.navigator&&this.viewer.navigator.setFlip(this.getFlip());this.viewer.forceRedraw();this.viewer.raiseEvent("flip",{flipped:e});return this},getMaxZoomPixelRatio:function(){return this.maxZoomPixelRatio},setMaxZoomPixelRatio:function(e,t=!0,i=!1){h.console.assert(!isNaN(e),"[Viewport.setMaxZoomPixelRatio] ratio must be a number");if(!isNaN(e)){this.maxZoomPixelRatio=e;t&&this.getZoom()>this.getMaxZoom()&&this.applyConstraints(i)}}}}(OpenSeadragon);!function(v){v.TiledImage=function(e){this._initialized=!1;v.console.assert(e.tileCache,"[TiledImage] options.tileCache is required");v.console.assert(e.drawer,"[TiledImage] options.drawer is required");v.console.assert(e.viewer,"[TiledImage] options.viewer is required");v.console.assert(e.imageLoader,"[TiledImage] options.imageLoader is required");v.console.assert(e.source,"[TiledImage] options.source is required");v.console.assert(!e.clip||e.clip instanceof v.Rect,"[TiledImage] options.clip must be an OpenSeadragon.Rect if present");v.EventSource.call(this);this._tileCache=e.tileCache;delete e.tileCache;this._drawer=e.drawer;delete e.drawer;this._imageLoader=e.imageLoader;delete e.imageLoader;e.clip instanceof v.Rect&&(this._clip=e.clip.clone());delete e.clip;var t=e.x||0;delete e.x;var i=e.y||0;delete e.y;this.normHeight=e.source.dimensions.y/e.source.dimensions.x;this.contentAspectX=e.source.dimensions.x/e.source.dimensions.y;var n=1;if(e.width){n=e.width;delete e.width;if(e.height){v.console.error("specifying both width and height to a tiledImage is not supported");delete e.height}}else if(e.height){n=e.height/this.normHeight;delete e.height}var r=e.fitBounds;delete e.fitBounds;var o=e.fitBoundsPlacement||OpenSeadragon.Placement.CENTER;delete e.fitBoundsPlacement;var s=e.degrees||0;delete e.degrees;var a=e.ajaxHeaders;delete e.ajaxHeaders;v.extend(!0,this,{viewer:null,tilesMatrix:{},coverage:{},loadingCoverage:{},lastDrawn:[],lastResetTime:0,_needsDraw:!0,_needsUpdate:!0,_hasOpaqueTile:!1,_tilesLoading:0,_tilesToDraw:[],_lastDrawn:[],_isBlending:!1,_wasBlending:!1,_isTainted:!1,springStiffness:v.DEFAULT_SETTINGS.springStiffness,animationTime:v.DEFAULT_SETTINGS.animationTime,minZoomImageRatio:v.DEFAULT_SETTINGS.minZoomImageRatio,wrapHorizontal:v.DEFAULT_SETTINGS.wrapHorizontal,wrapVertical:v.DEFAULT_SETTINGS.wrapVertical,immediateRender:v.DEFAULT_SETTINGS.immediateRender,blendTime:v.DEFAULT_SETTINGS.blendTime,alwaysBlend:v.DEFAULT_SETTINGS.alwaysBlend,minPixelRatio:v.DEFAULT_SETTINGS.minPixelRatio,smoothTileEdgesMinZoom:v.DEFAULT_SETTINGS.smoothTileEdgesMinZoom,iOSDevice:v.DEFAULT_SETTINGS.iOSDevice,debugMode:v.DEFAULT_SETTINGS.debugMode,crossOriginPolicy:v.DEFAULT_SETTINGS.crossOriginPolicy,ajaxWithCredentials:v.DEFAULT_SETTINGS.ajaxWithCredentials,placeholderFillStyle:v.DEFAULT_SETTINGS.placeholderFillStyle,opacity:v.DEFAULT_SETTINGS.opacity,preload:v.DEFAULT_SETTINGS.preload,compositeOperation:v.DEFAULT_SETTINGS.compositeOperation,subPixelRoundingForTransparency:v.DEFAULT_SETTINGS.subPixelRoundingForTransparency,maxTilesPerFrame:v.DEFAULT_SETTINGS.maxTilesPerFrame},e);this._preload=this.preload;delete this.preload;this._fullyLoaded=!1;this._xSpring=new v.Spring({initial:t,springStiffness:this.springStiffness,animationTime:this.animationTime});this._ySpring=new v.Spring({initial:i,springStiffness:this.springStiffness,animationTime:this.animationTime});this._scaleSpring=new v.Spring({initial:n,springStiffness:this.springStiffness,animationTime:this.animationTime});this._degreesSpring=new v.Spring({initial:s,springStiffness:this.springStiffness,animationTime:this.animationTime});this._updateForScale();r&&this.fitBounds(r,o,!0);this._ownAjaxHeaders={};this.setAjaxHeaders(a,!1);this._initialized=!0};v.extend(v.TiledImage.prototype,v.EventSource.prototype,{needsDraw:function(){return this._needsDraw},redraw:function(){this._needsDraw=!0},getFullyLoaded:function(){return this._fullyLoaded},_setFullyLoaded:function(e){if(e!==this._fullyLoaded){this._fullyLoaded=e;this.raiseEvent("fully-loaded-change",{fullyLoaded:this._fullyLoaded})}},reset:function(){this._tileCache.clearTilesFor(this);this.lastResetTime=v.now();this._needsDraw=!0},update:function(e){var t=this._xSpring.update();var i=this._ySpring.update();var n=this._scaleSpring.update();var r=this._degreesSpring.update();r=t||i||n||r||this._needsUpdate;if(r||e||!this._fullyLoaded){e=this._updateLevelsForViewport();this._setFullyLoaded(e)}this._needsUpdate=!1;if(r){this._updateForScale();this._raiseBoundsChange();return this._needsDraw=!0}return!1},setDrawn:function(){this._needsDraw=this._isBlending||this._wasBlending;return this._needsDraw},setTainted(e){this._isTainted=e},isTainted(){return this._isTainted},destroy:function(){this.reset();this.source.destroy&&this.source.destroy(this.viewer)},getBounds:function(e){return this.getBoundsNoRotate(e).rotate(this.getRotation(e),this._getRotationPoint(e))},getBoundsNoRotate:function(e){return e?new v.Rect(this._xSpring.current.value,this._ySpring.current.value,this._worldWidthCurrent,this._worldHeightCurrent):new v.Rect(this._xSpring.target.value,this._ySpring.target.value,this._worldWidthTarget,this._worldHeightTarget)},getWorldBounds:function(){v.console.error("[TiledImage.getWorldBounds] is deprecated; use TiledImage.getBounds instead");return this.getBounds()},getClippedBounds:function(e){var t=this.getBoundsNoRotate(e);if(this._clip){var i=(e?this._worldWidthCurrent:this._worldWidthTarget)/this.source.dimensions.x;i=this._clip.times(i);t=new v.Rect(t.x+i.x,t.y+i.y,i.width,i.height)}return t.rotate(this.getRotation(e),this._getRotationPoint(e))},getTileBounds:function(e,t,i){var n=this.source.getNumTiles(e);var r=(n.x+t%n.x)%n.x;var o=(n.y+i%n.y)%n.y;e=this.source.getTileBounds(e,r,o);this.getFlip()&&(e.x=Math.max(0,1-e.x-e.width));e.x+=(t-r)/n.x;e.y+=this._worldHeightCurrent/this._worldWidthCurrent*((i-o)/n.y);return e},getContentSize:function(){return new v.Point(this.source.dimensions.x,this.source.dimensions.y)},getSizeInWindowCoordinates:function(){var e=this.imageToWindowCoordinates(new v.Point(0,0));var t=this.imageToWindowCoordinates(this.getContentSize());return new v.Point(t.x-e.x,t.y-e.y)},_viewportToImageDelta:function(e,t,i){i=(i?this._scaleSpring.current:this._scaleSpring.target).value;return new v.Point(e*(this.source.dimensions.x/i),t*(this.source.dimensions.y*this.contentAspectX/i))},viewportToImageCoordinates:function(e,t,i){var n;if(e instanceof v.Point){i=t;n=e}else n=new v.Point(e,t);n=n.rotate(-this.getRotation(i),this._getRotationPoint(i));return i?this._viewportToImageDelta(n.x-this._xSpring.current.value,n.y-this._ySpring.current.value):this._viewportToImageDelta(n.x-this._xSpring.target.value,n.y-this._ySpring.target.value)},_imageToViewportDelta:function(e,t,i){i=(i?this._scaleSpring.current:this._scaleSpring.target).value;return new v.Point(e/this.source.dimensions.x*i,t/this.source.dimensions.y/this.contentAspectX*i)},imageToViewportCoordinates:function(e,t,i){if(e instanceof v.Point){i=t;t=e.y;e=e.x}t=this._imageToViewportDelta(e,t,i);if(i){t.x+=this._xSpring.current.value;t.y+=this._ySpring.current.value}else{t.x+=this._xSpring.target.value;t.y+=this._ySpring.target.value}return t.rotate(this.getRotation(i),this._getRotationPoint(i))},imageToViewportRectangle:function(e,t,i,n,r){var o=e;o instanceof v.Rect?r=t:o=new v.Rect(e,t,i,n);i=this.imageToViewportCoordinates(o.getTopLeft(),r);n=this._imageToViewportDelta(o.width,o.height,r);return new v.Rect(i.x,i.y,n.x,n.y,o.degrees+this.getRotation(r))},viewportToImageRectangle:function(e,t,i,n,r){var o=e;e instanceof v.Rect?r=t:o=new v.Rect(e,t,i,n);i=this.viewportToImageCoordinates(o.getTopLeft(),r);n=this._viewportToImageDelta(o.width,o.height,r);return new v.Rect(i.x,i.y,n.x,n.y,o.degrees-this.getRotation(r))},viewerElementToImageCoordinates:function(e){e=this.viewport.pointFromPixel(e,!0);return this.viewportToImageCoordinates(e)},imageToViewerElementCoordinates:function(e){e=this.imageToViewportCoordinates(e);return this.viewport.pixelFromPoint(e,!0)},windowToImageCoordinates:function(e){e=e.minus(OpenSeadragon.getElementPosition(this.viewer.element));return this.viewerElementToImageCoordinates(e)},imageToWindowCoordinates:function(e){return this.imageToViewerElementCoordinates(e).plus(OpenSeadragon.getElementPosition(this.viewer.element))},_viewportToTiledImageRectangle:function(e){var t=this._scaleSpring.current.value;e=e.rotate(-this.getRotation(!0),this._getRotationPoint(!0));return new v.Rect((e.x-this._xSpring.current.value)/t,(e.y-this._ySpring.current.value)/t,e.width/t,e.height/t,e.degrees)},viewportToImageZoom:function(e){return this._scaleSpring.current.value*this.viewport._containerInnerSize.x/this.source.dimensions.x*e},imageToViewportZoom:function(e){return e/(this._scaleSpring.current.value*this.viewport._containerInnerSize.x/this.source.dimensions.x)},setPosition:function(e,t){var i=this._xSpring.target.value===e.x&&this._ySpring.target.value===e.y;if(t){if(i&&this._xSpring.current.value===e.x&&this._ySpring.current.value===e.y)return;this._xSpring.resetTo(e.x);this._ySpring.resetTo(e.y);this._needsDraw=!0;this._needsUpdate=!0}else{if(i)return;this._xSpring.springTo(e.x);this._ySpring.springTo(e.y);this._needsDraw=!0;this._needsUpdate=!0}i||this._raiseBoundsChange()},setWidth:function(e,t){this._setScale(e,t)},setHeight:function(e,t){this._setScale(e/this.normHeight,t)},setCroppingPolygons:function(e){var t=function(e){return e instanceof v.Point||"number"==typeof e.x&&"number"==typeof e.y};try{if(!v.isArray(e))throw new Error("Provided cropping polygon is not an array");this._croppingPolygons=e.map(function(e){return e.map(function(e){try{if(t(e))return{x:e.x,y:e.y};throw new Error}catch(e){throw new Error("A Provided cropping polygon point is not supported")}})});this._needsDraw=!0}catch(e){v.console.error("[TiledImage.setCroppingPolygons] Cropping polygon format not supported");v.console.error(e);this.resetCroppingPolygons()}},resetCroppingPolygons:function(){this._croppingPolygons=null;this._needsDraw=!0},fitBounds:function(e,t,i){t=t||v.Placement.CENTER;var n=v.Placement.properties[t];var r=this.contentAspectX;var o=0;var s=0;var a=1;t=1;if(this._clip){r=this._clip.getAspectRatio();a=this._clip.width/this.source.dimensions.x;t=this._clip.height/this.source.dimensions.y;if(e.getAspectRatio()>r){o=this._clip.x/this._clip.height*e.height;s=this._clip.y/this._clip.height*e.height}else{o=this._clip.x/this._clip.width*e.width;s=this._clip.y/this._clip.width*e.width}}if(e.getAspectRatio()>r){var l=e.height/t;t=0;n.isHorizontallyCentered?t=(e.width-e.height*r)/2:n.isRight&&(t=e.width-e.height*r);this.setPosition(new v.Point(e.x-o+t,e.y-s),i);this.setHeight(l,i)}else{l=e.width/a;a=0;n.isVerticallyCentered?a=(e.height-e.width/r)/2:n.isBottom&&(a=e.height-e.width/r);this.setPosition(new v.Point(e.x-o,e.y-s+a),i);this.setWidth(l,i)}},getClip:function(){return this._clip?this._clip.clone():null},setClip:function(e){v.console.assert(!e||e instanceof v.Rect,"[TiledImage.setClip] newClip must be an OpenSeadragon.Rect or null");e instanceof v.Rect?this._clip=e.clone():this._clip=null;this._needsUpdate=!0;this._needsDraw=!0;this.raiseEvent("clip-change")},getFlip:function(){return this.flipped},setFlip:function(e){this.flipped=e},get flipped(){return this._flipped},set flipped(e){var t=this._flipped!==!!e;this._flipped=!!e;if(t){this.update(!0);this._needsDraw=!0;this._raiseBoundsChange()}},get wrapHorizontal(){return this._wrapHorizontal},set wrapHorizontal(e){var t=this._wrapHorizontal!==!!e;this._wrapHorizontal=!!e;if(this._initialized&&t){this.update(!0);this._needsDraw=!0}},get wrapVertical(){return this._wrapVertical},set wrapVertical(e){var t=this._wrapVertical!==!!e;this._wrapVertical=!!e;if(this._initialized&&t){this.update(!0);this._needsDraw=!0}},get debugMode(){return this._debugMode},set debugMode(e){this._debugMode=!!e;this._needsDraw=!0},getOpacity:function(){return this.opacity},setOpacity:function(e){this.opacity=e},get opacity(){return this._opacity},set opacity(e){if(e!==this.opacity){this._opacity=e;this._needsDraw=!0;this.raiseEvent("opacity-change",{opacity:this.opacity})}},getPreload:function(){return this._preload},setPreload:function(e){this._preload=!!e;this._needsDraw=!0},getRotation:function(e){return(e?this._degreesSpring.current:this._degreesSpring.target).value},setRotation:function(e,t){if(this._degreesSpring.target.value!==e||!this._degreesSpring.isAtTargetValue()){t?this._degreesSpring.resetTo(e):this._degreesSpring.springTo(e);this._needsDraw=!0;this._needsUpdate=!0;this._raiseBoundsChange()}},getDrawArea:function(){if(0===this._opacity&&!this._preload)return!1;var e=this._viewportToTiledImageRectangle(this.viewport.getBoundsWithMargins(!0));if(!this.wrapHorizontal&&!this.wrapVertical){var t=this._viewportToTiledImageRectangle(this.getClippedBounds(!0));e=e.intersection(t)}return e},getTilesToDraw:function(){let e=this._tilesToDraw.flat();this._updateTilesInViewport(e);e=this._tilesToDraw.flat();e.forEach(e=>{e.tile.beingDrawn=!0});this._lastDrawn=e;return e},_getRotationPoint:function(e){return this.getBoundsNoRotate(e).getCenter()},get compositeOperation(){return this._compositeOperation},set compositeOperation(e){if(e!==this._compositeOperation){this._compositeOperation=e;this._needsDraw=!0;this.raiseEvent("composite-operation-change",{compositeOperation:this._compositeOperation})}},getCompositeOperation:function(){return this._compositeOperation},setCompositeOperation:function(e){this.compositeOperation=e},setAjaxHeaders:function(e,t){if(v.isPlainObject(e=null===e?{}:e)){this._ownAjaxHeaders=e;this._updateAjaxHeaders(t)}else console.error("[TiledImage.setAjaxHeaders] Ignoring invalid headers, must be a plain object")},_updateAjaxHeaders:function(e){void 0===e&&(e=!0);v.isPlainObject(this.viewer.ajaxHeaders)?this.ajaxHeaders=v.extend({},this.viewer.ajaxHeaders,this._ownAjaxHeaders):this.ajaxHeaders=this._ownAjaxHeaders;if(e){var t,i;for(var n in this.tilesMatrix){t=this.source.getNumTiles(n);for(var r in this.tilesMatrix[n]){i=(t.x+r%t.x)%t.x;for(var o in this.tilesMatrix[n][r]){s=(t.y+o%t.y)%t.y;(o=this.tilesMatrix[n][r][o]).loadWithAjax=this.loadTilesWithAjax;if(o.loadWithAjax){var s=this.source.getTileAjaxHeaders(n,i,s);o.ajaxHeaders=v.extend({},this.ajaxHeaders,s)}else o.ajaxHeaders=null}}}for(var a=0;a<this._imageLoader.jobQueue.length;a++){var l=this._imageLoader.jobQueue[a];l.loadWithAjax=l.tile.loadWithAjax;l.ajaxHeaders=l.tile.loadWithAjax?l.tile.ajaxHeaders:null}}},_setScale:function(e,t){var i=this._scaleSpring.target.value===e;if(t){if(i&&this._scaleSpring.current.value===e)return;this._scaleSpring.resetTo(e);this._updateForScale();this._needsDraw=!0;this._needsUpdate=!0}else{if(i)return;this._scaleSpring.springTo(e);this._updateForScale();this._needsDraw=!0;this._needsUpdate=!0}i||this._raiseBoundsChange()},_updateForScale:function(){this._worldWidthTarget=this._scaleSpring.target.value;this._worldHeightTarget=this.normHeight*this._scaleSpring.target.value;this._worldWidthCurrent=this._scaleSpring.current.value;this._worldHeightCurrent=this.normHeight*this._scaleSpring.current.value},_raiseBoundsChange:function(){this.raiseEvent("bounds-change")},_isBottomItem:function(){return this.viewer.world.getItemAt(0)===this},_getLevelsInterval:function(){var e=Math.max(this.source.minLevel,Math.floor(Math.log(this.minZoomImageRatio)/Math.log(2)));var t=this.viewport.deltaPixelsFromPointsNoRotate(this.source.getPixelRatio(0),!0).x*this._scaleSpring.current.value;t=Math.min(Math.abs(this.source.maxLevel),Math.abs(Math.floor(Math.log(t/this.minPixelRatio)/Math.log(2))));t=Math.max(t,this.source.minLevel||0);return{lowestLevel:Math.min(e,t),highestLevel:t}},_updateLevelsForViewport:function(){var i=this._getLevelsInterval();var n=i.lowestLevel;i=i.highestLevel;var t=[];var r=this.getDrawArea();var o=v.now();this._lastDrawn.forEach(e=>{e.tile.beingDrawn=!1});this._tilesToDraw=[];this._tilesLoading=0;this.loadingCoverage={};if(!r){this._needsDraw=!1;return this._fullyLoaded}var s=new Array(i-n+1);for(let e=0,t=i;t>=n;t--,e++)s[e]=t;for(let e=i+1;e<=this.source.maxLevel;e++){var a=this.tilesMatrix[e]&&this.tilesMatrix[e][0]&&this.tilesMatrix[e][0][0];if(a&&a.isBottomMost&&a.isRightMost&&a.loaded){s.push(e);break}}let l=!1;for(let e=0;e<s.length;e++){var h=s[e];var c=this.viewport.deltaPixelsFromPointsNoRotate(this.source.getPixelRatio(h),!0).x*this._scaleSpring.current.value;if(e===s.length-1||c>=this.minPixelRatio)l=!0;else if(!l)continue;var u=this.viewport.deltaPixelsFromPointsNoRotate(this.source.getPixelRatio(h),!1).x*this._scaleSpring.current.value;var d=this.viewport.deltaPixelsFromPointsNoRotate(this.source.getPixelRatio(Math.max(this.source.getClosestLevel(),0)),!1).x*this._scaleSpring.current.value;d=this.immediateRender?1:d;c=Math.min(1,(c-.5)/.5);u=d/Math.abs(d-u);u=this._updateLevel(h,c,u,r,o,t);t=u.bestTiles;u=u.updatedTiles.filter(e=>e.loaded);c=function(t,i,n){return function(e){return{tile:e,level:t,levelOpacity:i,currentTime:n}}}(h,c,o);this._tilesToDraw[h]=u.map(c);if(this._providesCoverage(this.coverage,h))break}if(t&&0<t.length){t.forEach(function(e){e&&!e.context2D&&this._loadTile(e,o)},this);return!(this._needsDraw=!0)}return 0===this._tilesLoading},_updateTilesInViewport:function(i){let n=v.now();let r=this;this._tilesLoading=0;this._wasBlending=this._isBlending;this._isBlending=!1;this.loadingCoverage={};let o=i.length?i[0].level:0;if(this.getDrawArea()){let t=0;for(let e=0;e<i.length;e++){var s=i[e];!function(e){var t=e.tile;if(t&&t.loaded){e=r._blendTile(t,t.x,t.y,e.level,e.levelOpacity,n,o);r._isBlending=r._isBlending||e;r._needsDraw=r._needsDraw||e||r._wasBlending}}(s);this._providesCoverage(this.coverage,s.level)&&(t=Math.max(t,s.level))}if(0<t)for(var e in this._tilesToDraw)e<t&&delete this._tilesToDraw[e]}},_blendTile:function(e,t,i,n,r,o,s){let a=1e3*this.blendTime,l,h;e.blendStart||(e.blendStart=o);l=o-e.blendStart;h=a?Math.min(1,l/a):1;if(n===s){h=1;l=a}this.alwaysBlend&&(h*=r);e.opacity=h;if(1===h){this._setCoverage(this.coverage,n,t,i,!0);this._hasOpaqueTile=!0}return l<a},_updateLevel:function(e,t,i,n,r,o){var s=n.getBoundingBox().getTopLeft();var a=n.getBoundingBox().getBottomRight();this.viewer&&this.viewer.raiseEvent("update-level",{tiledImage:this,havedrawn:!0,level:e,opacity:t,visibility:i,drawArea:n,topleft:s,bottomright:a,currenttime:r,best:o});this._resetCoverage(this.coverage,e);this._resetCoverage(this.loadingCoverage,e);a=this._getCornerTiles(e,s,a);var l=a.topLeft;var h=a.bottomRight;var c=this.source.getNumTiles(e);var u=this.viewport.pixelFromPoint(this.viewport.getCenter());if(this.getFlip()){h.x+=1;this.wrapHorizontal||(h.x=Math.min(h.x,c.x-1))}a=Math.max(0,(h.x-l.x)*(h.y-l.y));var d=new Array(a);var p=0;for(var g=l.x;g<=h.x;g++)for(var m=l.y;m<=h.y;m++){var v;if(this.getFlip()){var f=(c.x+g%c.x)%c.x;v=g+c.x-f-f-1}else v=g;if(null!==n.intersection(this.getTileBounds(e,v,m))){f=this._updateTile(v,m,e,i,u,c,r,o);o=f.bestTiles;d[p]=f.tile;p+=1}}return{bestTiles:o,updatedTiles:d}},_positionTile:function(e,t,i,n,r){var o=e.bounds.getTopLeft();o.x*=this._scaleSpring.current.value;o.y*=this._scaleSpring.current.value;o.x+=this._xSpring.current.value;o.y+=this._ySpring.current.value;var s=e.bounds.getSize();s.x*=this._scaleSpring.current.value;s.y*=this._scaleSpring.current.value;e.positionedBounds.x=o.x;e.positionedBounds.y=o.y;e.positionedBounds.width=s.x;e.positionedBounds.height=s.y;var a=i.pixelFromPointNoRotate(o,!0),l=i.pixelFromPointNoRotate(o,!1),o=i.deltaPixelsFromPointsNoRotate(s,!0),s=i.deltaPixelsFromPointsNoRotate(s,!1),s=l.plus(s.divide(2)),s=n.squaredDistanceTo(s);if(this.viewer.drawer.minimumOverlapRequired(this)){t||(o=o.plus(new v.Point(1,1)));e.isRightMost&&this.wrapHorizontal&&(o.x+=.75);e.isBottomMost&&this.wrapVertical&&(o.y+=.75)}e.position=a;e.size=o;e.squaredDistance=s;e.visibility=r},_updateTile:function(e,t,i,n,r,o,s,a){s=this._getTile(e,t,i,s,o);this.viewer&&this.viewer.raiseEvent("update-tile",{tiledImage:this,tile:s});this._setCoverage(this.coverage,i,e,t,!1);o=s.loaded||s.loading||this._isCovered(this.loadingCoverage,i,e,t);this._setCoverage(this.loadingCoverage,i,e,t,o);if(!s.exists)return{bestTiles:a,tile:s};s.loaded&&1===s.opacity&&this._setCoverage(this.coverage,i,e,t,!0);this._positionTile(s,this.source.tileOverlap,this.viewport,r,n);if(!s.loaded)if(s.context2D)this._setTileLoaded(s);else{n=this._tileCache.getImageRecord(s.cacheKey);n&&this._setTileLoaded(s,n.getData())}s.loading?this._tilesLoading++:o||(a=this._compareTiles(a,s,this.maxTilesPerFrame));return{bestTiles:a,tile:s}},_getCornerTiles:function(e,t,i){var n;var r;if(this.wrapHorizontal){n=v.positiveModulo(t.x,1);r=v.positiveModulo(i.x,1)}else{n=Math.max(0,t.x);r=Math.min(1,i.x)}var o=1/this.source.aspectRatio;if(this.wrapVertical){s=v.positiveModulo(t.y,o);a=v.positiveModulo(i.y,o)}else{s=Math.max(0,t.y);a=Math.min(o,i.y)}var s=this.source.getTileAtPoint(e,new v.Point(n,s));var a=this.source.getTileAtPoint(e,new v.Point(r,a));e=this.source.getNumTiles(e);if(this.wrapHorizontal){s.x+=e.x*Math.floor(t.x);a.x+=e.x*Math.floor(i.x)}if(this.wrapVertical){s.y+=e.y*Math.floor(t.y/o);a.y+=e.y*Math.floor(i.y/o)}return{topLeft:s,bottomRight:a}},_getTile:function(e,t,i,n,r){var o,s,a,l,h,c,u,d,p,g=this.tilesMatrix,m=this.source;g[i]||(g[i]={});g[i][e]||(g[i][e]={});if(!g[i][e][t]||!g[i][e][t].flipped!=!this.flipped){o=(r.x+e%r.x)%r.x;s=(r.y+t%r.y)%r.y;a=this.getTileBounds(i,e,t);l=m.getTileBounds(i,o,s,!0);h=m.tileExists(i,o,s);c=m.getTileUrl(i,o,s);p=m.getTilePostData(i,o,s);if(this.loadTilesWithAjax){u=m.getTileAjaxHeaders(i,o,s);v.isPlainObject(this.ajaxHeaders)&&(u=v.extend({},this.ajaxHeaders,u))}else u=null;d=m.getContext2D?m.getContext2D(i,o,s):void 0;p=new v.Tile(i,e,t,a,h,c,d,this.loadTilesWithAjax,u,l,p,m.getTileHashKey(i,o,s,c,u,p));this.getFlip()?0==o&&(p.isRightMost=!0):o==r.x-1&&(p.isRightMost=!0);s==r.y-1&&(p.isBottomMost=!0);p.flipped=this.flipped;g[i][e][t]=p}(p=g[i][e][t]).lastTouchTime=n;return p},_loadTile:function(n,r){var o=this;n.loading=!0;this._imageLoader.addJob({src:n.getUrl(),tile:n,source:this.source,postData:n.postData,loadWithAjax:n.loadWithAjax,ajaxHeaders:n.ajaxHeaders,crossOriginPolicy:this.crossOriginPolicy,ajaxWithCredentials:this.ajaxWithCredentials,callback:function(e,t,i){o._onTileLoad(n,r,e,t,i)},abort:function(){n.loading=!1}})},_onTileLoad:function(e,t,i,n,r){if(i){e.exists=!0;if(t<this.lastResetTime){v.console.warn("Ignoring tile %s loaded before reset: %s",e,e.getUrl());e.loading=!1}else{var o=this;s=o.source.getClosestLevel(),o._setTileLoaded(e,i,s,r);var s}}else{v.console.error("Tile %s failed to load: %s - error: %s",e,e.getUrl(),n);this.viewer.raiseEvent("tile-load-failed",{tile:e,tiledImage:this,time:t,message:n,tileRequest:r});e.loading=!1;e.exists=!1}},_setTileLoaded:function(e,t,i,n){var r=0,o=!1,s=this;function a(){o&&v.console.error("Event 'tile-loaded' argument getCompletionCallback must be called synchronously. Its return value should be called asynchronously.");r++;return l}function l(){if(0===--r){e.loading=!1;e.loaded=!0;e.hasTransparency=s.source.hasTransparency(e.context2D,e.getUrl(),e.ajaxHeaders,e.postData);e.context2D||s._tileCache.cacheTile({data:t,tile:e,cutoff:i,tiledImage:s});s.viewer.raiseEvent("tile-ready",{tile:e,tiledImage:s,tileRequest:n});s._needsDraw=!0}}var h=a();this.viewer.raiseEvent("tile-loaded",{tile:e,tiledImage:this,tileRequest:n,get image(){v.console.error("[tile-loaded] event 'image' has been deprecated. Use 'data' property instead.");return t},data:t,getCompletionCallback:a});o=!0;h()},_compareTiles:function(e,t,i){if(!e)return[t];e.push(t);this._sortTiles(e);e.length>i&&e.pop();return e},_sortTiles:function(e){e.sort(function(e,t){return null===e?1:null===t?-1:e.visibility===t.visibility?e.squaredDistance-t.squaredDistance:t.visibility-e.visibility})},_providesCoverage:function(e,t,i,n){var r,o,s,a;if(!e[t])return!1;if(void 0!==i&&void 0!==n)return void 0===e[t][i]||void 0===e[t][i][n]||!0===e[t][i][n];for(s in r=e[t])if(Object.prototype.hasOwnProperty.call(r,s))for(a in o=r[s])if(Object.prototype.hasOwnProperty.call(o,a)&&!o[a])return!1;return!0},_isCovered:function(e,t,i,n){return void 0===i||void 0===n?this._providesCoverage(e,t+1):this._providesCoverage(e,t+1,2*i,2*n)&&this._providesCoverage(e,t+1,2*i,2*n+1)&&this._providesCoverage(e,t+1,2*i+1,2*n)&&this._providesCoverage(e,t+1,2*i+1,2*n+1)},_setCoverage:function(e,t,i,n,r){if(e[t]){e[t][i]||(e[t][i]={});e[t][i][n]=r}else v.console.warn("Setting coverage for a tile before its level's coverage has been reset: %s",t)},_resetCoverage:function(e,t){e[t]={}}})}(OpenSeadragon);!function(g){function m(e){g.console.assert(e,"[TileCache.cacheTile] options is required");g.console.assert(e.tile,"[TileCache.cacheTile] options.tile is required");g.console.assert(e.tiledImage,"[TileCache.cacheTile] options.tiledImage is required");this.tile=e.tile;this.tiledImage=e.tiledImage}function v(e){g.console.assert(e,"[ImageRecord] options is required");g.console.assert(e.data,"[ImageRecord] options.data is required");this._tiles=[];e.create.apply(null,[this,e.data,e.ownerTile]);this._destroyImplementation=e.destroy.bind(null,this);this.getImage=e.getImage.bind(null,this);this.getData=e.getData.bind(null,this);this.getRenderedContext=e.getRenderedContext.bind(null,this)}v.prototype={destroy:function(){this._destroyImplementation();this._tiles=null},addTile:function(e){g.console.assert(e,"[ImageRecord.addTile] tile is required");this._tiles.push(e)},removeTile:function(e){for(var t=0;t<this._tiles.length;t++)if(this._tiles[t]===e){this._tiles.splice(t,1);return}g.console.warn("[ImageRecord.removeTile] trying to remove unknown tile",e)},getTileCount:function(){return this._tiles.length}};g.TileCache=function(e){this._maxImageCacheCount=(e=e||{}).maxImageCacheCount||g.DEFAULT_SETTINGS.maxImageCacheCount;this._tilesLoaded=[];this._imagesLoaded=[];this._imagesLoadedCount=0};g.TileCache.prototype={numTilesLoaded:function(){return this._tilesLoaded.length},cacheTile:function(e){g.console.assert(e,"[TileCache.cacheTile] options is required");g.console.assert(e.tile,"[TileCache.cacheTile] options.tile is required");g.console.assert(e.tile.cacheKey,"[TileCache.cacheTile] options.tile.cacheKey is required");g.console.assert(e.tiledImage,"[TileCache.cacheTile] options.tiledImage is required");var t=e.cutoff||0;var i=this._tilesLoaded.length;var n=this._imagesLoaded[e.tile.cacheKey];if(!n){if(!e.data){g.console.error("[TileCache.cacheTile] options.image was renamed to options.data. '.image' attribute has been deprecated and will be removed in the future.");e.data=e.image}g.console.assert(e.data,"[TileCache.cacheTile] options.data is required to create an ImageRecord");n=this._imagesLoaded[e.tile.cacheKey]=new v({data:e.data,ownerTile:e.tile,create:e.tiledImage.source.createTileCache,destroy:e.tiledImage.source.destroyTileCache,getImage:e.tiledImage.source.getTileCacheDataAsImage,getData:e.tiledImage.source.getTileCacheData,getRenderedContext:e.tiledImage.source.getTileCacheDataAsContext2D});this._imagesLoadedCount++}n.addTile(e.tile);e.tile.cacheImageRecord=n;if(this._imagesLoadedCount>this._maxImageCacheCount){var r=null;var o=-1;var s=null;var a,l,h,c,u,d;for(var p=this._tilesLoaded.length-1;0<=p;p--)if(!((a=(d=this._tilesLoaded[p]).tile).level<=t||a.beingDrawn))if(r){c=a.lastTouchTime;l=r.lastTouchTime;u=a.level;h=r.level;if(c<l||c===l&&h<u){r=a;o=p;s=d}}else{r=a;o=p;s=d}if(r&&0<=o){this._unloadTile(s);i=o}}this._tilesLoaded[i]=new m({tile:e.tile,tiledImage:e.tiledImage})},clearTilesFor:function(e){g.console.assert(e,"[TileCache.clearTilesFor] tiledImage is required");var t;for(var i=0;i<this._tilesLoaded.length;++i)if((t=this._tilesLoaded[i]).tiledImage===e){this._unloadTile(t);this._tilesLoaded.splice(i,1);i--}},getImageRecord:function(e){g.console.assert(e,"[TileCache.getImageRecord] cacheKey is required");return this._imagesLoaded[e]},_unloadTile:function(e){g.console.assert(e,"[TileCache._unloadTile] tileRecord is required");var t=e.tile;var i=e.tiledImage;let n=t.getCanvasContext&&t.getCanvasContext();t.unload();t.cacheImageRecord=null;e=this._imagesLoaded[t.cacheKey];if(e){e.removeTile(t);if(!e.getTileCount()){e.destroy();delete this._imagesLoaded[t.cacheKey];this._imagesLoadedCount--;if(n){n.canvas.width=0;n.canvas.height=0;i.viewer.raiseEvent("image-unloaded",{context2D:n,tile:t})}}i.viewer.raiseEvent("tile-unloaded",{tile:t,tiledImage:i})}}}}(OpenSeadragon);!function(g){g.World=function(e){var t=this;g.console.assert(e.viewer,"[World] options.viewer is required");g.EventSource.call(this);this.viewer=e.viewer;this._items=[];this._needsDraw=!1;this._autoRefigureSizes=!0;this._needsSizesFigured=!1;this._delegatedFigureSizes=function(e){t._autoRefigureSizes?t._figureSizes():t._needsSizesFigured=!0};this._figureSizes()};g.extend(g.World.prototype,g.EventSource.prototype,{addItem:function(e,t){g.console.assert(e,"[World.addItem] item is required");g.console.assert(e instanceof g.TiledImage,"[World.addItem] only TiledImages supported at this time");if(void 0!==(t=t||{}).index){t=Math.max(0,Math.min(this._items.length,t.index));this._items.splice(t,0,e)}else this._items.push(e);this._autoRefigureSizes?this._figureSizes():this._needsSizesFigured=!0;this._needsDraw=!0;e.addHandler("bounds-change",this._delegatedFigureSizes);e.addHandler("clip-change",this._delegatedFigureSizes);this.raiseEvent("add-item",{item:e})},getItemAt:function(e){g.console.assert(void 0!==e,"[World.getItemAt] index is required");return this._items[e]},getIndexOfItem:function(e){g.console.assert(e,"[World.getIndexOfItem] item is required");return g.indexOf(this._items,e)},getItemCount:function(){return this._items.length},setItemIndex:function(e,t){g.console.assert(e,"[World.setItemIndex] item is required");g.console.assert(void 0!==t,"[World.setItemIndex] index is required");var i=this.getIndexOfItem(e);if(t>=this._items.length)throw new Error("Index bigger than number of layers.");if(t!==i&&-1!==i){this._items.splice(i,1);this._items.splice(t,0,e);this._needsDraw=!0;this.raiseEvent("item-index-change",{item:e,previousIndex:i,newIndex:t})}},removeItem:function(e){g.console.assert(e,"[World.removeItem] item is required");var t=g.indexOf(this._items,e);if(-1!==t){e.removeHandler("bounds-change",this._delegatedFigureSizes);e.removeHandler("clip-change",this._delegatedFigureSizes);e.destroy();this._items.splice(t,1);this._figureSizes();this._needsDraw=!0;this._raiseRemoveItem(e)}},removeAll:function(){this.viewer._cancelPendingImages();var e;var t;for(t=0;t<this._items.length;t++){(e=this._items[t]).removeHandler("bounds-change",this._delegatedFigureSizes);e.removeHandler("clip-change",this._delegatedFigureSizes);e.destroy()}var i=this._items;this._items=[];this._figureSizes();this._needsDraw=!0;for(t=0;t<i.length;t++){e=i[t];this._raiseRemoveItem(e)}},resetItems:function(){for(var e=0;e<this._items.length;e++)this._items[e].reset()},update:function(e){var t=!1;for(var i=0;i<this._items.length;i++)t=this._items[i].update(e)||t;return t},draw:function(){this.viewer.drawer.draw(this._items);this._needsDraw=!1;this._items.forEach(e=>{this._needsDraw=e.setDrawn()||this._needsDraw})},needsDraw:function(){for(var e=0;e<this._items.length;e++)if(this._items[e].needsDraw())return!0;return this._needsDraw},getHomeBounds:function(){return this._homeBounds.clone()},getContentFactor:function(){return this._contentFactor},setAutoRefigureSizes:function(e){if((this._autoRefigureSizes=e)&this._needsSizesFigured){this._figureSizes();this._needsSizesFigured=!1}},arrange:function(e){var t=(e=e||{}).immediately||!1;var i=e.layout||g.DEFAULT_SETTINGS.collectionLayout;var n=e.rows||g.DEFAULT_SETTINGS.collectionRows;var r=e.columns||g.DEFAULT_SETTINGS.collectionColumns;var o=e.tileSize||g.DEFAULT_SETTINGS.collectionTileSize;var s=o+(e.tileMargin||g.DEFAULT_SETTINGS.collectionTileMargin);var a;a=!e.rows&&r?r:Math.ceil(this._items.length/n);var l=0;var h=0;var c,u,d;this.setAutoRefigureSizes(!1);for(var p=0;p<this._items.length;p++){if(p&&p%a==0)if("horizontal"===i){h+=s;l=0}else{l+=s;h=0}d=(u=(d=(c=this._items[p]).getBounds()).width>d.height?o:o*(d.width/d.height))*(d.height/d.width);d=new g.Point(l+(o-u)/2,h+(o-d)/2);c.setPosition(d,t);c.setWidth(u,t);"horizontal"===i?l+=s:h+=s}this.setAutoRefigureSizes(!0)},_figureSizes:function(){var e=this._homeBounds?this._homeBounds.clone():null;var t=this._contentSize?this._contentSize.clone():null;var i=this._contentFactor||0;if(this._items.length){var n=this._items[0];var r=n.getBounds();this._contentFactor=n.getContentSize().x/r.width;var o=n.getClippedBounds().getBoundingBox();var s=o.x;var a=o.y;var l=o.x+o.width;var h=o.y+o.height;for(var c=1;c<this._items.length;c++){r=(n=this._items[c]).getBounds();this._contentFactor=Math.max(this._contentFactor,n.getContentSize().x/r.width);o=n.getClippedBounds().getBoundingBox();s=Math.min(s,o.x);a=Math.min(a,o.y);l=Math.max(l,o.x+o.width);h=Math.max(h,o.y+o.height)}this._homeBounds=new g.Rect(s,a,l-s,h-a);this._contentSize=new g.Point(this._homeBounds.width*this._contentFactor,this._homeBounds.height*this._contentFactor)}else{this._homeBounds=new g.Rect(0,0,1,1);this._contentSize=new g.Point(1,1);this._contentFactor=1}this._contentFactor===i&&this._homeBounds.equals(e)&&this._contentSize.equals(t)||this.raiseEvent("metrics-change",{})},_raiseRemoveItem:function(e){this.raiseEvent("remove-item",{item:e})}})}(OpenSeadragon);
//# sourceMappingURL=openseadragon.min.js.map