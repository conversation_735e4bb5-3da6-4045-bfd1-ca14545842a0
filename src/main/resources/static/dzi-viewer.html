<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenSeadragon DZI 查看器</title>
    <!-- 
    修复说明：SDPC格式切片加载问题修复
    问题描述：
    1. 点击加载切片后，先在左上角显示缩略图
    2. 然后自动放大，瓦片图覆盖缩略图
    3. 导致显示不一致和用户体验差
    
    修复方案：
    1. 移除独立的缩略图显示逻辑，直接加载DZI查看器
    2. 禁用SDPC文件的自动填充和自动缩放
    3. 设置合适的初始视图，避免自动放大行为
    4. 优化OpenSeadragon配置，确保显示一致性
    -->
    <!-- 禁用favicon请求，避免404错误 -->
    <link rel="icon" href="data:,">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .header {
            background-color: #4472C4;
            color: white;
            padding: 10px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 0;
        }
        .input-group {
            padding: 10px 20px;
            background-color: #f8f8f8;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
            font-size: 16px;
        }
        button {
            padding: 8px 16px;
            background-color: #4472C4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #365899;
        }
        #viewer-container {
            flex: 1;
            width: 100%;
            border: none;
            background-color: #f8f8f8;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            /* 新增：确保容器能够正确响应尺寸变化 */
            min-height: 400px;
            max-width: 100vw;
            max-height: calc(100vh - 120px); /* 减去头部和控制区域的高度 */
        }
        
        /* 新增：响应式媒体查询，优化不同屏幕尺寸的显示 */
        @media screen and (max-width: 768px) {
            /* 移动设备优化 */
            .input-group {
                flex-wrap: wrap;
                gap: 5px;
            }
            input[type="text"] {
                width: 200px;
                font-size: 14px;
            }
            button {
                font-size: 14px;
                padding: 6px 12px;
            }
            #viewer-container {
                min-height: 300px;
                max-height: calc(100vh - 140px);
            }
        }
        
        @media screen and (min-width: 1200px) {
            /* 大屏幕优化 */
            #viewer-container {
                min-height: 600px;
                max-height: calc(100vh - 110px);
            }
        }
        
        @media screen and (min-width: 1600px) {
            /* 超大屏幕优化 */
            #viewer-container {
                min-height: 800px;
                max-height: calc(100vh - 100px);
            }
        }
        
        /* 新增：确保OpenSeadragon容器完全填充父容器 */
        #viewer-container > div {
            width: 100% !important;
            height: 100% !important;
        }
    </style>
    <!-- 引入OpenSeadragon库 - 使用本地资源 -->
    <script>
        // OpenSeadragon库加载状态检查
        let openSeadragonLoaded = false;

        // 使用本地资源，避免国际网络加载问题
        function loadOpenSeadragon() {
            console.log('加载本地OpenSeadragon库...');

            const script = document.createElement('script');
            script.src = '/slice/openseadragon/js/openseadragon.min.js';
            script.async = false; // 同步加载确保立即可用

            script.onload = function() {
                if (typeof OpenSeadragon !== 'undefined') {
                    openSeadragonLoaded = true;
                    console.log('本地OpenSeadragon加载成功');
                    // 触发自定义事件，通知其他代码库已加载
                    window.dispatchEvent(new CustomEvent('openSeadragonLoaded'));
                } else {
                    console.error('本地OpenSeadragon加载失败：OpenSeadragon未定义');
                    showOpenSeadragonError();
                }
            };

            script.onerror = function() {
                console.error('本地OpenSeadragon文件加载失败');
                showOpenSeadragonError();
            };

            document.head.appendChild(script);
        }

        function showOpenSeadragonError() {
            const container = document.getElementById('viewer-container');
            if (container) {
                container.innerHTML = `
                    <div style="display:flex; justify-content:center; align-items:center; height:100%; text-align:center; padding:20px;">
                        <div>
                            <h3 style="color: #dc3545;">OpenSeadragon库加载失败</h3>
                            <p>本地资源文件可能缺失或损坏</p>
                            <button onclick="location.reload()" style="margin:10px; padding:8px 16px; background:#6c757d; color:white; border:none; border-radius:4px; cursor:pointer;">
                                刷新页面
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        // 立即加载本地资源
        loadOpenSeadragon();
    </script>
</head>
<body>
<div class="header">
    <h1>OpenSeadragon DZI 查看器</h1>
</div>

<div class="container">
    <div class="input-group">
        <label for="identifier">切片标识符 (TaskId):</label>
        <input type="text" id="identifier" placeholder="输入切片标识符（例如：01JXXSFGWNJ6RX4V5ZAQN8VR5J）" value="">
        <button onclick="loadSlide()">加载切片</button>
    </div>

    <!-- 颜色调整控制面板 -->
    <div class="color-control-panel" style="padding: 10px 20px; background-color: #f0f0f0; border-bottom: 1px solid #ddd;">
        <div style="display: flex; align-items: center; gap: 20px; flex-wrap: wrap;">
            <h4 style="margin: 0; color: #333;">颜色调整：</h4>

            <div class="control-item">
                <label for="brightness">亮度：</label>
                <input type="range" id="brightness" min="-1" max="1" step="0.1" value="0" style="width: 120px;">
                <span id="brightness-value">0.0</span>
            </div>

            <div class="control-item">
                <label for="contrast">对比度：</label>
                <input type="range" id="contrast" min="0" max="2" step="0.1" value="1" style="width: 120px;">
                <span id="contrast-value">1.0</span>
            </div>

            <div class="control-item">
                <label for="gamma">伽马：</label>
                <input type="range" id="gamma" min="0.1" max="3" step="0.1" value="1" style="width: 120px;">
                <span id="gamma-value">1.0</span>
            </div>

            <div class="control-item">
                <label for="saturation">饱和度：</label>
                <input type="range" id="saturation" min="0" max="2" step="0.1" value="1" style="width: 120px;">
                <span id="saturation-value">1.0</span>
            </div>

            <div class="control-item">
                <label for="hue">色调：</label>
                <input type="range" id="hue" min="-180" max="180" step="10" value="0" style="width: 120px;">
                <span id="hue-value">0°</span>
            </div>

            <div class="control-item">
                <label for="redGain">红色增益：</label>
                <input type="range" id="redGain" min="0" max="2" step="0.1" value="1" style="width: 120px;">
                <span id="redGain-value">1.0</span>
            </div>

            <div class="control-item">
                <label for="greenGain">绿色增益：</label>
                <input type="range" id="greenGain" min="0" max="2" step="0.1" value="1" style="width: 120px;">
                <span id="greenGain-value">1.0</span>
            </div>

            <div class="control-item">
                <label for="blueGain">蓝色增益：</label>
                <input type="range" id="blueGain" min="0" max="2" step="0.1" value="1" style="width: 120px;">
                <span id="blueGain-value">1.0</span>
            </div>

            <div class="control-item">
                <label for="sharpen">锐化：</label>
                <input type="range" id="sharpen" min="0" max="2" step="1" value="0" style="width: 120px;">
                <span id="sharpen-value">0</span>
            </div>

            <button onclick="resetColorSettings()" style="padding: 5px 10px; background-color: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer;">
                重置
            </button>

            <button onclick="applyColorCorrection()" style="padding: 5px 10px; background-color: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">
                应用
            </button>
        </div>
    </div>

    <div id="viewer-container"></div>
</div>

<script>
    // 性能API兼容性处理 - 修复 "performance is not defined" 错误
    if (typeof performance === 'undefined') {
        // 为不支持Performance API的环境提供fallback
        window.performance = {
            now: function() {
                return Date.now();
            },
            mark: function() {
                // 空实现
            },
            measure: function() {
                // 空实现
            },
            timing: {
                navigationStart: Date.now()
            }
        };
    }

    // 全局错误捕获处理 - 防止未捕获的错误影响页面
    window.addEventListener('error', function(event) {
        handleGlobalError(event.error || event.message, 'JavaScript错误');
        // 阻止错误传播，防止页面崩溃
        event.preventDefault();
        return true;
    });

    // 未处理的Promise拒绝捕获
    window.addEventListener('unhandledrejection', function(event) {
        handleGlobalError(event.reason, 'Promise拒绝');
        // 阻止错误传播
        event.preventDefault();
    });

    // 添加webContainerListener兼容性处理
    if (typeof webContainerListener === 'undefined') {
        window.webContainerListener = {
            addEventListener: function() { /* 空实现 */ },
            removeEventListener: function() { /* 空实现 */ }
        };
    }

    // Fetch API兼容性检查
    if (!window.fetch) {
        console.warn('当前浏览器不支持Fetch API，可能会影响功能');
        // 可以添加fetch polyfill的链接
    }

    // AbortController兼容性检查
    if (!window.AbortController) {
        window.AbortController = function() {
            this.signal = {
                aborted: false
            };
            this.abort = function() {
                this.signal.aborted = true;
            };
        };
    }

    // 添加更全面的错误监控和恢复机制
    let errorCount = 0;
    const MAX_ERRORS_BEFORE_RELOAD = 10;

    function handleGlobalError(error, source) {
        errorCount++;
        console.warn(`全局错误 #${errorCount}:`, error, source || '');

        // 静默处理错误，不显示弹窗提示
        if (errorCount >= MAX_ERRORS_BEFORE_RELOAD) {
            console.warn('页面错误较多，建议刷新页面');
            errorCount = 0; // 重置计数器
        }

        // 尝试恢复查看器
        try {
            if (viewer && viewer.world && typeof viewer.world.draw === 'function') {
                setTimeout(() => {
                    viewer.world.draw();
                }, 100);
            }
        } catch (e) {
            console.warn('尝试恢复查看器失败:', e);
        }
    }

    // OpenSeadragon查看器实例
    let viewer = null;

    // 客户端瓦片缓存
    const tileCache = new Map();
    const CACHE_SIZE_LIMIT = 3000; // 增加缓存大小限制，提高SDPC文件流畅性

    // 预先分配资源，减少内存分配开销
    const canvasPool = [];
    const MAX_CANVAS_POOL_SIZE = 50;

    // 获取一个画布从对象池中
    function getCanvasFromPool(width, height) {
        // 尝试从池中获取一个合适尺寸的画布
        if (canvasPool.length > 0) {
            // 查找与请求尺寸匹配的画布
            for (let i = 0; i < canvasPool.length; i++) {
                if (canvasPool[i].width === width && canvasPool[i].height === height) {
                    // 找到合适的，从池中删除并返回
                    return canvasPool.splice(i, 1)[0];
                }
            }

            // 如果没找到合适的，但池已满，重用第一个
            if (canvasPool.length >= MAX_CANVAS_POOL_SIZE) {
                const canvas = canvasPool.shift();
                canvas.width = width;
                canvas.height = height;
                return canvas;
            }
        }

        // 创建新画布
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        return canvas;
    }

    // 归还画布到池中
    function returnCanvasToPool(canvas) {
        if (canvasPool.length < MAX_CANVAS_POOL_SIZE) {
            // 清除画布内容
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            canvasPool.push(canvas);
        }
        // 超出池大小上限的就让垃圾回收处理
    }

    // 添加日志（保留函数但不显示在页面上）
    function addLog(message, type = 'info') {
        try {
            // 仅在控制台输出日志，添加错误处理
            const timestamp = new Date().toLocaleTimeString();
            console.log(`${timestamp}: ${message} [${type}]`);

            // 如果类型是error，也显示在页面上
            if (type === 'error') {
                setTimeout(() => {
                    alert(`错误: ${message}`);
                }, 100);
            }
        } catch (e) {
            // 如果日志输出失败，使用基本的console.log
            console.log(message);
            if (type === 'error') {
                alert(`错误: ${message}`);
            }
        }
    }

    // 获取URL参数的辅助函数
    function getURLParameter(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    // 窗口大小调整时重设查看器大小 - 优化自适应逻辑
    window.addEventListener('resize', function() {
        if (viewer && viewer.viewport) {
            // 优化：添加防抖处理，避免频繁调整
            clearTimeout(window.resizeTimeout);
            window.resizeTimeout = setTimeout(function() {
                try {
                    // 获取当前容器尺寸
                    const container = document.getElementById('viewer-container');
                    if (container) {
                        console.log('窗口大小变化，重新调整查看器适应:', {
                            containerWidth: container.clientWidth,
                            containerHeight: container.clientHeight,
                            windowWidth: window.innerWidth,
                            windowHeight: window.innerHeight
                        });
                        
                        // 检查是否为SDPC文件
                        const taskId = getURLParameter('taskId') || '';
                        const isSDPCFile = taskId.toLowerCase().includes('sdpc');
                        
                        if (isSDPCFile) {
                            // SDPC文件的特殊处理：确保图像完全适应新的屏幕尺寸
                            addLog('SDPC文件：调整视图以适应新的屏幕尺寸', 'info');
                            
                            // 重新计算最佳适应模式
                            viewer.viewport.goHome(false); // 禁用动画，立即适应
                            
                            // 确保图像居中并完全可见
                            setTimeout(() => {
                                if (viewer && viewer.viewport) {
                                    viewer.viewport.applyConstraints(false);
                                    viewer.forceRedraw();
                                }
                            }, 100);
                        } else {
                            // 其他格式的标准处理
                            viewer.viewport.goHome(true);
                        }
                    }
                } catch (error) {
                    console.error('窗口大小调整处理失败:', error);
                }
            }, 150); // 150ms防抖延迟
        }
    });

    // 初始化函数
    function initViewer(dziUrl) {
        // 如果已存在查看器，先销毁
        if (viewer) {
            viewer.destroy();
            viewer = null;
        }

        addLog(`初始化查看器，DZI URL: ${dziUrl}`, 'info');

        // 检查是否为SDPC文件，需要特殊处理
        const taskId = getURLParameter('taskId') || 'unknown';
        const isSDPCFile = taskId.toLowerCase().includes('sdpc') || dziUrl.toLowerCase().includes('sdpc');

        if (isSDPCFile) {
            addLog('检测到SDPC文件，启用首次加载优化', 'info');
        }

        // 清空容器内的所有内容，确保不会有缩略图残留
        const viewerContainer = document.getElementById('viewer-container');
        viewerContainer.innerHTML = '';

        // 缩放防抖变量
        let zoomTimeout = null;
        let lastZoomTime = 0;
        const ZOOM_THROTTLE_DELAY = 30; // 减少节流延迟，更快响应

        // 标记初始加载
        const startTime = Date.now();
        let isFirstLoad = true;
        let isImageFullyLoaded = false;

        // 添加初始加载状态指示器
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'dzi-loading-overlay';
        loadingOverlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.3);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                pointer-events: none;
            `;

        const loadingIndicator = document.createElement('div');
        loadingIndicator.style.cssText = `
                color: white;
                font-size: 18px;
                padding: 15px 20px;
                background-color: rgba(0,0,0,0.7);
                border-radius: 8px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            `;

        // 根据文件类型显示不同的加载信息
        if (isSDPCFile) {
            loadingIndicator.innerText = '正在初始化SDPC文件...';
        } else {
            loadingIndicator.innerText = '正在加载图像...';
        }

        loadingOverlay.appendChild(loadingIndicator);
        viewerContainer.appendChild(loadingOverlay);

        // 修复：SDPC文件不再需要特殊的预热延迟，直接初始化以提高响应速度
        if (isSDPCFile) {
            addLog('SDPC文件直接初始化，无需预热延迟', 'info');
            loadingIndicator.innerText = '正在初始化SDPC查看器...';
            // 立即初始化，不再延迟
            createViewerInstance(dziUrl, isSDPCFile);
        } else {
            // 非SDPC文件直接初始化
            createViewerInstance(dziUrl, isSDPCFile);
        }

        function createViewerInstance(dziUrl, isSDPCFile) {
            // 清空容器，确保没有残留内容
            const viewerContainer = document.getElementById('viewer-container');
            viewerContainer.innerHTML = '';
            
            // 检测是否为SVS格式
            const taskId = getURLParameter('taskId') || 'unknown';
            const isSVSFile = taskId.toLowerCase().includes('svs') || dziUrl.toLowerCase().includes('svs');
            
            if (isSVSFile) {
                addLog('检测到SVS文件，启用SVS优化配置', 'info');
            }
            
            // 创建新的查看器，针对SDPC文件优化配置
            try {
                addLog('正在创建OpenSeadragon查看器实例...', 'info');
                
                // 优化配置，特别针对SDPC和SVS文件
                const baseConfig = {
                    id: "viewer-container",
                    prefixUrl: "/slice/openseadragon/images/",
                    tileSources: dziUrl,
                    
                    // 基本设置
                    showNavigator: true,
                    showNavigationControl: true,
                    navigatorPosition: "TOP_RIGHT",
                    
                    // 禁用全屏功能 - 修复：防止点击时自动全屏
                    showFullPageControl: false,  // 不显示全屏按钮
                    fullPage: false,             // 禁用全屏模式
                    enableFullPageControl: false, // 禁用全屏控制
                    gestureSettingsMouse: {
                        clickToZoom: true,        // 保留点击缩放功能
                        dblClickToZoom: false,    // 禁用双击缩放，防止全屏
                        pinchToZoom: true,        // 保留触控缩放
                        flickEnabled: true,       // 保留滑动
                        flickMinSpeed: 120,
                        flickMomentum: 0.25
                    },
                    gestureSettingsTouch: {
                        clickToZoom: true,        // 保留触摸点击缩放
                        dblClickToZoom: false,    // 禁用触摸双击缩放，防止全屏
                        pinchToZoom: true,        // 保留触控缩放
                        flickEnabled: true,       // 保留滑动
                        flickMinSpeed: 120,
                        flickMomentum: 0.25
                    },
                    
                    // 性能设置 - 针对SDPC和SVS文件优化
                    debugMode: false,
                    timeout: (isSDPCFile || isSVSFile) ? 20000 : 60000, // SDPC和SVS文件进一步减少超时时间
                    
                    // 瓦片设置 - 优化拖拽性能：动态调整并发数
                    maxImageCacheCount: (isSDPCFile || isSVSFile) ? 2000 : 200, // 增加缓存以支持拖拽
                    imageLoaderLimit: (isSDPCFile || isSVSFile) ? 8 : 5,         // 拖拽时增加并发数到8
                    maxTilesPerFrame: (isSDPCFile || isSVSFile) ? 12 : 10,       // 拖拽时每帧最多12个瓦片
                    
                    // 关键修复：针对SDPC格式的瓦片加载和渲染优化
                    immediateRender: true,                                      // 强制启用即时渲染
                    blendTime: 0,                                              // 完全禁用瓦片混合动画
                    alwaysBlend: false,                                        // 禁用混合效果
                    
                    // 新增：SDPC格式优化的瓦片加载策略
                    tileFadeInTime: 0,                                         // 禁用瓦片淡入动画
                    tilePaddingBorder: (isSDPCFile || isSVSFile) ? 2 : 1,      // 增加瓦片边界填充
                    tileRetryMax: (isSDPCFile || isSVSFile) ? 3 : 1,          // 增加重试次数
                    tileRetryDelay: (isSDPCFile || isSVSFile) ? 100 : 500,    // 减少重试延迟
                    
                    // 关键：瓦片加载优先级和顺序优化
                    useCanvas: true,                                           // 强制使用Canvas渲染
                    opacity: 1.0,                                             // 完全不透明
                    compositeOperation: 'source-over',                         // 标准合成模式
                    
                    // 新增：SDPC格式瓦片加载顺序优化 - 修复相邻瓦片拼接问题
                    loadTilesWithAjax: true,                                  // 使用AJAX加载，更好的错误控制
                    showEmptyTiles: false,                                    // 不显示空瓦片，避免闪烁
                    placeholderFillStyle: null,                               // 禁用占位符，避免视觉跳跃
                    
                    // 关键修复：SDPC格式启用同步瓦片加载模式
                    tileLoadingPolicy: (isSDPCFile || isSVSFile) ? 'sync' : 'async',  // SDPC文件使用同步加载
                    
                    // 新增：瓦片位置和对齐优化
                    subPixelRoundingForTransparency: 'NEVER',                  // 禁用亚像素舍入
                    pixelDensityRatio: 1,                                      // 固定像素密度比例
                    
                    // 优化拖拽体验：启用智能预加载
                    preloadNearest: true,  // 启用预加载以提升拖拽体验
                    collectionMode: false,                                     // 禁用集合模式
                    collectionRows: 1,                                         // 单行显示
                    collectionColumns: 1,                                      // 单列显示
                    
                    // 关键修复：SDPC文件初始视图设置，避免从左上角小图自动放大的问题
                    defaultZoomLevel: (isSDPCFile || isSVSFile) ? null : 0.6,     // 修复：SDPC文件使用null让OpenSeadragon自动计算最佳缩放级别
                    minZoomLevel: 0.01,                         // 大幅降低最小缩放级别，允许显示完整全景图
                    maxZoomLevel: (isSDPCFile || isSVSFile) ? 25 : 15,          // SDPC和SVS文件增加最大缩放级别
                    homeFillsViewer: (isSDPCFile || isSVSFile) ? true : true,   // 修复：SDPC文件启用填充，确保显示完整图像
                    
                    // 关键修复：SDPC文件的坐标和显示修复，禁用自动调整行为
                    constrainDuringPan: true,                   // 限制拖拽范围，防止跑偏
                    wrapHorizontal: false,                      // 禁用水平环绕
                    wrapVertical: false,                        // 禁用垂直环绕
                    visibilityRatio: (isSDPCFile || isSVSFile) ? 1.0 : 1.0,    // 修复：确保完整可见
                    
                    // 关键修复：完全禁用自动调整和动画，避免从小图到大图的渐变效果
                    preserveViewport: false,                    // 禁用视口保持，允许重新计算最佳视图
                    springStiffness: 100,                       // 最大弹簧刚度，立即到位
                    animationTime: 0,                           // 完全禁用所有动画
                    
                    // 新增：SDPC文件优化的初始视图设置
                    initialPage: 0,                             // 从第0页开始
                    sequenceMode: false,                        // 禁用序列模式
                    showReferenceStrip: false,                  // 隐藏参考条
                    
                    // 关键修复：瓦片加载顺序优化，优先加载中心和高质量瓦片
                    placeholderFillStyle: null,                    // 禁用占位符，避免视觉跳跃
                    loadTilesWithAjax: true,                    // 使用AJAX加载，更好的错误控制
                    showEmptyTiles: false,                      // 不显示空瓦片，避免闪烁
                    
                    // 新增：启用自适应功能，让图像根据屏幕尺寸自动调整
                    autoResize: true,                           // 启用自动调整大小，适应不同屏幕
                    preserveOverlays: true,                     // 保持覆盖层位置
                    smoothTileEdgesMinZoom: 100,                // 最大缩放时才平滑瓦片边缘
                    iOSDevice: false,                           // 禁用iOS特殊处理
                    pixelsPerWheelLine: 40,                     // 固定滚轮响应
                    pixelsPerArrowPress: 40,                    // 固定箭头键响应
                    
                    // 新增：优化初始适应模式，确保图像能完全适应视口
                    fitBounds: null,                            // 使用默认边界适应
                    fitBoundsWithConstraints: true,             // 在约束条件下适应边界
                    
                    // CORS设置
                    crossOriginPolicy: 'Anonymous',
                    ajaxWithCredentials: false,
                    
                    // 错误处理
                    silenceMultiImageWarnings: true,
                    
                    // 性能优化设置
                    preloadNearest: true,                       // 启用预加载
                    
                    // 导航器优化设置
                    navigatorDisplayRegionColor: 'rgba(255, 0, 0, 0.4)', // 导航器选区颜色
                    navigatorBorderColor: '#555',                        // 导航器边框颜色
                    navigatorOpacity: 0.8,                              // 导航器透明度
                    
                    // 去除调试边框
                    debugGridColor: ['rgba(0,0,0,0)', 'rgba(0,0,0,0)', 'rgba(0,0,0,0)'],
                    showDebugInfo: false
                };

                // 添加调试信息
                console.log('OpenSeadragon配置:', baseConfig);
                addLog('开始初始化OpenSeadragon查看器...', 'info');
                
                viewer = OpenSeadragon(baseConfig);
                
                // 新增：启用SDPC专用优化
                if (viewer) {
                    // 为SDPC文件启用瓦片位置锁定
                    lockSDPCTilePositions(viewer);
                    
                    // 为SDPC文件启用瓦片加载管理器  
                    setupSDPCTileLoadingManager(viewer);
                    
                    // 新增：瓦片事件监控（用于测试）
                    setupTileEventReporting(viewer);
                    
                    // 新增：拖拽优化机制
                    setupDragOptimization(viewer, isSDPCFile);
                    
                    addLog('SDPC拖拽优化机制已启用', 'info');
                    
                    // 禁用视口变化时的动画
                    viewer.addHandler('viewport-change', function(event) {
                        if (event.immediately !== true) {
                            viewer.viewport.applyConstraints(true); // 立即应用约束，无动画
                        }
                    });
                    
                    // 禁用瓦片加载时的动画效果
                    viewer.addHandler('tile-loaded', function(event) {
                        // 瓦片加载完成后立即固定位置
                        if (event.tile && event.tile.element) {
                            event.tile.element.style.transition = 'none';
                        }
                    });
                    
                    // 禁用缩放动画
                    viewer.addHandler('zoom', function(event) {
                        viewer.viewport.applyConstraints(true);
                    });
                    
                    addLog('瓦片抖动防护机制已启用', 'info');
                    
                    // 新增：SDPC格式专用瓦片加载顺序管理器
                    if (isSDPCFile) {
                        setupSDPCTileLoadingManager(viewer);
                    }
                }
                
                // 验证初始化是否成功
                if (viewer) {
                    addLog('OpenSeadragon查看器初始化成功', 'success');
                    console.log('OpenSeadragon查看器对象:', viewer);
                } else {
                    throw new Error('OpenSeadragon查看器初始化返回null');
                }

            } catch (error) {
                addLog(`OpenSeadragon初始化失败: ${error.message}`, 'error');
                console.error('OpenSeadragon初始化错误:', error);
                console.error('错误堆栈:', error.stack);

                // 显示错误信息给用户
                const viewerContainer = document.getElementById('viewer-container');
                viewerContainer.innerHTML = `
                        <div style="display:flex; justify-content:center; align-items:center; height:100%; color:red; text-align:center;">
                            <div>
                                <h3>查看器初始化失败</h3>
                                <p>错误信息: ${error.message}</p>
                                <button onclick="location.reload()" style="margin-top:10px;">重新加载页面</button>
                            </div>
                        </div>
                    `;
                return;
            }

            // 添加详细的事件监听器来调试问题
            viewer.addHandler('open', function(event) {
                addLog('OpenSeadragon: 图像打开事件触发', 'success');
                console.log('Open事件详情:', event);
                
                // 移除加载指示器
                const loadingOverlay = document.getElementById('dzi-loading-overlay');
                if (loadingOverlay && loadingOverlay.parentNode) {
                    loadingOverlay.parentNode.removeChild(loadingOverlay);
                }
                
                // 关键修复：SDPC文件的初始视图设置，确保显示高质量图像
                setTimeout(() => {
                    if (viewer && viewer.viewport) {
                        if (isSDPCFile) {
                            // SDPC文件关键修复：让OpenSeadragon使用自动计算的最佳缩放级别
                            addLog('SDPC文件：设置最佳显示质量的初始视图', 'info');
                            
                            // 获取当前的自动计算的缩放级别（因为defaultZoomLevel设为null）
                            const homeZoom = viewer.viewport.getHomeZoom();
                            const currentZoom = viewer.viewport.getZoom();
                            
                            // 如果当前缩放级别太低（小于自动计算的最佳级别），则提升到最佳级别
                            if (currentZoom < homeZoom) {
                                viewer.viewport.zoomTo(homeZoom, null, false);
                                addLog(`SDPC文件：提升缩放级别到最佳质量 ${homeZoom.toFixed(2)}`, 'info');
                            } else {
                                // 使用标准的goHome，确保图像完整显示
                                viewer.viewport.goHome(false);
                                addLog('SDPC文件：使用标准最佳视图', 'info');
                            }
                            
                            // 延迟预加载高质量瓦片
                            setTimeout(() => {
                                if (typeof preloadCriticalLevelsForSDPC === 'function') {
                                    preloadCriticalLevelsForSDPC();
                                }
                                
                                // 新增：SDPC格式强制预加载当前视图的所有瓦片
                                forcePreloadVisibleTilesForSDPC();
                                
                                // SDPC文件自动应用优化的颜色参数
                                initSDPCColorParams();
                                
                                // 等待一段时间让图像稳定后自动应用颜色校正
                                setTimeout(() => {
                                    if (viewer) {
                                        applyColorCorrection();
                                    }
                                }, 1000); // 给图像更多时间稳定
                            }, 500); // 减少延迟，快速预加载
                            
                        } else {
                            // 非SDPC文件的标准处理
                            viewer.viewport.goHome(false);
                        }
                        
                        // 强制立即重绘，确保视图正确显示
                        viewer.forceRedraw();
                    }
                }, 100); // 进一步减少延迟，快速设置初始视图
            });
            
            viewer.addHandler('open-failed', function(event) {
                addLog(`OpenSeadragon: 图像打开失败 - ${event.message || '未知错误'}`, 'error');
                console.error('OpenSeadragon打开失败事件:', event);
                
                // 移除加载指示器并显示错误
                const loadingOverlay = document.getElementById('dzi-loading-overlay');
                if (loadingOverlay && loadingOverlay.parentNode) {
                    loadingOverlay.innerHTML = `
                        <div style="color: white; font-size: 18px; padding: 15px 20px; background-color: rgba(255,0,0,0.8); border-radius: 8px;">
                            图像加载失败: ${event.message || '未知错误'}
                        </div>
                    `;
                }
            });
            
            viewer.addHandler('tile-load-failed', function(event) {
                // 静默处理瓦片加载失败，避免控制台污染
                console.warn('瓦片加载失败');
            });
            
            // 移除详细的瓦片事件日志，避免控制台输出过多信息

            // 添加禁用全屏的事件监听器 - 修复：确保完全禁用全屏功能
            viewer.addHandler('full-page', function(event) {
                // 阻止全屏事件
                event.preventDefaultAction = true;
                addLog('已阻止全屏操作', 'info');
            });

            // 禁用双击全屏功能
            viewer.addHandler('canvas-double-click', function(event) {
                // 阻止默认的双击全屏行为
                event.preventDefaultAction = true;
                addLog('已阻止双击全屏操作', 'info');
            });

            // 禁用键盘全屏快捷键
            viewer.addHandler('canvas-key', function(event) {
                // 阻止F键或其他可能触发全屏的键盘事件
                if (event.originalEvent.key === 'f' || event.originalEvent.key === 'F' || 
                    event.originalEvent.keyCode === 70) {
                    event.preventDefaultAction = true;
                    addLog('已阻止键盘全屏快捷键', 'info');
                }
            });

            // 添加页面布局保护 - 修复：防止页面上方部分消失
            viewer.addHandler('resize', function() {
                // 确保页面其他元素不被隐藏
                ensurePageLayoutIntegrity();
            });

            // 添加其他可能影响布局的事件监听
            viewer.addHandler('viewport-change', function() {
                // 确保视口变化不影响页面布局
                ensurePageLayoutIntegrity();
            });

            setupViewerEventHandlers(isSDPCFile);
        }
    }

        function setupViewerEventHandlers(isSDPCFile) {
            // 简化的事件处理，专注于基本功能
            let tilesLoaded = 0;
            
            // 监听瓦片加载事件（简化日志）
            viewer.addHandler('tile-loaded', function(event) {
                tilesLoaded++;
                // 每10个瓦片报告一次进度，减少控制台输出
                if (tilesLoaded % 10 === 0) {
                    console.log(`瓦片加载进度: ${tilesLoaded}`);
                }
            });

            // 简化的图像打开处理
            viewer.addHandler('open', function() {
                addLog('图像打开完成', 'success');
                console.log('图像已成功打开');
            });

            // 移除复杂的缓存逻辑，保持简单
            
            // 移除重复的视图初始化，避免多次调用goHome导致自动缩放
        }

        // 简化的SDPC文件预加载函数
        function preloadCriticalLevelsForSDPC() {
            if (!viewer || !viewer.world || viewer.world.getItemCount() === 0) {
                console.log('SDPC预加载：查看器未就绪，跳过预加载');
                return;
            }

            try {
                const item = viewer.world.getItemAt(0);
                const source = item.source;
                
                if (!source || !source.Levels) {
                    console.log('SDPC预加载：无法获取层级信息，跳过预加载');
                    return;
                }
                
                // 关键修复：只预加载最低分辨率的几个层级，避免触发高分辨率加载
                const maxLevel = source.Levels.length - 1;
                const lowResLevels = [0, 1]; // 只预加载最低的2个层级
                
                console.log(`SDPC预加载：总层级=${maxLevel + 1}, 预加载低分辨率层级=[${lowResLevels.join(',')}]`);
                
                lowResLevels.forEach((level, index) => {
                    if (level <= maxLevel) {
                        setTimeout(() => {
                            preloadLevelForSDPC(level);
                        }, (index + 1) * 500); // 增加间隔时间，避免影响当前显示
                    }
                });
                
            } catch (error) {
                console.warn('SDPC关键层级预加载失败:', error);
            }
        }

        /**
         * 预加载SDPC文件的指定层级
         */
        function preloadLevelForSDPC(level) {
            if (!viewer || !viewer.world || viewer.world.getItemCount() === 0) {
                return;
            }

            try {
                const item = viewer.world.getItemAt(0);
                const source = item.source;
                
                if (!source || !source.Levels || level >= source.Levels.length || level < 0) {
                    return;
                }
                
                const levelInfo = source.Levels[level];
                const tilesX = Math.ceil(levelInfo.Width / source.TileSize);
                const tilesY = Math.ceil(levelInfo.Height / source.TileSize);
                
                // 获取当前视图范围
                const viewport = viewer.viewport;
                const bounds = viewport.getBounds();
                
                // 计算需要预加载的瓦片范围（当前视图的2倍范围）
                const tileSize = source.TileSize;
                const levelScale = Math.pow(2, level);
                
                const startTileX = Math.max(0, Math.floor((bounds.x * levelInfo.Width) / tileSize) - 1);
                const endTileX = Math.min(tilesX - 1, Math.ceil(((bounds.x + bounds.width) * levelInfo.Width) / tileSize) + 1);
                const startTileY = Math.max(0, Math.floor((bounds.y * levelInfo.Height) / tileSize) - 1);
                const endTileY = Math.min(tilesY - 1, Math.ceil(((bounds.y + bounds.height) * levelInfo.Height) / tileSize) + 1);
                
                // 限制预加载的瓦片数量，避免过度加载
                const maxTiles = 16; // 最多预加载16个瓦片
                let preloadedCount = 0;
                
                for (let tileY = startTileY; tileY <= endTileY && preloadedCount < maxTiles; tileY++) {
                    for (let tileX = startTileX; tileX <= endTileX && preloadedCount < maxTiles; tileX++) {
                        // 创建瓦片URL并预加载
                        const tileUrl = source.getTileUrl(level, tileX, tileY);
                        
                        // 使用Image对象进行预加载
                        const img = new Image();
                        img.onload = function() {
                            // 预加载成功，无需特殊处理
                        };
                        img.onerror = function() {
                            // 预加载失败，静默处理
                        };
                        img.src = tileUrl;
                        
                        preloadedCount++;
                    }
                }
                
                if (preloadedCount > 0) {
                    addLog(`SDPC层级${level}预加载${preloadedCount}个瓦片`, 'info');
                }
                
            } catch (error) {
                console.warn('预加载SDPC层级失败:', error);
            }
        }

        // 在加载切片之前先尝试获取缩略图并显示
        function loadSlide() {
            // 获取输入的标识符
            const identifier = document.getElementById("identifier").value.trim();
            if (!identifier) {
                addLog("请输入有效的切片标识符!", 'error');
                alert("请输入有效的切片标识符!"); // 添加弹窗提示替代日志显示
                return;
            }

            // 检查OpenSeadragon是否已加载
            if (!openSeadragonLoaded || typeof OpenSeadragon === 'undefined') {
                const container = document.getElementById('viewer-container');
                container.innerHTML = `
                    <div style="display:flex; justify-content:center; align-items:center; height:100%; text-align:center;">
                        <div>
                            <h3>正在加载OpenSeadragon库...</h3>
                            <p>请稍候，库加载完成后将自动加载切片</p>
                            <div style="margin-top: 15px;">
                                <div style="display: inline-block; width: 20px; height: 20px; border: 2px solid #f3f3f3; border-radius: 50%; border-top: 2px solid #3498db; animation: spin 1s linear infinite;"></div>
                            </div>
                            <div style="margin-top: 15px;">
                                <button onclick="retryLoadOpenSeadragon()" style="margin:5px; padding:8px 16px; background:#007bff; color:white; border:none; border-radius:4px; cursor:pointer;">
                                    重试加载库
                                </button>
                            </div>
                        </div>
                    </div>
                    <style>
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    </style>
                `;

                // 等待OpenSeadragon加载完成
                window.addEventListener('openSeadragonLoaded', function() {
                    addLog(`OpenSeadragon加载完成，开始加载切片: ${identifier}`, 'info');
                    loadSlideInternal(identifier);
                }, { once: true });

                return;
            }

            loadSlideInternal(identifier);
        }

        // 内部加载切片函数
        function loadSlideInternal(identifier) {
            addLog(`正在加载切片: ${identifier}`, 'info');

            // 显示加载中的状态
            const viewerContainer = document.getElementById('viewer-container');
            viewerContainer.innerHTML = '<div style="display:flex; justify-content:center; align-items:center; height:100%; background-color: rgba(0,0,0,0.7);">' +
                '<div style="color: white; font-size: 18px; padding: 20px; border-radius: 5px;">' +
                '正在加载图像，请稍候...' +
                '<div class="loading-spinner" style="margin-top: 10px; text-align: center;">⏳</div>' +
                '</div>' +
                '</div>';

            // 修复SDPC格式切片问题：直接加载DZI查看器，不显示单独的缩略图
            // 问题分析：缩略图和DZI瓦片坐标系不一致，导致先显示缩略图后被瓦片覆盖
            addLog("开始加载DZI切片查看器（跳过独立缩略图显示）...", 'info');
            
            // 直接加载DZI查看器，让OpenSeadragon自己处理缩略图显示
            loadDziViewer(identifier);
        }

        // 加载DZI查看器
        function loadDziViewer(identifier) {
            // 修复：添加identifier验证，避免空值导致错误的DZI URL
            if (!identifier || identifier.trim() === '') {
                addLog("错误：identifier为空，无法构建DZI URL", 'error');
                alert("错误：切片标识符为空，请检查输入!");
                return;
            }
            
            // 清空容器内的所有内容，确保不会有缩略图残留
            const viewerContainer = document.getElementById('viewer-container');
            viewerContainer.innerHTML = '<div style="display:flex; justify-content:center; align-items:center; height:100%; background-color: rgba(0,0,0,0.7);">' +
                '<div style="color: white; font-size: 18px; padding: 20px; border-radius: 5px;">' +
                '正在加载DZI查看器，请稍候...' +
                '<div class="loading-spinner" style="margin-top: 10px; text-align: center;">⏳</div>' +
                '</div>' +
                '</div>';

            // 构建DZI URL - 修复：添加完整的context-path和固定cname参数
            const dziUrl = `/slice/api/dzi/${identifier}.dzi?cname=qpDhc`;
            addLog(`构建DZI URL: ${dziUrl}`, 'info');

            // 新增：适配内容区域DZI，动态设置容器宽高比
            fetch(dziUrl)
                .then(response => response.text())
                .then(xmlData => {
                    // 解析DZI XML获取内容区域宽高
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(xmlData, "text/xml");
                    const sizeElement = xmlDoc.getElementsByTagName("Size")[0];
                    let dziWidth = 1, dziHeight = 1;
                    if (sizeElement) {
                        dziWidth = parseInt(sizeElement.getAttribute("Width"));
                        dziHeight = parseInt(sizeElement.getAttribute("Height"));
                    }
                    // 修复：不再动态修改viewer-container样式，保持原有布局
                    // 注释掉动态设置宽高比的代码，避免影响页面布局导致头部消失
                    const viewerContainer = document.getElementById('viewer-container');
                    // 移除可能导致布局异常的样式修改
                    // if (viewerContainer && dziWidth > 0 && dziHeight > 0) {
                    //     viewerContainer.style.aspectRatio = `${dziWidth} / ${dziHeight}`;
                    //     viewerContainer.style.height = '';
                    //     viewerContainer.style.width = '';
                    // }
                    // 初始化OpenSeadragon，传递内容区域宽高
                    initViewerWithDzi(identifier, dziUrl, dziWidth, dziHeight);
                })
                .catch(() => {
                    // 失败时回退
                    initViewer(dziUrl);
                });
        }

        // 新增：带DZI宽高的初始化，适配内容区域
        function initViewerWithDzi(identifier, dziUrl, dziWidth, dziHeight) {
            // 清空容器
            const viewerContainer = document.getElementById('viewer-container');
            viewerContainer.innerHTML = '';
            // 关键适配：初始化OpenSeadragon时，viewport宽高比与DZI一致
            const baseConfig = {
                id: "viewer-container",
                prefixUrl: "/slice/openseadragon/images/",
                tileSources: {
                    type: 'image',
                    width: dziWidth,
                    height: dziHeight,
                    url: dziUrl
                },
                showNavigator: true,
                showNavigationControl: true,
                navigatorPosition: "TOP_RIGHT",
                
                // 禁用全屏功能 - 修复：防止点击时自动全屏
                showFullPageControl: false,  // 不显示全屏按钮
                fullPage: false,             // 禁用全屏模式
                enableFullPageControl: false, // 禁用全屏控制
                gestureSettingsMouse: {
                    clickToZoom: true,        // 保留点击缩放功能
                    dblClickToZoom: false,    // 禁用双击缩放，防止全屏
                    pinchToZoom: true,        // 保留触控缩放
                    flickEnabled: true,       // 保留滑动
                    flickMinSpeed: 120,
                    flickMomentum: 0.25
                },
                gestureSettingsTouch: {
                    clickToZoom: true,        // 保留触摸点击缩放
                    dblClickToZoom: false,    // 禁用触摸双击缩放，防止全屏
                    pinchToZoom: true,        // 保留触控缩放
                    flickEnabled: true,       // 保留滑动
                    flickMinSpeed: 120,
                    flickMomentum: 0.25
                },
                debugMode: false,
                maxImageCacheCount: isSDPCFile ? 2000 : 200,
                imageLoaderLimit: isSDPCFile ? 25 : 5,
                maxTilesPerFrame: isSDPCFile ? 50 : 10,
                immediateRender: true,                // 强制启用即时渲染
                blendTime: 0,                      // 完全禁用瓦片混合动画
                alwaysBlend: false,
                // 关键适配：SDPC禁用homeFillsViewer，保持内容比例
                homeFillsViewer: false,
                preserveImageSizeOnResize: true,
                constrainDuringPan: true,
                wrapHorizontal: false,
                wrapVertical: false,
                visibilityRatio: 1.0,
                preserveViewport: false,
                springStiffness: 100,                 // 最大弹簧刚度，立即到位
                animationTime: 0,                     // 完全禁用所有动画
                initialPage: 0,
                sequenceMode: false,
                showReferenceStrip: false,
                placeholderFillStyle: "rgba(255,255,255,0.1)",
                loadTilesWithAjax: true,
                showEmptyTiles: false,
                crossOriginPolicy: 'Anonymous',
                ajaxWithCredentials: false,
                silenceMultiImageWarnings: true,
                preloadNearest: true,
                navigatorDisplayRegionColor: 'rgba(255, 0, 0, 0.4)',
                navigatorBorderColor: '#555',
                navigatorOpacity: 0.8,
                debugGridColor: ['rgba(0,0,0,0)', 'rgba(0,0,0,0)', 'rgba(0,0,0,0)'],
                showDebugInfo: false
            };
            // 初始化OpenSeadragon
            viewer = OpenSeadragon(baseConfig);

            // 添加禁用全屏的事件监听器 - 修复：确保完全禁用全屏功能
            viewer.addHandler('full-page', function(event) {
                // 阻止全屏事件
                event.preventDefaultAction = true;
                addLog('已阻止全屏操作', 'info');
            });

            // 禁用双击全屏功能
            viewer.addHandler('canvas-double-click', function(event) {
                // 阻止默认的双击全屏行为
                event.preventDefaultAction = true;
                addLog('已阻止双击全屏操作', 'info');
            });

            // 禁用键盘全屏快捷键
            viewer.addHandler('canvas-key', function(event) {
                // 阻止F键或其他可能触发全屏的键盘事件
                if (event.originalEvent.key === 'f' || event.originalEvent.key === 'F' || 
                    event.originalEvent.keyCode === 70) {
                    event.preventDefaultAction = true;
                    addLog('已阻止键盘全屏快捷键', 'info');
                }
            });

            // 添加页面布局保护 - 修复：防止页面上方部分消失
            viewer.addHandler('resize', function() {
                // 确保页面其他元素不被隐藏
                ensurePageLayoutIntegrity();
            });

            // 添加其他可能影响布局的事件监听
            viewer.addHandler('viewport-change', function() {
                // 确保视口变化不影响页面布局
                ensurePageLayoutIntegrity();
            });

            // 同步预加载所有关键资源
            const preloadDeepZoom = () => {
                // 1. 预加载DZI描述文件
                const linkDescriptor = document.createElement('link');
                linkDescriptor.rel = 'preload';
                linkDescriptor.href = dziUrl;
                linkDescriptor.as = 'fetch';
                linkDescriptor.crossOrigin = 'anonymous';
                document.head.appendChild(linkDescriptor);

                // 2. 预加载第0层瓦片（最低分辨率全景图） - 修复：添加完整的context-path
                const linkLevel0 = document.createElement('link');
                linkLevel0.rel = 'preload';
                linkLevel0.href = `/slice/api/dzi/${identifier}_files/0/0_0.jpg`;
                linkLevel0.as = 'image';
                linkLevel0.crossOrigin = 'anonymous';
                document.head.appendChild(linkLevel0);

                // 3. 预加载缩略图 - 修复：添加完整的context-path
                const linkThumb = document.createElement('link');
                linkThumb.rel = 'preload';
                linkThumb.href = `/slice/api/dzi/thumbnail/${identifier}.jpg`;
                linkThumb.as = 'image';
                linkThumb.crossOrigin = 'anonymous';
                document.head.appendChild(linkThumb);
            };

            // 立即预加载关键资源，不等待
            preloadDeepZoom();

            // 添加更大的时间戳随机数，确保不会有缓存问题
            const timeStamp = new Date().getTime() + Math.floor(Math.random() * 1000);
            // 构建包含cname参数和时间戳的URL
            const baseUrl = dziUrl.split('?')[0];
            const cnameParam = dziUrl.includes('cname=') ? dziUrl.split('?')[1] : 'cname=qpDhc';
            const dziUrlWithTimestamp = `${baseUrl}?${cnameParam}&t=${timeStamp}`;

            // 增加超时时间，给大文件更多时间
            let loadingTimeout = setTimeout(() => {
                addLog("加载DZI描述文件超时，尝试使用备用方式加载", 'warn');
                // 清空容器内的所有内容，确保不会有缩略图残留
                const viewerContainer = document.getElementById('viewer-container');
                viewerContainer.innerHTML = '';
                // 直接尝试初始化查看器，跳过DZI描述文件检查
                initViewer(`${dziUrl.split('?')[0]}?cname=qpDhc`);
            }, 10000); // 增加到10秒

            // 添加并行预请求，提前触发服务端生成 - 修复：添加完整的context-path
            const prefetchFirstTiles = () => {
                // 并行请求最低两层的中心瓦片
                const prefetchUrls = [
                    `/slice/api/dzi/${identifier}_files/0/0_0.jpg`,
                    `/slice/api/dzi/${identifier}_files/1/0_0.jpg`,
                    `/slice/api/dzi/${identifier}_files/1/1_0.jpg`,
                    `/slice/api/dzi/${identifier}_files/1/0_1.jpg`,
                    `/slice/api/dzi/${identifier}_files/1/1_1.jpg`
                ];

                // 使用Promise.all并行请求，添加更好的错误处理
                Promise.all(prefetchUrls.map(url =>
                    fetch(url, {priority: 'high', mode: 'no-cors'})
                        .catch(e => {
                            console.debug(`预请求瓦片失败: ${url}`, e.message || e);
                            return null; // 返回null而不是抛出错误
                        })
                )).then((results) => {
                    const successCount = results.filter(r => r !== null).length;
                    console.debug(`预热瓦片完成: ${successCount}/${prefetchUrls.length} 成功`);
                }).catch(e => {
                    console.warn('预热瓦片过程出错:', e);
                });
            };

            // 立即开始预请求瓦片
            prefetchFirstTiles();

            // 使用优化的fetch策略 - 优先级高，更积极的缓存控制，超时控制
            const controller = new AbortController();
            const timeout = setTimeout(() => controller.abort(), 15000); // 增加到15秒

            fetch(dziUrlWithTimestamp, {
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                },
                cache: 'no-store',
                priority: 'high',
                signal: controller.signal
            })
                .then(response => {
                    // 清除超时计时器
                    clearTimeout(loadingTimeout);
                    clearTimeout(timeout);

                    if (!response.ok) {
                        throw new Error(`DZI描述文件请求失败，状态码: ${response.status}`);
                    }
                    return response.text();
                })
                .then(xmlData => {
                    addLog("DZI描述文件获取成功", 'success');

                    // 解析XML以获取图像尺寸信息
                    try {
                        const parser = new DOMParser();
                        const xmlDoc = parser.parseFromString(xmlData, "text/xml");
                        const sizeElement = xmlDoc.getElementsByTagName("Size")[0];
                        if (sizeElement) {
                            const width = sizeElement.getAttribute("Width");
                            const height = sizeElement.getAttribute("Height");
                            addLog(`图像尺寸: ${width}x${height}`, 'info');

                            // 检查图像尺寸是否过大
                            const pixelCount = parseInt(width) * parseInt(height);
                            if (pixelCount > 100000000) { // 1亿像素
                                addLog(`警告: 图像非常大 (${pixelCount} 像素)，可能导致性能问题`, 'warn');
                            }
                        }
                    } catch (e) {
                        addLog(`解析DZI XML失败: ${e.message}`, 'error');
                    }

                    // 清空容器内的所有内容，确保不会有缩略图残留
                    const viewerContainer = document.getElementById('viewer-container');
                    viewerContainer.innerHTML = '';

                    // 初始化查看器
                    initViewer(dziUrl);

                    // 提前预加载层级，使用立即执行
                    prewarmTileCache(identifier);
                })
                .catch(error => {
                    // 清除超时计时器
                    clearTimeout(loadingTimeout);
                    clearTimeout(timeout);

                    addLog(`DZI描述文件获取失败: ${error.message}`, 'error');
                    console.error("DZI描述文件获取失败:", error);

                    // 清空容器内的所有内容，确保不会有缩略图残留
                    const viewerContainer = document.getElementById('viewer-container');
                    viewerContainer.innerHTML = '';

                    // 尝试直接初始化查看器，即使描述文件获取失败
                    addLog("尝试直接加载切片，跳过描述文件检查", 'info');
                    initViewer(dziUrl);
                });
        }

        // 防止图像加载时抖动的预加载策略
        function preloadLevel(level) {
            if (!viewer || !viewer.world || !viewer.world.getItemAt(0)) return;

            try {
                const tiledImage = viewer.world.getItemAt(0);
                const imageSize = tiledImage.source ? { x: tiledImage.source.width || 1000, y: tiledImage.source.height || 1000 } : { x: 1000, y: 1000 };

                // 计算当前层级的瓦片数
                const tileSize = 256; // 标准瓦片大小
                const scale = Math.pow(2, level);
                const levelWidth = Math.ceil(imageSize.x / scale / tileSize);
                const levelHeight = Math.ceil(imageSize.y / scale / tileSize);

                addLog(`预加载层级 ${level}，瓦片网格: ${levelWidth}x${levelHeight}`, 'info');

                // 获取当前视口中心位置和大小
                const viewportCenter = viewer.viewport.getCenter();
                const viewportBounds = viewer.viewport.getBounds();

                // 计算可见区域的瓦片范围
                const visibleLeft = Math.max(0, Math.floor((viewportBounds.x - viewportBounds.width/2) * imageSize.x / scale / tileSize));
                const visibleTop = Math.max(0, Math.floor((viewportBounds.y - viewportBounds.height/2) * imageSize.y / scale / tileSize));
                const visibleRight = Math.min(levelWidth - 1, Math.ceil((viewportBounds.x + viewportBounds.width/2) * imageSize.x / scale / tileSize));
                const visibleBottom = Math.min(levelHeight - 1, Math.ceil((viewportBounds.y + viewportBounds.height/2) * imageSize.y / scale / tileSize));

                // 计算中心瓦片
                const centerTileX = Math.floor(viewportCenter.x * imageSize.x / scale / tileSize);
                const centerTileY = Math.floor(viewportCenter.y * imageSize.y / scale / tileSize);

                // 使用更优的预加载策略：先预加载可见区域，然后是周围区域

                // 使用更安全的预加载策略，避免API调用错误
                addLog(`预加载层级 ${level} 的瓦片，网格大小: ${levelWidth}x${levelHeight}`, 'info');

                // 不再使用可能不存在的getTileBounds API，而是通过其他方式触发瓦片加载
                // 使用视口操作来自然触发瓦片预加载
                setTimeout(() => {
                    try {
                        if (viewer && viewer.viewport) {
                            // 记录当前状态
                            const currentZoom = viewer.viewport.getZoom();
                            const currentCenter = viewer.viewport.getCenter();

                            // 轻微调整视口来触发瓦片加载系统
                            const preloadZoom = Math.max(currentZoom * 0.9, 0.1);
                            viewer.viewport.zoomTo(preloadZoom, currentCenter, true);

                            // 短暂延迟后恢复
                            setTimeout(() => {
                                if (viewer && viewer.viewport) {
                                    viewer.viewport.zoomTo(currentZoom, currentCenter, true);
                                }
                            }, 50);
                        }
                    } catch (e) {
                        console.warn('预加载瓦片时出错:', e);
                    }
                }, 100);

                // 强制立即重绘当前层级，但使用请求动画帧优化
                requestAnimationFrame(function() {
                    if (viewer) {
                        viewer.forceRedraw();
                    }
                });
            } catch (e) {
                console.warn("预加载层级瓦片时出错:", e);
            }
        }

        // 预热瓦片缓存函数
        function prewarmTileCache(identifier) {
            // 获取当前可见区域的瓦片
            if (!viewer || !viewer.world || !viewer.world.getItemAt(0)) {
                console.warn("查看器未初始化，无法预热瓦片缓存");
                return;
            }

            addLog("开始预热瓦片缓存...", 'info');

            try {
                // 先获取层级信息
                const tiledImage = viewer.world.getItemAt(0);
                const maxLevel = tiledImage.source && tiledImage.source.maxLevel ? tiledImage.source.maxLevel : (tiledImage.source && tiledImage.source.Levels ? tiledImage.source.Levels.length - 1 : 10);

                // 获取图像尺寸
                const imageSize = tiledImage.source ? { x: tiledImage.source.width || 1000, y: tiledImage.source.height || 1000 } : { x: 1000, y: 1000 };

                // 优先加载最低两个层级(最小尺寸全局视图)
                // 层级0通常只有1个瓦片且包含整个图像的低分辨率视图
                preloadLevel(0);

                // 然后加载下一层级（第1层）的四个象限瓦片
                const level1Tiles = [
                    {level: 1, x: 0, y: 0},
                    {level: 1, x: 1, y: 0},
                    {level: 1, x: 0, y: 1},
                    {level: 1, x: 1, y: 1}
                ];

                // 并行预加载第1层的关键瓦片
                level1Tiles.forEach(tile => {
                    // 使用直接请求而不是通过OpenSeadragon API
                    const tileUrl = `/slice/api/dzi/${identifier}_files/${tile.level}/${tile.x}_${tile.y}.jpg`;

                    fetch(tileUrl, {
                        method: 'GET',
                        priority: 'high',
                        cache: 'force-cache',
                        mode: 'no-cors'
                    }).catch(e => {
                        // 忽略错误，这只是预加载
                        console.debug(`预加载瓦片出错 (${tile.level}, ${tile.x}, ${tile.y}): ${e.message}`);
                    });
                });

                // 计算当前层级，考虑视图尺寸与图像尺寸的对比
                const viewportBounds = viewer.viewport.getBounds();
                const zoomFactor = Math.max(
                    viewportBounds.width / imageSize.x,
                    viewportBounds.height / imageSize.y
                );
                const optimalLevel = Math.max(0, Math.ceil(-Math.log2(zoomFactor)));

                // 确保在合理范围内
                const targetLevel = Math.min(optimalLevel, maxLevel);

                // 预加载当前视图可能需要的层级
                if (targetLevel > 1) {
                    addLog(`预加载目标层级: ${targetLevel}`, 'info');

                    // 计算当前视图对应的瓦片范围
                    const tileSize = 256; // 标准瓦片尺寸
                    const scale = Math.pow(2, targetLevel);

                    const viewportCenter = viewer.viewport.getCenter();
                    const vpCenterX = Math.floor(viewportCenter.x * imageSize.x / scale / tileSize);
                    const vpCenterY = Math.floor(viewportCenter.y * imageSize.y / scale / tileSize);

                    // 只加载中心及周围瓦片（最多3x3的九宫格）
                    const radius = 1;
                    for (let y = vpCenterY - radius; y <= vpCenterY + radius; y++) {
                        for (let x = vpCenterX - radius; x <= vpCenterX + radius; x++) {
                            if (x < 0 || y < 0) continue;

                            // 优先加载中心瓦片
                            const priority = (x === vpCenterX && y === vpCenterY) ? 'high' : 'auto';

                            // 使用直接请求
                            const tileUrl = `/slice/api/dzi/${identifier}_files/${targetLevel}/${x}_${y}.jpg`;
                            fetch(tileUrl, {
                                method: 'GET',
                                priority: priority,
                                cache: 'force-cache',
                                mode: 'no-cors'
                            }).catch(() => {/* 忽略错误 */});
                        }
                    }
                }

                // 强制重绘，触发瓦片加载系统
                viewer.forceRedraw();

                addLog("瓦片缓存预热完成", 'success');

            } catch (e) {
                console.warn("预热瓦片缓存时出错:", e);
                addLog(`预热瓦片缓存出错: ${e.message}`, 'error');
            }
        }

        // 优化瓦片缓存
        function optimizeTileCache() {
            // 如果缓存过大，清理远离当前视口的瓦片
            if (tileCache.size > CACHE_SIZE_LIMIT * 0.95) { // 从90%提高到95%，进一步减少清理频率
                try {
                    const viewport = viewer.viewport;
                    const currentBounds = viewport.getBounds();
                    const currentZoom = viewport.getZoom();

                    // 计算有效视口范围（包括额外缓冲区）
                    const bufferFactor = 2.0; // 保留当前视口2倍范围内的瓦片
                    const validBounds = new OpenSeadragon.Rect(
                        currentBounds.x - currentBounds.width * bufferFactor / 2,
                        currentBounds.y - currentBounds.height * bufferFactor / 2,
                        currentBounds.width * bufferFactor,
                        currentBounds.height * bufferFactor
                    );

                    // 获取当前缩放级别的合理范围
                    const minValidLevel = Math.max(0, Math.floor(currentZoom) - 2);
                    const maxValidLevel = Math.floor(currentZoom) + 2;

                    // 标记需要删除的键
                    const keysToDelete = [];

                    // 检查每个缓存项
                    tileCache.forEach((value, key) => {
                        try {
                            // 提取瓦片级别和坐标信息
                            const match = key.match(/\/(\d+)\/(\d+)_(\d+)\./);
                            if (match) {
                                const level = parseInt(match[1]);
                                const x = parseInt(match[2]);
                                const y = parseInt(match[3]);

                                // 如果级别不在有效范围，标记为删除
                                if (level < minValidLevel || level > maxValidLevel) {
                                    keysToDelete.push(key);
                                    return;
                                }

                                // 检查是否在有效视口范围内
                                if (!validBounds.containsPoint(new OpenSeadragon.Point(x, y))) {
                                    keysToDelete.push(key);
                                    return;
                                }
                            }
                        } catch (e) {
                            // 解析错误，保守地保留该缓存项
                        }
                    });

                    // 删除标记的键
                    keysToDelete.forEach(key => tileCache.delete(key));

                    addLog(`已清理 ${keysToDelete.length} 个瓦片缓存项，当前缓存大小: ${tileCache.size}`, 'info');
                } catch (e) {
                    console.warn("优化缓存时出错:", e);
                }
            }
        }

        // 在页面加载完成后执行的初始化代码
        window.addEventListener('load', function() {
            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const taskId = urlParams.get('taskId');

            // 如果有taskId参数，等待OpenSeadragon加载后自动加载该切片
            if (taskId) {
                document.getElementById('identifier').value = taskId;

                // 检查OpenSeadragon是否已加载
                if (openSeadragonLoaded && typeof OpenSeadragon !== 'undefined') {
                    loadSlide();
                } else {
                    // 等待OpenSeadragon加载完成
                    window.addEventListener('openSeadragonLoaded', function() {
                        loadSlide();
                    }, { once: true });
                }
            }

            // 优化滚轮事件，提高响应速度
            const viewerContainer = document.getElementById('viewer-container');
            if (viewerContainer) {
                // 减少DOM重绘，优化性能核心部分
                viewerContainer.style.willChange = "transform";
                viewerContainer.style.backfaceVisibility = "hidden";
                viewerContainer.style.perspective = "1000px";
                viewerContainer.style.transform = "translateZ(0)";

                // 滚轮缩放节流变量
                let wheelTimeout = null;
                let lastWheelTime = 0;
                const WHEEL_THROTTLE_DELAY = 16; // 约60fps

                // 使用passive: false选项来确保preventDefault工作
                viewerContainer.addEventListener('wheel', function(e) {
                    if (!viewer || !viewer.viewport) {
                        return;
                    }

                    e.preventDefault();
                    e.stopPropagation();

                    // 节流处理，避免滚轮事件过于频繁
                    const currentTime = Date.now();
                    if (currentTime - lastWheelTime < WHEEL_THROTTLE_DELAY) {
                        return;
                    }
                    lastWheelTime = currentTime;

                    // 清除之前的超时
                    if (wheelTimeout) {
                        clearTimeout(wheelTimeout);
                    }

                    // 延迟执行缩放，确保平滑性
                    wheelTimeout = setTimeout(() => {
                        try {
                            // 检查查看器和视口是否仍然有效
                            if (!viewer || !viewer.viewport) {
                                return;
                            }

                            // 计算缩放因子 - 减小缩放步长，提供更平滑的体验
                            const zoomFactor = e.deltaY < 0 ? 1.15 : 0.85; // 向上滚动放大，向下滚动缩小
                            
                            // 获取当前缩放级别，检查缩放限制
                            const currentZoom = viewer.viewport.getZoom();
                            const minZoom = viewer.viewport.getMinZoom();
                            const maxZoom = viewer.viewport.getMaxZoom();
                            
                            const newZoom = currentZoom * zoomFactor;
                            
                            // 检查缩放边界
                            if (newZoom < minZoom || newZoom > maxZoom) {
                                console.debug('缩放已达到边界限制');
                                return;
                            }
                            
                            // 获取鼠标相对于查看器的位置
                            const viewerRect = viewerContainer.getBoundingClientRect();
                            const viewportPoint = viewer.viewport.pointFromPixel(
                                new OpenSeadragon.Point(
                                    e.clientX - viewerRect.left,
                                    e.clientY - viewerRect.top
                                )
                            );
                            
                            // 以鼠标位置为中心进行缩放 - 使用immediate=true提供更响应的体验
                            viewer.viewport.zoomBy(zoomFactor, viewportPoint, true);
                            
                            // 确保布局完整性
                            setTimeout(() => {
                                if (typeof ensurePageLayoutIntegrity === 'function') {
                                    ensurePageLayoutIntegrity();
                                }
                            }, 100);
                            
                        } catch (error) {
                            console.warn('滚轮缩放处理出错:', error);
                            try {
                                // fallback: 简单的中心缩放
                                if (viewer && viewer.viewport) {
                                    const zoomFactor = e.deltaY < 0 ? 1.15 : 0.85;
                                    viewer.viewport.zoomBy(zoomFactor, null, true);
                                }
                            } catch (fallbackError) {
                                console.error('fallback缩放也失败:', fallbackError);
                            }
                        }
                    }, 10); // 10ms延迟，确保平滑性

                }, { passive: false });

                // 优化鼠标移动性能，防止频繁重绘
                let mouseMoveThrottle;
                viewerContainer.addEventListener('mousemove', function(e) {
                    if (mouseMoveThrottle) return;
                    mouseMoveThrottle = setTimeout(function() {
                        mouseMoveThrottle = null;
                    }, 16); // 约60fps
                });

                // 优化触摸事件，减少移动设备上的抖动
                viewerContainer.addEventListener('touchmove', function(e) {
                    if (e.touches.length > 1) {
                        e.preventDefault();
                    }
                }, { passive: false });

                // 连续绘制控制
                let continuousDrawing = false;
                function requestContinuousRedraw() {
                    if (continuousDrawing || !viewer) return;

                    continuousDrawing = true;
                    let lastTimestamp = 0;

                    function redraw(timestamp) {
                        if (!viewer) {
                            continuousDrawing = false;
                            return;
                        }

                        // 限制重绘频率
                        if (timestamp - lastTimestamp > 100) {
                            viewer.world.draw();
                            lastTimestamp = timestamp;
                        }

                        requestAnimationFrame(redraw);
                    }

                    requestAnimationFrame(redraw);
                }

                // 视口变化时启动连续绘制
                if (viewer) {
                    viewer.addHandler('viewport-change', function() {
                        requestContinuousRedraw();
                    });
                }
            }

            // 全局错误处理，防止JS错误导致的渲染中断（移除重复的事件监听器）
            // 全局错误处理已在脚本开头添加，此处不再重复
        });

        // 页面关闭时的清理逻辑 - 新增
        window.addEventListener('beforeunload', function(e) {
            // 获取当前标识符
            const currentIdentifier = document.getElementById('identifier').value;

            // 通知后端停止异步任务
            if (currentIdentifier && currentIdentifier.trim() !== '') {
                try {
                    // 使用sendBeacon发送停止请求（在页面关闭时更可靠）
                    navigator.sendBeacon(`/slice/api/dzi/stop-async/${currentIdentifier}`);
                    console.log('已通知后端停止异步任务:', currentIdentifier);
                } catch (error) {
                    console.warn('通知后端停止异步任务失败:', error);
                }
            }

            // 停止查看器和清理资源
            if (viewer) {
                try {
                    viewer.destroy();
                    viewer = null;
                    addLog('页面关闭，已清理查看器资源', 'info');
                } catch (error) {
                    console.warn('清理查看器资源时出错:', error);
                }
            }

            // 清理缓存
            if (tileCache) {
                tileCache.clear();
                addLog('页面关闭，已清理瓦片缓存', 'info');
            }

            // 清理画布池
            if (canvasPool) {
                canvasPool.length = 0;
                addLog('页面关闭，已清理画布池', 'info');
            }
        });

        // 计算图像总瓦片数量的辅助函数
        function calculateTotalTiles(tiledImage) {
            try {
                // 使用更安全的方式获取层级数量
                const maxLevel = tiledImage.source && tiledImage.source.maxLevel ?
                    tiledImage.source.maxLevel :
                    (tiledImage.source && tiledImage.source.Levels ? tiledImage.source.Levels.length - 1 : 10);

                let totalTiles = 0;

                // 获取图像基础尺寸
                const baseWidth = tiledImage.source && tiledImage.source.width ? tiledImage.source.width : 1000;
                const baseHeight = tiledImage.source && tiledImage.source.height ? tiledImage.source.height : 1000;

                for (let level = 0; level <= maxLevel; level++) {
                    // 计算该层级的尺寸（每层级缩小一半）
                    const levelScale = Math.pow(0.5, maxLevel - level);
                    const levelWidth = baseWidth * levelScale;
                    const levelHeight = baseHeight * levelScale;

                    // 计算该层级的瓦片数量
                    const tileSize = 256; // 标准瓦片大小
                    const tilesX = Math.ceil(levelWidth / tileSize);
                    const tilesY = Math.ceil(levelHeight / tileSize);

                    // 该层级的瓦片总数
                    totalTiles += tilesX * tilesY;
                }

                return totalTiles;
            } catch (e) {
                console.warn('计算总瓦片数量时出错:', e);
                return 100; // 返回一个合理的默认值
            }
        }

        // 全局变量存储当前的颜色参数
        let currentColorParams = {
            brightness: 0.0,
            contrast: 1.0,
            gamma: 1.0,
            saturation: 1.0,
            hue: 0.0,
            redGain: 1.0,
            greenGain: 1.0,
            blueGain: 1.0,
            sharpen: 0,
            colorStyle: 0
        };

        // 检测是否为SDPC文件并设置默认颜色参数
        function initSDPCColorParams() {
            const identifier = document.getElementById("identifier").value.trim();
            const isSDPCFile = identifier.toLowerCase().includes('sdpc');
            
            if (isSDPCFile) {
                // SDPC文件使用优化的默认颜色参数，提高显示质量
                currentColorParams = {
                    brightness: 0.1,    // 轻微增加亮度
                    contrast: 1.1,      // 轻微增加对比度  
                    gamma: 0.9,         // 轻微降低伽马值，提亮暗部
                    saturation: 1.05,   // 轻微增加饱和度
                    hue: 0.0,
                    redGain: 1.0,
                    greenGain: 1.0,
                    blueGain: 1.0,
                    sharpen: 0,
                    colorStyle: 0
                };
                
                // 更新UI控件显示
                updateColorControlsUI();
                
                console.log('SDPC文件：应用优化的默认颜色参数');
            }
        }

        // 更新颜色控制UI显示
        function updateColorControlsUI() {
            document.getElementById('brightness').value = currentColorParams.brightness;
            document.getElementById('brightness-value').textContent = currentColorParams.brightness.toFixed(1);
            document.getElementById('contrast').value = currentColorParams.contrast;
            document.getElementById('contrast-value').textContent = currentColorParams.contrast.toFixed(1);
            document.getElementById('gamma').value = currentColorParams.gamma;
            document.getElementById('gamma-value').textContent = currentColorParams.gamma.toFixed(1);
            document.getElementById('saturation').value = currentColorParams.saturation;
            document.getElementById('saturation-value').textContent = currentColorParams.saturation.toFixed(1);
            document.getElementById('hue').value = currentColorParams.hue;
            document.getElementById('hue-value').textContent = currentColorParams.hue + '°';
            document.getElementById('redGain').value = currentColorParams.redGain;
            document.getElementById('redGain-value').textContent = currentColorParams.redGain.toFixed(1);
            document.getElementById('greenGain').value = currentColorParams.greenGain;
            document.getElementById('greenGain-value').textContent = currentColorParams.greenGain.toFixed(1);
            document.getElementById('blueGain').value = currentColorParams.blueGain;
            document.getElementById('blueGain-value').textContent = currentColorParams.blueGain.toFixed(1);
            document.getElementById('sharpen').value = currentColorParams.sharpen;
            document.getElementById('sharpen-value').textContent = currentColorParams.sharpen.toString();
        }

        // 初始化颜色控制事件监听器
        function initColorControls() {
            // 绑定滑动条事件
            ['brightness', 'contrast', 'gamma', 'saturation', 'hue', 'redGain', 'greenGain', 'blueGain', 'sharpen'].forEach(param => {
                const slider = document.getElementById(param);
                const valueDisplay = document.getElementById(param + '-value');

                if (slider && valueDisplay) {
                    // 防抖函数，避免频繁触发颜色校正
                    let debounceTimer;

                    slider.addEventListener('input', function() {
                        let value = parseFloat(slider.value);
                        currentColorParams[param] = value;

                        // 更新显示值
                        if (param === 'hue') {
                            valueDisplay.textContent = value + '°';
                        } else if (param === 'sharpen') {
                            valueDisplay.textContent = value.toString(); // 锐化显示整数
                        } else {
                            valueDisplay.textContent = value.toFixed(1);
                        }

                        // 防抖处理，在用户停止拖动500ms后自动应用颜色校正
                        clearTimeout(debounceTimer);
                        debounceTimer = setTimeout(() => {
                            if (viewer) {
                                applyColorCorrection();
                            }
                        }, 500);
                    });

                    // 当用户松开滑动条时立即应用
                    slider.addEventListener('mouseup', function() {
                        clearTimeout(debounceTimer);
                        if (viewer) {
                            applyColorCorrection();
                        }
                    });

                    // 处理触摸事件（移动设备）
                    slider.addEventListener('touchend', function() {
                        clearTimeout(debounceTimer);
                        if (viewer) {
                            applyColorCorrection();
                        }
                    });
                }
            });
        }

        // 重置颜色设置
        function resetColorSettings() {
            currentColorParams = {
                brightness: 0.0,
                contrast: 1.0,
                gamma: 1.0,
                saturation: 1.0,
                hue: 0.0,
                redGain: 1.0,
                greenGain: 1.0,
                blueGain: 1.0,
                sharpen: 0,
                colorStyle: 0
            };

            // 更新UI控件
            document.getElementById('brightness').value = 0.0;
            document.getElementById('brightness-value').textContent = '0.0';
            document.getElementById('contrast').value = 1.0;
            document.getElementById('contrast-value').textContent = '1.0';
            document.getElementById('gamma').value = 1.0;
            document.getElementById('gamma-value').textContent = '1.0';
            document.getElementById('saturation').value = 1.0;
            document.getElementById('saturation-value').textContent = '1.0';
            document.getElementById('hue').value = 0.0;
            document.getElementById('hue-value').textContent = '0°';
            document.getElementById('redGain').value = 1.0;
            document.getElementById('redGain-value').textContent = '1.0';
            document.getElementById('greenGain').value = 1.0;
            document.getElementById('greenGain-value').textContent = '1.0';
            document.getElementById('blueGain').value = 1.0;
            document.getElementById('blueGain-value').textContent = '1.0';
            document.getElementById('sharpen').value = 0;
            document.getElementById('sharpen-value').textContent = '0';

            // 如果查看器已加载，重新加载切片以应用默认颜色
            if (viewer) {
                applyColorCorrection();
            }
        }

        // 应用颜色校正
        function applyColorCorrection() {
            if (!viewer) {
                alert('请先加载切片！');
                return;
            }

            const identifier = document.getElementById("identifier").value.trim();
            if (!identifier) {
                alert('请输入有效的切片标识符！');
                return;
            }

            addLog('正在应用颜色校正...', 'info');

            try {
                // 保存当前视图状态
                const currentZoom = viewer.viewport.getZoom();
                const currentCenter = viewer.viewport.getCenter();

                // 构建带颜色参数的自定义瓦片源
                const customTileSource = {
                    type: 'legacy-image-pyramid',
                    levels: []
                };

                // 获取原始DZI描述文件来构建自定义瓦片源
                const dziUrl = `/slice/api/dzi/${identifier}.dzi?cname=qpDhc`;

                fetch(dziUrl)
                    .then(response => response.text())
                    .then(dziXml => {
                        // 解析DZI XML获取图像信息
                        const parser = new DOMParser();
                        const xmlDoc = parser.parseFromString(dziXml, "text/xml");
                        const imageElement = xmlDoc.getElementsByTagName("Image")[0];
                        const sizeElement = xmlDoc.getElementsByTagName("Size")[0];

                        if (!imageElement || !sizeElement) {
                            throw new Error('无法解析DZI描述文件');
                        }

                        const width = parseInt(sizeElement.getAttribute("Width"));
                        const height = parseInt(sizeElement.getAttribute("Height"));
                        const tileSize = parseInt(imageElement.getAttribute("TileSize") || "256");
                        const overlap = parseInt(imageElement.getAttribute("Overlap") || "0");

                        // 计算层级数量
                        const maxLevel = Math.ceil(Math.log(Math.max(width, height)) / Math.log(2));

                        // 构建自定义瓦片源的层级信息
                        for (let level = 0; level <= maxLevel; level++) {
                            const scale = Math.pow(0.5, maxLevel - level);
                            const levelWidth = Math.ceil(width * scale);
                            const levelHeight = Math.ceil(height * scale);

                            customTileSource.levels.push({
                                url: `/slice/api/dzi/${identifier}_files/${level}/TileGroup0/`,
                                width: levelWidth,
                                height: levelHeight
                            });
                        }

                        // 创建自定义瓦片源
                        const tileSource = {
                            height: height,
                            width: width,
                            tileSize: tileSize,
                            overlap: overlap,
                            minLevel: 0,
                            maxLevel: maxLevel,

                            // 自定义瓦片URL生成函数
                            getTileUrl: function(level, x, y) {
                                // 构建包含颜色参数的查询字符串
                                const queryParams = new URLSearchParams({
                                    brightness: currentColorParams.brightness,
                                    contrast: currentColorParams.contrast,
                                    gamma: currentColorParams.gamma,
                                    saturation: currentColorParams.saturation,
                                    hue: currentColorParams.hue,
                                    redGain: currentColorParams.redGain,
                                    greenGain: currentColorParams.greenGain,
                                    blueGain: currentColorParams.blueGain,
                                    sharpen: currentColorParams.sharpen,
                                    colorStyle: currentColorParams.colorStyle
                                });

                                return `/slice/api/dzi/${identifier}_files/${level}/${x}_${y}.jpg?${queryParams.toString()}`;
                            }
                        };

                        // 使用自定义瓦片源重新打开查看器
                        viewer.open(tileSource);

                        // 添加一次性事件监听器处理加载结果
                        const handleOpen = function() {
                            addLog('颜色校正应用成功！', 'success');

                            // 恢复视图状态
                            setTimeout(() => {
                                try {
                                    viewer.viewport.zoomTo(currentZoom, currentCenter, true);
                                } catch (e) {
                                    viewer.viewport.goHome();
                                }
                            }, 200);

                            // 清理事件监听器
                            viewer.removeHandler('open', handleOpen);
                            viewer.removeHandler('open-failed', handleOpenFailed);
                        };

                        const handleOpenFailed = function(event) {
                            addLog('颜色校正应用失败：' + (event.message || '未知错误'), 'error');

                            // 回退到原始DZI
                            const originalDziUrl = `/slice/api/dzi/${identifier}.dzi?cname=qpDhc`;
                            viewer.open(originalDziUrl);

                            // 清理事件监听器
                            viewer.removeHandler('open', handleOpen);
                            viewer.removeHandler('open-failed', handleOpenFailed);
                        };

                        // 绑定事件监听器
                        viewer.addHandler('open', handleOpen);
                        viewer.addHandler('open-failed', handleOpenFailed);

                    }) // 闭合 .then(dziXml => { 的大括号
                    .catch(error => {
                        addLog('获取DZI描述文件失败：' + error.message, 'error');
                        console.error('DZI fetch error:', error);
                    });

            } catch (error) {
                addLog('应用颜色校正时出错：' + error.message, 'error');
                console.error('Color correction error:', error);
            }
        }

        // 页面加载完成后初始化颜色控制
        document.addEventListener('DOMContentLoaded', function() {
            // 确保initColorControls函数存在后再调用
            if (typeof initColorControls === 'function') {
                initColorControls();
            }
        });

        // 确保页面布局完整性的函数 - 修复：防止页面上方部分消失
        function ensurePageLayoutIntegrity() {
            try {
                // 确保页面头部始终可见
                const header = document.querySelector('.header');
                if (header) {
                    header.style.display = 'block';
                    header.style.visibility = 'visible';
                    header.style.position = 'static';
                }

                // 确保主容器布局正确
                const container = document.querySelector('.container');
                if (container) {
                    container.style.display = 'flex';
                    container.style.flexDirection = 'column';
                    container.style.flex = '1';
                }

                // 确保输入组可见
                const inputGroup = document.querySelector('.input-group');
                if (inputGroup) {
                    inputGroup.style.display = 'flex';
                    inputGroup.style.visibility = 'visible';
                }

                // 确保颜色控制面板可见
                const colorPanel = document.querySelector('.color-control-panel');
                if (colorPanel) {
                    colorPanel.style.display = 'block';
                    colorPanel.style.visibility = 'visible';
                }

                // 确保查看器容器正确设置
                const viewerContainer = document.getElementById('viewer-container');
                if (viewerContainer) {
                    viewerContainer.style.flex = '1';
                    viewerContainer.style.position = 'relative';
                    viewerContainer.style.overflow = 'hidden';
                    // 移除可能造成问题的样式
                    viewerContainer.style.removeProperty('position-absolute');
                    viewerContainer.style.removeProperty('top');
                    viewerContainer.style.removeProperty('left');
                    viewerContainer.style.removeProperty('z-index');
                }

                // 确保body布局正确
                const body = document.body;
                if (body) {
                    body.style.display = 'flex';
                    body.style.flexDirection = 'column';
                    body.style.height = '100vh';
                    body.style.margin = '0';
                    body.style.padding = '0';
                    body.style.overflow = 'hidden';
                }

                console.debug('页面布局完整性检查完成');
            } catch (error) {
                console.warn('页面布局完整性检查出错:', error);
            }
        }

        // 页面加载后立即执行布局完整性检查
        document.addEventListener('DOMContentLoaded', function() {
            ensurePageLayoutIntegrity();
        });

        // 页面加载完成后也执行一次
        window.addEventListener('load', function() {
            setTimeout(ensurePageLayoutIntegrity, 100);
        });

        // 定期检查页面布局完整性
        setInterval(function() {
            ensurePageLayoutIntegrity();
        }, 3000); // 每3秒检查一次，及时发现并修复布局问题

        // 立即执行一次布局检查，确保页面从开始就是正确的
        setTimeout(ensurePageLayoutIntegrity, 50);

        // 将函数挂载到 window，确保按钮可用
        window.loadSlide = loadSlide;
        window.resetColorSettings = resetColorSettings;
        window.applyColorCorrection = applyColorCorrection;
        window.ensurePageLayoutIntegrity = ensurePageLayoutIntegrity; // 导出布局修复函数

        // 全局禁用浏览器原生全屏快捷键 - 修复：彻底禁止所有全屏行为
        document.addEventListener('keydown', function(event) {
            // 禁用F11等浏览器原生全屏快捷键
            if (event.key === 'F11' || 
                (event.altKey && event.key === 'Enter') ||
                (event.ctrlKey && event.shiftKey && event.key === 'F')) {
                event.preventDefault();
                event.stopPropagation();
                addLog('已阻止浏览器原生全屏快捷键', 'info');
            }
        }, true); // 使用capture阶段确保优先处理

        /**
         * 为SDPC文件预加载关键层级 - 修复版本，避免触发自动缩放
         */
        function preloadCriticalLevelsForSDPC() {
            if (!viewer || !viewer.world || viewer.world.getItemCount() === 0) {
                console.log('SDPC预加载：查看器未就绪，跳过预加载');
                return;
            }

            try {
                const item = viewer.world.getItemAt(0);
                const source = item.source;
                
                if (!source || !source.Levels) {
                    console.log('SDPC预加载：无法获取层级信息，跳过预加载');
                    return;
                }
                
                // 关键修复：只预加载最低分辨率的几个层级，避免触发高分辨率加载
                const maxLevel = source.Levels.length - 1;
                const lowResLevels = [0, 1]; // 只预加载最低的2个层级
                
                console.log(`SDPC预加载：总层级=${maxLevel + 1}, 预加载低分辨率层级=[${lowResLevels.join(',')}]`);
                
                lowResLevels.forEach((level, index) => {
                    if (level <= maxLevel) {
                        setTimeout(() => {
                            preloadLevelForSDPC(level);
                        }, (index + 1) * 500); // 增加间隔时间，避免影响当前显示
                    }
                });
                
            } catch (error) {
                console.warn('SDPC关键层级预加载失败:', error);
            }
        }

        /**
         * 预加载SDPC文件的指定层级
         */
        function preloadLevelForSDPC(level) {
            if (!viewer || !viewer.world || viewer.world.getItemCount() === 0) {
                return;
            }

            try {
                const item = viewer.world.getItemAt(0);
                const source = item.source;
                
                if (!source || !source.Levels || level >= source.Levels.length || level < 0) {
                    return;
                }
                
                const levelInfo = source.Levels[level];
                const tilesX = Math.ceil(levelInfo.Width / source.TileSize);
                const tilesY = Math.ceil(levelInfo.Height / source.TileSize);
                
                // 获取当前视图范围
                const viewport = viewer.viewport;
                const bounds = viewport.getBounds();
                
                // 计算需要预加载的瓦片范围（当前视图的2倍范围）
                const tileSize = source.TileSize;
                const levelScale = Math.pow(2, level);
                
                const startTileX = Math.max(0, Math.floor((bounds.x * levelInfo.Width) / tileSize) - 1);
                const endTileX = Math.min(tilesX - 1, Math.ceil(((bounds.x + bounds.width) * levelInfo.Width) / tileSize) + 1);
                const startTileY = Math.max(0, Math.floor((bounds.y * levelInfo.Height) / tileSize) - 1);
                const endTileY = Math.min(tilesY - 1, Math.ceil(((bounds.y + bounds.height) * levelInfo.Height) / tileSize) + 1);
                
                // 限制预加载的瓦片数量，避免过度加载
                const maxTiles = 16; // 最多预加载16个瓦片
                let preloadedCount = 0;
                
                for (let tileY = startTileY; tileY <= endTileY && preloadedCount < maxTiles; tileY++) {
                    for (let tileX = startTileX; tileX <= endTileX && preloadedCount < maxTiles; tileX++) {
                        // 创建瓦片URL并预加载
                        const tileUrl = source.getTileUrl(level, tileX, tileY);
                        
                        // 使用Image对象进行预加载
                        const img = new Image();
                        img.onload = function() {
                            // 预加载成功，无需特殊处理
                        };
                        img.onerror = function() {
                            // 预加载失败，静默处理
                        };
                        img.src = tileUrl;
                        
                        preloadedCount++;
                    }
                }
                
                if (preloadedCount > 0) {
                    addLog(`SDPC层级${level}预加载${preloadedCount}个瓦片`, 'info');
                }
                
            } catch (error) {
                console.warn('预加载SDPC层级失败:', error);
            }
        }

        // 2. open事件后只设置一次初始视图，避免多次goHome/zoomTo
        let hasSetInitialView = false;
        viewer.addHandler('open', function(event) {
            if (!hasSetInitialView) {
                hasSetInitialView = true;
                // 只设置一次初始视图
                viewer.viewport.goHome(false);
                viewer.forceRedraw();
            }
            // ... existing code ...
        });

        // 3. tile-drawn事件中强制像素对齐，消除亚像素抖动
        viewer.addHandler("tile-drawn", function(event) {
            const ctx = event.tile.context;
            ctx.imageSmoothingEnabled = true;
            if (ctx.imageSmoothingQuality !== undefined) {
                ctx.imageSmoothingQuality = "high";
            }
            // 强制像素对齐
            ctx.setTransform(1, 0, 0, 1, Math.floor(event.tile.position.x), Math.floor(event.tile.position.y));
        });

        // 4. continuousDrawing逻辑简化，避免多余重绘
        // 移除requestContinuousRedraw相关代码，仅在必要时forceRedraw

        // 1. 增大前端tile缓存和并发加载参数，合并到OpenSeadragon实例化处
        // ...在createViewerInstance、initViewerWithDzi等OpenSeadragon实例化配置中，直接设置：
        // maxImageCacheCount: 5000, imageLoaderLimit: 50, maxTilesPerFrame: 100
        // 不再重复声明baseConfig
        // ... existing code ...
        // 2. 智能预加载：每次缩放/拖拽时，预加载视口周边一圈瓦片
        function smartPreloadTiles() {
            if (!viewer || !viewer.world || viewer.world.getItemCount() === 0) return;
            const item = viewer.world.getItemAt(0);
            const source = item.source;
            if (!source) return;
            const tileSize = source.tileSize || source.TileSize || 256;
            const level = viewer.viewport.getZoom(true);
            const currentLevel = Math.round(level);
            const bounds = viewer.viewport.getBounds(true);
            const imageWidth = source.width;
            const imageHeight = source.height;
            // 计算当前视口对应的tile范围
            const scale = Math.pow(2, source.maxLevel - currentLevel);
            const left = Math.floor(bounds.x * imageWidth / tileSize / scale) - 1;
            const right = Math.ceil((bounds.x + bounds.width) * imageWidth / tileSize / scale) + 1;
            const top = Math.floor(bounds.y * imageHeight / tileSize / scale) - 1;
            const bottom = Math.ceil((bounds.y + bounds.height) * imageHeight / tileSize / scale) + 1;
            // 预加载视口周边一圈
            for (let x = left; x <= right; x++) {
                for (let y = top; y <= bottom; y++) {
                    if (x < 0 || y < 0) continue;
                    const tileUrl = source.getTileUrl ? source.getTileUrl(currentLevel, x, y) : null;
                    if (tileUrl) {
                        const img = new Image();
                        img.src = tileUrl;
                    }
                }
            }
        }
        // ... existing code ...
        // 在viewer初始化后绑定智能预加载事件
        function bindSmartPreload() {
            if (viewer) {
                // 检查是否为SDPC文件
                const taskId = getURLParameter('taskId') || '';
                const isSDPCFile = taskId.toLowerCase().includes('sdpc');
                
                // 关键修复：只对非SDPC文件启用智能预加载，避免并发过高
                if (!isSDPCFile) {
                    viewer.addHandler('zoom', smartPreloadTiles);
                    viewer.addHandler('pan', smartPreloadTiles);
                    addLog('已启用智能预加载', 'info');
                } else {
                    addLog('SDPC文件跳过智能预加载，使用按需加载策略', 'info');
                }
            }
        }

        /**
         * 强制预加载SDPC文件当前视图的所有可见瓦片
         * 确保一次性显示，避免分块加载
         */
        function forcePreloadVisibleTilesForSDPC() {
            if (!viewer || !viewer.world || viewer.world.getItemCount() === 0) {
                console.log('SDPC强制预加载：查看器未就绪，跳过预加载');
                return;
            }

            try {
                const item = viewer.world.getItemAt(0);
                const source = item.source;
                
                if (!source || !source.Levels) {
                    console.log('SDPC强制预加载：无法获取层级信息，跳过预加载');
                    return;
                }
                
                // 获取当前视图的最佳层级
                const viewport = viewer.viewport;
                const zoom = viewport.getZoom();
                const homeZoom = viewport.getHomeZoom();
                
                // 计算当前显示的层级
                const currentLevel = Math.max(0, Math.min(source.Levels.length - 1, 
                    Math.floor(Math.log2(zoom / homeZoom)) + source.Levels.length - 1));
                
                console.log(`SDPC强制预加载：当前层级=${currentLevel}, 缩放=${zoom.toFixed(3)}, 基础缩放=${homeZoom.toFixed(3)}`);
                
                // 预加载当前层级的所有可见瓦片
                preloadLevelTilesForSDPC(currentLevel, true);
                
                // 同时预加载相邻层级（上下各一级）
                if (currentLevel > 0) {
                    setTimeout(() => preloadLevelTilesForSDPC(currentLevel - 1, false), 200);
                }
                if (currentLevel < source.Levels.length - 1) {
                    setTimeout(() => preloadLevelTilesForSDPC(currentLevel + 1, false), 400);
                }
                
            } catch (error) {
                console.warn('SDPC强制预加载失败:', error);
            }
        }
        
        /**
         * 预加载指定层级的瓦片
         * @param {number} level 层级
         * @param {boolean} isCurrentLevel 是否为当前显示层级
         */
        function preloadLevelTilesForSDPC(level, isCurrentLevel = false) {
            if (!viewer || !viewer.world || viewer.world.getItemCount() === 0) {
                return;
            }

            try {
                const item = viewer.world.getItemAt(0);
                const source = item.source;
                
                if (!source || !source.Levels || level >= source.Levels.length || level < 0) {
                    return;
                }
                
                const levelInfo = source.Levels[level];
                const tileSize = source.TileSize || 256;
                const tilesX = Math.ceil(levelInfo.Width / tileSize);
                const tilesY = Math.ceil(levelInfo.Height / tileSize);
                
                // 获取当前视图范围
                const viewport = viewer.viewport;
                const bounds = viewport.getBounds();
                
                // 计算需要预加载的瓦片范围
                const startTileX = Math.max(0, Math.floor((bounds.x * levelInfo.Width) / tileSize));
                const endTileX = Math.min(tilesX - 1, Math.ceil(((bounds.x + bounds.width) * levelInfo.Width) / tileSize));
                const startTileY = Math.max(0, Math.floor((bounds.y * levelInfo.Height) / tileSize));
                const endTileY = Math.min(tilesY - 1, Math.ceil(((bounds.y + bounds.height) * levelInfo.Height) / tileSize));
                
                // 限制预加载数量（当前层级可以多加载一些）
                const maxTiles = isCurrentLevel ? 50 : 20;
                let preloadedCount = 0;
                
                console.log(`SDPC预加载层级${level}: 瓦片范围 X[${startTileX}-${endTileX}], Y[${startTileY}-${endTileY}], 最大${maxTiles}个瓦片`);
                
                // 优先加载中心瓦片
                const centerX = Math.floor((startTileX + endTileX) / 2);
                const centerY = Math.floor((startTileY + endTileY) / 2);
                
                // 创建瓦片坐标数组，按距离中心的远近排序
                const tileCoords = [];
                for (let tileY = startTileY; tileY <= endTileY; tileY++) {
                    for (let tileX = startTileX; tileX <= endTileX; tileX++) {
                        const distance = Math.abs(tileX - centerX) + Math.abs(tileY - centerY);
                        tileCoords.push({ x: tileX, y: tileY, distance: distance });
                    }
                }
                
                // 按距离排序，优先加载中心瓦片
                tileCoords.sort((a, b) => a.distance - b.distance);
                
                // 预加载瓦片
                tileCoords.slice(0, maxTiles).forEach((coord, index) => {
                    setTimeout(() => {
                        try {
                            // 构造瓦片URL（根据DZI标准）
                            const tileUrl = source.getTileUrl ? 
                                source.getTileUrl(level, coord.x, coord.y) :
                                `${dziUrl.replace('.dzi', '')}_files/${level}/${coord.x}_${coord.y}.jpg`;
                            
                            // 使用Image对象进行预加载
                            const img = new Image();
                            img.onload = function() {
                                // 预加载成功
                                if (isCurrentLevel && index === 0) {
                                    console.log(`SDPC当前层级${level}中心瓦片预加载成功`);
                                }
                            };
                            img.onerror = function() {
                                // 预加载失败，静默处理
                            };
                            img.src = tileUrl;
                        } catch (e) {
                            // 忽略预加载错误
                        }
                    }, index * 10); // 每个瓦片间隔10ms加载
                });
                
            } catch (error) {
                console.warn(`SDPC层级${level}预加载失败:`, error);
            }
        }

        // 获取DZI文件并初始化查看器
        function setupSDPCTileLoadingManager(viewer) {
            console.log('正在设置SDPC瓦片加载顺序管理器...');
            
            // 瓦片加载队列管理
            const tileLoadQueue = new Map(); // key: level, value: 瓦片坐标数组
            const loadingTiles = new Set(); // 正在加载的瓦片
            let isProcessingQueue = false;
            
            // 拦截原始的瓦片加载函数
            const originalTileSource = viewer.world.getItemAt(0).source;
            if (originalTileSource && originalTileSource.getTileUrl) {
                const originalGetTileUrl = originalTileSource.getTileUrl;
                
                // 重写瓦片URL获取函数，添加加载顺序控制
                originalTileSource.getTileUrl = function(level, x, y) {
                    const tileKey = `${level}_${x}_${y}`;
                    
                    // 将瓦片添加到加载队列
                    if (!tileLoadQueue.has(level)) {
                        tileLoadQueue.set(level, []);
                    }
                    
                    const queue = tileLoadQueue.get(level);
                    const tileInfo = { x, y, level, key: tileKey };
                    
                    // 检查是否已经在队列中
                    if (!queue.find(t => t.key === tileKey) && !loadingTiles.has(tileKey)) {
                        queue.push(tileInfo);
                        
                        // 按照空间位置排序：优先加载相邻的瓦片
                        queue.sort((a, b) => {
                            // 先按Y坐标排序（行优先）
                            if (a.y !== b.y) return a.y - b.y;
                            // 再按X坐标排序（列优先）
                            return a.x - b.x;
                        });
                        
                        console.log(`SDPC瓦片加入队列: 层级${level}, 坐标(${x},${y}), 队列长度: ${queue.length}`);
                    }
                    
                    // 启动队列处理
                    if (!isProcessingQueue) {
                        processSDPCTileQueue();
                    }
                    
                    return originalGetTileUrl.call(this, level, x, y);
                };
            }
            
            // SDPC瓦片队列处理器
            function processSDPCTileQueue() {
                if (isProcessingQueue) return;
                isProcessingQueue = true;
                
                // 处理所有层级的队列
                for (const [level, queue] of tileLoadQueue.entries()) {
                    if (queue.length === 0) continue;
                    
                    // 每次处理一行瓦片（确保相邻瓦片同时加载）
                    const firstTile = queue[0];
                    const currentRow = firstTile.y;
                    
                    // 找出同一行的所有瓦片
                    const rowTiles = [];
                    let i = 0;
                    while (i < queue.length && queue[i].y === currentRow) {
                        const tile = queue[i];
                        if (!loadingTiles.has(tile.key)) {
                            rowTiles.push(tile);
                            loadingTiles.add(tile.key);
                        }
                        i++;
                    }
                    
                    // 从队列中移除这些瓦片
                    tileLoadQueue.set(level, queue.slice(i));
                    
                    if (rowTiles.length > 0) {
                        console.log(`SDPC同步加载第${currentRow}行瓦片: ${rowTiles.length}个瓦片`);
                        
                        // 同步加载这一行的所有瓦片
                        loadSDPCTileRow(rowTiles);
                        
                        // 限制同时处理的行数，避免过载
                        break;
                    }
                }
                
                // 继续处理队列
                setTimeout(() => {
                    isProcessingQueue = false;
                    // 检查是否还有待处理的瓦片
                    const hasMoreTiles = Array.from(tileLoadQueue.values()).some(queue => queue.length > 0);
                    if (hasMoreTiles) {
                        processSDPCTileQueue();
                    }
                }, 50); // 50ms间隔，确保不会阻塞UI
            }
            
            // 加载一行瓦片
            function loadSDPCTileRow(rowTiles) {
                // 使用Promise.all确保整行瓦片同时完成
                const promises = rowTiles.map(tile => {
                    return new Promise((resolve, reject) => {
                        const img = new Image();
                        const tileUrl = originalTileSource.getTileUrl(tile.level, tile.x, tile.y);
                        
                        img.onload = function() {
                            loadingTiles.delete(tile.key);
                            console.log(`SDPC瓦片加载成功: 层级${tile.level}, 坐标(${tile.x},${tile.y})`);
                            resolve(tile);
                        };
                        
                        img.onerror = function() {
                            loadingTiles.delete(tile.key);
                            console.warn(`SDPC瓦片加载失败: 层级${tile.level}, 坐标(${tile.x},${tile.y})`);
                            reject(tile);
                        };
                        
                        // 设置加载超时
                        setTimeout(() => {
                            if (loadingTiles.has(tile.key)) {
                                loadingTiles.delete(tile.key);
                                console.warn(`SDPC瓦片加载超时: 层级${tile.level}, 坐标(${tile.x},${tile.y})`);
                                reject(tile);
                            }
                        }, 5000); // 5秒超时
                        
                        img.src = tileUrl;
                    });
                });
                
                // 等待整行瓦片加载完成
                Promise.allSettled(promises).then(results => {
                    const successCount = results.filter(r => r.status === 'fulfilled').length;
                    const failedCount = results.filter(r => r.status === 'rejected').length;
                    
                    if (successCount > 0) {
                        console.log(`SDPC瓦片行加载完成: 成功${successCount}个, 失败${failedCount}个`);
                        
                        // 强制重绘，确保瓦片正确显示
                        viewer.forceRedraw();
                    }
                });
            }
            
            console.log('SDPC瓦片加载顺序管理器设置完成');
        }

        // 初始化函数

        // 新增：SDPC格式专用WebGL直接渲染解决方案
        // 完全绕过OpenSeadragon的瓦片拼接机制，直接使用WebGL渲染
        function initSDPCWebGLRenderer(identifier, dziUrl) {
            console.log('正在初始化SDPC WebGL直接渲染器...');
            
            // 检查WebGL支持
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) {
                console.warn('WebGL不支持，回退到普通渲染模式');
                return false;
            }
            
            // 获取DZI信息
            fetch(dziUrl)
                .then(response => response.text())
                .then(xmlText => {
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
                    const imageElement = xmlDoc.querySelector('Image');
                    
                    if (!imageElement) {
                        throw new Error('无法解析DZI文件');
                    }
                    
                    const width = parseInt(imageElement.getAttribute('TileSize')) || 256;
                    const height = parseInt(imageElement.getAttribute('TileSize')) || 256;
                    const overlap = parseInt(imageElement.getAttribute('Overlap')) || 0;
                    const format = imageElement.getAttribute('Format') || 'jpg';
                    
                    // 创建SDPC专用的WebGL渲染器
                    const sdpcRenderer = new SDPCWebGLRenderer(gl, identifier, width, height, overlap);
                    
                    // 替换OpenSeadragon的瓦片加载系统
                    sdpcRenderer.initialize().then(() => {
                        console.log('SDPC WebGL渲染器初始化完成');
                        addLog('已启用SDPC WebGL直接渲染模式', 'success');
                    });
                })
                .catch(error => {
                    console.error('SDPC WebGL渲染器初始化失败:', error);
                    return false;
                });
            
            return true;
        }
        
        // SDPC WebGL渲染器类
        class SDPCWebGLRenderer {
            constructor(gl, identifier, tileWidth, tileHeight, overlap) {
                this.gl = gl;
                this.identifier = identifier;
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                this.overlap = overlap;
                this.tileCache = new Map();
                this.renderQueue = [];
                this.isRendering = false;
            }
            
            async initialize() {
                // 初始化WebGL着色器程序
                const vertexShaderSource = `
                    attribute vec2 a_position;
                    attribute vec2 a_texcoord;
                    varying vec2 v_texcoord;
                    
                    void main() {
                        gl_Position = vec4(a_position, 0.0, 1.0);
                        v_texcoord = a_texcoord;
                    }
                `;
                
                const fragmentShaderSource = `
                    precision mediump float;
                    varying vec2 v_texcoord;
                    uniform sampler2D u_texture;
                    
                    void main() {
                        gl_FragColor = texture2D(u_texture, v_texcoord);
                    }
                `;
                
                this.program = this.createProgram(vertexShaderSource, fragmentShaderSource);
                this.positionLocation = this.gl.getAttribLocation(this.program, 'a_position');
                this.texcoordLocation = this.gl.getAttribLocation(this.program, 'a_texcoord');
                this.textureLocation = this.gl.getUniformLocation(this.program, 'u_texture');
                
                // 创建缓冲区
                this.positionBuffer = this.gl.createBuffer();
                this.texcoordBuffer = this.gl.createBuffer();
                
                return true;
            }
            
            createProgram(vertexSource, fragmentSource) {
                const vertexShader = this.createShader(this.gl.VERTEX_SHADER, vertexSource);
                const fragmentShader = this.createShader(this.gl.FRAGMENT_SHADER, fragmentSource);
                
                const program = this.gl.createProgram();
                this.gl.attachShader(program, vertexShader);
                this.gl.attachShader(program, fragmentShader);
                this.gl.linkProgram(program);
                
                if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
                    throw new Error('WebGL程序链接失败');
                }
                
                return program;
            }
            
            createShader(type, source) {
                const shader = this.gl.createShader(type);
                this.gl.shaderSource(shader, source);
                this.gl.compileShader(shader);
                
                if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
                    throw new Error('着色器编译失败: ' + this.gl.getShaderInfoLog(shader));
                }
                
                return shader;
            }
            
            // 批量预加载瓦片
            async preloadTileGrid(level, startX, startY, endX, endY) {
                const promises = [];
                
                // 按行优先顺序创建加载任务
                for (let y = startY; y <= endY; y++) {
                    const rowPromises = [];
                    for (let x = startX; x <= endX; x++) {
                        rowPromises.push(this.loadTile(level, x, y));
                    }
                    // 确保同一行的瓦片同时完成加载
                    promises.push(Promise.all(rowPromises));
                }
                
                // 等待所有行按顺序完成
                for (const rowPromise of promises) {
                    await rowPromise;
                }
            }
            
            async loadTile(level, x, y) {
                const cacheKey = `${level}_${x}_${y}`;
                
                if (this.tileCache.has(cacheKey)) {
                    return this.tileCache.get(cacheKey);
                }
                
                const url = `/slice/api/dzi/${this.identifier}_files/${level}/${x}_${y}.jpg`;
                
                try {
                    const response = await fetch(url);
                    if (!response.ok) {
                        throw new Error(`瓦片加载失败: ${response.status}`);
                    }
                    
                    const blob = await response.blob();
                    const image = await this.createImageFromBlob(blob);
                    
                    // 创建WebGL纹理
                    const texture = this.createTexture(image);
                    
                    const tileData = {
                        texture: texture,
                        x: x,
                        y: y,
                        level: level,
                        width: image.width,
                        height: image.height
                    };
                    
                    this.tileCache.set(cacheKey, tileData);
                    return tileData;
                    
                } catch (error) {
                    console.error(`瓦片加载失败 (${level}, ${x}, ${y}):`, error);
                    return null;
                }
            }
            
            createImageFromBlob(blob) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = () => resolve(img);
                    img.onerror = reject;
                    img.src = URL.createObjectURL(blob);
                });
            }
            
            createTexture(image) {
                const texture = this.gl.createTexture();
                this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
                this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, this.gl.RGBA, this.gl.UNSIGNED_BYTE, image);
                this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
                this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
                this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);
                this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR);
                
                return texture;
            }
        }
        
        // 原有的SDPC瓦片加载管理器保留作为备用方案
        function setupSDPCTileLoadingManager(viewer) {
            if (!viewer) return;
            
            console.log('正在设置SDPC瓦片加载顺序管理器...');
            
            // 尝试使用WebGL直接渲染
            const currentTaskId = getURLParameter('taskId') || '';
            
            // 修复：如果没有taskId参数，直接返回，避免构建错误的DZI URL
            if (!currentTaskId || currentTaskId.trim() === '') {
                console.log('未找到taskId参数，跳过SDPC瓦片加载管理器设置');
                return;
            }
            
            const dziUrl = `/slice/api/dzi/${currentTaskId}.dzi?cname=qpDhc`;
            
            if (initSDPCWebGLRenderer(currentTaskId, dziUrl)) {
                addLog('已启用SDPC WebGL直接渲染模式，避免瓦片拼接问题', 'success');
                return; // WebGL渲染成功，直接返回
            }
            
            // WebGL失败时使用改进的瓦片加载策略
            addLog('WebGL不可用，使用改进的瓦片加载策略', 'warning');
            
            // 新增：SDPC瓦片同步渲染管理器
            const sdpcTileRenderer = new SDPCTileSyncRenderer(viewer);
            sdpcTileRenderer.initialize();
        }
        
        // SDPC瓦片同步渲染器类 - 确保相邻瓦片同步显示
        class SDPCTileSyncRenderer {
            constructor(viewer) {
                this.viewer = viewer;
                this.pendingTiles = new Map(); // 等待渲染的瓦片
                this.tileRows = new Map(); // 按行分组的瓦片
                this.currentLevel = -1;
                this.renderTimeout = null;
            }
            
            initialize() {
                console.log('初始化SDPC瓦片同步渲染器...');
                
                // 拦截瓦片加载完成事件
                this.viewer.addHandler('tile-loaded', (event) => {
                    this.handleTileLoaded(event);
                });
                
                // 拦截瓦片绘制事件
                this.viewer.addHandler('tile-drawing', (event) => {
                    this.handleTileDrawing(event);
                });
                
                // 监听视图变化，重置同步状态
                this.viewer.addHandler('viewport-change', (event) => {
                    this.handleViewportChange(event);
                });
                
                addLog('SDPC瓦片同步渲染器已启用', 'success');
            }
            
            handleTileLoaded(event) {
                const tile = event.tile;
                if (!tile || !tile.url) return;
                
                // 解析瓦片坐标
                const match = tile.url.match(/_files\/(\d+)\/(\d+)_(\d+)\.jpg/);
                if (!match) return;
                
                const level = parseInt(match[1]);
                const x = parseInt(match[2]);
                const y = parseInt(match[3]);
                
                console.debug(`瓦片加载完成: Level ${level}, (${x}, ${y})`);
                
                // 按行分组瓦片
                const rowKey = `${level}_${y}`;
                if (!this.tileRows.has(rowKey)) {
                    this.tileRows.set(rowKey, new Map());
                }
                this.tileRows.get(rowKey).set(x, tile);
                
                // 延迟渲染，等待同行的其他瓦片
                this.scheduleRowRender(level, y);
            }
            
            handleTileDrawing(event) {
                const tile = event.tile;
                if (!tile || !tile.url) return;
                
                // 解析瓦片坐标
                const match = tile.url.match(/_files\/(\d+)\/(\d+)_(\d+)\.jpg/);
                if (!match) return;
                
                const level = parseInt(match[1]);
                const x = parseInt(match[2]);
                const y = parseInt(match[3]);
                
                // 检查同行是否还有待渲染的瓦片
                const rowKey = `${level}_${y}`;
                if (this.hasIncompleteRow(rowKey)) {
                    // 暂停这个瓦片的渲染，等待同行其他瓦片
                    event.preventDefault?.();
                    console.debug(`延迟渲染瓦片 (${x}, ${y})，等待同行其他瓦片`);
                    return;
                }
                
                console.debug(`渲染瓦片: Level ${level}, (${x}, ${y})`);
            }
            
            scheduleRowRender(level, y) {
                // 清除之前的超时
                if (this.renderTimeout) {
                    clearTimeout(this.renderTimeout);
                }
                
                // 延迟50ms，等待同行其他瓦片加载
                this.renderTimeout = setTimeout(() => {
                    this.renderCompletedRows(level);
                }, 50);
            }
            
            renderCompletedRows(level) {
                console.debug(`检查Level ${level}的完整行...`);
                
                // 获取当前视图的瓦片范围
                const tiledImage = this.viewer.world.getItemAt(0);
                if (!tiledImage) return;
                
                const viewport = this.viewer.viewport;
                const bounds = viewport.getBounds();
                
                // 计算可见瓦片范围
                const tileSize = 256;
                const scale = Math.pow(2, level);
                const imageSize = tiledImage.source;
                
                if (!imageSize) return;
                
                const startTileX = Math.floor(bounds.x * imageSize.width / scale / tileSize);
                const endTileX = Math.ceil((bounds.x + bounds.width) * imageSize.width / scale / tileSize);
                const startTileY = Math.floor(bounds.y * imageSize.height / scale / tileSize);
                const endTileY = Math.ceil((bounds.y + bounds.height) * imageSize.height / scale / tileSize);
                
                // 检查每一行是否完整
                for (let y = startTileY; y <= endTileY; y++) {
                    const rowKey = `${level}_${y}`;
                    const row = this.tileRows.get(rowKey);
                    
                    if (!row) continue;
                    
                    // 检查这一行的瓦片是否都已加载
                    let isRowComplete = true;
                    for (let x = startTileX; x <= endTileX; x++) {
                        if (!row.has(x)) {
                            isRowComplete = false;
                            break;
                        }
                    }
                    
                    if (isRowComplete) {
                        console.debug(`第${y}行瓦片完整，开始同步渲染`);
                        this.renderRow(level, y, startTileX, endTileX);
                        this.tileRows.delete(rowKey); // 清理已渲染的行
                    }
                }
            }
            
            renderRow(level, y, startX, endX) {
                // 按X坐标顺序渲染这一行的瓦片
                for (let x = startX; x <= endX; x++) {
                    const rowKey = `${level}_${y}`;
                    const row = this.tileRows.get(rowKey);
                    const tile = row?.get(x);
                    
                    if (tile && tile.element) {
                        // 确保瓦片立即可见
                        tile.element.style.opacity = '1';
                        tile.element.style.transition = 'none';
                        tile.element.style.transform = 'none';
                        
                        console.debug(`同步渲染瓦片: (${x}, ${y})`);
                    }
                }
                
                // 强制重绘这一行
                requestAnimationFrame(() => {
                    if (this.viewer && this.viewer.forceRedraw) {
                        this.viewer.forceRedraw();
                    }
                });
            }
            
            hasIncompleteRow(rowKey) {
                const row = this.tileRows.get(rowKey);
                if (!row) return false;
                
                // 简化判断：如果行中瓦片数量较少，可能还不完整
                return row.size < 3; // 假设大部分行至少有3个瓦片
            }
            
            handleViewportChange(event) {
                // 视图变化时清理旧的状态
                const currentZoom = this.viewer.viewport.getZoom();
                const newLevel = Math.floor(Math.log2(currentZoom));
                
                if (newLevel !== this.currentLevel) {
                    console.debug(`缩放级别变化: ${this.currentLevel} -> ${newLevel}`);
                    this.currentLevel = newLevel;
                    // 清理旧级别的瓦片缓存
                    this.tileRows.clear();
                }
            }
        }
        
        // 新增：更简单的SDPC瓦片位置锁定方案
        function lockSDPCTilePositions(viewer) {
            if (!viewer) return;
            
            console.log('启用SDPC瓦片位置锁定...');
            
            // 拦截所有瓦片的位置更新
            viewer.addHandler('tile-drawn', function(event) {
                const tile = event.tile;
                if (!tile || !tile.element) return;
                
                // 强制像素对齐，避免亚像素偏移
                const element = tile.element;
                const style = element.style;
                
                // 解析当前transform
                const transform = style.transform;
                if (transform && transform.includes('translate')) {
                    const match = transform.match(/translate\(([^,]+),\s*([^)]+)\)/);
                    if (match) {
                        const x = Math.round(parseFloat(match[1]));
                        const y = Math.round(parseFloat(match[2]));
                        
                        // 4像素对齐，确保边界稳定
                        const alignedX = Math.round(x / 4) * 4;
                        const alignedY = Math.round(y / 4) * 4;
                        
                        style.transform = `translate(${alignedX}px, ${alignedY}px)`;
                        style.imageRendering = 'crisp-edges'; // 避免模糊
                    }
                }
                
                // 确保瓦片完全不透明且无过渡
                style.opacity = '1';
                style.transition = 'none';
                style.filter = 'none';
            });
            
            addLog('SDPC瓦片位置锁定已启用', 'success');
        }
        
        // 新增：瓦片事件报告功能（用于测试监控）
        function setupTileEventReporting(viewer) {
            if (!viewer) return;
            
            console.log('设置瓦片事件报告...');
            
            // 监听瓦片加载完成事件
            viewer.addHandler('tile-loaded', function(event) {
                const tile = event.tile;
                if (!tile) return;
                
                // 解析瓦片信息
                const match = tile.url?.match(/_files\/(\d+)\/(\d+)_(\d+)\.jpg/) || [];
                const level = match[1] ? parseInt(match[1]) : 0;
                const x = match[2] ? parseInt(match[2]) : 0;
                const y = match[3] ? parseInt(match[3]) : 0;
                
                // 计算加载时间
                const loadTime = tile.loadingTime || 0;
                
                // 向父窗口发送事件
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'tile-loaded',
                        level: level,
                        x: x,
                        y: y,
                        loadTime: loadTime,
                        size: tile.size || 0,
                        url: tile.url || ''
                    }, '*');
                }
                
                console.debug(`瓦片加载完成: Level ${level}, (${x}, ${y}), 耗时: ${loadTime}ms`);
            });
            
            // 监听缩放事件
            viewer.addHandler('zoom', function(event) {
                const zoom = viewer.viewport.getZoom();
                
                // 向父窗口发送缩放事件
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'zoom-changed',
                        eventType: 'zoom',
                        zoom: zoom
                    }, '*');
                }
            });
            
            // 监听拖拽事件
            viewer.addHandler('pan', function(event) {
                const center = viewer.viewport.getCenter();
                
                // 向父窗口发送拖拽事件
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'zoom-changed',
                        eventType: 'pan',
                        zoom: viewer.viewport.getZoom(),
                        center: center
                    }, '*');
                }
            });
            
            // 监听错误事件
            viewer.addHandler('tile-load-failed', function(event) {
                // 向父窗口发送错误事件
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'error',
                        error: 'tile-load-failed',
                        details: event.message || '瓦片加载失败'
                    }, '*');
                }
            });
            
            // 监听查看器就绪事件
            viewer.addHandler('open', function(event) {
                // 向父窗口发送就绪事件
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'viewer-ready'
                    }, '*');
                }
            });
            
            console.log('瓦片事件报告已设置');
        }
        
        // 监听来自测试页面的消息
        window.addEventListener('message', function(event) {
            if (event.origin !== window.location.origin) return;
            
            const data = event.data;
            
            switch(data.type) {
                case 'setup-tile-monitoring':
                    console.log('收到瓦片监控设置请求');
                    // 瓦片监控已在viewer初始化时自动设置
                    break;
                case 'start-zoom-test':
                    performZoomTest();
                    break;
                case 'start-pan-test':
                    performPanTest();
                    break;
            }
        });
        
        // 缩放测试序列
        function performZoomTest() {
            if (!viewer) return;
            
            console.log('开始缩放测试序列...');
            
            const originalZoom = viewer.viewport.getZoom();
            const originalCenter = viewer.viewport.getCenter();
            
            // 执行一系列缩放操作
            setTimeout(() => {
                viewer.viewport.zoomTo(originalZoom * 2, originalCenter, false);
            }, 500);
            
            setTimeout(() => {
                viewer.viewport.zoomTo(originalZoom * 4, originalCenter, false);
            }, 1500);
            
            setTimeout(() => {
                viewer.viewport.zoomTo(originalZoom * 0.5, originalCenter, false);
            }, 2500);
            
            setTimeout(() => {
                viewer.viewport.zoomTo(originalZoom, originalCenter, false);
                
                // 发送测试完成消息
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'test-completed'
                    }, '*');
                }
            }, 3500);
        }
        
        // 拖拽优化机制
        function setupDragOptimization(viewer, isSDPCFile) {
            if (!viewer || typeof viewer.addHandler !== 'function') {
                console.error('setupDragOptimization: viewer对象无效');
                return;
            }
            
            console.log('拖拽优化机制初始化开始...');
            
            let isDragging = false;
            let dragStartTime = 0;
            let dragEndTimeout = null;
            let preloadBackup = true;
            
            // 备份原始预加载设置
            const originalSettings = {
                preloadNearest: viewer.preloadNearest || true
            };
            
            // 拖拽开始处理
            viewer.addHandler('pan', function(event) {
                if (!isDragging) {
                    isDragging = true;
                    dragStartTime = Date.now();
                    
                    // 拖拽时临时提升性能 - 启用更积极的预加载
                    if (isSDPCFile) {
                        console.log('SDPC拖拽开始：启用积极预加载模式');
                        // 临时启用更强的预加载
                        if (viewer.drawer && viewer.drawer.context) {
                            viewer.drawer.context.imageSmoothingEnabled = false; // 禁用图像平滑提升性能
                        }
                    }
                }
                
                // 重置拖拽结束计时器
                if (dragEndTimeout) {
                    clearTimeout(dragEndTimeout);
                }
                
                // 拖拽结束后恢复设置
                dragEndTimeout = setTimeout(() => {
                    if (isDragging) {
                        isDragging = false;
                        const dragDuration = Date.now() - dragStartTime;
                        console.log(`SDPC拖拽结束：持续时间 ${dragDuration}ms，恢复正常设置`);
                        
                        // 恢复原始设置
                        if (isSDPCFile && viewer.drawer && viewer.drawer.context) {
                            viewer.drawer.context.imageSmoothingEnabled = true;
                        }
                    }
                }, 100); // 拖拽停止100ms后认为结束
            });
            
            // 监听viewport变化，在拖拽时优化瓦片请求
            viewer.addHandler('viewport-change', function(event) {
                if (isDragging && isSDPCFile) {
                    // 拖拽时立即刷新可见瓦片
                    const center = viewer.viewport.getCenter();
                    console.log(`拖拽优化：当前中心 (${center.x.toFixed(3)}, ${center.y.toFixed(3)})`);
                    
                    // 强制立即更新瓦片
                    if (viewer.world && viewer.world.getItemCount() > 0) {
                        const tiledImage = viewer.world.getItemAt(0);
                        if (tiledImage) {
                            tiledImage.update();
                        }
                    }
                }
            });
            
            // 优化瓦片加载优先级
            viewer.addHandler('tile-load-failed', function(event) {
                if (isDragging && isSDPCFile) {
                    console.log('拖拽中瓦片加载失败，尝试重新加载');
                    // 在拖拽时快速重试失败的瓦片
                    setTimeout(() => {
                        if (event.tile && typeof event.tile.load === 'function') {
                            event.tile.load();
                        }
                    }, 50);
                }
            });
            
            console.log('拖拽优化机制初始化完成');
        }
        
        // 拖拽测试序列
        function performPanTest() {
            if (!viewer) return;
            
            console.log('开始拖拽测试序列...');
            
            const originalCenter = viewer.viewport.getCenter();
            
            // 执行一系列拖拽操作
            setTimeout(() => {
                viewer.viewport.panTo(new OpenSeadragon.Point(originalCenter.x + 0.2, originalCenter.y), false);
            }, 500);
            
            setTimeout(() => {
                viewer.viewport.panTo(new OpenSeadragon.Point(originalCenter.x, originalCenter.y + 0.2), false);
            }, 1500);
            
            setTimeout(() => {
                viewer.viewport.panTo(new OpenSeadragon.Point(originalCenter.x - 0.2, originalCenter.y), false);
            }, 2500);
            
            setTimeout(() => {
                viewer.viewport.panTo(originalCenter, false);
                
                // 发送测试完成消息
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'test-completed'
                    }, '*');
                }
            }, 3500);
        }
</script>
</body>
</html>
