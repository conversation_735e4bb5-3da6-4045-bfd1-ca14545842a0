# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /slice

# Spring相关配置
spring:
  application:
    name: ccaa-slice

  # 允许Bean覆盖（解决Bean名称冲突问题）
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

  # 数据库配置
  datasource:
    url: **********************************************************************************************************************************
    username: root
    password: 3cu8rt00@A
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB

  # Redis配置
  data:
    redis:
      host: host.docker.internal
      port: 6379
      password: 123456
      database: 0
      timeout: 5000ms
      connect-timeout: 5000ms
      client-name: ccaa-slice

      # 连接池配置
      lettuce:
        pool:
          max-active: 16
          max-idle: 8
          min-idle: 2
          max-wait: 3000ms
          time-between-eviction-runs: 10000ms

# Mybatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: cn.ccaa.slice.core.model
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: INPUT
      logic-delete-field: valid_flag
      logic-delete-value: 0
      logic-not-delete-value: 1

# 存储服务配置
storage:
  type: local  # 配置存储类型为本地存储
  # 本地切片文件保存路径
  local-slice-dir: /mitr/server/ccaa-slice/local_slices  # 本地切片文件保存根目录
  # 临时文件配置
  temp-dir: /mitr/server/ccaa-slice/temp  # 临时文件目录
  temp-file-ttl-hours: 24  # 临时文件存活时间（小时）
  # 本地存储根目录配置
  local:
    root-dir: /mitr/server/ccaa-slice/storage  # 本地存储根目录

# 切片相关配置
slide:
  # OpenSlide库配置
  openslide:
    library-path: /usr/local/lib64/libopenslide.so

  # SqraySlide库配置
  sqrayslide:
    enabled: true                    # 如果库不可用，可以设置为false禁用SqraySlide解析器
    library-path: /usr/local/sqrayslide/lib/libsqrayslideservice.so
    jpeg-quality: 95                 # 提高JPEG质量，改善SDPC文件显示效果
    # 背景处理选项（解决SDPC文件黑色细线问题）
    use-transparent-background: false  # 是否使用透明背景
    background-color: "255,255,255"    # 背景色：白色（RGB格式），避免黑色细线问题
    auto-trim-white-border: true       # 自动裁剪边框（包括黑色细线）
    # SDPC文件性能优化配置 - 优化：提高流畅性
    cache-enabled: true                # 启用SDPC文件缓存优化
    preload-enabled: true              # 优化：启用预加载，提高缩放丝滑度
    max-cache-size: 25000             # 优化：增加最大缓存数量，提高流畅性
    supported-formats:
      - ".sdpc"

# 日志配置
logging:
  level:
    root: info
    '[cn.ccaa.slice]': info
    '[cn.ccaa.slice.service.dzi]': info                     # 修改：降低日志级别减少硬盘写入
    '[cn.ccaa.slice.web.controller.DziController]': info    # 修改：降低日志级别减少硬盘写入
    '[cn.ccaa.slice.service.UnifiedSlideService]': warn     # 修改：只记录警告和错误
    '[cn.ccaa.slice.service.analysis.impl.SliceAnalysisServiceImpl]': info
    '[cn.ccaa.slice.parsers.openslide]': info               # 修改：降低日志级别
    '[cn.ccaa.slice.parsers.sqrayslide]': info
    '[org.springframework.cache]': warn                     # 修改：只记录缓存警告
    '[org.springframework.data.redis]': warn                # 修改：只记录Redis警告
  file:
    name: logs/ccaa-slice.log
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'

callback:
  basic-info-path: BasicInfo
  info-path: Info
