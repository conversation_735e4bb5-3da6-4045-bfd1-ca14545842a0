<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分片上传测试 V3 - 三种场景</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .scenarios {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }
        .scenario {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        .scenario.local { border-color: #4CAF50; }
        .scenario.ant { border-color: #2196F3; }
        .scenario.pool { border-color: #FF9800; }
        
        .scenario h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid;
            padding-bottom: 10px;
        }
        .scenario.local h3 { border-color: #4CAF50; }
        .scenario.ant h3 { border-color: #2196F3; }
        .scenario.pool h3 { border-color: #FF9800; }
        
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        input[type="file"] {
            padding: 3px;
        }
        textarea {
            height: 100px;
            resize: vertical;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .progress-container {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.info { color: #007bff; }
        .log-entry.success { color: #28a745; }
        .log-entry.error { color: #dc3545; }
        .log-entry.warning { color: #ffc107; }
        
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
        }
        .status.uploading { background: #fff3cd; color: #856404; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        
        .params-display {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        /* 批量查询区域样式 */
        .batch-query-section {
            margin: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px solid #6f42c1;
        }
        
        .batch-query-section h3 {
            margin-top: 0;
            color: #6f42c1;
            border-bottom: 2px solid #6f42c1;
            padding-bottom: 10px;
        }
        
        .batch-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .batch-input-area {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        
        .batch-result-area {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        
        .batch-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .progress-item {
            margin: 5px 0;
            padding: 8px;
            background: #e9ecef;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        
        .progress-item.completed {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .progress-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        /* 新增字段样式 */
        .new-fields-section {
            background: #f0f8ff;
            border: 2px dashed #007bff;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .new-fields-section label {
            color: #0056b3;
            font-weight: bold;
            position: relative;
        }
        
        .new-fields-section label::after {
            content: "🆕";
            position: absolute;
            right: 0;
            font-size: 12px;
        }
        
        .new-fields-section input {
            border: 2px solid #007bff;
            background: #ffffff;
        }
        
        .new-fields-section input:focus {
            border-color: #0056b3;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        @media (max-width: 768px) {
            .scenarios {
                grid-template-columns: 1fr;
            }
            .batch-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>分片上传测试 V3</h1>
            <p>支持三种上传场景：本地上传、蚂蚁上传、切片池上传 + 批量查询进度 + 重新推送 + 切片删除</p>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; margin-top: 15px; text-align: left;">
                <h4 style="margin-top: 0; color: #ffd700;">🆕 新增字段测试功能</h4>
                <p style="margin: 5px 0; font-size: 14px;">
                    • <strong>orgId</strong> - 机构ID：用于标识上传文件所属的机构<br>
                    • <strong>ahId</strong> - 院区ID：用于标识上传文件所属的院区<br>
                    • <strong>projId</strong> - 项目ID：用于标识上传文件所属的项目<br>
                    • <strong>caseId</strong> - 病例ID：用于标识上传文件所属的病例，仅在调用第三方Info接口时传递<br>
                    • <strong>identifier</strong> - 标识符：🔄 <strong>支持同文件多次上传的关键字段</strong><br>
                    • <strong>sectionId</strong> - 章节ID：🆕 <strong>仅传递给BasicInfo接口，不存储到数据库</strong>
                </p>
                <h4 style="margin: 15px 0 5px 0; color: #ff6b6b;">🔒 sliceId唯一性校验</h4>
                <p style="margin: 5px 0; font-size: 14px;">
                    • <strong>蚂蚁上传</strong>：每个分片都会校验sliceId唯一性<br>
                    • <strong>第1个分片</strong>：严格检查数据库和Redis，立即拒绝重复<br>
                    • <strong>第2-n个分片</strong>：检查Redis任务存在性，防止无效上传<br>
                    • 重复的sliceId会在第1个分片就被拒绝，避免浪费网络带宽<br>
                    • 支持"测试重复sliceId"按钮进行功能验证
                </p>
                <h4 style="margin: 15px 0 5px 0; color: #ff6b6b;">🔄 重新推送功能</h4>
                <p style="margin: 5px 0; font-size: 14px;">
                    • <strong>接口地址</strong>：POST /api/upload/repush/{id}<br>
                    • <strong>推送流程</strong>：查询切片 → 检查图片 → 重新解析(如需要) → 推送到三方<br>
                    • <strong>智能检查</strong>：自动检查缩略图、标签图、玻片图是否存在<br>
                    • <strong>自动处理</strong>：图片不存在时会重新解析切片文件生成图片<br>
                    • <strong>📤 三方推送</strong>：复用现有推送逻辑调用三方Info接口
                </p>
                <h4 style="margin: 15px 0 5px 0; color: #dc3545;">🗑️ 切片删除功能</h4>
                <p style="margin: 5px 0; font-size: 14px;">
                    • <strong>接口地址</strong>：DELETE /api/upload/delete/{id}?cname=xxx<br>
                    • <strong>删除流程</strong>：权限验证 → 文件删除 → 缓存清理 → 数据库删除<br>
                    • <strong>权限验证</strong>：通过切片ID和合作伙伴代码进行联合查询验证<br>
                    • <strong>文件删除</strong>：删除主切片文件、缩略图、标签图、玻片图<br>
                    • <strong>⚠️ 不可逆操作</strong>：删除后无法恢复，请谨慎使用
                </p>
                <p style="margin: 5px 0; font-size: 13px; color: #e6e6e6;">
                    ℹ️ 这些字段都是可选的，会存储到数据库t_c_slice表对应字段中。测试页面已为不同场景预设了不同的默认值。<br>
                    🔄 <strong>identifier字段</strong>：支持同文件多次上传，留空时会自动生成唯一标识符。
                </p>
            </div>
        </div>
        
        <div class="scenarios">
            <!-- 本地上传场景 -->
            <div class="scenario local">
                <h3>🏠 本地上传</h3>
                <p><strong>特点：</strong>传入taskId，使用主键id作为穿线标识，不使用Redis</p>
                
                <div class="form-group">
                    <label for="local-taskId">任务ID (taskId):</label>
                    <input type="text" id="local-taskId" value="01JW7RXWDXT2G8ZJFF" placeholder="输入任务ID">
                </div>
                
                <div class="form-group">
                    <label for="local-file">选择文件:</label>
                    <input type="file" id="local-file">
                </div>
                
                <div class="form-group">
                    <label for="local-chunkSize">分片大小 (MB):</label>
                    <select id="local-chunkSize">
                        <option value="1">1 MB</option>
                        <option value="2">2 MB</option>
                        <option value="5">5 MB</option>
                        <option value="100">100 MB</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="local-partnerCode">合作伙伴代码:</label>
                    <input type="text" id="local-partnerCode" value="qpDhc" placeholder="输入合作伙伴代码">
                </div>
                
                <div class="form-group">
                    <label for="local-auth">Authorization (可选):</label>
                    <input type="text" id="local-auth" value="" placeholder="Bearer token或其他认证信息">
                </div>
                
                <div class="form-group">
                    <label for="local-caseId">病例ID (caseId):</label>
                    <input type="text" id="local-caseId" value="case001" placeholder="输入病例ID">
                </div>
                
                <!-- 新增字段测试区域 -->
                <div class="new-fields-section">
                    <h5 style="margin-top: 0; color: #0056b3;">🆕 新增字段测试</h5>
                    <div class="form-group">
                        <label for="local-orgId">机构ID (orgId):</label>
                        <input type="text" id="local-orgId" value="org001" placeholder="输入机构ID">
                    </div>
                    
                    <div class="form-group">
                        <label for="local-ahId">院区ID (ahId):</label>
                        <input type="text" id="local-ahId" value="ah001" placeholder="输入院区ID">
                    </div>
                    
                    <div class="form-group">
                        <label for="local-projId">项目ID (projId):</label>
                        <input type="text" id="local-projId" value="proj001" placeholder="输入项目ID">
                    </div>
                    
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="local-identifier">标识符 (identifier) 🔄:</label>
                        <input type="text" id="local-identifier" value="" placeholder="用于区分同文件多次上传，留空自动生成">
                        <small style="color: #666; font-size: 12px;">💡 支持同文件多次上传的关键字段</small>
                    </div>
                    
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="local-sectionId">章节ID (sectionId) 🆕:</label>
                        <input type="text" id="local-sectionId" value="section001" placeholder="输入章节ID">
                        <small style="color: #666; font-size: 12px;">🆕 新增字段：用于传递给BasicInfo接口，不存储到数据库</small>
                    </div>
                </div>
                
                <button onclick="startLocalUpload()">开始本地上传</button>
                <button onclick="clearLocalLog()">清空日志</button>
                <button onclick="resetLocalNewFields()" style="background: #6c757d;">重置新字段</button>
                
                <div class="params-display" id="local-params"></div>
                
                <div class="progress-container" id="local-progress" style="display: none;">
                    <div>上传进度: <span id="local-progress-text">0%</span></div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="local-progress-bar" style="width: 0%"></div>
                    </div>
                    <div>分片: <span id="local-chunk-info">0/0</span></div>
                </div>
                
                <div class="status" id="local-status" style="display: none;"></div>
                <div class="log" id="local-log"></div>
            </div>
            
            <!-- 蚂蚁上传场景 -->
            <div class="scenario ant">
                <h3>🐜 蚂蚁上传</h3>
                <p><strong>特点：</strong>传入sliceId，使用Redis进度管理，支持进度查询</p>
                
                <div class="form-group">
                    <label for="ant-sliceId">切片ID (sliceId):</label>
                    <input type="text" id="ant-sliceId" value="slice_" placeholder="输入切片ID">
                </div>
                
                <div class="form-group">
                    <label for="ant-file">选择文件:</label>
                    <input type="file" id="ant-file">
                </div>
                
                <div class="form-group">
                    <label for="ant-chunkSize">分片大小 (MB):</label>
                    <select id="ant-chunkSize">
                        <option value="1">1 MB</option>
                        <option value="2">2 MB</option>
                        <option value="5">5 MB</option>
                        <option value="10">10 MB</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="ant-partnerCode">合作伙伴代码:</label>
                    <input type="text" id="ant-partnerCode" value="qpDhc" placeholder="输入合作伙伴代码">
                </div>
                
                <div class="form-group">
                    <label for="ant-auth">Authorization:</label>
                    <input type="text" id="ant-auth" value="Bearer token123" placeholder="输入认证信息">
                </div>
                
                <div class="form-group">
                    <label for="ant-caseId">病例ID (caseId):</label>
                    <input type="text" id="ant-caseId" value="case002" placeholder="输入病例ID">
                </div>
                
                <!-- 新增字段测试区域 -->
                <div class="new-fields-section">
                    <h5 style="margin-top: 0; color: #0056b3;">🆕 新增字段测试</h5>
                    <div class="form-group">
                        <label for="ant-orgId">机构ID (orgId):</label>
                        <input type="text" id="ant-orgId" value="org002" placeholder="输入机构ID">
                    </div>
                    
                    <div class="form-group">
                        <label for="ant-ahId">院区ID (ahId):</label>
                        <input type="text" id="ant-ahId" value="ah002" placeholder="输入院区ID">
                    </div>
                    
                    <div class="form-group">
                        <label for="ant-projId">项目ID (projId):</label>
                        <input type="text" id="ant-projId" value="proj002" placeholder="输入项目ID">
                    </div>
                    
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="ant-identifier">标识符 (identifier) 🔄:</label>
                        <input type="text" id="ant-identifier" value="" placeholder="用于区分同文件多次上传，留空自动生成">
                        <small style="color: #666; font-size: 12px;">💡 支持同文件多次上传的关键字段</small>
                    </div>
                    
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="ant-sectionId">章节ID (sectionId) 🆕:</label>
                        <input type="text" id="ant-sectionId" value="section002" placeholder="输入章节ID">
                        <small style="color: #666; font-size: 12px;">🆕 新增字段：用于传递给BasicInfo接口，不存储到数据库</small>
                    </div>
                </div>
                
                <button onclick="startAntUpload()">开始蚂蚁上传</button>
                <button onclick="queryAntProgress()">查询进度</button>
                <button onclick="clearAntLog()">清空日志</button>
                <button onclick="addToAntBatch()">添加到批量查询</button>
                <button onclick="resetAntNewFields()" style="background: #6c757d;">重置新字段</button>
                <button onclick="testSliceIdUniqueness()" style="background: #dc3545;">测试重复sliceId</button>
                
                <div class="params-display" id="ant-params"></div>
                
                <div class="progress-container" id="ant-progress" style="display: none;">
                    <div>上传进度: <span id="ant-progress-text">0%</span></div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="ant-progress-bar" style="width: 0%"></div>
                    </div>
                    <div>分片: <span id="ant-chunk-info">0/0</span></div>
                </div>
                
                <div class="status" id="ant-status" style="display: none;"></div>
                <div class="log" id="ant-log"></div>
            </div>
            
            <!-- 切片池上传场景 -->
            <div class="scenario pool">
                <h3>🏊 切片池上传</h3>
                <p><strong>特点：</strong>不传taskId和sliceId，使用主键id作为穿线标识，不使用Redis</p>
                
                <div class="form-group">
                    <label for="pool-file">选择文件:</label>
                    <input type="file" id="pool-file">
                </div>
                
                <div class="form-group">
                    <label for="pool-chunkSize">分片大小 (MB):</label>
                    <select id="pool-chunkSize">
                        <option value="1">1 MB</option>
                        <option value="2">2 MB</option>
                        <option value="5">5 MB</option>
                        <option value="10">10 MB</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="pool-partnerCode">合作伙伴代码:</label>
                    <input type="text" id="pool-partnerCode" value="qpDhc" placeholder="输入合作伙伴代码">
                </div>
                
                <div class="form-group">
                    <label for="pool-auth">Authorization (可选):</label>
                    <input type="text" id="pool-auth" value="" placeholder="Bearer token或其他认证信息">
                </div>
                
                <div class="form-group">
                    <label for="pool-caseId">病例ID (caseId):</label>
                    <input type="text" id="pool-caseId" value="case003" placeholder="输入病例ID">
                </div>
                
                <!-- 新增字段测试区域 -->
                <div class="new-fields-section">
                    <h5 style="margin-top: 0; color: #0056b3;">🆕 新增字段测试</h5>
                    <div class="form-group">
                        <label for="pool-orgId">机构ID (orgId):</label>
                        <input type="text" id="pool-orgId" value="org003" placeholder="输入机构ID">
                    </div>
                    
                    <div class="form-group">
                        <label for="pool-ahId">院区ID (ahId):</label>
                        <input type="text" id="pool-ahId" value="ah003" placeholder="输入院区ID">
                    </div>
                    
                    <div class="form-group">
                        <label for="pool-projId">项目ID (projId):</label>
                        <input type="text" id="pool-projId" value="proj003" placeholder="输入项目ID">
                    </div>
                    
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="pool-identifier">标识符 (identifier) 🔄:</label>
                        <input type="text" id="pool-identifier" value="" placeholder="用于区分同文件多次上传，留空自动生成">
                        <small style="color: #666; font-size: 12px;">💡 支持同文件多次上传的关键字段</small>
                    </div>
                    
                    <div class="form-group" style="margin-bottom: 0;">
                        <label for="pool-sectionId">章节ID (sectionId) 🆕:</label>
                        <input type="text" id="pool-sectionId" value="section003" placeholder="输入章节ID">
                        <small style="color: #666; font-size: 12px;">🆕 新增字段：用于传递给BasicInfo接口，不存储到数据库</small>
                    </div>
                </div>
                
                <button onclick="startPoolUpload()">开始切片池上传</button>
                <button onclick="clearPoolLog()">清空日志</button>
                <button onclick="resetPoolNewFields()" style="background: #6c757d;">重置新字段</button>
                
                <div class="params-display" id="pool-params"></div>
                
                <div class="progress-container" id="pool-progress" style="display: none;">
                    <div>上传进度: <span id="pool-progress-text">0%</span></div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="pool-progress-bar" style="width: 0%"></div>
                    </div>
                    <div>分片: <span id="pool-chunk-info">0/0</span></div>
                </div>
                
                <div class="status" id="pool-status" style="display: none;"></div>
                <div class="log" id="pool-log"></div>
            </div>
        </div>
        
        <!-- AI批量截图测试区域 -->
        <div class="batch-query-section" style="border-color: #e91e63;">
            <h3 style="color: #e91e63; border-color: #e91e63;">🤖 AI批量截图功能测试</h3>
            <p><strong>功能：</strong>根据AI算法分析得到的异常图像坐标，批量生成截图并返回base64编码的图像数据</p>
            <p><strong>使用场景：</strong>AI算法检测到病理切片中的异常区域后，需要生成这些区域的截图用于进一步分析</p>
            <p><strong>坐标格式：</strong>📍 传入左上角坐标(x,y)，直接进行截图</p>
            
            <div class="batch-grid">
                <div class="batch-input-area">
                    <h4>AI批量截图参数设置</h4>
                    
                    <div class="form-group">
                        <label for="ai-capture-url">API地址:</label>
                        <input type="text" id="ai-capture-url" value="http://127.0.0.1:8089/slice/api/slice/SaveAIQpCaptureBatch" placeholder="请输入API地址">
                    </div>
                    
                    <div class="form-group">
                        <label for="ai-capture-requests">请求参数 (JSON数组格式):</label>
                        <textarea id="ai-capture-requests" style="height: 200px; font-family: 'Courier New', monospace;" placeholder="请输入JSON格式的请求参数（x,y为左上角坐标）">[
  {
    "fileId": "01JYP476YF6GGSPQ1KKTHXEFXE",
    "cutimgid": "ai_capture_001",
    "x": 1000,
    "y": 1000,
    "w": 200,
    "h": 200
  },
  {
    "fileId": "01JYP476YF6GGSPQ1KKTHXEFXE",
    "cutimgid": "ai_capture_002",
    "x": 1500,
    "y": 1200,
    "w": 150,
    "h": 150
  },
  {
    "fileId": "01JYP476YF6GGSPQ1KKTHXEFXE",
    "cutimgid": "ai_capture_003",
    "x": 2000,
    "y": 800,
    "w": 180,
    "h": 180
  }
]</textarea>
                    </div>
                    
                    <button onclick="sendAIBatchCaptureRequest()" style="background: #e91e63;">🚀 发送AI批量截图请求</button>
                    <button onclick="fillAISampleData()" style="background: #6c757d;">📋 填充示例数据</button>
                    <button onclick="clearAICaptureResults()">🗑️ 清空结果</button>
                    
                    <div class="params-display" id="ai-capture-params"></div>
                    
                    <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                        <h5 style="margin-top: 0;">📋 请求格式说明：</h5>
                        <div style="font-family: 'Courier New', monospace;">
                            入参字段说明：<br>
                            - fileId: 文件ID（关联 si_slice_file.file_id）<br>
                            - cutimgid: 截图唯一标识（用于保存文件名）<br>
                            - x: 截图区域左上角 X 坐标 📍<br>
                            - y: 截图区域左上角 Y 坐标 📍<br>
                            - w: 截图区域宽度<br>
                            - h: 截图区域高度<br>
                            📌 坐标说明: 直接使用传入的左上角坐标进行截图<br><br>
                            出参格式：<br>
                            {<br>
                            &nbsp;&nbsp;"status": "success",<br>
                            &nbsp;&nbsp;"data": [{"cutimgid": "xxx", "image": "base64String...", "msg": "成功"}],<br>
                            &nbsp;&nbsp;"msg": "批量截图完成，成功: 3, 失败: 0"<br>
                            }
                        </div>
                    </div>
                </div>
                
                <div class="batch-result-area">
                    <h4>AI批量截图结果</h4>
                    <div class="batch-result" id="ai-capture-result">
                        <div class="log-entry info">等待发送请求...</div>
                    </div>
                    
                    <!-- AI图片预览区域 -->
                    <div id="ai-capture-images" style="margin-top: 15px;">
                        <h5>🖼️ AI截图预览:</h5>
                        <div id="ai-images-container" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 10px;">
                            <!-- 生成的图像将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 切片截图测试区域 -->
        <div class="batch-query-section" style="border-color: #28a745;">
            <h3 style="color: #28a745; border-color: #28a745;">📸 切片截图功能测试</h3>
            <p><strong>功能：</strong>根据坐标参数生成切片截图，返回原图和缩略图文件</p>
                            <p><strong>截图模式：</strong>📸JSON格式响应（JSON+Base64编码的图片数据）</p>
            
            <div class="batch-grid">
                <div class="batch-input-area">
                    <h4>截图参数设置</h4>
                    
                    <div class="form-group">
                        <label for="capture-fileId">切片文件ID (fileId):</label>
                        <input type="text" id="capture-fileId" value="" placeholder="输入切片文件ID">
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <div class="form-group">
                            <label for="capture-zoom">缩放倍率 (zoom):</label>
                            <input type="number" id="capture-zoom" value="1.574901312368582" step="0.1" placeholder="1.574901312368582">
                        </div>
                        
                        <div class="form-group">
                            <label for="capture-csize">屏幕像素比 (csize):</label>
                            <input type="number" id="capture-csize" value="3174.0561049476405" step="0.1" placeholder="3174.0561049476405">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="capture-level">层级 (level):</label>
                        <select id="capture-level">
                            <option value="0">0 - 最高分辨率</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10</option>
                            <option value="11">11</option>
                            <option value="12">12</option>
                            <option value="13" selected>13</option>
                            <option value="14">14</option>
                            <option value="15">15</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="capture-content">描述 (content) <span style="color: red;">*</span>:</label>
                        <input type="text" id="capture-content" value="病理切片截图 - 细胞区域采样" placeholder="输入截图描述内容（必填）">
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <div class="form-group">
                            <label for="capture-w">宽度 (w):</label>
                            <input type="number" id="capture-w" value="160" placeholder="160">
                        </div>
                        
                        <div class="form-group">
                            <label for="capture-h">高度 (h):</label>
                            <input type="number" id="capture-h" value="120" placeholder="120">
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <div class="form-group">
                            <label for="capture-x">X坐标 (x):</label>
                            <input type="number" id="capture-x" value="1664.056104947638" step="0.1" placeholder="1664.056104947638">
                        </div>
                        
                        <div class="form-group">
                            <label for="capture-y">Y坐标 (y):</label>
                            <input type="number" id="capture-y" value="1351.6322225945228" step="0.1" placeholder="1351.6322225945228">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="capture-rotate">旋转角度 (rotate):</label>
                        <input type="number" id="capture-rotate" value="0" step="0.1" placeholder="0">
                    </div>
                    
                    <div class="form-group">
                        <label for="capture-coords">四点坐标 (coords) - JSON格式:</label>
                        <textarea id="capture-coords" style="height: 80px; font-size: 11px;">[
  {"x": 1664.056104947638, "y": 1351.6322225945228},
  {"x": 1824.056104947638, "y": 1351.6322225945228},
  {"x": 1824.056104947638, "y": 1471.6322225945228},
  {"x": 1664.056104947638, "y": 1471.6322225945228}
]</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="capture-caseId">病例ID (caseId):</label>
                        <input type="text" id="capture-caseId" value="" placeholder="输入病例ID（可为空）">
                    </div>
                    
                    <div class="form-group">
                        <label for="capture-function">功能选项 (functionOption):</label>
                        <select id="capture-function">
                            <option value="0">0 - 截图功能</option>
                            <option value="1">1 - 细胞学采图功能</option>
                        </select>
                    </div>
                    
                                    <button onclick="startCaptureJson()" style="background: #007bff;">📸 开始截图</button>
                    <button onclick="fillSampleCaptureData()" style="background: #6c757d;">填充示例数据</button>
                    <button onclick="clearCaptureResult()">清空结果</button>
                    
                    <div class="params-display" id="capture-params"></div>
                </div>
                
                <div class="batch-result-area">
                    <h4>截图结果</h4>
                    <div class="batch-result" id="capture-result"></div>
                    
                    <!-- 图片预览区域 -->
                    <div id="capture-images" style="margin-top: 15px; display: none;">
                        <h5>图片预览:</h5>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div>
                                <strong>原图:</strong>
                                <div style="border: 1px solid #dee2e6; border-radius: 4px; padding: 5px; text-align: center; background: #f8f9fa;">
                                    <img id="capture-original" style="max-width: 100%; max-height: 200px;" />
                                </div>
                            </div>
                            <div>
                                <strong>缩略图:</strong>
                                <div style="border: 1px solid #dee2e6; border-radius: 4px; padding: 5px; text-align: center; background: #f8f9fa;">
                                    <img id="capture-thumbnail" style="max-width: 100%; max-height: 200px;" />
                                </div>
                            </div>
                        </div>
                        <div style="margin-top: 10px;">
                            <button onclick="downloadCaptureImages()" style="background: #007bff; font-size: 12px;">💾 下载图片</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 批量查询进度区域 -->
        <div class="batch-query-section">
            <h3>📊 批量查询上传进度</h3>
            <p><strong>功能：</strong>一次性查询多个切片ID的上传进度，支持最多50个切片ID</p>
            
            <div class="batch-grid">
                <div class="batch-input-area">
                    <h4>输入区域</h4>
                    <div class="form-group">
                        <label for="batch-sliceIds">切片ID列表 (每行一个，最多50个):</label>
                        <textarea id="batch-sliceIds" placeholder="请输入切片ID，每行一个，例如：&#10;slice_1234567890_1001&#10;slice_1234567890_1002&#10;slice_1234567890_1003"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="batch-auth">Authorization (可选):</label>
                        <input type="text" id="batch-auth" value="Bearer token123" placeholder="输入认证信息">
                    </div>
                    
                    <button onclick="batchQueryProgress()">批量查询进度</button>
                    <button onclick="clearBatchSliceIds()">清空列表</button>
                    <button onclick="clearBatchResult()">清空结果</button>
                    
                    <div style="margin-top: 15px;">
                        <h5>快速添加:</h5>
                        <button onclick="addCurrentAntSliceId()" style="font-size: 12px;">添加蚂蚁上传ID</button>
                        <button onclick="addSampleSliceIds()" style="font-size: 12px;">添加示例ID</button>
                    </div>
                    
                    <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                        <h5>🆕 新增字段管理:</h5>
                        <button onclick="resetAllNewFields()" style="font-size: 12px; background: #28a745;">重置所有字段</button>
                        <button onclick="clearAllNewFields()" style="font-size: 12px; background: #dc3545;">清空所有字段</button>
                    </div>
                </div>
                
                <div class="batch-result-area">
                    <h4>查询结果</h4>
                    <div class="batch-result" id="batch-result"></div>
                </div>
            </div>
        </div>
        
        <!-- 重新推送测试区域 -->
        <div class="batch-query-section" style="border-color: #ff6b6b;">
            <h3 style="color: #ff6b6b; border-color: #ff6b6b;">🔄 重新推送接口测试</h3>
            <p><strong>功能：</strong>根据切片ID重新调用三方Info接口进行推送，如果图片不存在会重新解析生成</p>
            <p><strong>接口地址：</strong>POST /api/upload/repush/{id}</p>
            
            <div class="batch-grid">
                <div class="batch-input-area">
                    <h4>重新推送参数设置</h4>
                    
                    <div class="form-group">
                        <label for="repush-id">切片ID (主键ID) <span style="color: red;">*</span>:</label>
                        <input type="text" id="repush-id" value="" placeholder="输入要重新推送的切片主键ID">
                        <small style="color: #666; font-size: 12px;">📌 这是数据库中的主键ID，不是sliceId</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="repush-auth">Authorization (可选):</label>
                        <input type="text" id="repush-auth" value="" placeholder="Bearer token...">
                        <small style="color: #666; font-size: 12px;">如果需要认证，请填入Authorization头</small>
                    </div>
                    
                    <div style="display: flex; gap: 8px; flex-wrap: wrap; margin-top: 15px;">
                        <button onclick="repushSliceAnalysis()" style="background: #ff6b6b; color: white; border: none; padding: 12px 24px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: bold;">🔄 重新推送</button>
                        <button onclick="fillRepushExample()" style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">📋 填充示例数据</button>
                        <button onclick="getRepushIdFromCapture()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">📸 从截图区获取ID</button>
                        <button onclick="testRepushWithReprocess()" style="background: #6f42c1; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">🧪 测试重新处理</button>
                        <button onclick="clearRepushResults()" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">🧹 清空结果</button>
                    </div>
                    
                    <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                        <h5 style="margin-top: 0;">📋 重新推送流程说明：</h5>
                        <div style="font-family: 'Courier New', monospace;">
                            1. 🔍 根据ID查询切片信息<br>
                            2. 🔐 检查partnerCode是否存在<br>
                            3. 📂 检查切片文件是否存在<br>
                            4. 🖼️ 检查图片文件(缩略图/标签图/玻片图)是否存在<br>
                            5. 🔄 如果图片不存在，重新解析切片文件生成图片<br>
                            6. 📤 调用三方推送逻辑进行Info接口推送<br><br>
                            📌 <strong>注意事项：</strong><br>
                            • ID必须是数据库中存在的主键ID<br>
                            • 切片记录必须有partnerCode才会进行推送<br>
                            • 如果需要重新处理，会调用切片解析服务<br>
                            • 推送成功后会返回详细的状态信息
                        </div>
                    </div>
                </div>
                
                <div class="batch-result-area">
                    <h4>重新推送结果</h4>
                    <div class="batch-result" id="repush-result">
                        <div class="log-entry info">等待重新推送操作...</div>
                    </div>
                    
                    <!-- 推送状态显示 -->
                    <div id="repush-status" style="margin-top: 15px; display: none;">
                        <h5>📊 推送状态:</h5>
                        <div id="repush-progress" style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: 'Courier New', monospace; font-size: 12px;">
                            <!-- 推送进度将在这里显示 -->
                        </div>
                    </div>
                    
                    <!-- 推送详情显示 -->
                    <div id="repush-details" style="margin-top: 15px; display: none;">
                        <h5>📋 推送详情:</h5>
                        <div id="repush-details-content" style="background: #e9ecef; padding: 10px; border-radius: 4px; font-family: 'Courier New', monospace; font-size: 11px; max-height: 200px; overflow-y: auto;">
                            <!-- 推送详情将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 切片删除测试区域 -->
        <div class="batch-query-section" style="border-color: #dc3545;">
            <h3 style="color: #dc3545; border-color: #dc3545;">🗑️ 切片文件删除功能测试</h3>
            <p><strong>功能：</strong>根据切片ID和合作伙伴代码删除切片文件及相关数据</p>
            <p><strong>删除流程：</strong>先删除文件 → 清理缓存 → 删除数据库记录</p>
            <p><strong>接口地址：</strong>DELETE /api/upload/delete/{id}?cname=xxx</p>
            
            <div class="batch-grid">
                <div class="batch-input-area">
                    <h4>删除参数设置</h4>
                    
                    <div class="form-group">
                        <label for="delete-id">切片ID (主键ID) <span style="color: red;">*</span>:</label>
                        <input type="text" id="delete-id" value="" placeholder="输入要删除的切片主键ID">
                        <small style="color: #666; font-size: 12px;">📌 这是数据库中的主键ID，不是sliceId</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="delete-cname">合作伙伴代码 (cname) <span style="color: red;">*</span>:</label>
                        <input type="text" id="delete-cname" value="qpDhc" placeholder="输入合作伙伴代码">
                        <small style="color: #666; font-size: 12px;">对应数据库的partner_code字段</small>
                    </div>

                    <div class="form-group">
                        <label for="delete-auth">Authorization (可选):</label>
                        <input type="text" id="delete-auth" value="" placeholder="Bearer token...">
                        <small style="color: #666; font-size: 12px;">如果需要认证，请填入Authorization头</small>
                    </div>
                    
                    <div style="display: flex; gap: 8px; flex-wrap: wrap; margin-top: 15px;">
                        <button onclick="deleteSlice()" style="background: #dc3545; color: white; border: none; padding: 12px 24px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: bold;">🗑️ 删除切片文件</button>
                        <button onclick="fillDeleteExample()" style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">📋 填充示例数据</button>
                        <button onclick="getRecentUploadId()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">🚀 获取最近上传ID</button>
                        <button onclick="testDeletePermission()" style="background: #6f42c1; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">🔍 测试权限验证</button>
                        <button onclick="clearDeleteResults()" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">🧹 清空结果</button>
                        <button onclick="forceCleanAllCache()" style="background: #e74c3c; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">🧹 强制清理缓存</button>
                    </div>
                </div>
                
                <div class="batch-result-area">
                    <h4>删除结果</h4>
                    <div class="batch-result" id="delete-result">
                        <div class="log-entry info">等待删除操作...</div>
                    </div>
                    
                    <!-- 删除状态显示 -->
                    <div id="delete-status" style="margin-top: 15px; display: none;">
                        <h5>📊 删除状态:</h5>
                        <div id="delete-progress" style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: 'Courier New', monospace; font-size: 12px;">
                            <!-- 删除进度将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 公共部分提取
        // const HOST = '**************';
        const HOST = '*********';
        const API_BASE = `http://${HOST}/slice/api/upload`;
        const SLICE_API_BASE = `http://${HOST}/slice/api/slice`;
        // 如需切换本地，修改HOST即可

        // 存储已上传的sliceId列表，用于批量查询
        let uploadedSliceIds = [];
        
        // 通用日志函数
        function addLog(containerId, message, type = 'info') {
            const logContainer = document.getElementById(containerId);
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 批量查询日志函数
        function addBatchLog(message, type = 'info') {
            const resultContainer = document.getElementById('batch-result');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultContainer.appendChild(logEntry);
            resultContainer.scrollTop = resultContainer.scrollHeight;
        }
        
        // 清空日志
        function clearLocalLog() { document.getElementById('local-log').innerHTML = ''; }
        function clearAntLog() { document.getElementById('ant-log').innerHTML = ''; }
        function clearPoolLog() { document.getElementById('pool-log').innerHTML = ''; }
        function clearBatchResult() { document.getElementById('batch-result').innerHTML = ''; }
        function clearBatchSliceIds() { document.getElementById('batch-sliceIds').value = ''; }
        
        // 更新进度
        function updateProgress(prefix, current, total, percentage) {
            document.getElementById(`${prefix}-progress`).style.display = 'block';
            document.getElementById(`${prefix}-progress-text`).textContent = `${percentage}%`;
            document.getElementById(`${prefix}-progress-bar`).style.width = `${percentage}%`;
            document.getElementById(`${prefix}-chunk-info`).textContent = `${current}/${total}`;
        }
        
        // 显示状态
        function showStatus(prefix, message, type) {
            const statusEl = document.getElementById(`${prefix}-status`);
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
        }
        
        // 显示参数
        function showParams(prefix, params) {
            const paramsEl = document.getElementById(`${prefix}-params`);
            paramsEl.textContent = JSON.stringify(params, null, 2);
        }
        
        // 生成随机sliceId
        function generateSliceId() {
            return 'slice_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }
        
        // 添加sliceId到批量查询列表
        function addSliceIdToBatchList(sliceId) {
            if (!sliceId || !sliceId.trim()) {
                return;
            }
            
            const textarea = document.getElementById('batch-sliceIds');
            const currentList = textarea.value.trim();
            const sliceIdList = currentList ? currentList.split('\n').map(id => id.trim()) : [];
            
            // 检查是否已存在，避免重复添加
            if (!sliceIdList.includes(sliceId)) {
                if (currentList) {
                    textarea.value = currentList + '\n' + sliceId;
                } else {
                    textarea.value = sliceId;
                }
                addBatchLog(`✅ 已自动添加切片ID: ${sliceId}`, 'success');
                
                // 记录到全局列表
                if (!uploadedSliceIds.includes(sliceId)) {
                    uploadedSliceIds.push(sliceId);
                }
            } else {
                addBatchLog(`ℹ️ 切片ID已存在: ${sliceId}`, 'info');
            }
        }
        
        // 本地上传
        async function startLocalUpload() {
            const taskId = document.getElementById('local-taskId').value;
            const file = document.getElementById('local-file').files[0];
            const chunkSizeMB = parseInt(document.getElementById('local-chunkSize').value);
            const partnerCode = document.getElementById('local-partnerCode').value;
            const auth = document.getElementById('local-auth').value;
            // 获取新增字段
            const orgId = document.getElementById('local-orgId').value;
            const ahId = document.getElementById('local-ahId').value;
            const projId = document.getElementById('local-projId').value;
            const caseId = document.getElementById('local-caseId').value;
            // 获取identifier字段
            let identifier = document.getElementById('local-identifier').value.trim();
            
            // 如果identifier为空，自动生成一个
            if (!identifier) {
                identifier = 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
                document.getElementById('local-identifier').value = identifier;
            }
            
            // 获取sectionId字段
            const sectionId = document.getElementById('local-sectionId').value;
            
            if (!taskId || !file || !partnerCode) {
                addLog('local-log', '请填写所有必填字段', 'error');
                return;
            }
            
            const params = {
                taskId: taskId,
                filename: file.name,
                totalSize: file.size,
                partnerCode: partnerCode,
                authorization: auth || '(未设置)',
                chunkSizeMB: chunkSizeMB,
                // 新增字段
                orgId: orgId || '(未设置)',
                ahId: ahId || '(未设置)',
                projId: projId || '(未设置)',
                caseId: caseId || '(未设置)',
                identifier: identifier,
                sectionId: sectionId || '(未设置)'
            };
            showParams('local', params);
            
            addLog('local-log', `开始本地上传: ${file.name} (${(file.size/1024/1024).toFixed(2)}MB)`, 'info');
            if (auth) {
                addLog('local-log', `使用Authorization: ${auth.substring(0, 20)}...`, 'info');
            }
            // 记录新增字段信息
            addLog('local-log', `新增字段 - 机构ID: ${orgId || '(空)'}, 院区ID: ${ahId || '(空)'}, 项目ID: ${projId || '(空)'}, 病例ID: ${caseId || '(空)'}`, 'info');
            addLog('local-log', `🔄 标识符: ${identifier} - 支持同文件多次上传`, 'info');
            addLog('local-log', `🆕 章节ID: ${sectionId || '(空)'} - 仅传递给BasicInfo接口`, 'info');
            showStatus('local', '上传中...', 'uploading');
            
            await uploadFile('local', file, chunkSizeMB * 1024 * 1024, {
                taskId: taskId,
                partnerCode: partnerCode,
                authorization: auth,
                orgId: orgId,
                ahId: ahId,
                projId: projId,
                caseId: caseId,
                identifier: identifier,
                sectionId: sectionId
            });
        }
        
        // 蚂蚁上传
        async function startAntUpload() {
            let sliceId = document.getElementById('ant-sliceId').value;
            const file = document.getElementById('ant-file').files[0];
            const chunkSizeMB = parseInt(document.getElementById('ant-chunkSize').value);
            const partnerCode = document.getElementById('ant-partnerCode').value;
            const auth = document.getElementById('ant-auth').value;
            // 获取新增字段
            const orgId = document.getElementById('ant-orgId').value;
            const ahId = document.getElementById('ant-ahId').value;
            const projId = document.getElementById('ant-projId').value;
            const caseId = document.getElementById('ant-caseId').value;
            // 获取identifier字段
            let identifier = document.getElementById('ant-identifier').value.trim();
            
            // 如果identifier为空，自动生成一个
            if (!identifier) {
                identifier = 'ant_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
                document.getElementById('ant-identifier').value = identifier;
            }
            
            // 获取sectionId字段
            const sectionId = document.getElementById('ant-sectionId').value;
            
            if (!file || !partnerCode) {
                addLog('ant-log', '请填写所有必填字段', 'error');
                return;
            }
            
            // 如果sliceId为空或只是默认值，生成一个新的
            if (!sliceId || sliceId === 'slice_') {
                sliceId = generateSliceId();
                document.getElementById('ant-sliceId').value = sliceId;
            }
            
            const params = {
                sliceId: sliceId,
                filename: file.name,
                totalSize: file.size,
                partnerCode: partnerCode,
                authorization: auth || '(未设置)',
                chunkSizeMB: chunkSizeMB,
                // 新增字段
                orgId: orgId || '(未设置)',
                ahId: ahId || '(未设置)',
                projId: projId || '(未设置)',
                caseId: caseId || '(未设置)',
                identifier: identifier,
                sectionId: sectionId || '(未设置)'
            };
            showParams('ant', params);
            
            addLog('ant-log', `开始蚂蚁上传: ${file.name} (${(file.size/1024/1024).toFixed(2)}MB)`, 'info');
            if (auth) {
                addLog('ant-log', `使用Authorization: ${auth.substring(0, 20)}...`, 'info');
            }
            // 记录新增字段信息
            addLog('ant-log', `新增字段 - 机构ID: ${orgId || '(空)'}, 院区ID: ${ahId || '(空)'}, 项目ID: ${projId || '(空)'}, 病例ID: ${caseId || '(空)'}`, 'info');
            addLog('ant-log', `🔄 标识符: ${identifier} - 支持同文件多次上传`, 'info');
            addLog('ant-log', `🆕 章节ID: ${sectionId || '(空)'} - 仅传递给BasicInfo接口`, 'info');
            showStatus('ant', '上传中...', 'uploading');
            
            // 自动添加到批量查询列表
            addSliceIdToBatchList(sliceId);
            
            await uploadFile('ant', file, chunkSizeMB * 1024 * 1024, {
                sliceId: sliceId,
                partnerCode: partnerCode,
                authorization: auth,
                orgId: orgId,
                ahId: ahId,
                projId: projId,
                caseId: caseId,
                identifier: identifier,
                sectionId: sectionId
            });
        }
        
        // 切片池上传
        async function startPoolUpload() {
            const file = document.getElementById('pool-file').files[0];
            const chunkSizeMB = parseInt(document.getElementById('pool-chunkSize').value);
            const partnerCode = document.getElementById('pool-partnerCode').value;
            const auth = document.getElementById('pool-auth').value;
            // 获取新增字段
            const orgId = document.getElementById('pool-orgId').value;
            const ahId = document.getElementById('pool-ahId').value;
            const projId = document.getElementById('pool-projId').value;
            const caseId = document.getElementById('pool-caseId').value;
            // 获取identifier字段
            let identifier = document.getElementById('pool-identifier').value.trim();
            
            // 如果identifier为空，自动生成一个
            if (!identifier) {
                identifier = 'pool_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
                document.getElementById('pool-identifier').value = identifier;
            }
            
            // 获取sectionId字段
            const sectionId = document.getElementById('pool-sectionId').value;
            
            if (!file || !partnerCode) {
                addLog('pool-log', '请填写所有必填字段', 'error');
                return;
            }
            
            const params = {
                filename: file.name,
                totalSize: file.size,
                partnerCode: partnerCode,
                authorization: auth || '(未设置)',
                chunkSizeMB: chunkSizeMB,
                // 新增字段
                orgId: orgId || '(未设置)',
                ahId: ahId || '(未设置)',
                projId: projId || '(未设置)',
                caseId: caseId || '(未设置)',
                identifier: identifier,
                sectionId: sectionId || '(未设置)'
            };
            showParams('pool', params);
            
            addLog('pool-log', `开始切片池上传: ${file.name} (${(file.size/1024/1024).toFixed(2)}MB)`, 'info');
            if (auth) {
                addLog('pool-log', `使用Authorization: ${auth.substring(0, 20)}...`, 'info');
            }
            // 记录新增字段信息
            addLog('pool-log', `新增字段 - 机构ID: ${orgId || '(空)'}, 院区ID: ${ahId || '(空)'}, 项目ID: ${projId || '(空)'}, 病例ID: ${caseId || '(空)'}`, 'info');
            addLog('pool-log', `🔄 标识符: ${identifier} - 支持同文件多次上传`, 'info');
            addLog('pool-log', `🆕 章节ID: ${sectionId || '(空)'} - 仅传递给BasicInfo接口`, 'info');
            showStatus('pool', '上传中...', 'uploading');
            
            await uploadFile('pool', file, chunkSizeMB * 1024 * 1024, {
                partnerCode: partnerCode,
                authorization: auth,
                orgId: orgId,
                ahId: ahId,
                projId: projId,
                caseId: caseId,
                identifier: identifier,
                sectionId: sectionId
            });
        }
        
        // 通用上传函数
        async function uploadFile(prefix, file, chunkSize, extraParams) {
            const totalChunks = Math.ceil(file.size / chunkSize);
            let uploadedChunks = 0;
            
            addLog(`${prefix}-log`, `文件分片数量: ${totalChunks}`, 'info');
            
            try {
                for (let i = 0; i < totalChunks; i++) {
                    const start = i * chunkSize;
                    const end = Math.min(start + chunkSize, file.size);
                    const chunk = file.slice(start, end);
                    
                    const formData = new FormData();
                    formData.append('file', chunk);
                    formData.append('filename', file.name);
                    formData.append('totalSize', file.size);
                    formData.append('chunkNumber', i + 1);
                    formData.append('totalChunks', totalChunks);
                    formData.append('currentChunkSize', chunk.size);
                    
                    // 添加额外参数（除了authorization，它会作为请求头发送）
                    Object.keys(extraParams).forEach(key => {
                        if (key !== 'authorization' && extraParams[key] !== undefined && extraParams[key] !== null && extraParams[key] !== '') {
                            formData.append(key, extraParams[key]);
                        }
                    });
                    
                    // 特别处理新增字段，即使为空也要发送（测试目的）
                    const newFields = ['orgId', 'ahId', 'projId', 'caseId', 'identifier', 'sectionId'];
                    newFields.forEach(field => {
                        if (extraParams[field] !== undefined && extraParams[field] !== null) {
                            // 如果字段已经通过上面的循环添加了，就不重复添加
                            let hasAdded = false;
                            for (let pair of formData.entries()) {
                                if (pair[0] === field) {
                                    hasAdded = true;
                                    break;
                                }
                            }
                            if (!hasAdded) {
                                formData.append(field, extraParams[field] || '');
                            }
                        }
                    });
                    
                    // 构建请求头
                    const headers = {};
                    if (extraParams.authorization && extraParams.authorization.trim() !== '') {
                        headers['Authorization'] = extraParams.authorization.trim();
                    }
                    
                    addLog(`${prefix}-log`, `上传分片 ${i + 1}/${totalChunks} (${(chunk.size/1024).toFixed(1)}KB)`, 'info');
                    
                    const response = await fetch(`${API_BASE}/file`, {
                        method: 'POST',
                        headers: headers,
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    // 修改：适配新的响应格式 {"code":200,"data":{"chunkNumber":xxx}}
                    if (response.ok && result.code === 200) {
                        uploadedChunks++;
                        const percentage = Math.round((uploadedChunks / totalChunks) * 100);
                        updateProgress(prefix, uploadedChunks, totalChunks, percentage);
                        addLog(`${prefix}-log`, `分片 ${i + 1} 上传成功`, 'success');
                    } else {
                        throw new Error(result.message || '上传失败');
                    }
                    
                    // 添加小延迟避免过快请求
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                addLog(`${prefix}-log`, '所有分片上传完成！', 'success');
                showStatus(prefix, '上传成功', 'success');
                
            } catch (error) {
                addLog(`${prefix}-log`, `上传失败: ${error.message}`, 'error');
                showStatus(prefix, '上传失败', 'error');
            }
        }
        
        // 查询蚂蚁上传进度
        async function queryAntProgress() {
            const sliceId = document.getElementById('ant-sliceId').value;
            const auth = document.getElementById('ant-auth').value;
            
            if (!sliceId || sliceId === 'slice_') {
                addLog('ant-log', '请先输入有效的sliceId', 'warning');
                return;
            }
            
            try {
                addLog('ant-log', `查询进度: ${sliceId}`, 'info');
                
                // 构建请求头
                const headers = {};
                if (auth && auth.trim() !== '') {
                    headers['Authorization'] = auth.trim();
                    addLog('ant-log', `使用Authorization查询进度`, 'info');
                }
                
                const response = await fetch(`${API_BASE}/progress/${sliceId}`, {
                    method: 'GET',
                    headers: headers
                });
                const result = await response.json();
                
                if (response.ok && result.success) {
                    const data = result.data;
                    addLog('ant-log', `进度查询成功: ${data.progress}% (${data.uploadedChunks}/${data.totalChunks})`, 'success');
                    updateProgress('ant', data.uploadedChunks, data.totalChunks, data.progress);
                    
                    if (data.isComplete) {
                        showStatus('ant', '上传完成', 'success');
                    }
                } else {
                    addLog('ant-log', `进度查询失败: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog('ant-log', `进度查询异常: ${error.message}`, 'error');
            }
        }
        
        // 添加当前蚂蚁上传的sliceId到批量查询
        function addToAntBatch() {
            const sliceId = document.getElementById('ant-sliceId').value;
            if (!sliceId || sliceId === 'slice_') {
                addBatchLog('❌ 请先输入有效的sliceId', 'error');
                return;
            }
            addSliceIdToBatchList(sliceId);
        }
        
        // 添加当前蚂蚁上传的sliceId
        function addCurrentAntSliceId() {
            const sliceId = document.getElementById('ant-sliceId').value;
            if (!sliceId || sliceId === 'slice_') {
                addBatchLog('❌ 蚂蚁上传区域的sliceId为空或无效', 'error');
                return;
            }
            addSliceIdToBatchList(sliceId);
        }
        
        // 添加示例sliceId
        function addSampleSliceIds() {
            const sampleIds = [
                'slice_1234567890_1001',
                'slice_1234567890_1002',
                'slice_1234567890_1003'
            ];
            
            sampleIds.forEach(id => addSliceIdToBatchList(id));
            addBatchLog('✅ 已添加示例切片ID', 'success');
        }
        
        // 批量查询进度
        async function batchQueryProgress() {
            const sliceIdListText = document.getElementById('batch-sliceIds').value.trim();
            const auth = document.getElementById('batch-auth').value.trim();
            
            if (!sliceIdListText) {
                addBatchLog('❌ 请输入要查询的切片ID列表', 'error');
                return;
            }
            
            // 解析切片ID列表
            const sliceIds = sliceIdListText.split('\n')
                .map(id => id.trim())
                .filter(id => id.length > 0);
            
            if (sliceIds.length === 0) {
                addBatchLog('❌ 切片ID列表为空', 'error');
                return;
            }
            
            if (sliceIds.length > 50) {
                addBatchLog(`❌ 切片ID数量超过限制，最多支持50个，当前${sliceIds.length}个`, 'error');
                return;
            }
            
            addBatchLog(`🔍 开始批量查询 ${sliceIds.length} 个切片的上传进度...`, 'info');
            addBatchLog(`查询的切片ID: ${sliceIds.join(', ')}`, 'info');
            
            try {
                const requestBody = {
                    sliceIds: sliceIds
                };
                
                // 构建请求头
                const headers = {
                    'Content-Type': 'application/json'
                };
                if (auth && auth.trim() !== '') {
                    headers['Authorization'] = auth.trim();
                    addBatchLog(`使用Authorization进行批量查询`, 'info');
                }
                
                const response = await fetch(`${API_BASE}/progress/batch`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestBody)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addBatchLog('✅ 批量查询成功！', 'success');
                    addBatchLog(`返回 ${Object.keys(result.data).length} 个切片的进度信息:`, 'info');
                    
                    // 显示每个切片的进度
                    for (const [sliceId, progress] of Object.entries(result.data)) {
                        const progressBar = '█'.repeat(Math.floor(progress / 5)) + '░'.repeat(20 - Math.floor(progress / 5));
                        const progressClass = progress === 100 ? 'completed' : 'info';
                        addBatchLog(`<div class="progress-item ${progressClass}">
                            <strong>${sliceId}</strong>: ${progress}%<br>
                            <div style="font-family: monospace;">[${progressBar}]</div>
                        </div>`, progressClass);
                    }
                    
                    // 显示未找到的切片ID
                    const foundSliceIds = Object.keys(result.data);
                    const notFoundSliceIds = sliceIds.filter(id => !foundSliceIds.includes(id));
                    if (notFoundSliceIds.length > 0) {
                        addBatchLog(`❌ 未找到以下切片ID: ${notFoundSliceIds.join(', ')}`, 'error');
                    }
                    
                    // 统计信息
                    const completedCount = Object.values(result.data).filter(progress => progress === 100).length;
                    const inProgressCount = Object.values(result.data).filter(progress => progress > 0 && progress < 100).length;
                    const notStartedCount = Object.values(result.data).filter(progress => progress === 0).length;
                    
                    addBatchLog(`📊 统计信息: 已完成 ${completedCount} 个，进行中 ${inProgressCount} 个，未开始 ${notStartedCount} 个`, 'info');
                    
                } else {
                    addBatchLog(`❌ 批量查询失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                addBatchLog(`❌ 批量查询请求失败: ${error.message}`, 'error');
            }
        }
        
        // 重置新增字段函数
        function resetLocalNewFields() {
            document.getElementById('local-orgId').value = 'org001';
            document.getElementById('local-ahId').value = 'ah001';
            document.getElementById('local-projId').value = 'proj001';
            document.getElementById('local-identifier').value = '';
            document.getElementById('local-sectionId').value = 'section001';
            addLog('local-log', '✅ 新增字段已重置为默认值', 'info');
        }
        
        function resetAntNewFields() {
            document.getElementById('ant-orgId').value = 'org002';
            document.getElementById('ant-ahId').value = 'ah002';
            document.getElementById('ant-projId').value = 'proj002';
            document.getElementById('ant-identifier').value = '';
            document.getElementById('ant-sectionId').value = 'section002';
            addLog('ant-log', '✅ 新增字段已重置为默认值', 'info');
        }
        
        function resetPoolNewFields() {
            document.getElementById('pool-orgId').value = 'org003';
            document.getElementById('pool-ahId').value = 'ah003';
            document.getElementById('pool-projId').value = 'proj003';
            document.getElementById('pool-identifier').value = '';
            document.getElementById('pool-sectionId').value = 'section003';
            addLog('pool-log', '✅ 新增字段已重置为默认值', 'info');
        }
        
        // 一键清空所有新增字段
        function clearAllNewFields() {
            ['local', 'ant', 'pool'].forEach(prefix => {
                document.getElementById(`${prefix}-orgId`).value = '';
                document.getElementById(`${prefix}-ahId`).value = '';
                document.getElementById(`${prefix}-projId`).value = '';
                document.getElementById(`${prefix}-identifier`).value = '';
                document.getElementById(`${prefix}-sectionId`).value = '';
            });
            addBatchLog('🧹 所有场景的新增字段已清空', 'info');
        }
        
        // 一键重置所有新增字段
        function resetAllNewFields() {
            resetLocalNewFields();
            resetAntNewFields();
            resetPoolNewFields();
            addBatchLog('🔄 所有场景的新增字段已重置为默认值', 'success');
        }
        
        // 测试sliceId唯一性功能
        async function testSliceIdUniqueness() {
            const currentSliceId = document.getElementById('ant-sliceId').value;
            
            if (!currentSliceId || currentSliceId === 'slice_') {
                addLog('ant-log', '❌ 请先设置一个有效的sliceId', 'error');
                return;
            }
            
            // 创建一个小测试文件
            const testFileContent = 'Test file for sliceId uniqueness validation';
            const testFile = new File([testFileContent], 'test_uniqueness.txt', { type: 'text/plain' });
            
            addLog('ant-log', `🧪 开始测试sliceId唯一性功能，当前sliceId: ${currentSliceId}`, 'info');
            addLog('ant-log', '🔥 重要：现在修改后第1个分片就会进行严格校验！', 'info');
            addLog('ant-log', '第一次上传（第1个分片）应该成功...', 'info');
            
            // 第一次上传
            try {
                const formData1 = new FormData();
                formData1.append('file', testFile);
                formData1.append('filename', testFile.name);
                formData1.append('totalSize', testFile.size);
                formData1.append('sliceId', currentSliceId);
                formData1.append('chunkNumber', 1);
                formData1.append('totalChunks', 1);
                formData1.append('currentChunkSize', testFile.size);
                formData1.append('partnerCode', 'test');
                
                const auth = document.getElementById('ant-auth').value;
                const headers1 = {};
                if (auth && auth.trim() !== '') {
                    headers1['Authorization'] = auth.trim();
                }
                
                const response1 = await fetch(`${API_BASE}/file`, {
                    method: 'POST',
                    headers: headers1,
                    body: formData1
                });
                
                const result1 = await response1.json();
                
                // 修改：适配新的响应格式
                if (response1.ok && result1.code === 200) {
                    addLog('ant-log', '✅ 第一次上传成功，数据库记录已创建', 'success');
                    
                    // 等待一秒
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    // 第二次上传相同的sliceId，应该在第1个分片就失败
                    addLog('ant-log', '第二次使用相同sliceId上传第1个分片，应该被立即拒绝...', 'info');
                    
                    const formData2 = new FormData();
                    formData2.append('file', testFile);
                    formData2.append('filename', 'test_uniqueness_2.txt');
                    formData2.append('totalSize', testFile.size);
                    formData2.append('sliceId', currentSliceId);
                    formData2.append('chunkNumber', 1);  // 🔥 第1个分片就应该被拒绝
                    formData2.append('totalChunks', 1);
                    formData2.append('currentChunkSize', testFile.size);
                    formData2.append('partnerCode', 'test');
                    
                    const response2 = await fetch(`${API_BASE}/file`, {
                        method: 'POST',
                        headers: headers1,
                        body: formData2
                    });
                    
                    const result2 = await response2.json();
                    
                    // 修改：适配新的响应格式，第二次上传应该失败（不是200状态码）
                    if (!response2.ok || result2.code !== 200) {
                        addLog('ant-log', `✅ 第二次上传在第1个分片被正确拒绝: ${result2.message}`, 'success');
                        addLog('ant-log', '🎉 sliceId唯一性校验功能测试通过！现在会在第1个分片就检测重复！', 'success');
                    } else {
                        addLog('ant-log', '❌ 测试失败：第二次上传的第1个分片应该被拒绝但却成功了', 'error');
                    }
                    
                } else {
                    addLog('ant-log', `❌ 第一次上传失败: ${result1.message}`, 'error');
                }
                
            } catch (error) {
                addLog('ant-log', `❌ 测试过程中发生异常: ${error.message}`, 'error');
            }
            
            // 为下次测试生成新的sliceId
            const newSliceId = generateSliceId();
            document.getElementById('ant-sliceId').value = newSliceId;
            addLog('ant-log', `💡 已为下次测试生成新的sliceId: ${newSliceId}`, 'info');
        }
        
        // AI批量截图相关功能
        
        // 添加AI截图日志
        function addAICaptureLog(message, type = 'info') {
            const resultContainer = document.getElementById('ai-capture-result');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultContainer.appendChild(logEntry);
            resultContainer.scrollTop = resultContainer.scrollHeight;
        }
        
        // 清空AI截图结果
        function clearAICaptureResults() {
            document.getElementById('ai-capture-result').innerHTML = '<div class="log-entry info">等待发送请求...</div>';
            document.getElementById('ai-images-container').innerHTML = '';
        }
        
        // 填充AI示例数据
        function fillAISampleData() {
            document.getElementById('ai-capture-requests').value = `[
  {
    "fileId": "01JYP476YF6GGSPQ1KKTHXEFXE",
    "cutimgid": "ai_capture_001",
    "x": 1000,
    "y": 1000,
    "w": 200,
    "h": 200
  },
  {
    "fileId": "01JYP476YF6GGSPQ1KKTHXEFXE",
    "cutimgid": "ai_capture_002",
    "x": 1500,
    "y": 1200,
    "w": 150,
    "h": 150
  },
  {
    "fileId": "01JYP476YF6GGSPQ1KKTHXEFXE",
    "cutimgid": "ai_capture_003",
    "x": 2000,
    "y": 800,
    "w": 180,
    "h": 180
  }
]`;
            addAICaptureLog('✅ 已填充AI示例数据（左上角坐标格式）', 'success');
            addAICaptureLog('📌 提示：坐标为左上角，直接用于截图', 'info');
        }
        
        // 发送AI批量截图请求
        async function sendAIBatchCaptureRequest() {
            const apiUrl = document.getElementById('ai-capture-url').value.trim();
            const requestBodyText = document.getElementById('ai-capture-requests').value.trim();

            if (!apiUrl) {
                addAICaptureLog('❌ 请输入API地址', 'error');
                return;
            }

            if (!requestBodyText) {
                addAICaptureLog('❌ 请输入请求参数', 'error');
                return;
            }

            // 解析JSON请求体
            let requestBody;
            try {
                requestBody = JSON.parse(requestBodyText);
                if (!Array.isArray(requestBody)) {
                    throw new Error('请求体必须是数组格式');
                }
            } catch (error) {
                addAICaptureLog(`❌ JSON解析失败: ${error.message}`, 'error');
                return;
            }

            addAICaptureLog(`🔍 开始发送AI批量截图请求，数量: ${requestBody.length}`, 'info');
            addAICaptureLog(`📋 请求地址: ${apiUrl}`, 'info');

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                
                addAICaptureLog('✅ AI批量截图请求成功！', 'success');
                addAICaptureLog(`📊 响应状态: ${result.status}`, 'info');
                addAICaptureLog(`📝 响应消息: ${result.msg}`, 'info');
                addAICaptureLog(`📦 返回数据数量: ${result.data ? result.data.length : 0}`, 'info');

                // 显示详细结果
                if (result.data && result.data.length > 0) {
                    result.data.forEach((item, index) => {
                        const status = item.image && item.image.length > 0 ? '✅ 成功' : '❌ 失败';
                        addAICaptureLog(`${index + 1}. cutimgid: ${item.cutimgid}, 状态: ${status}, 消息: ${item.msg}`, 
                               item.image && item.image.length > 0 ? 'success' : 'error');
                        
                        // 显示图像
                        if (item.image && item.image.length > 0) {
                            displayAIImage(item.cutimgid, item.image);
                        }
                    });
                } else {
                    addAICaptureLog('⚠️ 没有返回图像数据', 'error');
                }

            } catch (error) {
                addAICaptureLog(`❌ AI批量截图请求失败: ${error.message}`, 'error');
                console.error('AI截图请求错误详情:', error);
            }
        }
        
        // 显示AI图像
        function displayAIImage(cutimgid, base64Image) {
            const imagesContainer = document.getElementById('ai-images-container');
            
            const imageContainer = document.createElement('div');
            imageContainer.style.textAlign = 'center';
            imageContainer.style.padding = '10px';
            imageContainer.style.border = '1px solid #ddd';
            imageContainer.style.borderRadius = '4px';
            imageContainer.style.background = '#f8f9fa';
            
            const img = document.createElement('img');
            img.src = `data:image/jpeg;base64,${base64Image}`;
            img.alt = cutimgid;
            img.style.maxWidth = '100%';
            img.style.maxHeight = '120px';
            img.style.borderRadius = '4px';
            
            const label = document.createElement('div');
            label.textContent = cutimgid;
            label.style.fontSize = '11px';
            label.style.color = '#666';
            label.style.marginTop = '5px';
            label.style.wordBreak = 'break-all';
            
            const sizeLabel = document.createElement('div');
            sizeLabel.textContent = `${Math.round(base64Image.length * 0.75 / 1024)}KB`;
            sizeLabel.style.fontSize = '10px';
            sizeLabel.style.color = '#999';
            sizeLabel.style.marginTop = '2px';
            
            imageContainer.appendChild(img);
            imageContainer.appendChild(label);
            imageContainer.appendChild(sizeLabel);
            imagesContainer.appendChild(imageContainer);
            
            addAICaptureLog(`🖼️ 图像已生成: ${cutimgid} (大小: ${Math.round(base64Image.length * 0.75 / 1024)}KB)`, 'success');
        }
        
        // 切片截图相关功能
        
        // 添加截图日志
        function addCaptureLog(message, type = 'info') {
            const resultContainer = document.getElementById('capture-result');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultContainer.appendChild(logEntry);
            resultContainer.scrollTop = resultContainer.scrollHeight;
        }
        
        // 清空截图结果
        function clearCaptureResult() {
            document.getElementById('capture-result').innerHTML = '';
            document.getElementById('capture-images').style.display = 'none';
            document.getElementById('capture-params').textContent = '';
        }
        
                 // 填充示例数据
         function fillSampleCaptureData() {
                 document.getElementById('capture-fileId').value = '01JYP476YF6GGSPQ1KKTHXEFXE';
    document.getElementById('capture-zoom').value = '1.574901312368582';
    document.getElementById('capture-csize').value = '3174.0561049476405';
    document.getElementById('capture-level').value = '13';
    document.getElementById('capture-content').value = '病理切片截图测试 - 肿瘤细胞区域 - ' + new Date().toLocaleString();
             document.getElementById('capture-w').value = '160';
             document.getElementById('capture-h').value = '120';
             document.getElementById('capture-x').value = '1664.056104947638';
             document.getElementById('capture-y').value = '1351.6322225945228';
             document.getElementById('capture-rotate').value = '0';
             document.getElementById('capture-coords').value = `[
   {"x": 1664.056104947638, "y": 1351.6322225945228},
   {"x": 1824.056104947638, "y": 1351.6322225945228},
   {"x": 1824.056104947638, "y": 1471.6322225945228},
   {"x": 1664.056104947638, "y": 1471.6322225945228}
 ]`;
             document.getElementById('capture-function').value = '0';
             document.getElementById('capture-caseId').value = 'sample-case-' + Date.now();
             addCaptureLog('✅ 已填充示例数据（包含示例fileId）', 'success');
         }
        
                 
        
        // 截图接口
        async function startCaptureJson() {
            const fileId = document.getElementById('capture-fileId').value.trim();
            const zoom = parseFloat(document.getElementById('capture-zoom').value);
            const csize = parseFloat(document.getElementById('capture-csize').value);
            const level = parseInt(document.getElementById('capture-level').value);
            const content = document.getElementById('capture-content').value.trim();
            const w = parseFloat(document.getElementById('capture-w').value);
            const h = parseFloat(document.getElementById('capture-h').value);
            const x = parseFloat(document.getElementById('capture-x').value);
            const y = parseFloat(document.getElementById('capture-y').value);
            const rotate = parseFloat(document.getElementById('capture-rotate').value);
            const coordsText = document.getElementById('capture-coords').value.trim();
            const functionOption = document.getElementById('capture-function').value;
            const caseId = document.getElementById('capture-caseId').value.trim();
            
            // 参数验证
            if (!fileId) {
                addCaptureLog('❌ 请输入切片文件ID', 'error');
                return;
            }

            if (!content) {
                addCaptureLog('❌ 请输入描述内容', 'error');
                return;
            }
            
            // 解析坐标JSON
            let coords;
            try {
                coords = JSON.parse(coordsText);
                if (!Array.isArray(coords) || coords.length !== 4) {
                    throw new Error('坐标数组必须包含4个点');
                }
                coords.forEach((coord, index) => {
                    if (typeof coord.x !== 'number' || typeof coord.y !== 'number') {
                        throw new Error(`第${index + 1}个坐标点格式错误`);
                    }
                });
            } catch (error) {
                addCaptureLog(`❌ 坐标解析失败: ${error.message}`, 'error');
                return;
            }
            
            // 构建请求体
            const requestBody = {
                fileId: fileId,
                zoom: zoom,
                csize: csize,
                level: level,
                content: content,
                w: w,
                h: h,
                x: x,
                y: y,
                rotate: rotate,
                coords: coords,
                functionOption: functionOption,
                caseId: caseId
            };
            
            addCaptureLog(`🔍 开始截图请求...`, 'info');
            addCaptureLog(`📋 参数: fileId=${fileId}, 坐标=(${x},${y}), 尺寸=${w}x${h}, 层级=${level}`, 'info');
            
            try {
                const response = await fetch(`${SLICE_API_BASE}/capture`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                addCaptureLog('✅ 截图请求成功！', 'success');
                
                if (result.code === 200 && result.data) {
                    const data = result.data;
                    addCaptureLog(`📦 响应数据结构完整`, 'success');
                    addCaptureLog(`📋 fileId: ${data.fileId}`, 'info');
                    addCaptureLog(`📋 功能选项: ${data.functionOption}`, 'info');
                    
                    // 显示Base64图片
                    displayJsonImages(data);
                    
                } else {
                    addCaptureLog(`❌ 接口返回错误: ${result.msg}`, 'error');
                }
                
            } catch (error) {
                addCaptureLog(`❌ 截图请求失败: ${error.message}`, 'error');
                console.error('截图错误详情:', error);
            }
        }
        
        // 显示JSON响应中的Base64图片
        function displayJsonImages(data) {
            const imagesContainer = document.getElementById('capture-images');
            const originalImg = document.getElementById('capture-original');
            const thumbnailImg = document.getElementById('capture-thumbnail');
            
            if (data.cutImageOri) {
                const originalSrc = `data:image/jpeg;base64,${data.cutImageOri}`;
                originalImg.src = originalSrc;
                originalImg.onload = () => {
                    addCaptureLog(`📸 原图尺寸: ${originalImg.naturalWidth}x${originalImg.naturalHeight}`, 'success');
                };
                addCaptureLog(`📸 原图Base64数据长度: ${data.cutImageOri.length} 字符`, 'info');
                
                // 存储用于下载
                const binaryString = atob(data.cutImageOri);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }
                window.captureOriginalBlob = new Blob([bytes], { type: 'image/jpeg' });
            } else {
                originalImg.src = '';
                addCaptureLog('❌ 未找到原图Base64数据', 'error');
            }
            
            if (data.cutImage) {
                const thumbnailSrc = `data:image/jpeg;base64,${data.cutImage}`;
                thumbnailImg.src = thumbnailSrc;
                thumbnailImg.onload = () => {
                    addCaptureLog(`🖼️ 缩略图尺寸: ${thumbnailImg.naturalWidth}x${thumbnailImg.naturalHeight}`, 'success');
                };
                addCaptureLog(`🖼️ 缩略图Base64数据长度: ${data.cutImage.length} 字符`, 'info');
                
                // 存储用于下载
                const binaryString = atob(data.cutImage);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }
                window.captureThumbnailBlob = new Blob([bytes], { type: 'image/jpeg' });
            } else {
                thumbnailImg.src = '';
                addCaptureLog('❌ 未找到缩略图Base64数据', 'error');
            }
            
            imagesContainer.style.display = 'block';
        }
        

        
        // 下载截图图片
        function downloadCaptureImages() {
            const fileId = document.getElementById('capture-fileId').value.trim();
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            
            if (window.captureOriginalBlob) {
                const a = document.createElement('a');
                a.href = URL.createObjectURL(window.captureOriginalBlob);
                a.download = `${fileId}_original_${timestamp}.jpg`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                addCaptureLog('📥 原图下载已开始', 'success');
            }
            
            if (window.captureThumbnailBlob) {
                const a = document.createElement('a');
                a.href = URL.createObjectURL(window.captureThumbnailBlob);
                a.download = `${fileId}_thumbnail_${timestamp}.jpg`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                addCaptureLog('📥 缩略图下载已开始', 'success');
            }
            
            if (!window.captureOriginalBlob && !window.captureThumbnailBlob) {
                addCaptureLog('❌ 没有可下载的图片', 'error');
            }
        }
        
        // 页面加载时生成默认sliceId
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('ant-sliceId').value = generateSliceId();
            addBatchLog('📋 批量查询功能已就绪，可以添加切片ID进行批量查询', 'info');
            addBatchLog('🆕 新增字段测试功能已就绪，支持orgId、ahId、projId参数测试', 'info');
            addAICaptureLog('🤖 AI批量截图测试功能已就绪', 'info');
            addAICaptureLog('💡 提示: 根据AI算法分析的异常图像坐标，批量生成截图', 'info');
            addAICaptureLog('🔔 注意: 请确保fileId是系统中存在的真实切片文件ID', 'info');
            addCaptureLog('📸 切片截图测试功能已就绪', 'info');
            addCaptureLog('💡 提示: 请确保fileId是系统中存在的真实切片文件ID', 'info');
            addCaptureLog('🔔 注意: content描述字段为必填项，已设置默认值', 'info');
            addDeleteLog('🗑️ 切片删除测试功能已就绪', 'info');
            addDeleteLog('⚠️ 警告: 删除操作不可逆，请谨慎使用', 'warning');
            addDeleteLog('💡 提示: 需要提供正确的切片ID和合作伙伴代码', 'info');
            addDeleteLog('🔍 删除流程: 权限验证 → 文件删除 → 缓存清理 → 数据库删除', 'info');
            addRepushLog('🔄 重新推送测试功能已就绪', 'info');
            addRepushLog('💡 提示: 根据切片ID重新调用三方Info接口进行推送', 'info');
            addRepushLog('🔄 功能: 检查图片文件，如需要会重新解析切片生成图片', 'info');
        });
        
        // 重新推送相关功能
        
        // 添加重新推送日志
        function addRepushLog(message, type = 'info') {
            const resultContainer = document.getElementById('repush-result');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultContainer.appendChild(logEntry);
            resultContainer.scrollTop = resultContainer.scrollHeight;
        }
        
        // 清空重新推送结果
        function clearRepushResults() {
            document.getElementById('repush-result').innerHTML = '<div class="log-entry info">等待重新推送操作...</div>';
            document.getElementById('repush-status').style.display = 'none';
            document.getElementById('repush-details').style.display = 'none';
        }
        
        // 填充重新推送示例数据
        function fillRepushExample() {
            document.getElementById('repush-id').value = '01JYP476YF6GGSPQ1KKTHXEFXE';
            document.getElementById('repush-auth').value = '';
            addRepushLog('✅ 已填充示例数据', 'success');
            addRepushLog('⚠️ 请确认ID是否正确，避免误操作', 'warning');
        }
        
        // 从截图区域获取ID
        function getRepushIdFromCapture() {
            const captureFileId = document.getElementById('capture-fileId');
            if (captureFileId && captureFileId.value.trim()) {
                document.getElementById('repush-id').value = captureFileId.value.trim();
                addRepushLog('✅ 已从截图测试区域获取ID: ' + captureFileId.value.trim(), 'success');
            } else {
                addRepushLog('❌ 截图测试区域没有有效的fileId', 'error');
                addRepushLog('💡 提示: 请先在截图区域填入有效的fileId，或手动输入切片ID', 'info');
            }
        }
        
        // 更新重新推送进度显示
        function updateRepushProgress(message, type) {
            const progressContainer = document.getElementById('repush-progress');
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = type === 'success' ? '✅' : type === 'error' ? '❌' : '🔄';
            
            progressContainer.innerHTML = `
                <div style="color: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};">
                    ${statusIcon} [${timestamp}] ${message}
                </div>
            `;
        }
        
        // 显示重新推送详情
        function displayRepushDetails(data) {
            const detailsContainer = document.getElementById('repush-details-content');
            detailsContainer.innerHTML = JSON.stringify(data, null, 2);
            document.getElementById('repush-details').style.display = 'block';
        }
        
        // 执行重新推送操作
        async function repushSliceAnalysis() {
            const id = document.getElementById('repush-id').value.trim();
            const auth = document.getElementById('repush-auth').value.trim();
            
            // 参数验证
            if (!id) {
                addRepushLog('❌ 请输入切片ID', 'error');
                return;
            }
            
            // 构建请求参数
            const params = {
                id: id,
                authorization: auth || '(未设置)'
            };
            
            addRepushLog(`📋 重新推送参数: ID=${id}`, 'info');
            if (auth) {
                addRepushLog(`🔑 使用Authorization: ${auth.substring(0, 20)}...`, 'info');
            }
            
            // 二次确认
            const confirmMessage = `🔄 确认要重新推送吗？\n\n切片ID: ${id}\n\n此操作会重新调用三方Info接口进行推送`;
            if (!confirm(confirmMessage)) {
                addRepushLog('✋ 用户取消了重新推送操作', 'info');
                return;
            }
            
            addRepushLog(`🔄 开始重新推送切片: ${id}`, 'info');
            
            // 显示推送状态
            document.getElementById('repush-status').style.display = 'block';
            updateRepushProgress('🔍 正在查询切片信息...', 'info');
            
            try {
                // 构建推送URL
                const repushUrl = `${API_BASE}/repush/${encodeURIComponent(id)}`;
                addRepushLog(`📍 请求URL: ${repushUrl}`, 'info');
                
                // 构建请求头
                const headers = {
                    'Content-Type': 'application/json'
                };
                if (auth && auth.trim() !== '') {
                    headers['Authorization'] = auth.trim();
                }
                
                updateRepushProgress('🔄 正在执行重新推送...', 'info');
                
                const response = await fetch(repushUrl, {
                    method: 'POST',
                    headers: headers
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    updateRepushProgress('✅ 重新推送成功完成', 'success');
                    addRepushLog('🎉 重新推送成功！', 'success');
                    addRepushLog(`📊 响应消息: ${result.message || '推送完成'}`, 'success');
                    
                    // 显示详细结果
                    if (result.data) {
                        addRepushLog('📝 推送详情:', 'info');
                        
                        if (result.data.id) {
                            addRepushLog(`🆔 切片ID: ${result.data.id}`, 'info');
                        }
                        
                        if (result.data.partnerCode) {
                            addRepushLog(`🤝 合作伙伴代码: ${result.data.partnerCode}`, 'info');
                        }
                        
                        if (result.data.needReprocess !== undefined) {
                            addRepushLog(`🔄 是否需要重新处理: ${result.data.needReprocess ? '是' : '否'}`, 
                                       result.data.needReprocess ? 'warning' : 'success');
                        }
                        
                        if (result.data.pushSuccess !== undefined) {
                            addRepushLog(`📤 推送状态: ${result.data.pushSuccess ? '成功' : '失败'}`, 
                                       result.data.pushSuccess ? 'success' : 'error');
                        }
                        
                        if (result.data.status) {
                            addRepushLog(`📊 整体状态: ${result.data.status}`, 
                                       result.data.status === 'success' ? 'success' : 'warning');
                        }
                        
                        // 显示完整的数据详情
                        displayRepushDetails(result.data);
                    }
                    
                } else {
                    updateRepushProgress('❌ 重新推送失败', 'error');
                    addRepushLog(`❌ 重新推送失败: ${result.message || '未知错误'}`, 'error');
                    addRepushLog(`📊 响应状态码: ${response.status}`, 'error');
                    
                    // 显示可能的原因
                    if (response.status === 404) {
                        addRepushLog('💡 可能原因: 切片不存在或ID无效', 'warning');
                    } else if (response.status === 400) {
                        addRepushLog('💡 可能原因: 参数错误或ID格式不正确', 'warning');
                    } else if (response.status === 500) {
                        addRepushLog('💡 可能原因: 服务器内部错误或推送失败', 'warning');
                    }
                    
                    // 如果有错误详情，也显示
                    if (result.data) {
                        displayRepushDetails(result.data);
                    }
                }
                
            } catch (error) {
                updateRepushProgress('❌ 请求失败', 'error');
                addRepushLog(`❌ 重新推送请求失败: ${error.message}`, 'error');
                console.error('重新推送错误详情:', error);
                
                addRepushLog('💡 请检查:', 'warning');
                addRepushLog('• 网络连接是否正常', 'warning');
                addRepushLog('• 服务器是否运行', 'warning');
                addRepushLog('• 接口地址是否正确', 'warning');
            }
        }
        
        // 测试重新处理功能（模拟删除图片文件后重新推送）
        async function testRepushWithReprocess() {
            const id = document.getElementById('repush-id').value.trim();
            
            if (!id) {
                addRepushLog('❌ 请先输入切片ID', 'error');
                return;
            }
            
            addRepushLog('🧪 开始测试重新处理功能', 'info');
            addRepushLog('💡 此功能用于测试当图片文件不存在时的重新解析逻辑', 'info');
            addRepushLog(`🔍 测试切片ID: ${id}`, 'info');
            
            // 这里可以先删除图片文件缓存，然后调用重新推送
            // 暂时只提供提示信息
            addRepushLog('📝 测试步骤建议:', 'info');
            addRepushLog('1. 先手动清理对应切片的图片文件缓存', 'info');
            addRepushLog('2. 然后点击"重新推送"按钮', 'info');
            addRepushLog('3. 观察系统是否会重新解析切片文件生成图片', 'info');
            addRepushLog('4. 检查推送是否成功完成', 'info');
            
            // 实际可以调用缓存清理接口
            setTimeout(() => {
                addRepushLog('✅ 测试准备完成，可以进行重新推送测试', 'success');
            }, 1000);
        }
        
        // 切片删除相关功能
        
        // 添加删除日志
        function addDeleteLog(message, type = 'info') {
            const resultContainer = document.getElementById('delete-result');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultContainer.appendChild(logEntry);
            resultContainer.scrollTop = resultContainer.scrollHeight;
        }
        
        // 清空删除结果
        function clearDeleteResults() {
            document.getElementById('delete-results').innerHTML = '';
            // 移除对不存在元素的引用
            addDeleteLog('🧹 结果已清空', 'info');
        }
        
        // 填充示例删除数据
        function fillSampleDeleteData() {
            document.getElementById('delete-id').value = '01JYP476YF6GGSPQ1KKTHXEFXE';
            document.getElementById('delete-cname').value = 'qpDhc';
            document.getElementById('delete-auth').value = '';
            addDeleteLog('✅ 已填充示例删除数据', 'success');
            addDeleteLog('⚠️ 请确认ID和cname是否正确，避免误删', 'warning');
        }
        
        // 获取最近上传的ID
        function getLastUploadedId() {
            // 尝试从其他区域获取最近的ID
            const captureFileId = document.getElementById('capture-fileId').value.trim();
            
            if (captureFileId) {
                document.getElementById('delete-id').value = captureFileId;
                addDeleteLog(`✅ 已获取到切片ID: ${captureFileId}`, 'success');
                addDeleteLog('⚠️ 请确认此ID是否为要删除的切片', 'warning');
            } else {
                addDeleteLog('❌ 未找到可用的切片ID，请手动输入', 'error');
                addDeleteLog('💡 提示: 可以从截图测试区域或数据库中获取ID', 'info');
            }
        }
        
        // 测试删除权限验证
        async function testDeletePermission() {
            const id = document.getElementById('delete-id').value.trim();
            const cname = document.getElementById('delete-cname').value.trim();
            
            if (!id || !cname) {
                addDeleteLog('❌ 请先输入切片ID和合作伙伴代码', 'error');
                return;
            }
            
            addDeleteLog('🔍 测试权限验证（模拟查询）...', 'info');
            addDeleteLog(`查询参数: ID=${id}, cname=${cname}`, 'info');
            
            // 这里可以添加一个查询接口来验证权限，暂时模拟
            setTimeout(() => {
                addDeleteLog('💡 权限验证测试提示:', 'info');
                addDeleteLog('• 确保ID是数据库中存在的主键ID', 'info');
                addDeleteLog('• 确保cname与数据库中的partner_code匹配', 'info');
                addDeleteLog('• 如果参数正确，可以进行删除操作', 'success');
            }, 500);
        }
        
        // 执行删除操作
        async function deleteSlice() {
            const id = document.getElementById('delete-id').value.trim();
            const cname = document.getElementById('delete-cname').value.trim();
            const auth = document.getElementById('delete-auth').value.trim();
            
            // 参数验证
            if (!id) {
                addDeleteLog('❌ 请输入切片ID', 'error');
                return;
            }
            
            if (!cname) {
                addDeleteLog('❌ 请输入合作伙伴代码', 'error');
                return;
            }
            
            // 构建请求参数
            const params = {
                id: id,
                cname: cname,
                authorization: auth || '(未设置)'
            };
            
            // 显示参数到日志中而不是不存在的元素
            addDeleteLog(`📋 删除参数: ${JSON.stringify(params, null, 2)}`, 'info');
            
            // 二次确认
            const confirmMessage = `⚠️ 确认要删除切片吗？\n\n切片ID: ${id}\n合作伙伴代码: ${cname}\n\n此操作不可逆！`;
            if (!confirm(confirmMessage)) {
                addDeleteLog('✋ 用户取消了删除操作', 'info');
                return;
            }
            
            addDeleteLog(`🗑️ 开始删除切片: ${id}`, 'info');
            addDeleteLog(`📋 合作伙伴代码: ${cname}`, 'info');
            if (auth) {
                addDeleteLog(`🔑 使用Authorization: ${auth.substring(0, 20)}...`, 'info');
            }
            
            // 显示删除状态
            document.getElementById('delete-status').style.display = 'block';
            updateDeleteProgress('🔍 正在验证权限...', 'info');
            
            try {
                // 构建删除URL
                const deleteUrl = `${API_BASE}/delete/${encodeURIComponent(id)}?cname=${encodeURIComponent(cname)}`;
                addDeleteLog(`📍 请求URL: ${deleteUrl}`, 'info');
                
                // 构建请求头
                const headers = {};
                if (auth && auth.trim() !== '') {
                    headers['Authorization'] = auth.trim();
                }
                
                updateDeleteProgress('🗂️ 正在删除文件...', 'info');
                
                const response = await fetch(deleteUrl, {
                    method: 'DELETE',
                    headers: headers
                });
                
                const result = await response.json();
                
                if (response.ok && result.code === 200) {
                    updateDeleteProgress('✅ 删除成功完成', 'success');
                    addDeleteLog('🎉 切片删除成功！', 'success');
                    addDeleteLog(`📊 响应消息: ${result.data || result.message || '删除完成'}`, 'success');
                    
                    // 显示删除步骤
                    addDeleteLog('📝 删除步骤:', 'info');
                    addDeleteLog('✅ 1. 权限验证 - 通过', 'success');
                    addDeleteLog('✅ 2. 文件删除 - 完成', 'success');
                    addDeleteLog('✅ 3. 缓存清理 - 完成', 'success');
                    addDeleteLog('✅ 4. 数据库删除 - 完成', 'success');
                    
                } else {
                    updateDeleteProgress('❌ 删除失败', 'error');
                    addDeleteLog(`❌ 删除失败: ${result.message || result.msg || '未知错误'}`, 'error');
                    addDeleteLog(`📊 响应状态码: ${response.status}`, 'error');
                    
                    // 显示可能的原因
                    if (response.status === 404) {
                        addDeleteLog('💡 可能原因: 切片不存在或ID/cname不匹配', 'warning');
                    } else if (response.status === 403) {
                        addDeleteLog('💡 可能原因: 权限不足或认证失败', 'warning');
                    } else if (response.status === 500) {
                        addDeleteLog('💡 可能原因: 服务器内部错误或文件删除失败', 'warning');
                    }
                }
                
            } catch (error) {
                updateDeleteProgress('❌ 请求失败', 'error');
                addDeleteLog(`❌ 删除请求失败: ${error.message}`, 'error');
                console.error('删除错误详情:', error);
                
                addDeleteLog('💡 请检查:', 'warning');
                addDeleteLog('• 网络连接是否正常', 'warning');
                addDeleteLog('• 服务器是否运行', 'warning');
                addDeleteLog('• 接口地址是否正确', 'warning');
            }
        }
        
        // 更新删除进度显示
        function updateDeleteProgress(message, type) {
            const progressContainer = document.getElementById('delete-progress');
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = type === 'success' ? '✅' : type === 'error' ? '❌' : '🔄';
            
            progressContainer.innerHTML = `
                <div style="color: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};">
                    ${statusIcon} [${timestamp}] ${message}
                </div>
            `;
        }

        // 强制清理所有DZI缓存
        function forceCleanAllCache() {
            const identifier = document.getElementById('delete-id').value.trim();
            
            if (!identifier) {
                addDeleteLog('⚠️ 请先填入切片ID再清理缓存', 'warning');
                return;
            }

            addDeleteLog('🧹 开始强制清理所有DZI缓存...', 'info');
            
            const params = new URLSearchParams();
            params.append('identifier', identifier);
            
            fetch(`${API_BASE}/admin/clear-all-cache`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: params
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addDeleteLog('✅ 缓存清理成功', 'success');
                    addDeleteLog(`📊 清理统计: 总计${data.data.totalClearedItems}个项目`, 'info');
                    
                    if (data.data.controllerCacheCleared !== undefined) {
                        addDeleteLog(`📦 控制器缓存: 清理${data.data.controllerCacheCleared}个项目`, 'info');
                    }
                    
                    if (data.data.serviceCacheCleared !== undefined) {
                        addDeleteLog(`🎯 服务层缓存: 清理${data.data.serviceCacheCleared}个项目`, 'info');
                    }
                    
                    if (data.data.redisCacheCleared !== undefined) {
                        addDeleteLog(`🔄 Redis缓存: 清理${data.data.redisCacheCleared}个项目`, 'info');
                    }
                    
                    addDeleteLog('💡 建议: 刷新DZI查看器页面以验证缓存清理效果', 'info');
                } else {
                    addDeleteLog('❌ 缓存清理失败: ' + (data.message || '未知错误'), 'error');
                    if (data.data && data.data.error) {
                        addDeleteLog('🔍 详细错误: ' + data.data.error, 'error');
                    }
                }
            })
            .catch(error => {
                addDeleteLog('❌ 缓存清理请求失败: ' + error.message, 'error');
                console.error('Cache clear error:', error);
            });
        }

        // 填充删除示例数据
        function fillDeleteExample() {
            document.getElementById('delete-id').value = '01JZ80SNAF9XCJWBET8VK3Z8S3';
            document.getElementById('delete-cname').value = 'qpDhc';
            document.getElementById('delete-auth').value = '';
            addDeleteLog('📋 已填充示例数据', 'info');
        }

        // 获取最近上传的ID
        function getRecentUploadId() {
            // 尝试从截图测试区域获取fileId
            const captureFileId = document.getElementById('capture-fileId');
            if (captureFileId && captureFileId.value.trim()) {
                document.getElementById('delete-id').value = captureFileId.value.trim();
                addDeleteLog('🚀 已从截图测试区域获取ID: ' + captureFileId.value.trim(), 'success');
                return;
            }

            // 如果没有找到，提示用户
            addDeleteLog('💡 提示: 请从其他测试区域复制有效的切片ID，或查询数据库获取', 'info');
            addDeleteLog('🔍 可以从以下位置获取ID:', 'info');
            addDeleteLog('• 切片截图测试区域的fileId字段', 'info');
            addDeleteLog('• 数据库t_c_slice表的主键id字段', 'info');
            addDeleteLog('• 上传成功的接口响应结果', 'info');
        }
    </script>
</body>
</html>
