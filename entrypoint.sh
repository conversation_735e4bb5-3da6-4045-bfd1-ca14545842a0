#!/bin/bash
set -e

export JAVA_HOME=/usr/local/java/jdk-22.0.2+9
export PATH=$JAVA_HOME/bin:$PATH

# 设置系统语言和时区
export LANG=zh_CN.UTF-8
export LANGUAGE=zh_CN:zh
export LC_ALL=zh_CN.UTF-8
export TZ=Asia/Shanghai

# 应用目录
APP_DIR="/mitr/server/ccaa-slice"
APP_JAR="${APP_DIR}/ccaa-slice.jar"
APP_CONFIG="${APP_DIR}/application.yml"

# 环境变量（可在 docker run 时通过 -e 覆盖）
JAVA_OPTS=${JAVA_OPTS:-"-Xms4g -Xmx8g -Xss10m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:ParallelGCThreads=4 -XX:ConcGCThreads=4 -XX:InitiatingHeapOccupancyPercent=45 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/mitr/server/ccaa-slice/logs/heapdump.hprof -Xlog:gc*:file=/mitr/server/ccaa-slice/logs/gc.log:time,uptime:filecount=5,filesize=20M -Dfile.encoding=UTF-8 -Djna.debug_load=false -Djna.tmpdir=/mitr/server/ccaa-slice/temp -Djna.nosys=false -Djna.noclasspath=false -Djna.encoding=UTF-8 -Djna.protect=true -Djava.library.path=/usr/local/lib/tron:/usr/local/sqrayslide/lib:/usr/local/lib64:/usr/lib/x86_64-linux-gnu --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/sun.security.util=ALL-UNNAMED -XX:+UnlockDiagnosticVMOptions -XX:+DebugNonSafepoints -XX:+LogVMOutput -XX:+UseStringDeduplication -XX:+UseLargePages"}
ENABLE_GC_LOG=${ENABLE_GC_LOG:-"false"}

# 创建日志目录
mkdir -p "${APP_DIR}/logs"

# 确保临时目录存在并有正确权限
mkdir -p "${APP_DIR}/temp"
chmod 777 "${APP_DIR}/temp"
echo "临时目录权限已设置: ${APP_DIR}/temp"

# 检查和修复JNA库问题
echo "检查JNA库环境..."

# 创建JNA临时目录
export JNA_TMPDIR="${APP_DIR}/temp"
mkdir -p "${JNA_TMPDIR}"
chmod 777 "${JNA_TMPDIR}"

# 检查系统中是否有JNA native库
echo "搜索系统中的JNA native库..."
find /usr -name "*jnidispatch*" 2>/dev/null || echo "未找到系统JNA库"

# 尝试从JAR中提取JNA native库
echo "尝试从JAR中提取JNA native库..."
cd "${APP_DIR}"

# 创建临时目录用于提取
mkdir -p "${APP_DIR}/jna-extract"
cd "${APP_DIR}/jna-extract"

# 提取JNA库
java -cp "${APP_JAR}" -Djna.tmpdir="${APP_DIR}/jna-extract" -Djna.nosys=false -Djna.noclasspath=false \
  -c "import com.sun.jna.Native; System.out.println(\"JNA库路径: \" + System.getProperty(\"jna.tmpdir\")); Native.loadLibrary(\"c\");" 2>/dev/null || echo "JNA库提取失败"

# 查找提取的库文件
echo "查找提取的JNA库文件..."
find "${APP_DIR}/jna-extract" -name "*jnidispatch*" 2>/dev/null || echo "未找到提取的JNA库"

# 如果找到了库文件，复制到系统目录
if [ -f "${APP_DIR}/jna-extract/jna"*"/libjnidispatch.so" ]; then
    echo "找到JNA库文件，复制到系统目录..."
    mkdir -p /usr/lib/x86_64-linux-gnu/
    cp "${APP_DIR}/jna-extract/jna"*"/libjnidispatch.so" /usr/lib/x86_64-linux-gnu/
    # 创建符号链接
    cd /usr/lib/x86_64-linux-gnu/
    ln -sf libjnidispatch.so jnidispatch 2>/dev/null || true
    echo "JNA库已安装到系统目录"
fi

# 清理临时文件
rm -rf "${APP_DIR}/jna-extract"

# 设置LD_LIBRARY_PATH
export LD_LIBRARY_PATH="/usr/local/lib/tron:/usr/local/sqrayslide/lib:/usr/local/lib64:/usr/lib/x86_64-linux-gnu:${LD_LIBRARY_PATH}"

# 添加GC日志参数
if [ "${ENABLE_GC_LOG}" = "true" ]; then
  JAVA_OPTS="${JAVA_OPTS} -Xlog:gc*:file=${APP_DIR}/logs/gc.log:time,uptime:filecount=5,filesize=20M"
  echo "GC日志已启用，输出到: ${APP_DIR}/logs/gc.log"
fi

echo "启动应用..."
echo "Java参数: ${JAVA_OPTS}"

# 启动应用
cd "${APP_DIR}"
exec java -Dfile.encoding=UTF-8 ${JAVA_OPTS} \
    --enable-native-access=ALL-UNNAMED \
    --add-opens=java.base/java.lang=ALL-UNNAMED \
    --add-opens=java.base/java.util=ALL-UNNAMED \
    --add-opens=java.base/sun.nio.ch=ALL-UNNAMED \
    --add-opens=java.base/java.io=ALL-UNNAMED \
    -jar "${APP_JAR}" --spring.config.location="${APP_CONFIG}"
